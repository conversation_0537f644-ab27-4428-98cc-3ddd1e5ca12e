MAILTO=<EMAIL>

# +--------- Minute (0-59)
# |    +------- Hour (0-23)
# |    |    +----- Day Of Month (1-31)
# |    |    |    +--- Month (1 -12)
# |    |    |    |    +- Day Of Week (0-6) (Sunday = 0)
# |    |    |    |    |
# *    *    *    *    *     COMMAND
13     *    *    *    *     {{COMMAND}} resource:permanent-remove >> {{LOGPATH}}/permanent-remove.log 2>&1
12    */2   *    *    *     {{COMMAND}} letsencrypt:renew >> {{LOGPATH}}/letsencrypt-renew.log 2>&1
15    */2   *    *    *     {{COMMAND}} letsencrypt:result:cleanup >> {{LOGPATH}}/letsencrypt-result-cleanup.log 2>&1
*/5    *    *    *    *     {{COMMAND}} letsencrypt:request:cancel-inactive >> {{LOGPATH}}/letsencrypt-request-inactive-cleanup.log 2>&1
0      12   *    *    *     {{COMMAND}} kafka:export-account-resources >> {{LOGPATH}}/export-account-resources.log 2>&1
*/30  9-16  *    *   1-5    {{COMMAND}} certificate:cleanup -l 20 --skip-skipping-log >> {{LOGPATH}}/certificate-cleanup.log 2>&1
