# This file is a "template" of which env vars needs to be defined in your configuration or in an .env file
# Set variables here that may be different on each deployment target of the app, e.g. development, staging, production.
# https://symfony.com/doc/current/best_practices/configuration.html#infrastructure-related-configuration

###> symfony/framework-bundle ###
APP_ENV=dev
APP_SECRET=%generate(secret)%
#TRUSTED_PROXIES=127.0.0.1,*********
#TRUSTED_HOSTS=localhost,example.com
###< symfony/framework-bundle ###

###> doctrine/doctrine-bundle ###
# Format described at http://docs.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# For an SQLite database, use: "sqlite:///%kernel.project_dir%/var/data.db"
# Configure your db driver and server_version in config/packages/doctrine.yaml
DATABASE_URL=postgresql://db_user:db_password@127.0.0.1:5432/db_name?serverVersion=10
###< doctrine/doctrine-bundle ###

# RabbitMQ configuration
RABBITMQ_API_DSN=rabbitmq://username:password@127.0.0.1:5672/api
ENABLE_NOTIFICATION_TO_RABBIT=true

# Redis configuration
REDIS_CACHE_DSN=redis://127.0.0.1:6379/0
REDIS_CACHE_PROFILE=3.2

# production only settings
# MON_MASTER_PATH=/path/to/mc/lock
# SENTRY_DSN=https://public:<EMAIL>/1
# CERTIFICATES_PATH=/path/to/bucket

OPEN_API_SPEC_HOST=nxgapi.cdn77.eu
OPEN_API_SPEC_SCHEME=http

KAFKA_BOOTSTRAP_SERVERS=1.kafka.cdn77.dev:9092,2.kafka.cdn77.dev:9092,3.kafka.cdn77.dev:9092,4.kafka.cdn77.dev:9092,5.kafka.cdn77.dev:9092

CANARY_URL_PRODUCTION=nxg-api.cdn77.eu
CANARY_URL_CANARY=canary.nxg-api.cdn77.eu

LE_DEBUG_RESOURCES_LIST=[]
