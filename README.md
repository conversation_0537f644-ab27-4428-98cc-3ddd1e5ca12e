# NXGAPI - Nginx API

## Installation via Docker

### 1. Workspace

You need to download workspace (postgres/adminer) from `https://gitlab.cdn77.eu/cdn/docker-workspace`
and run `make` or `docker-compose up -d`

### 2. Project Setup
#### 2.1 Docker

In this repository run:

`docker-compose up -d`

the project has an sql dump included in the sql directory at `./sql/cdn77_schema.sql` the schema is imported with docker setup so no further database setup should be required.

#### 2.2 Requirements & Dependencies

* PHP 8.3 with extensions installed (see `composer.json`)
* Run `composer install` to install dependencies
* Run `bin/console rabbitmq:setup` to setup `rabbitmq`

#### 2.3 Environment

You have to add `.env` and add local variable from `.env.dist` to run locally or with your dev server setup

Then go to http://nxgapi.localhost/ or use `nxgapi.localhost` as your base uri for any REST api calls.

### 3. Tests & QA

For tests add `.env.test` file and set your `APP_ENV` variable to `APP_ENV=test`

For local docker database use `DATABASE_URL="********************************************/nxgapi?serverVersion=10"`

NXGAPI also uses phpstan static analysis, to run it use:

`./vendor/bin/phpstan`

To run coding standard check use:

`./vendor/bin/phpcs`

Optionally use autofix to resolve coding standard issues with:

`./vendor/bin/phpcbf`

### 4. Production

Produkce jsou virtuální stroje, kde vždy jeden je součástí master fail-over "clusteru" a je zodpovědný za odbavování requestů, ostatní jsou "dormant".

Na produkci používáme Nginx + PHP FPM + atomic deploy.

V MONu jsou senzory pro kontrolu NXG API, které testují FPM i jeden endpoint aplikace.
