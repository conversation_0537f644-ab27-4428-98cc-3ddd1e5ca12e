<?xml version="1.0"?>
<phpunit
    bootstrap="vendor/autoload.php"
    colors="true"
    backupGlobals="false"
    backupStaticAttributes="false"
    beStrictAboutChangesToGlobalState="true"
    beStrictAboutOutputDuringTests="true"
    beStrictAboutTestsThatDoNotTestAnything="true"
    beStrictAboutTodoAnnotatedTests="true"
>
    <php>
        <ini name="error_reporting" value="-1"/>
    </php>
    <testsuites>
        <testsuite name="MON Master Detector Tests Suite">
            <directory>tests/</directory>
        </testsuite>
    </testsuites>
    <logging>
        <log
            type="coverage-text"
            target="php://stdout"
            showUncoveredFiles="true"
            showOnlySummary="true"
        />
    </logging>
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">src/</directory>
        </whitelist>
    </filter>
</phpunit>
