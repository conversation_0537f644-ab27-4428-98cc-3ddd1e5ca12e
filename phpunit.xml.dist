<?xml version="1.0" encoding="UTF-8"?>

<!-- https://phpunit.de/manual/current/en/appendixes.configuration.html -->
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         beStrictAboutChangesToGlobalState="true"
         beStrictAboutOutputDuringTests="true"
         beStrictAboutTodoAnnotatedTests="true"
         colors="true"
         executionOrder="random"
         failOnRisky="true"
         failOnWarning="true"
         bootstrap="tests/bootstrap.php"
         failOnEmptyTestSuite="true"
         failOnIncomplete="true"
>
    <php>
        <ini name="error_reporting" value="E_ALL"/>

        <env name="KERNEL_CLASS" value="Cdn77\NxgApi\Kernel"/>
    </php>

    <listeners>
        <listener class="DAMA\DoctrineTestBundle\PHPUnit\PHPUnitListener" />
    </listeners>
</phpunit>
