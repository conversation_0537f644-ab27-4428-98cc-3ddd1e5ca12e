parameters:
	ignoreErrors:
		-
			message: "#^Parameter \\#1 \\$environment of class Cdn77\\\\NxgApi\\\\Kernel constructor expects string, mixed given\\.$#"
			count: 1
			path: bin/console

		-
			message: "#^Parameter \\#1 \\$location of static method Cdn77\\\\NxgApi\\\\Account\\\\Domain\\\\DTO\\\\AccountDatacenterLocation\\:\\:fromArray\\(\\) expects array\\<string, bool\\|string\\>, array\\<string, mixed\\> given\\.$#"
			count: 1
			path: src/Account/Infrastructure/DoctrineLocationRepository.php

		-
			message: "#^Cannot access offset 'id'\\|'uid' on mixed\\.$#"
			count: 1
			path: src/CanaryCheck/Domain/Comparison/ComparisonList.php

		-
			message: "#^Parameter \\#2 \\$array of function array_key_exists expects array, mixed given\\.$#"
			count: 1
			path: src/CanaryCheck/Domain/Comparison/ComparisonList.php

		-
			message: "#^Parameter \\#2 \\$canary of method Cdn77\\\\NxgApi\\\\CanaryCheck\\\\Domain\\\\Comparison\\\\Comparison\\:\\:compareDetail\\(\\) expects array\\<int\\|string, mixed\\>, mixed given\\.$#"
			count: 1
			path: src/CanaryCheck/Domain/Comparison/ComparisonList.php

		-
			message: "#^Parameter \\#3 \\$production of method Cdn77\\\\NxgApi\\\\CanaryCheck\\\\Domain\\\\Comparison\\\\Comparison\\:\\:compareDetail\\(\\) expects array\\<int\\|string, mixed\\>, mixed given\\.$#"
			count: 1
			path: src/CanaryCheck/Domain/Comparison/ComparisonList.php

		-
			message: "#^Parameter \\#1 \\$value of static method Cdn77\\\\NxgApi\\\\Certificate\\\\Domain\\\\Enum\\\\MoveOnlyParameter\\:\\:tryFrom\\(\\) expects int\\|string, mixed given\\.$#"
			count: 1
			path: src/Certificate/Application/Console/CertificateCleanupCommand.php

		-
			message: "#^Parameter \\#2 \\.\\.\\.\\$values of function sprintf expects bool\\|float\\|int\\|string\\|null, mixed given\\.$#"
			count: 1
			path: src/Certificate/Application/Console/CertificateCleanupCommand.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Certificate\\\\Infrastructure\\\\Finder\\\\DbalInactiveFinder\\:\\:findInactiveResources\\(\\) should return array\\<int\\> but returns array\\<int, mixed\\>\\.$#"
			count: 1
			path: src/Certificate/Infrastructure/Finder/DbalInactiveFinder.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Certificate\\\\Infrastructure\\\\Finder\\\\DbalInactiveFinder\\:\\:findResourcesWithInactiveSsl\\(\\) should return array\\<int\\> but returns array\\<int, mixed\\>\\.$#"
			count: 1
			path: src/Certificate/Infrastructure/Finder/DbalInactiveFinder.php

		-
			message: "#^Cannot cast mixed to string\\.$#"
			count: 1
			path: src/Certificate/Infrastructure/Finder/DbalSslFilesForCleanupFinder.php

		-
			message: "#^Parameter \\#1 \\$row of static method Cdn77\\\\NxgApi\\\\Certificate\\\\Application\\\\DTO\\\\SslFileCleanup\\:\\:fromFetchResult\\(\\) expects array\\<string, bool\\|int\\|string\\>, array\\<string, mixed\\> given\\.$#"
			count: 1
			path: src/Certificate/Infrastructure/Finder/DbalSslFilesForCleanupFinder.php

		-
			message: "#^Parameter \\#1 \\$callback of function array_map expects \\(callable\\(mixed\\)\\: mixed\\)\\|null, 'intval' given\\.$#"
			count: 1
			path: src/Certificate/Infrastructure/Finder/DbalUsedResourceIdsFinder.php

		-
			message: "#^Cannot access offset 'edges' on mixed\\.$#"
			count: 2
			path: src/Clap/Infrastructure/GraphQLAccountFinder.php

		-
			message: "#^Parameter \\#1 \\$edges of method Cdn77\\\\NxgApi\\\\Clap\\\\Infrastructure\\\\GraphQLAccountFinder\\:\\:getIds\\(\\) expects array\\<int, array\\<string, array\\<string, string\\>\\>\\>, mixed given\\.$#"
			count: 2
			path: src/Clap/Infrastructure/GraphQLAccountFinder.php

		-
			message: "#^Parameter \\#1 \\$keys of function array_combine expects array\\<int\\|string\\>, mixed given\\.$#"
			count: 1
			path: src/Core/Application/OpenApi/PathGenerator.php

		-
			message: "#^Parameter \\#2 \\$values of function array_combine expects array, mixed given\\.$#"
			count: 1
			path: src/Core/Application/OpenApi/PathGenerator.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Core\\\\Application\\\\OpenApi\\\\PathGenerator\\:\\:\\$compiledRoutes \\(array\\<string, array\\<int, mixed\\>\\>\\) does not accept mixed\\.$#"
			count: 1
			path: src/Core/Application/OpenApi/PathGenerator.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Core\\\\Application\\\\Request\\\\ControllerSchemaSerializer\\:\\:deserialize\\(\\) should return Cdn77\\\\NxgApi\\\\Schema\\\\Legacy\\\\ErrorsSchema\\|TSchema of Cdn77\\\\NxgApi\\\\Core\\\\Application\\\\Payload\\\\Schema but returns mixed\\.$#"
			count: 1
			path: src/Core/Application/Request/ControllerSchemaSerializer.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Core\\\\Application\\\\Request\\\\ControllerSchemaSerializer\\:\\:deserializeQueryString\\(\\) should return Cdn77\\\\NxgApi\\\\Schema\\\\Legacy\\\\ErrorsSchema\\|T of Cdn77\\\\NxgApi\\\\Core\\\\Application\\\\Payload\\\\Schema but returns mixed\\.$#"
			count: 1
			path: src/Core/Application/Request/ControllerSchemaSerializer.php

		-
			message: "#^Parameter \\#1 \\$class of method Cdn77\\\\NxgApi\\\\Core\\\\Application\\\\Response\\\\PropertyNaming\\\\PropertyPathConverter\\:\\:getMetadata\\(\\) expects string, mixed given\\.$#"
			count: 1
			path: src/Core/Application/Response/PropertyNaming/PropertyPathConverter.php

		-
			message: "#^Parameter \\#2 \\$string of function explode expects string, mixed given\\.$#"
			count: 1
			path: src/Core/Application/Serializer/CommaSeparatedIntegersHandler.php

		-
			message: "#^Parameter \\#2 \\$subject of function Safe\\\\preg_match expects string, mixed given\\.$#"
			count: 1
			path: src/Core/Application/Serializer/CommaSeparatedIntegersHandler.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Core\\\\Application\\\\Utility\\\\ValueReplacer\\:\\:jsonStringToArray\\(\\) should return array\\<string, mixed\\>\\|null but returns mixed\\.$#"
			count: 1
			path: src/Core/Application/Utility/ValueReplacer.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Core\\\\Application\\\\Utility\\\\ValueReplacer\\:\\:notNullValueToDecodedJson\\(\\) should return array\\<string, mixed\\>\\|null but returns mixed\\.$#"
			count: 1
			path: src/Core/Application/Utility/ValueReplacer.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Core\\\\Infrastructure\\\\Repository\\\\DoctrineSslRepository\\:\\:findForResource\\(\\) should return Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Ssl\\|null but returns mixed\\.$#"
			count: 1
			path: src/Core/Infrastructure/Repository/DoctrineSslRepository.php

		-
			message: "#^Cannot cast mixed to int\\.$#"
			count: 1
			path: src/Entity/Legacy/Id/ResourceIdGenerator.php

		-
			message: "#^Cannot access offset 'state' on mixed\\.$#"
			count: 1
			path: src/LetsEncrypt/Infrastructure/Finder/DoctrineRequestFinder.php

		-
			message: "#^Cannot access offset 'stateReason' on mixed\\.$#"
			count: 1
			path: src/LetsEncrypt/Infrastructure/Finder/DoctrineRequestFinder.php

		-
			message: "#^Cannot access offset 'context' on mixed\\.$#"
			count: 1
			path: src/Log/Formatter/RequestsFormatter.php

		-
			message: "#^Cannot access offset 'datetime' on mixed\\.$#"
			count: 1
			path: src/Log/Formatter/RequestsFormatter.php

		-
			message: "#^Cannot access offset 'extra' on mixed\\.$#"
			count: 1
			path: src/Log/Formatter/RequestsFormatter.php

		-
			message: "#^Cannot access offset 'message' on mixed\\.$#"
			count: 1
			path: src/Log/Formatter/RequestsFormatter.php

		-
			message: "#^Cannot access offset 'payload' on mixed\\.$#"
			count: 3
			path: src/Log/Formatter/RequestsFormatter.php

		-
			message: "#^Cannot access offset 'request_id' on mixed\\.$#"
			count: 1
			path: src/Log/Formatter/RequestsFormatter.php

		-
			message: "#^Cannot cast mixed to string\\.$#"
			count: 1
			path: src/Log/Formatter/RequestsFormatter.php

		-
			message: "#^Parameter \\#1 \\$input of method Cdn77\\\\NxgApi\\\\Log\\\\Formatter\\\\RequestsFormatter\\:\\:stripSensitiveInformation\\(\\) expects string, mixed given\\.$#"
			count: 1
			path: src/Log/Formatter/RequestsFormatter.php

		-
			message: "#^Parameter \\#2 \\.\\.\\.\\$values of function sprintf expects bool\\|float\\|int\\|string\\|null, mixed given\\.$#"
			count: 1
			path: src/Log/Formatter/RequestsFormatter.php

		-
			message: "#^Parameter \\#3 \\.\\.\\.\\$values of function sprintf expects bool\\|float\\|int\\|string\\|null, mixed given\\.$#"
			count: 1
			path: src/Log/Formatter/RequestsFormatter.php

		-
			message: "#^Parameter \\#4 \\.\\.\\.\\$values of function sprintf expects bool\\|float\\|int\\|string\\|null, mixed given\\.$#"
			count: 1
			path: src/Log/Formatter/RequestsFormatter.php

		-
			message: "#^Cannot access offset 'request_id' on mixed\\.$#"
			count: 1
			path: src/Log/Processor/RequestIdProcessor.php

		-
			message: "#^Parameter \\#1 \\$resourceConfiguration of static method Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Application\\\\Payload\\\\ResourceSchema\\:\\:fromDto\\(\\) expects Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Application/Payload/ResourceSchema.php

		-
			message: "#^Argument of an invalid type mixed supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: src/NgxConfGen/Application/Payload/ResourcesSchema.php

		-
			message: "#^Parameter \\#1 \\$datetime of class DateTimeImmutable constructor expects string, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#1 \\$id of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, mixed given\\.$#"
			count: 6
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#1 \\$value of static method Cdn77\\\\NxgApi\\\\Core\\\\Application\\\\Utility\\\\ValueReplacer\\:\\:jsonStringToArray\\(\\) expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#10 \\$cacheExpiry of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#11 \\$cacheExpiry404 of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#12 \\$cacheBypass of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#13 \\$purgeAllKey of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#14 \\$sslFileIndex of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#15 \\$fullLogs of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#16 \\$ignoredQueryParams of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects array\\<int, string\\>\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#17 \\$httpsRedirectCode of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#18 \\$refererDenyEmpty of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects bool\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#19 \\$refererType of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#2 \\$accountId of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#2 \\$string of function explode expects string, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#20 \\$refererDomains of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects array\\<int, string\\>\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#21 \\$ipProtectionType of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#22 \\$ipProtectionAddresses of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects array\\<int, string\\>\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#23 \\$geoProtectionType of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#24 \\$geoProtectionCountries of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects array\\<int, string\\>\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#25 \\$cacheMinUses of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#26 \\$streamingPlaylistBypass of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#27 \\$cacheLockAge of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#28 \\$cacheLockTimeout of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#29 \\$cacheContentLengthLimit of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#3 \\$cdnUrl of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects string, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#30 \\$cacheNoContentLengthLimit of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#31 \\$upstreamFailTimeout of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#32 \\$upstreamNextAttempts of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#33 \\$customData of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects array\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#34 \\$quic of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#35 \\$waf of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#36 \\$corsOriginHeader of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#37 \\$corsTimingEnabled of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#38 \\$corsWildcardEnabled of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#4 \\$groupId of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#41 \\$secureTokenType of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#42 \\$secureTokenValue of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#43 \\$secureLinkExpiryParam of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#44 \\$secureLinkTokenParam of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#45 \\$secureLinkPathlenParam of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#46 \\$secureLinkSecretParam of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#48 \\$origins of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects array\\<int, array\\<string, mixed\\>\\>, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#49 \\$responseHeaders of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects array\\<string, string\\>\\|null, array\\<string, mixed\\>\\|null given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#7 \\$disableQueryString of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#8 \\$ignoreSetCookie of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#9 \\$mp4PseudoStreaming of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceConfiguration.php

		-
			message: "#^Parameter \\#1 \\$priority of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceOriginConfiguration constructor expects int, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceOriginConfiguration.php

		-
			message: "#^Parameter \\#10 \\$s3BucketName of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceOriginConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceOriginConfiguration.php

		-
			message: "#^Parameter \\#11 \\$s3Type of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceOriginConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceOriginConfiguration.php

		-
			message: "#^Parameter \\#14 \\$originHeaders of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceOriginConfiguration constructor expects array\\<string, string\\>\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceOriginConfiguration.php

		-
			message: "#^Parameter \\#16 \\$followRedirectCodes of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceOriginConfiguration constructor expects array\\<int, int\\>\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceOriginConfiguration.php

		-
			message: "#^Parameter \\#2 \\$host of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceOriginConfiguration constructor expects string, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceOriginConfiguration.php

		-
			message: "#^Parameter \\#3 \\$scheme of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceOriginConfiguration constructor expects string, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceOriginConfiguration.php

		-
			message: "#^Parameter \\#4 \\$basedir of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceOriginConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceOriginConfiguration.php

		-
			message: "#^Parameter \\#5 \\$port of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceOriginConfiguration constructor expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceOriginConfiguration.php

		-
			message: "#^Parameter \\#6 \\$timeout of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceOriginConfiguration constructor expects int\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceOriginConfiguration.php

		-
			message: "#^Parameter \\#7 \\$s3AccessKeyId of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceOriginConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceOriginConfiguration.php

		-
			message: "#^Parameter \\#8 \\$s3Secret of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceOriginConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceOriginConfiguration.php

		-
			message: "#^Parameter \\#9 \\$s3Region of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceOriginConfiguration constructor expects string\\|null, mixed given\\.$#"
			count: 1
			path: src/NgxConfGen/Domain/Value/ResourceOriginConfiguration.php

		-
			message: "#^Cannot cast mixed to int\\.$#"
			count: 1
			path: src/NgxConfGen/Infrastructure/Finder/DbalResourceConfigurationFinder.php

		-
			message: "#^Argument of an invalid type mixed supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: src/Repository/Legacy/CdnResourceRepository.php

		-
			message: "#^Cannot access offset 'cnames' on mixed\\.$#"
			count: 1
			path: src/Repository/Legacy/CdnResourceRepository.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Repository\\\\Legacy\\\\CdnResourceRepository\\:\\:doGetAllCnames\\(\\) should return array\\<string\\> but returns array\\<int\\<0, max\\>, mixed\\>\\.$#"
			count: 1
			path: src/Repository/Legacy/CdnResourceRepository.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Repository\\\\Legacy\\\\CdnResourceRepository\\:\\:findByDomain\\(\\) should return Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\|null but returns mixed\\.$#"
			count: 1
			path: src/Repository/Legacy/CdnResourceRepository.php

		-
			message: "#^Cannot access offset 'inDns' on mixed\\.$#"
			count: 1
			path: src/Repository/Legacy/IpRepository.php

		-
			message: "#^Cannot access offset 'locationId' on mixed\\.$#"
			count: 1
			path: src/Repository/Legacy/IpRepository.php

		-
			message: "#^Cannot access offset 'ok' on mixed\\.$#"
			count: 1
			path: src/Repository/Legacy/IpRepository.php

		-
			message: "#^Cannot access offset 'paused' on mixed\\.$#"
			count: 1
			path: src/Repository/Legacy/IpRepository.php

		-
			message: "#^Cannot access offset 'serverId' on mixed\\.$#"
			count: 1
			path: src/Repository/Legacy/IpRepository.php

		-
			message: "#^Parameter \\#1 \\$locationId of class Cdn77\\\\NxgApi\\\\DTO\\\\Legacy\\\\ServerUpIpListDTO constructor expects int, mixed given\\.$#"
			count: 1
			path: src/Repository/Legacy/IpRepository.php

		-
			message: "#^Parameter \\#2 \\$serverId of class Cdn77\\\\NxgApi\\\\DTO\\\\Legacy\\\\ServerUpIpListDTO constructor expects int, mixed given\\.$#"
			count: 1
			path: src/Repository/Legacy/IpRepository.php

		-
			message: "#^Parameter \\#3 \\$inDns of class Cdn77\\\\NxgApi\\\\DTO\\\\Legacy\\\\ServerUpIpListDTO constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/Repository/Legacy/IpRepository.php

		-
			message: "#^Parameter \\#4 \\$ok of class Cdn77\\\\NxgApi\\\\DTO\\\\Legacy\\\\ServerUpIpListDTO constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/Repository/Legacy/IpRepository.php

		-
			message: "#^Parameter \\#5 \\$up of class Cdn77\\\\NxgApi\\\\DTO\\\\Legacy\\\\ServerUpIpListDTO constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/Repository/Legacy/IpRepository.php

		-
			message: "#^Argument of an invalid type mixed supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: src/Repository/Legacy/LocationRepository.php

		-
			message: "#^Cannot access offset 'active' on mixed\\.$#"
			count: 1
			path: src/Repository/Legacy/LocationRepository.php

		-
			message: "#^Cannot access offset 'assigned' on mixed\\.$#"
			count: 1
			path: src/Repository/Legacy/LocationRepository.php

		-
			message: "#^Cannot access offset 'location' on mixed\\.$#"
			count: 1
			path: src/Repository/Legacy/LocationRepository.php

		-
			message: "#^Cannot access offset 'locationId' on mixed\\.$#"
			count: 1
			path: src/Repository/Legacy/LocationRepository.php

		-
			message: "#^Parameter \\#1 \\$location of class Cdn77\\\\NxgApi\\\\DTO\\\\Legacy\\\\ResourceLocationDTO constructor expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Location, mixed given\\.$#"
			count: 1
			path: src/Repository/Legacy/LocationRepository.php

		-
			message: "#^Parameter \\#1 \\$location of static method Cdn77\\\\NxgApi\\\\Resource\\\\Domain\\\\DTO\\\\ResourceDatacenterLocation\\:\\:fromArray\\(\\) expects array\\<string, bool\\|string\\>, array\\<string, mixed\\> given\\.$#"
			count: 2
			path: src/Repository/Legacy/LocationRepository.php

		-
			message: "#^Parameter \\#2 \\$locationId of class Cdn77\\\\NxgApi\\\\DTO\\\\Legacy\\\\ResourceLocationDTO constructor expects string, mixed given\\.$#"
			count: 1
			path: src/Repository/Legacy/LocationRepository.php

		-
			message: "#^Parameter \\#3 \\$active of class Cdn77\\\\NxgApi\\\\DTO\\\\Legacy\\\\ResourceLocationDTO constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/Repository/Legacy/LocationRepository.php

		-
			message: "#^Parameter \\#4 \\$assigned of class Cdn77\\\\NxgApi\\\\DTO\\\\Legacy\\\\ResourceLocationDTO constructor expects bool, mixed given\\.$#"
			count: 1
			path: src/Repository/Legacy/LocationRepository.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Repository\\\\Legacy\\\\ServerRepository\\:\\:findOneByUidWithIpsAndLocations\\(\\) should return Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null but returns mixed\\.$#"
			count: 1
			path: src/Repository/Legacy/ServerRepository.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Repository\\\\LetsEncrypt\\\\ResultRepository\\:\\:countSuccessCertificateGenerationForDomainsSinceDate\\(\\) should return int but returns mixed\\.$#"
			count: 1
			path: src/Repository/LetsEncrypt/ResultRepository.php

		-
			message: "#^Parameter \\#2 \\$schema of static method Cdn77\\\\NxgApi\\\\Resource\\\\Domain\\\\Command\\\\EnableDatacentersForResource\\:\\:fromSchema\\(\\) expects Cdn77\\\\NxgApi\\\\Resource\\\\Application\\\\Payload\\\\EnabledResourceDatacentersSchema, mixed given\\.$#"
			count: 1
			path: src/Resource/Application/Controller/EnableDatacentersController.php

		-
			message: "#^Parameter \\#2 \\$resourceIds of class Cdn77\\\\NxgApi\\\\Resource\\\\Domain\\\\DTO\\\\AccountResources constructor expects array\\<int, int\\>, mixed given\\.$#"
			count: 1
			path: src/Resource/Infrastructure/Finder/DbalAccountResourcesFinder.php

		-
			message: "#^Parameter \\#2 \\$subject of function Safe\\\\preg_match expects string, mixed given\\.$#"
			count: 1
			path: src/Resource/Infrastructure/Finder/FilesystemMainCertificateFinder.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Service\\\\Legacy\\\\Certificate\\\\CertificateManager\\:\\:findLatestInstantSslRequest\\(\\) should return Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Request\\|null but returns mixed\\.$#"
			count: 1
			path: src/Service/Legacy/Certificate/CertificateManager.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Service\\\\Legacy\\\\Certificate\\\\CertificateManager\\:\\:findLatestInstantSslResult\\(\\) should return Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Result\\|null but returns mixed\\.$#"
			count: 1
			path: src/Service/Legacy/Certificate/CertificateManager.php

		-
			message: "#^Cannot access offset 'commonName' on mixed\\.$#"
			count: 1
			path: src/Service/Legacy/Certificate/OpenSSLCertificateMetadataParser.php

		-
			message: "#^Cannot access offset 'subjectAltName' on mixed\\.$#"
			count: 1
			path: src/Service/Legacy/Certificate/OpenSSLCertificateMetadataParser.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Service\\\\Legacy\\\\Certificate\\\\OpenSSLCertificateMetadataParser\\:\\:parseCommonName\\(\\) should return string\\|null but returns mixed\\.$#"
			count: 1
			path: src/Service/Legacy/Certificate/OpenSSLCertificateMetadataParser.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Service\\\\Legacy\\\\Certificate\\\\OpenSSLCertificateMetadataParser\\:\\:parseSerial\\(\\) should return string but returns mixed\\.$#"
			count: 1
			path: src/Service/Legacy/Certificate/OpenSSLCertificateMetadataParser.php

		-
			message: "#^Parameter \\#1 \\$validity of method Cdn77\\\\NxgApi\\\\Service\\\\Legacy\\\\Certificate\\\\OpenSSLCertificateMetadataParser\\:\\:parseValidityField\\(\\) expects int, mixed given\\.$#"
			count: 2
			path: src/Service/Legacy/Certificate/OpenSSLCertificateMetadataParser.php

		-
			message: "#^Parameter \\#2 \\$subject of function Safe\\\\preg_split expects string, mixed given\\.$#"
			count: 1
			path: src/Service/Legacy/Certificate/OpenSSLCertificateMetadataParser.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Service\\\\Legacy\\\\GeoProtection\\\\GeoProtectionManager\\:\\:findProtectionForUpdate\\(\\) should return Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\ResourceGeoProtection\\|null but returns mixed\\.$#"
			count: 1
			path: src/Service/Legacy/GeoProtection/GeoProtectionManager.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Service\\\\Legacy\\\\HotlinkProtection\\\\RefererManager\\:\\:findReferer\\(\\) should return Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\ResourceRefererProtection\\|null but returns mixed\\.$#"
			count: 1
			path: src/Service/Legacy/HotlinkProtection/RefererManager.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Service\\\\Legacy\\\\IpProtection\\\\IpProtectionManager\\:\\:findProtectionForUpdate\\(\\) should return Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\ResourceIpProtection\\|null but returns mixed\\.$#"
			count: 1
			path: src/Service/Legacy/IpProtection/IpProtectionManager.php

		-
			message: "#^Cannot access offset int\\|string on mixed\\.$#"
			count: 1
			path: src/Service/Legacy/Locations/ResourceLocationsChanger.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Service\\\\Legacy\\\\Locations\\\\ResourceLocationsChanger\\:\\:getCurrentPops\\(\\) should return array\\<array\\> but returns array\\.$#"
			count: 1
			path: src/Service/Legacy/Locations/ResourceLocationsChanger.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Service\\\\Legacy\\\\Locations\\\\ResourceLocationsChanger\\:\\:getPossiblePops\\(\\) should return array\\<array\\> but returns array\\.$#"
			count: 1
			path: src/Service/Legacy/Locations/ResourceLocationsChanger.php

		-
			message: "#^Cannot access property \\$date on mixed\\.$#"
			count: 1
			path: src/Service/Messaging/Message/ResourceChangeMessage.php

		-
			message: "#^Cannot access property \\$id on mixed\\.$#"
			count: 1
			path: src/Service/Messaging/Message/ResourceChangeMessage.php

		-
			message: "#^Cannot access property \\$type on mixed\\.$#"
			count: 1
			path: src/Service/Messaging/Message/ResourceChangeMessage.php

		-
			message: "#^Parameter \\#1 \\$string of function trim expects string, mixed given\\.$#"
			count: 1
			path: src/Types/ResourceCnamesArrayType.php

		-
			message: "#^Parameter \\#2 \\$array of function implode expects array\\<string\\>, mixed given\\.$#"
			count: 1
			path: src/Types/ResourceCnamesArrayType.php

		-
			message: "#^Parameter \\#2 \\$array of function implode expects array\\|null, mixed given\\.$#"
			count: 1
			path: src/Types/ResourceCnamesArrayType.php

		-
			message: "#^Cannot cast mixed to string\\.$#"
			count: 1
			path: src/Validator/Constraints/AnyValidator.php

		-
			message: "#^Parameter \\#1 \\$certificate of method Cdn77\\\\NxgApi\\\\Validator\\\\Constraints\\\\Certificate\\\\CertificateValidator\\:\\:isPemCertificateValid\\(\\) expects string, mixed given\\.$#"
			count: 1
			path: src/Validator/Constraints/Certificate/CertificateValidator.php

		-
			message: "#^Parameter \\#1 \\$private_key of function Safe\\\\openssl_pkey_get_private expects array\\|OpenSSLAsymmetricKey\\|OpenSSLCertificate\\|string, mixed given\\.$#"
			count: 1
			path: src/Validator/Constraints/Certificate/PrivateKeyValidator.php

		-
			message: "#^Parameter \\#2 \\$subject of function Safe\\\\preg_match expects string, mixed given\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/AllowedCnameDomainValidator.php

		-
			message: "#^Cannot access property \\$cdnResource on mixed\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/AllowedOriginDomainValidator.php

		-
			message: "#^Parameter \\#2 \\$subject of function Safe\\\\preg_match expects string, mixed given\\.$#"
			count: 4
			path: src/Validator/Constraints/Resource/AllowedOriginDomainValidator.php

		-
			message: "#^Parameter \\#2 \\$subject of function Safe\\\\preg_match expects string, mixed given\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/DomainOrIpValidator.php

		-
			message: "#^Parameter \\#2 \\$value of method Symfony\\\\Component\\\\Validator\\\\Violation\\\\ConstraintViolationBuilderInterface\\:\\:setParameter\\(\\) expects string, mixed given\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/DomainOrIpValidator.php

		-
			message: "#^Parameter \\#1 \\$string of function strlen expects string, mixed given\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/DomainValidator.php

		-
			message: "#^Parameter \\#2 \\$subject of function Safe\\\\preg_match expects string, mixed given\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/DomainValidator.php

		-
			message: "#^Parameter \\#2 \\$value of method Symfony\\\\Component\\\\Validator\\\\Violation\\\\ConstraintViolationBuilderInterface\\:\\:setParameter\\(\\) expects string, mixed given\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/DomainValidator.php

		-
			message: "#^Argument of an invalid type mixed supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/LocationGroupValidator.php

		-
			message: "#^Cannot cast mixed to string\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/LocationGroupValidator.php

		-
			message: "#^Cannot access property \\$cdnResource on mixed\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/Mp4Validator.php

		-
			message: "#^Parameter \\#1 \\$mp4 of method Cdn77\\\\NxgApi\\\\Validator\\\\Constraints\\\\Resource\\\\Mp4Validator\\:\\:isSetMp4AndDisabledQueryString\\(\\) expects int, mixed given\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/Mp4Validator.php

		-
			message: "#^Parameter \\#1 \\$object_or_class of function method_exists expects object\\|string, mixed given\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/Mp4Validator.php

		-
			message: "#^Cannot access property \\$cdnResource on mixed\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/ResponseHeadersValidator.php

		-
			message: "#^Cannot access property \\$headers on mixed\\.$#"
			count: 3
			path: src/Validator/Constraints/Resource/ResponseHeadersValidator.php

		-
			message: "#^Cannot access property \\$cdnResource on mixed\\.$#"
			count: 2
			path: src/Validator/Constraints/Resource/SecureTokenValidator.php

		-
			message: "#^Cannot call method resourceDto\\(\\) on mixed\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/SslValidator.php

		-
			message: "#^Parameter \\#1 \\$cnames of method Cdn77\\\\NxgApi\\\\Repository\\\\Legacy\\\\CdnResourceRepository\\:\\:findUsedCnames\\(\\) expects array\\<string\\>, mixed given\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/UniqueCnameValidator.php

		-
			message: "#^Parameter \\#1 \\$value of function count expects array\\|Countable, mixed given\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/UniqueCnameValidator.php

		-
			message: "#^Parameter \\#2 \\$cnames of method Cdn77\\\\NxgApi\\\\Validator\\\\Constraints\\\\Resource\\\\UniqueCnameValidator\\:\\:getCnameIndex\\(\\) expects array\\<string\\>, mixed given\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/UniqueCnameValidator.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Account/Application/Controller/DatacenterListControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Account/Application/Controller/DatacenterListControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Account\\\\Application\\\\Controller\\\\DatacenterListControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Account/Application/Controller/DatacenterListControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Account/Application/Controller/DatacenterListControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Certificate/CertificateCleanerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Certificate/CertificateCleanerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Certificate\\\\CertificateCleanerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Certificate/CertificateCleanerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Certificate/CertificateCleanerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Controller/Internal/Server/DetailControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Controller/Internal/Server/DetailControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Internal\\\\Server\\\\DetailControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/DetailControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/DetailControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Controller/Internal/Server/Force/ForceDownControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Controller/Internal/Server/Force/ForceDownControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Internal\\\\Server\\\\Force\\\\ForceDownControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/Force/ForceDownControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/Force/ForceDownControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Controller/Internal/Server/Force/ForceUpControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Controller/Internal/Server/Force/ForceUpControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Internal\\\\Server\\\\Force\\\\ForceUpControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/Force/ForceUpControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/Force/ForceUpControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Controller/Internal/Server/Force/UnforceControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Controller/Internal/Server/Force/UnforceControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Internal\\\\Server\\\\Force\\\\UnforceControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/Force/UnforceControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/Force/UnforceControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Controller/Internal/Server/ListControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Controller/Internal/Server/ListControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Internal\\\\Server\\\\ListControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/ListControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/ListControllerTest.php

		-
			message: "#^Parameter \\#1 \\$value of function count expects array\\|Countable, mixed given\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Groups/ListControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Controller/Legacy/Resources/DetailControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Controller/Legacy/Resources/DetailControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Legacy\\\\Resources\\\\DetailControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/DetailControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/DetailControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Controller/Legacy/Resources/DomainLookupControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Controller/Legacy/Resources/DomainLookupControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Legacy\\\\Resources\\\\DomainLookupControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/DomainLookupControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/DomainLookupControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Controller/Legacy/Resources/ListControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Controller/Legacy/Resources/ListControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Legacy\\\\Resources\\\\ListControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/ListControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 2
			path: tests/Functional/Controller/Legacy/Resources/ListControllerTest.php

		-
			message: "#^Parameter \\#1 \\$resources of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Legacy\\\\Resources\\\\ListControllerTest\\:\\:sortCustomDataForMultipleResources\\(\\) expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/ListControllerTest.php

		-
			message: "#^Parameter \\#2 \\$callback of function usort expects callable\\(mixed, mixed\\)\\: int, Closure\\(array, array\\)\\: int\\<\\-1, 1\\> given\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/ListControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Controller/Legacy/Resources/Ssl/SetControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Controller/Legacy/Resources/Ssl/SetControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Legacy\\\\Resources\\\\Ssl\\\\SetControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/Ssl/SetControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/Ssl/SetControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Entity/Legacy/Id/ResourceIdGeneratorTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Entity/Legacy/Id/ResourceIdGeneratorTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Entity\\\\Legacy\\\\Id\\\\ResourceIdGeneratorTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Entity/Legacy/Id/ResourceIdGeneratorTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Entity/Legacy/Id/ResourceIdGeneratorTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/FullLogs/Application/Controller/DisableControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/FullLogs/Application/Controller/DisableControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\FullLogs\\\\Application\\\\Controller\\\\DisableControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/FullLogs/Application/Controller/DisableControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/FullLogs/Application/Controller/DisableControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/FullLogs/Application/Controller/EnableControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/FullLogs/Application/Controller/EnableControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\FullLogs\\\\Application\\\\Controller\\\\EnableControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/FullLogs/Application/Controller/EnableControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/FullLogs/Application/Controller/EnableControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/FullLogs/Application/Controller/StatusControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/FullLogs/Application/Controller/StatusControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\FullLogs\\\\Application\\\\Controller\\\\StatusControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/FullLogs/Application/Controller/StatusControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/FullLogs/Application/Controller/StatusControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Ip/Application/Controller/StatusControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Ip/Application/Controller/StatusControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Ip\\\\Application\\\\Controller\\\\StatusControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Ip/Application/Controller/StatusControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Ip/Application/Controller/StatusControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/LetsEncrypt/Application/Console/InactiveResourceCancellationCommandTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/LetsEncrypt/Application/Console/InactiveResourceCancellationCommandTest.php

		-
			message: "#^Cannot call method getId\\(\\) on mixed\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Console/InactiveResourceCancellationCommandTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\LetsEncrypt\\\\Application\\\\Console\\\\InactiveResourceCancellationCommandTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Console/InactiveResourceCancellationCommandTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Console/InactiveResourceCancellationCommandTest.php

		-
			message: "#^Parameter \\#1 \\$request of method Cdn77\\\\NxgApi\\\\Repository\\\\LetsEncrypt\\\\TaskRepository\\:\\:getRequestTask\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Request, mixed given\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Console/InactiveResourceCancellationCommandTest.php

		-
			message: "#^Parameter \\#1 \\$request of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\LetsEncrypt\\\\Application\\\\Console\\\\InactiveResourceCancellationCommandTest\\:\\:validateRequestCancelled\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Request, mixed given\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Console/InactiveResourceCancellationCommandTest.php

		-
			message: "#^Parameter \\#1 \\$request of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\LetsEncrypt\\\\Application\\\\Console\\\\InactiveResourceCancellationCommandTest\\:\\:validateRequestPending\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Request, mixed given\\.$#"
			count: 2
			path: tests/Functional/LetsEncrypt/Application/Console/InactiveResourceCancellationCommandTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Cannot call method cancel\\(\\) on mixed\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Cannot call method getCertificate\\(\\) on mixed\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Cannot call method getId\\(\\) on mixed\\.$#"
			count: 8
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Cannot call method getPrivateKey\\(\\) on mixed\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Cannot call method hasInstantSsl\\(\\) on mixed\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Cannot call method setDeleted\\(\\) on mixed\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Cannot call method setSuspended\\(\\) on mixed\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\LetsEncrypt\\\\Application\\\\Controller\\\\CreateControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Parameter \\#1 \\$certificatePair of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\LetsEncrypt\\\\Application\\\\Controller\\\\CreateControllerTest\\:\\:extractCertificateDomains\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\CertificatePair, mixed given\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Parameter \\#1 \\$certificatePair of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\LetsEncrypt\\\\Application\\\\Controller\\\\CreateControllerTest\\:\\:extractCertificateExpiration\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\CertificatePair, mixed given\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Parameter \\#1 \\$object of method Doctrine\\\\Persistence\\\\ObjectManager\\:\\:persist\\(\\) expects object, mixed given\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Parameter \\#1 \\$request of class Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Task constructor expects Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Request, mixed given\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Parameter \\#1 \\$request of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\LetsEncrypt\\\\Application\\\\Controller\\\\CreateControllerTest\\:\\:runInactiveResourceRequest\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Request, mixed given\\.$#"
			count: 2
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Parameter \\#2 \\$request of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\LetsEncrypt\\\\Application\\\\Controller\\\\CreateControllerTest\\:\\:prepareResult\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Request, mixed given\\.$#"
			count: 3
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/LetsEncrypt/Application/Controller/ListControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/LetsEncrypt/Application/Controller/ListControllerTest.php

		-
			message: "#^Cannot call method getDomains\\(\\) on mixed\\.$#"
			count: 3
			path: tests/Functional/LetsEncrypt/Application/Controller/ListControllerTest.php

		-
			message: "#^Cannot call method getId\\(\\) on mixed\\.$#"
			count: 4
			path: tests/Functional/LetsEncrypt/Application/Controller/ListControllerTest.php

		-
			message: "#^Cannot call method setSuspended\\(\\) on mixed\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/ListControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\LetsEncrypt\\\\Application\\\\Controller\\\\ListControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/ListControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/ListControllerTest.php

		-
			message: "#^Cannot access property \\$certificate on mixed\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Payload/ResultSchemaTest.php

		-
			message: "#^Cannot access property \\$privateKey on mixed\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Payload/ResultSchemaTest.php

		-
			message: "#^Cannot access property \\$requestId on mixed\\.$#"
			count: 2
			path: tests/Functional/LetsEncrypt/Application/Payload/ResultSchemaTest.php

		-
			message: "#^Cannot call method getDescription\\(\\) on mixed\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Payload/ResultSchemaTest.php

		-
			message: "#^Argument of an invalid type mixed supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: tests/Functional/NgxConfGen/Application/Controller/ResourceControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/NgxConfGen/Application/Controller/ResourceControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/NgxConfGen/Application/Controller/ResourceControllerTest.php

		-
			message: "#^Cannot access offset 'host' on mixed\\.$#"
			count: 1
			path: tests/Functional/NgxConfGen/Application/Controller/ResourceControllerTest.php

		-
			message: "#^Cannot access offset 'priority' on mixed\\.$#"
			count: 1
			path: tests/Functional/NgxConfGen/Application/Controller/ResourceControllerTest.php

		-
			message: "#^Cannot access offset 0 on mixed\\.$#"
			count: 1
			path: tests/Functional/NgxConfGen/Application/Controller/ResourceControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\NgxConfGen\\\\Application\\\\Controller\\\\ResourceControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/NgxConfGen/Application/Controller/ResourceControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 6
			path: tests/Functional/NgxConfGen/Application/Controller/ResourceControllerTest.php

		-
			message: "#^Parameter \\#2 \\$callback of function usort expects callable\\(mixed, mixed\\)\\: int, Closure\\(array, array\\)\\: int\\<\\-1, 1\\> given\\.$#"
			count: 1
			path: tests/Functional/NgxConfGen/Application/Controller/ResourceControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/NgxConfGen/Application/Controller/ResourcesControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/NgxConfGen/Application/Controller/ResourcesControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\NgxConfGen\\\\Application\\\\Controller\\\\ResourcesControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/NgxConfGen/Application/Controller/ResourcesControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 2
			path: tests/Functional/NgxConfGen/Application/Controller/ResourcesControllerTest.php

		-
			message: "#^Parameter \\#2 \\$callback of function usort expects callable\\(mixed, mixed\\)\\: int, Closure\\(array, array\\)\\: int\\<\\-1, 1\\> given\\.$#"
			count: 1
			path: tests/Functional/NgxConfGen/Application/Controller/ResourcesControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_reference' on mixed\\.$#"
			count: 14
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_url' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Cannot access offset 'id' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\AddControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Parameter \\#1 \\$resource of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\AddControllerTest\\:\\:findCertificateRequestsForResourceId\\(\\) expects int, mixed given\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Parameter \\#1 \\$resourceId of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\AddControllerTest\\:\\:assertSingleCreatingMessageInQueue\\(\\) expects int, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Parameter \\#1 \\$responseData of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\AddControllerTest\\:\\:addDynamicParametersFromResponse\\(\\) expects array\\<array\\>, array given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Parameter \\#2 \\$subject of function Safe\\\\preg_match expects string, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Cname/AddTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Cname/AddTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Cname\\\\AddTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Cname/AddTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Cname/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Cname/EditTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Cname/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Cname\\\\EditTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Cname/EditTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Cname/EditTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/DatacenterListControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/DatacenterListControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\DatacenterListControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/DatacenterListControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/DatacenterListControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/DeleteControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/DeleteControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\DeleteControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/DeleteControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/DeleteControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/EditControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/EditControllerTest.php

		-
			message: "#^Cannot access offset 0 on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/EditControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\EditControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/EditControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/EditControllerTest.php

		-
			message: "#^Parameter \\#2 \\$cdnResourceRequestData of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\EditControllerTest\\:\\:addRequiredData\\(\\) expects array\\<string, array\\<int\\|string, bool\\|Cdn77\\\\NxgApi\\\\Resource\\\\Application\\\\Payload\\\\Header\\\\ResponseHeaderSchema\\|float\\|int\\|string\\|null\\>\\|bool\\|int\\|string\\|null\\>, array\\<string, mixed\\> given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/EditControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/EnableDatacentersControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/EnableDatacentersControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\EnableDatacentersControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/EnableDatacentersControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/EnableDatacentersControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_reference' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Cannot access offset 'codes' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Cannot access offset 'enabled' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Cannot access offset 'follow_redirect' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\FollowRedirect\\\\AddTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Parameter \\#1 \\$dataForNewCdn of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\FollowRedirect\\\\AddTest\\:\\:callApiGetResponse\\(\\) expects array\\<int\\>, array\\<mixed, mixed\\> given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Parameter \\#1 \\$dataForNewCdn of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\FollowRedirect\\\\AddTest\\:\\:callApiGetResponse\\(\\) expects array\\<int\\>, array\\<string, mixed\\> given\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Parameter \\#2 \\$subject of function Safe\\\\preg_match expects string, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/EditTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\FollowRedirect\\\\EditTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/EditTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/EditTest.php

		-
			message: "#^Parameter \\#2 \\$data of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\FollowRedirect\\\\EditTest\\:\\:callApiGetResponse\\(\\) expects array\\<string, string\\|null\\>, array\\<string, mixed\\> given\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/EditTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/GeoLocationProtection/EditTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/GeoLocationProtection/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\GeoLocationProtection\\\\EditTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/GeoLocationProtection/EditTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/GeoLocationProtection/EditTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/GetCertificateControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/GetCertificateControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\GetCertificateControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/GetCertificateControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/GetCertificateControllerTest.php

		-
			message: "#^Parameter \\#1 \\$location of method League\\\\Flysystem\\\\FilesystemWriter\\:\\:delete\\(\\) expects string, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/GetCertificateControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/HotlinkProtection/EditTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/HotlinkProtection/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\HotlinkProtection\\\\EditTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/HotlinkProtection/EditTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/HotlinkProtection/EditTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/InstantSsl/EditTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/InstantSsl/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\InstantSsl\\\\EditTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/InstantSsl/EditTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/InstantSsl/EditTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/IpProtection/EditTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/IpProtection/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\IpProtection\\\\EditTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/IpProtection/EditTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/IpProtection/EditTest.php

		-
			message: "#^Cannot access offset 'cdn_reference' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/OriginBasedir/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 4
			path: tests/Functional/Resource/Application/Controller/OriginBasedir/AddTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/OriginBasedir/AddTest.php

		-
			message: "#^Cannot access offset 'origin_basedir' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/OriginBasedir/AddTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\OriginBasedir\\\\AddTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/OriginBasedir/AddTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/OriginBasedir/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/OriginBasedir/EditTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/OriginBasedir/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\OriginBasedir\\\\EditTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/OriginBasedir/EditTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/OriginBasedir/EditTest.php

		-
			message: "#^Parameter \\#2 \\$data of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\OriginBasedir\\\\EditTest\\:\\:callApiGetResponse\\(\\) expects array\\<string, string\\|null\\>, array\\<string, mixed\\> given\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/OriginBasedir/EditTest.php

		-
			message: "#^Cannot access offset 'access_key_id' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Cannot access offset 'basedir' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Cannot access offset 'bucket_name' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_reference' on mixed\\.$#"
			count: 4
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Cannot access offset 'host' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Cannot access offset 'port' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Cannot access offset 'region' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Cannot access offset 'scheme' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Cannot access offset 'secret' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Cannot access offset 'type' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\AddTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Parameter \\#1 \\$resourceId of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\AddTest\\:\\:assertSingleCreatingMessageInQueue\\(\\) expects int, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Parameter \\#1 \\$resourceId of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\AddTest\\:\\:evaluateOriginsByResourceId\\(\\) expects int, mixed given\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Parameter \\#1 \\$responseData of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\AddTest\\:\\:addDynamicParametersFromResponse\\(\\) expects array\\<array\\>, array given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Parameter \\#2 \\$subject of function Safe\\\\preg_match expects string, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_reference' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/Basedir/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/Basedir/AddTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Origins/Basedir/AddTest.php

		-
			message: "#^Cannot access offset 'origin_basedir' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/Basedir/AddTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\Basedir\\\\AddTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/Basedir/AddTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/Basedir/AddTest.php

		-
			message: "#^Cannot access offset 'basedir' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Origins/Basedir/EditTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/Basedir/EditTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Origins/Basedir/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\Basedir\\\\EditTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/Basedir/EditTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/Basedir/EditTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/EditTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Origins/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\EditTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/EditTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/EditTest.php

		-
			message: "#^Parameter \\#2 \\$cdnResourceRequestData of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\EditTest\\:\\:addRequiredData\\(\\) expects array\\<string, array\\<int\\|string, bool\\|Cdn77\\\\NxgApi\\\\Resource\\\\Application\\\\Payload\\\\Header\\\\ResponseHeaderSchema\\|float\\|int\\|string\\|null\\>\\|bool\\|int\\|string\\|null\\>, array\\<string, mixed\\> given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/EditTest.php

		-
			message: "#^Cannot access offset 'cdn_reference' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/AddTest.php

		-
			message: "#^Cannot access offset 'codes' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/AddTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/AddTest.php

		-
			message: "#^Cannot access offset 'enabled' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/AddTest.php

		-
			message: "#^Cannot access offset 'follow_redirect' on mixed\\.$#"
			count: 4
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/AddTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\FollowRedirect\\\\AddTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/AddTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/AddTest.php

		-
			message: "#^Parameter \\#2 \\$subject of function Safe\\\\preg_match expects string, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/EditTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\FollowRedirect\\\\EditTest\\:\\:prepareRequestData\\(\\) should return array\\<string, array\\<string, mixed\\>\\> but returns array\\<string, mixed\\>\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\FollowRedirect\\\\EditTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/EditTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/EditTest.php

		-
			message: "#^Parameter \\#2 \\$requestData of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\FollowRedirect\\\\EditTest\\:\\:addRequiredOriginData\\(\\) expects array\\<string, array\\<int\\|string, bool\\|float\\|int\\|string\\|null\\>\\|bool\\|int\\|string\\|null\\>, array\\<string, array\\<string, mixed\\>\\> given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/EditTest.php

		-
			message: "#^Cannot access offset 'cdn_reference' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/OriginHeaders/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/OriginHeaders/AddTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Origins/OriginHeaders/AddTest.php

		-
			message: "#^Cannot access offset 'origin_headers' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/OriginHeaders/AddTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\OriginHeaders\\\\AddTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/OriginHeaders/AddTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/OriginHeaders/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_reference' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/Port/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/Port/AddTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Origins/Port/AddTest.php

		-
			message: "#^Cannot access offset 'origin_port' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/Port/AddTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\Port\\\\AddTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/Port/AddTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/Port/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_reference' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/Priority/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/Priority/AddTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Origins/Priority/AddTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\Priority\\\\AddTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/Priority/AddTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/Priority/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/SslVerifyDisable/EditTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Origins/SslVerifyDisable/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\SslVerifyDisable\\\\EditTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/SslVerifyDisable/EditTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/SslVerifyDisable/EditTest.php

		-
			message: "#^Cannot access offset 'cdn_reference' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/Timeout/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/Timeout/AddTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/Origins/Timeout/AddTest.php

		-
			message: "#^Cannot access offset 'origin_timeout' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/Origins/Timeout/AddTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\Timeout\\\\AddTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/Timeout/AddTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/Timeout/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_reference' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/ResponseHeaders/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/ResponseHeaders/AddTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/ResponseHeaders/AddTest.php

		-
			message: "#^Cannot access offset 'response_headers' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/ResponseHeaders/AddTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\ResponseHeaders\\\\AddTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/ResponseHeaders/AddTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/ResponseHeaders/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/ResponseHeaders/EditTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/ResponseHeaders/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\ResponseHeaders\\\\EditTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/ResponseHeaders/EditTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/ResponseHeaders/EditTest.php

		-
			message: "#^Parameter \\#2 \\$cdnResourceRequestData of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\ResponseHeaders\\\\EditTest\\:\\:addRequiredData\\(\\) expects array\\<string, array\\<int\\|string, bool\\|Cdn77\\\\NxgApi\\\\Resource\\\\Application\\\\Payload\\\\Header\\\\ResponseHeaderSchema\\|float\\|int\\|string\\|null\\>\\|bool\\|int\\|string\\|null\\>, array\\<string, mixed\\> given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/ResponseHeaders/EditTest.php

		-
			message: "#^Cannot access offset 'cdn_reference' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot access offset 'secure_link_expiry…' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot access offset 'secure_link_pathlen…' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot access offset 'secure_link_rewrite…' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot access offset 'secure_link_secret…' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot access offset 'secure_link_token…' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot access offset 'secure_token' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot access offset 'secure_token_type' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot access offset 'secure_token_value' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SecureToken\\\\AddTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SecureToken\\\\EditTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/ServersStatusControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/ServersStatusControllerTest.php

		-
			message: "#^Cannot access offset 'servers' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/ServersStatusControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\ServersStatusControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/ServersStatusControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/ServersStatusControllerTest.php

		-
			message: "#^Parameter \\#1 \\$servers of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\ServersStatusControllerTest\\:\\:sortServers\\(\\) expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/ServersStatusControllerTest.php

		-
			message: "#^Parameter \\#2 \\$callback of function usort expects callable\\(mixed, mixed\\)\\: int, Closure\\(array, array\\)\\: int\\<\\-1, 1\\> given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/ServersStatusControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/SetCertificateControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/SetCertificateControllerTest.php

		-
			message: "#^Generator expects value type array\\<int, array\\|Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\CertificatePair\\|DateTimeImmutable\\>, array given\\.$#"
			count: 5
			path: tests/Functional/Resource/Application/Controller/SetCertificateControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SetCertificateControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SetCertificateControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SetCertificateControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/SslListControllerTest.php

		-
			message: "#^Cannot access offset 'certificates' on mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SslListControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/SslListControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SslListControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SslListControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SslListControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/SslVerifyDisable/EditTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/SslVerifyDisable/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SslVerifyDisable\\\\EditTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SslVerifyDisable/EditTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SslVerifyDisable/EditTest.php

		-
			message: "#^Parameter \\#2 \\$data of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SslVerifyDisable\\\\EditTest\\:\\:callApiGetResponse\\(\\) expects array\\<string, string\\|null\\>, array\\<string, mixed\\> given\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/SslVerifyDisable/EditTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/StatusControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/StatusControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\StatusControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/StatusControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/StatusControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/SuspensionControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/SuspensionControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SuspensionControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SuspensionControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SuspensionControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Domain/Finder/MainCertificateFinderTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Domain/Finder/MainCertificateFinderTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Domain\\\\Finder\\\\MainCertificateFinderTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Domain/Finder/MainCertificateFinderTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Domain/Finder/MainCertificateFinderTest.php

		-
			message: "#^Parameter \\#1 \\$location of method League\\\\Flysystem\\\\FilesystemWriter\\:\\:delete\\(\\) expects string, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Domain/Finder/MainCertificateFinderTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Domain/Finder/ResourcesForPermanentRemoveFinderTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Domain/Finder/ResourcesForPermanentRemoveFinderTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Domain\\\\Finder\\\\ResourcesForPermanentRemoveFinderTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Domain/Finder/ResourcesForPermanentRemoveFinderTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Domain/Finder/ResourcesForPermanentRemoveFinderTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Resource/Domain/ResourceSuspenderTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Resource/Domain/ResourceSuspenderTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Domain\\\\ResourceSuspenderTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Resource/Domain/ResourceSuspenderTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Resource/Domain/ResourceSuspenderTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Server/Application/Controller/AllStatusControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Server/Application/Controller/AllStatusControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Server\\\\Application\\\\Controller\\\\AllStatusControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Server/Application/Controller/AllStatusControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Server/Application/Controller/AllStatusControllerTest.php

		-
			message: "#^Parameter \\#2 \\$callback of function usort expects callable\\(mixed, mixed\\)\\: int, Closure\\(array, array\\)\\: int\\<\\-1, 1\\> given\\.$#"
			count: 1
			path: tests/Functional/Server/Application/Controller/AllStatusControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Server/Application/Controller/PauseServerControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Server/Application/Controller/PauseServerControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Server\\\\Application\\\\Controller\\\\PauseServerControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Server/Application/Controller/PauseServerControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Server/Application/Controller/PauseServerControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Server/Application/Controller/ServerIdListControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Server/Application/Controller/ServerIdListControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Server\\\\Application\\\\Controller\\\\ServerIdListControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Server/Application/Controller/ServerIdListControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 2
			path: tests/Functional/Server/Application/Controller/ServerIdListControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Server/Application/Controller/UnpauseServerControllerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Server/Application/Controller/UnpauseServerControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Server\\\\Application\\\\Controller\\\\UnpauseServerControllerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Server/Application/Controller/UnpauseServerControllerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Server/Application/Controller/UnpauseServerControllerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Server/Domain/ServerPauserTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Server/Domain/ServerPauserTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Server\\\\Domain\\\\ServerPauserTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Server/Domain/ServerPauserTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Server/Domain/ServerPauserTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Service/ExternalApi/CertificateBucket/CertificateBucketTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Service/ExternalApi/CertificateBucket/CertificateBucketTest.php

		-
			message: "#^Cannot call method shouldReceive\\(\\) on mixed\\.$#"
			count: 3
			path: tests/Functional/Service/ExternalApi/CertificateBucket/CertificateBucketTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Service\\\\ExternalApi\\\\CertificateBucket\\\\CertificateBucketTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Service/ExternalApi/CertificateBucket/CertificateBucketTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Service/ExternalApi/CertificateBucket/CertificateBucketTest.php

		-
			message: "#^Parameter \\#1 \\$namingStrategy of class Cdn77\\\\NxgApi\\\\Service\\\\ExternalApi\\\\CertificateBucket\\\\CertificateBucket constructor expects Cdn77\\\\NxgApi\\\\Service\\\\ExternalApi\\\\CertificateBucket\\\\NamingStrategy, mixed given\\.$#"
			count: 3
			path: tests/Functional/Service/ExternalApi/CertificateBucket/CertificateBucketTest.php

		-
			message: "#^Parameter \\#1 \\$sslFile of method Cdn77\\\\NxgApi\\\\Service\\\\ExternalApi\\\\CertificateBucket\\\\CertificateBucket\\:\\:save\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SslFile, mixed given\\.$#"
			count: 3
			path: tests/Functional/Service/ExternalApi/CertificateBucket/CertificateBucketTest.php

		-
			message: "#^Parameter \\#2 \\$certificatePair of method Cdn77\\\\NxgApi\\\\Service\\\\ExternalApi\\\\CertificateBucket\\\\CertificateBucket\\:\\:save\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\CertificatePair, mixed given\\.$#"
			count: 3
			path: tests/Functional/Service/ExternalApi/CertificateBucket/CertificateBucketTest.php

		-
			message: "#^Parameter \\#2 \\$filesystem of class Cdn77\\\\NxgApi\\\\Service\\\\ExternalApi\\\\CertificateBucket\\\\CertificateBucket constructor expects League\\\\Flysystem\\\\FilesystemOperator, mixed given\\.$#"
			count: 2
			path: tests/Functional/Service/ExternalApi/CertificateBucket/CertificateBucketTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Service/Legacy/Delete/ResourceDeleterTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Service/Legacy/Delete/ResourceDeleterTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Service\\\\Legacy\\\\Delete\\\\ResourceDeleterTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Service/Legacy/Delete/ResourceDeleterTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Service/Legacy/Delete/ResourceDeleterTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Service/LetsEncrypt/RenewalManagerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Service/LetsEncrypt/RenewalManagerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Service\\\\LetsEncrypt\\\\RenewalManagerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Service/LetsEncrypt/RenewalManagerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Service/LetsEncrypt/RenewalManagerTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Functional/Service/LetsEncrypt/RequestManagerTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Functional/Service/LetsEncrypt/RequestManagerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Service\\\\LetsEncrypt\\\\RequestManagerTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Functional/Service/LetsEncrypt/RequestManagerTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Functional/Service/LetsEncrypt/RequestManagerTest.php

		-
			message: "#^Parameter \\#1 \\$id of class Cdn77\\\\NxgApi\\\\NgxConfGen\\\\Domain\\\\Value\\\\ResourceConfiguration constructor expects int, mixed given\\.$#"
			count: 1
			path: tests/Unit/DTO/NxgConfGen/ResourceDTOTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Unit/Entity/LetsEncrypt/RequestTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Unit/Entity/LetsEncrypt/RequestTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Unit\\\\Entity\\\\LetsEncrypt\\\\RequestTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Unit/Entity/LetsEncrypt/RequestTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Unit/Entity/LetsEncrypt/RequestTest.php

		-
			message: "#^Cannot access offset 'payload' on mixed\\.$#"
			count: 2
			path: tests/Unit/Log/Formatter/RequestFormatterTest.php

		-
			message: "#^Parameter \\#1 \\$id of class Cdn77\\\\NxgApi\\\\Schema\\\\Dns\\\\Generator\\\\ServerDetail constructor expects int, mixed given\\.$#"
			count: 1
			path: tests/Unit/Schema/Dns/Generator/ServerDetailTest.php

		-
			message: "#^Cannot access offset 'cdn_resource' on mixed\\.$#"
			count: 2
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot access offset 'custom_data' on mixed\\.$#"
			count: 3
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Unit\\\\Schema\\\\Legacy\\\\Resources\\\\ResourceDetailInfoTest\\:\\:prepareResourceFromResponse\\(\\) should return array but returns mixed\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Parameter \\#1 \\$array of function ksort expects array, mixed given\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php
