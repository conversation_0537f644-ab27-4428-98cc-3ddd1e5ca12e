# This file is a "template" of which env vars needs to be defined in your configuration or in an .env file
# Set variables here that may be different on each deployment target of the app, e.g. development, staging, production.
# https://symfony.com/doc/current/best_practices/configuration.html#infrastructure-related-configuration

###> symfony/framework-bundle ###
APP_ENV=test
APP_SECRET=%generate(secret)%
#TRUSTED_PROXIES=127.0.0.1,*********
#TRUSTED_HOSTS=localhost,example.com
###< symfony/framework-bundle ###

###> symfony/swiftmailer-bundle ###
# For Gmail as a transport, use: "gmail://username:password@localhost"
# For a generic SMTP server, use: "smtp://localhost:25?encryption=&auth_mode="
# Delivery is disabled by default via "null://localhost"
MAILER_URL=null://localhost
###< symfony/swiftmailer-bundle ###

###> doctrine/doctrine-bundle ###
# Format described at http://docs.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# For an SQLite database, use: "sqlite:///%kernel.project_dir%/var/data.db"
# Configure your db driver and server_version in config/packages/doctrine.yaml
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:5432/mc?serverVersion=10
#DATABASE_URL=postgresql://nxgapi:<EMAIL>:5432/mc?serverVersion=10
#DATABASE_URL=postgresql://mc_nxgapi_dev:<EMAIL>:5432/mc?serverVersion=10
###< doctrine/doctrine-bundle ###

#RABBIT_IP=**************
# RabbitMQ configuration
#RABBITMQ_API_DSN=rabbitmq://test:test@${RABBIT_IP}:5672/api
#RABBITMQ_PURGER_DSN=rabbitmq://test:test@${RABBIT_IP}:5672/purger
#RABBITMQ_API_DISABLED=false
#RABBITMQ_PURGER_JOIN_LIMIT=100
#ENABLE_NOTIFICATION_TO_RABBIT=true

# Redis configuration
REDIS_CACHE_DSN=redis://127.0.0.1:6379/0
REDIS_CACHE_PROFILE=3.2
REDIS_IP_CATEGORIZATION_DSN=redis://127.0.0.1:6379/0
REDIS_IP_CATEGORIZATION_PROFILE=3.2
REDIS_IP_CATEGORIZATION_PREFIX="nxgapi:ip:"

# LE debug
LE_DEBUG_RESOURCES_LIST=[]

# production only settings
MON_MASTER_PATH=/var/lock/mc
SENTRY_DSN=https://21dc57970225487598b922dffa41af11:<EMAIL>/22
SENTRY_ENV=test
CERTIFICATES_PATH=C:\laragon\www\nxg-api\var\cache\certificates
CERTIFICATES_COLD_PATH=C:\laragon\www\nxg-api\var\cache\certificates_cold
CERTIFICATES_STORAGE_PATH=C:\laragon\www\nxg-api\var\cache\certificates_storage

OPEN_API_SPEC_HOST=nxgapi.cdn77.eu
OPEN_API_SPEC_SCHEME=http

CLAP_GRAPHQL_API_URL=https://api.cdn77.com/v3/graphql
CLAP_GRAPHQL_BEARER_TOKEN='bnhnLWFwaQ==$mBLYF.lpROwlhhupjWeCH2hNdDJ2aAfm$MjlmYjcyYjAtZTFiMS00YzZkLTkyYTMtYTYwNmU2YzIwNTA2' # nxg-api app token
#CLAP_GRAPHQL_BEARER_TOKEN='UxMlJxUHHyLKcWn1AYYkyczofLxtSMJ6$YzI4MmI5YjMtMjQyNy00NGU3LWFlY2MtOGQxYWQ1ZjI2Njkx' # OL token

