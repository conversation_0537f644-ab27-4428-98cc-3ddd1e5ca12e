parameters:
	ignoreErrors:
		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\IgnoredQueryParam\\:\\:\\$resource type mapping mismatch\\: database can contain Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\|null but property expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\.$#"
			count: 1
			path: src/Entity/Legacy/IgnoredQueryParam.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Ipv6\\:\\:\\$server is never written, only read\\.$#"
			count: 1
			path: src/Entity/Legacy/Ipv6.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Pop\\:\\:\\$location type mapping mismatch\\: database can contain Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Location\\|null but property expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Location\\.$#"
			count: 1
			path: src/Entity/Legacy/Pop.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\ResourceGeoProtection\\:\\:\\$resource type mapping mismatch\\: database can contain Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\|null but property expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\.$#"
			count: 1
			path: src/Entity/Legacy/ResourceGeoProtection.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\ResourceGeoProtectionLocation\\:\\:\\$geoProtection type mapping mismatch\\: database can contain Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\ResourceGeoProtection\\|null but property expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\ResourceGeoProtection\\.$#"
			count: 1
			path: src/Entity/Legacy/ResourceGeoProtectionLocation.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\ResourceIpProtection\\:\\:\\$resource type mapping mismatch\\: database can contain Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\|null but property expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\.$#"
			count: 1
			path: src/Entity/Legacy/ResourceIpProtection.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\ResourceIpProtectionAddress\\:\\:\\$ipProtection type mapping mismatch\\: database can contain Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\ResourceIpProtection\\|null but property expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\ResourceIpProtection\\.$#"
			count: 1
			path: src/Entity/Legacy/ResourceIpProtectionAddress.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\:\\:\\$pop type mapping mismatch\\: database can contain Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Pop\\|null but property expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Pop\\.$#"
			count: 1
			path: src/Entity/Legacy/Server.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Task\\:\\:\\$request type mapping mismatch\\: database can contain Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Request\\|null but property expects Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Request\\.$#"
			count: 1
			path: src/Entity/LetsEncrypt/Task.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Entity\\\\PopRedirection\\\\Redirection\\:\\:\\$pop type mapping mismatch\\: database can contain Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Pop\\|null but property expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Pop\\.$#"
			count: 1
			path: src/Entity/PopRedirection/Redirection.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Entity\\\\PopRedirection\\\\Target\\:\\:\\$pop type mapping mismatch\\: database can contain Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Pop\\|null but property expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Pop\\.$#"
			count: 1
			path: src/Entity/PopRedirection/Target.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Entity\\\\PopRedirection\\\\Target\\:\\:\\$redirection type mapping mismatch\\: database can contain Cdn77\\\\NxgApi\\\\Entity\\\\PopRedirection\\\\Redirection\\|null but property expects Cdn77\\\\NxgApi\\\\Entity\\\\PopRedirection\\\\Redirection\\.$#"
			count: 1
			path: src/Entity/PopRedirection/Target.php

		-
			message: "#^Call to static method Webmozart\\\\Assert\\\\Assert\\:\\:allIsInstanceOf\\(\\) with array\\<int, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\> and 'Cdn77\\\\\\\\NxgApi\\\\\\\\Entity\\\\\\\\Legacy\\\\\\\\CdnResource' will always evaluate to true\\.$#"
			count: 1
			path: src/Repository/Legacy/CdnResourceRepository.php

		-
			message: "#^Parameter \\#1 \\$server of class Cdn77\\\\NxgApi\\\\DTO\\\\Internal\\\\Server\\\\ServerWithPrimaryIpPairDTO constructor expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Ip\\|Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server given\\.$#"
			count: 1
			path: src/Repository/Legacy/ServerRepository.php

		-
			message: "#^Parameter \\#2 \\$primaryIp of class Cdn77\\\\NxgApi\\\\DTO\\\\Internal\\\\Server\\\\ServerWithPrimaryIpPairDTO constructor expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Ip, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Ip\\|Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server given\\.$#"
			count: 1
			path: src/Repository/Legacy/ServerRepository.php
