<?xml version="1.0"?>
<ruleset
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="vendor/squizlabs/php_codesniffer/phpcs.xsd"
>
    <arg name="cache" value=".phpcs-cache"/>

    <rule ref="Cdn77">
        <exclude name="SlevomatCodingStandard.TypeHints.PropertyTypeHint.MissingNativeTypeHint"/>
        <exclude name="SlevomatCodingStandard.TypeHints.ParameterTypeHint.MissingNativeTypeHint"/>

        <exclude name="SlevomatCodingStandard.Classes.RequireConstructorPropertyPromotion.RequiredConstructorPropertyPromotion"/>
        <exclude name="SlevomatCodingStandard.Classes.ClassStructure.IncorrectGroupOrder"/>

        <exclude name="Cdn77.NamingConventions.ValidConstantName.ClassConstantNotUpperCase"/>
        <exclude name="Cdn77.NamingConventions.ValidConstantName.ConstantNotUpperCase"/>
    </rule>

    <file>bin/console</file>
    <file>public/</file>
    <file>src/</file>
    <file>tests/</file>
</ruleset>
