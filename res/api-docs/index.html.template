<!-- HTML for static distribution bundle build -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <title>NXG API Docs</title>
    <link rel="stylesheet" type="text/css" href="./dist/swagger-ui.css" >
    <link rel="stylesheet" type="text/css" href="./dist/stylesheet/style.css" >
    <link rel="icon" type="image/png" href="./dist/favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="./dist/favicon-16x16.png" sizes="16x16" />
    <style>
      html
      {
        box-sizing: border-box;
        overflow: -moz-scrollbars-vertical;
        overflow-y: scroll;
      }

      *,
      *:before,
      *:after
      {
        box-sizing: inherit;
      }

      body
      {
        margin:0;
        background: #fafafa;
      }
    </style>
  </head>

  <body>
    <div id="swagger-ui"></div>

    <script src="./dist/swagger-ui-bundle.js"> </script>
    <script src="./dist/swagger-ui-standalone-preset.js"> </script>
    <script>
    window.onload = function() {
      const ui = SwaggerUIBundle({
        spec: {{SPEC}},
        dom_id: '#swagger-ui',
        deepLinking: false,
        docExpansion: 'list',
        filter: false,
        defaultModelsExpandDepth: -1,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout"
      })

      window.ui = ui
    }
  </script>
  </body>
</html>
