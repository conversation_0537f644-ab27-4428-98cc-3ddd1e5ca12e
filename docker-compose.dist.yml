version: "3.6"
services:
    nginx:
        build: .docker/nginx
        volumes:
            - ./:/app:delegated
        environment:
            VIRTUAL_HOST: nxgapi.localhost
        restart: always
        networks:
            default:
            workspace:

    php-fpm:
        container_name: nxgapi-php-fpm
        build: .docker/php-fpm
        restart: always
        depends_on:
            - nginx
        working_dir: /app
        volumes:
            - ./:/app:delegated
        environment:
            PHP_IDE_CONFIG: serverName=nxgapi-docker
        networks:
            default:
            workspace:

    php-cli:
        build: .docker/php-cli
        volumes:
            - ./:/app
            - composer-home:/home/<USER>/.composer
            - ~/.ssh:/home/<USER>/.ssh:ro
        environment:
            - SSH_AUTH_SOCK=${SSH_AUTH_SOCK}
            - XDEBUG_CONFIG="remote_host=dockerhost"
            - PHP_IDE_CONFIG=serverName=docker
        working_dir: /app
        restart: "no"

    postgres:
        image: postgres:10
        restart: always
        volumes:
            - postgres:/var/lib/postgresql/data:delegated
            - ./sql/cdn77_schema.sql:/docker-entrypoint-initdb.d/cdn77_schema.sql:ro
        environment:
            POSTGRES_DB: nxgapi
            POSTGRES_PASSWORD: postgres
        networks:
            default:
            workspace:
                aliases:
                    - nxgapi-postgres

    redis:
        image: redis:latest
        restart: always

    rabbitmq:
        image: rabbitmq:3-management
        environment:
            RABBITMQ_DEFAULT_VHOST: api
        ports:
            - '15672:15672'
            - '5672:5672'

volumes:
    postgres:
    composer-home:
        external: true

networks:
    workspace:
        name: workspace
        external: true
