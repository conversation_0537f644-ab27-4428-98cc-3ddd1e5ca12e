<?php

declare(strict_types=1);

namespace Cdn77\NxgApi;

use Symfony\Component\Dotenv\Dotenv;
use Symfony\Component\ErrorHandler\Debug;
use Symfony\Component\HttpFoundation\Request;
use Webmozart\Assert\Assert;

use function basename;
use function getenv;
use function header;
use function in_array;

use const PHP_SAPI;

require __DIR__ . '/../vendor/autoload.php';

// to handle cors preflight options request (needed by box configurator frontend)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('Access-Control-Allow-Methods: *');

    die;
}

// The check is to ensure we don't use .env in production
if (getenv('APP_ENV') === false) {
    (new Dotenv())->usePutenv()->load(__DIR__ . '/../.env');
}

$debug = (bool) getenv('APP_DEBUG', false);

if ($debug) {
    if (
        ! (bool) getenv('DOCKER_ENV')
        && (
            isset($_SERVER['HTTP_CLIENT_IP'])
            || isset($_SERVER['HTTP_X_FORWARDED_FOR'])
            || ! (in_array(@$_SERVER['REMOTE_ADDR'], ['127.0.0.1', '::1'], true) || PHP_SAPI === 'cli-server')
        )
    ) {
        header('HTTP/1.1 403 Forbidden');
        exit('You are not allowed to access this file. Check ' . basename(__FILE__) . ' for more information.');
    }

    Debug::enable();
}

$env = getenv('APP_ENV');
Assert::string($env);
$kernel = new Kernel($env, $debug);
$request = Request::createFromGlobals();
$response = $kernel->handle($request);
$response->send();
$kernel->terminate($request, $response);
