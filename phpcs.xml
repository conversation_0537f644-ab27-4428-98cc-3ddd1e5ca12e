<?xml version="1.0" ?>
<ruleset>
    <rule ref="Cdn77">
        <exclude name="SlevomatCodingStandard.Classes.SuperfluousInterfaceNaming.SuperfluousSuffix"/>
        <exclude name="SlevomatCodingStandard.Classes.SuperfluousExceptionNaming.SuperfluousSuffix"/>
    </rule>

    <file>src/</file>
    <file>tests/</file>

    <arg name="encoding" value="utf-8"/>
    <arg name="extensions" value="php"/>
    <arg value="s"/>
    <arg value="p"/>
</ruleset>
