ifdef CI
COMPOSER_ARGS += --no-progress --no-interaction
PHPCS_ARGS += -q
PHPSTAN_ARGS += --no-progress
endif

.PHONY: build
build: vendor
	@echo "Build successfully done."

.PHONY: list
list:
	@$(MAKE) -pRrq -f $(lastword $(MAKEFILE_LIST)) : 2>/dev/null | awk -v RS= -F: '/^# File/,/^# Finished Make data base/ {if ($$1 !~ "^[#.]") {print $$1}}' | sort | egrep -v -e '^[^[:alnum:]]' -e '^$@$$'

vendor: composer.json
	$(MAKE) validate-composer
	composer install $(COMPOSER_ARGS)

.PHONY: composer-unused
composer-unused:
	vendor/bin/composer-unused

.PHONY: cs
cs: vendor
	vendor/bin/phpcs $(PHPCS_ARGS)

.PHONY: cs-fix
cs-fix: vendor
	vendor/bin/phpcbf

.PHONY: static-analysis
static-analysis: vendor
	vendor/bin/phpstan analyse $(PHPSTAN_ARGS)

.PHONY: validate-composer
validate-composer:
	composer validate --no-check-all --strict

.PHONY: test
test: test-unit test-integration

.PHONY: test-unit
test-unit:
	vendor/bin/phpunit tests/Unit

.PHONY: test-integration
test-integration:
	vendor/bin/phpunit tests/Functional

.PHONY: release
release:
	$(MAKE) -j2 vendor --always-make

	mv -t $(releaseDir) \
		.ci \
		bin \
		config \
		public \
		src \
		vendor \
		composer.json \
		crontab

.PHONY: check
check: build validate-composer cs static-analysis test

.PHONY: clean
clean: clean-vendor

.PHONY: clean-vendor
clean-vendor:
	rm -rf vendor
