{"beberlei/assert": {"version": "v3.2.1"}, "bunny/bunny": {"version": "v0.2.4"}, "cache/psr-6-doctrine-bridge": {"version": "3.0.1"}, "cdn77/coding-standard": {"version": "v0.4.0"}, "cdn77/descriptive-rest": {"version": "dev-master"}, "cdn77/mon-master-detector": {"version": "v0.1.0"}, "cdn77/mon-master-detector-bundle": {"version": "v0.1.0"}, "cdn77/rabbitmq-bundle": {"version": "dev-multiple-connections"}, "cdn77/rabbitmq-bundle-legacy": {"version": "dev-multiple-connections"}, "composer/xdebug-handler": {"version": "1.2.1"}, "consistence/consistence": {"version": "1.0.2"}, "consistence/consistence-doctrine": {"version": "1.4"}, "consistence/consistence-doctrine-symfony": {"version": "1.3"}, "dama/doctrine-test-bundle": {"version": "4.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "4.0", "ref": "56eaa387b5e48ebcc7c95a893b47dfa1ad51449c"}}, "dealerdirect/phpcodesniffer-composer-installer": {"version": "v0.4.3"}, "doctrine/annotations": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "cb4152ebcadbe620ea2261da1a1c5a9b8cea7672"}}, "doctrine/cache": {"version": "v1.7.1"}, "doctrine/collections": {"version": "v1.5.0"}, "doctrine/common": {"version": "v2.8.1"}, "doctrine/dbal": {"version": "v2.6.2"}, "doctrine/doctrine-bundle": {"version": "1.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.6", "ref": "44d3aa7752dd46f77ba11af2297a25e1dedfb4d0"}}, "doctrine/doctrine-cache-bundle": {"version": "1.3.2"}, "doctrine/event-manager": {"version": "v1.0.0"}, "doctrine/inflector": {"version": "v1.2.0"}, "doctrine/instantiator": {"version": "1.1.0"}, "doctrine/lexer": {"version": "v1.0.1"}, "doctrine/orm": {"version": "v2.5.12"}, "doctrine/persistence": {"version": "v1.0.0"}, "doctrine/reflection": {"version": "v1.0.0"}, "egulias/email-validator": {"version": "2.1.2"}, "evenement/evenement": {"version": "v3.0.1"}, "hamcrest/hamcrest-php": {"version": "v2.0.0"}, "jakub-onderka/php-parallel-lint": {"version": "v0.9.2"}, "jdorn/sql-formatter": {"version": "v1.2.17"}, "jean85/pretty-package-versions": {"version": "1.0.2"}, "jms/metadata": {"version": "1.6.0"}, "jms/parser-lib": {"version": "1.0.0"}, "jms/serializer": {"version": "1.9.1"}, "jms/serializer-bundle": {"version": "2.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.0", "ref": "fe60ce509ef04a3f40da96e3979bc8d9b13b2372"}, "files": ["config/packages/dev/jms_serializer.yaml", "config/packages/jms_serializer.yaml", "config/packages/prod/jms_serializer.yaml"]}, "kdyby/datetime-provider": {"version": "v1.0.0-beta1"}, "kdyby/datetime-provider-bundle": {"version": "v1.0.0-beta2"}, "kdyby/strict-objects": {"version": "v1.0.0"}, "league/flysystem": {"version": "1.0.41"}, "league/flysystem-memory": {"version": "1.0.1"}, "mhujer/jms-serializer-uuid": {"version": "1.0.1"}, "mhujer/jms-serializer-uuid-bundle": {"version": "3.0.0"}, "mockery/mockery": {"version": "1.0.x-dev"}, "monolog/monolog": {"version": "1.23.0"}, "myclabs/deep-copy": {"version": "1.7.0"}, "nette/bootstrap": {"version": "v2.4.5"}, "nette/di": {"version": "v2.4.10"}, "nette/finder": {"version": "v2.4.1"}, "nette/neon": {"version": "v2.4.2"}, "nette/php-generator": {"version": "v2.6.3"}, "nette/robot-loader": {"version": "v3.0.2"}, "nette/utils": {"version": "v2.4.8"}, "nikic/php-parser": {"version": "v3.1.1"}, "ocramius/package-versions": {"version": "1.1.3"}, "oneup/flysystem-bundle": {"version": "dev-memory-adapter"}, "paragonie/random_compat": {"version": "v2.0.11"}, "phar-io/manifest": {"version": "1.0.1"}, "phar-io/version": {"version": "1.0.1"}, "php": {"version": "7.4"}, "phpcollection/phpcollection": {"version": "0.5.0"}, "phpdocumentor/reflection-common": {"version": "1.0.1"}, "phpdocumentor/reflection-docblock": {"version": "4.1.1"}, "phpdocumentor/type-resolver": {"version": "0.4.0"}, "phpoption/phpoption": {"version": "1.5.0"}, "phpspec/prophecy": {"version": "v1.7.2"}, "phpstan/phpdoc-parser": {"version": "0.1"}, "phpstan/phpstan": {"version": "0.9"}, "phpstan/phpstan-beberlei-assert": {"version": "0.10"}, "phpstan/phpstan-doctrine": {"version": "0.9"}, "phpstan/phpstan-strict-rules": {"version": "0.9"}, "phpunit/php-code-coverage": {"version": "5.2.3"}, "phpunit/php-file-iterator": {"version": "1.4.2"}, "phpunit/php-text-template": {"version": "1.2.1"}, "phpunit/php-timer": {"version": "1.0.9"}, "phpunit/php-token-stream": {"version": "2.0.1"}, "phpunit/phpunit": {"version": "4.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.7", "ref": "21c0a65782a07f3fbd305c59e93139ee49dd3a95"}}, "predis/predis": {"version": "v1.1.1"}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.0.0"}, "psr/log": {"version": "1.0.2"}, "psr/simple-cache": {"version": "1.0.0"}, "ramsey/uuid": {"version": "3.7.1"}, "ramsey/uuid-doctrine": {"version": "1.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.3", "ref": "f7895777cd673fcefbc321a9063077a24e5df646"}}, "react/cache": {"version": "v0.4.1"}, "react/dns": {"version": "v0.4.11"}, "react/event-loop": {"version": "v0.4.3"}, "react/promise": {"version": "v2.5.1"}, "react/promise-timer": {"version": "v1.2.0"}, "react/socket": {"version": "v0.8.5"}, "react/stream": {"version": "v0.7.4"}, "roave/security-advisories": {"version": "dev-master"}, "sebastian/code-unit-reverse-lookup": {"version": "1.0.1"}, "sebastian/comparator": {"version": "2.1.0"}, "sebastian/diff": {"version": "2.0.1"}, "sebastian/environment": {"version": "3.1.0"}, "sebastian/exporter": {"version": "3.1.0"}, "sebastian/global-state": {"version": "2.0.0"}, "sebastian/object-enumerator": {"version": "3.0.3"}, "sebastian/object-reflector": {"version": "1.1.1"}, "sebastian/recursion-context": {"version": "3.0.0"}, "sebastian/resource-operations": {"version": "1.0.0"}, "sebastian/version": {"version": "2.0.1"}, "sentry/sentry": {"version": "1.8.0"}, "slevomat/coding-standard": {"version": "4.0.0"}, "snc/redis-bundle": {"version": "2.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.0", "ref": "9ef855ff444add54c2d66bdf3f4b7b2b6a120259"}}, "squizlabs/php_codesniffer": {"version": "3.1.1"}, "swiftmailer/swiftmailer": {"version": "v6.0.2"}, "symfony/browser-kit": {"version": "v3.3.10"}, "symfony/cache": {"version": "v3.3.10"}, "symfony/cache-contracts": {"version": "v1.1.1"}, "symfony/config": {"version": "v3.3.10"}, "symfony/console": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "9f94d3ea453cd8a3b95db7f82592d7344fe3a76a"}}, "symfony/contracts": {"version": "v1.1.0"}, "symfony/debug": {"version": "v3.3.10"}, "symfony/dependency-injection": {"version": "v3.3.10"}, "symfony/deprecation-contracts": {"version": "v2.1.2"}, "symfony/doctrine-bridge": {"version": "v3.3.10"}, "symfony/dom-crawler": {"version": "v3.3.10"}, "symfony/dotenv": {"version": "v3.3.10"}, "symfony/event-dispatcher": {"version": "v3.3.10"}, "symfony/expression-language": {"version": "v3.3.10"}, "symfony/filesystem": {"version": "v3.3.10"}, "symfony/finder": {"version": "v3.3.10"}, "symfony/flex": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "e921bdbfe20cdefa3b82f379d1cd36df1bc8d115"}}, "symfony/framework-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "c0fdace641ed81c02668d6bfc0fc76a2b39ee7c9"}}, "symfony/http-foundation": {"version": "v3.3.10"}, "symfony/http-kernel": {"version": "v3.3.10"}, "symfony/inflector": {"version": "v3.3.10"}, "symfony/intl": {"version": "v3.3.10"}, "symfony/mime": {"version": "v4.3.0"}, "symfony/monolog-bridge": {"version": "v3.3.10"}, "symfony/monolog-bundle": {"version": "3.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.1", "ref": "c24944bd87dacf0bb8fa218dc21e4a70fff56882"}}, "symfony/polyfill-ctype": {"version": "v1.8.0"}, "symfony/polyfill-iconv": {"version": "v1.11.0"}, "symfony/polyfill-intl-grapheme": {"version": "v1.17.0"}, "symfony/polyfill-intl-icu": {"version": "v1.6.0"}, "symfony/polyfill-intl-idn": {"version": "v1.11.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.17.0"}, "symfony/polyfill-mbstring": {"version": "v1.6.0"}, "symfony/polyfill-php56": {"version": "v1.17.0"}, "symfony/polyfill-php72": {"version": "v1.7.0"}, "symfony/polyfill-php80": {"version": "v1.17.0"}, "symfony/polyfill-util": {"version": "v1.17.0"}, "symfony/process": {"version": "v3.3.10"}, "symfony/property-access": {"version": "v3.3.10"}, "symfony/routing": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "a249484db698d1a847a30291c8f732414ac47e25"}}, "symfony/security": {"version": "v3.3.10"}, "symfony/security-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "85834af1496735f28d831489d12ab1921a875e0d"}}, "symfony/service-contracts": {"version": "v1.1.2"}, "symfony/stopwatch": {"version": "v4.1.1"}, "symfony/string": {"version": "v5.1.2"}, "symfony/swiftmailer-bundle": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.5", "ref": "8daf8ede72274989fb0dc66b83cb4bc1693afbb9"}}, "symfony/translation": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "124be9a2b7cc035fd8c56f4fd2e19c907c1f3fb8"}}, "symfony/translation-contracts": {"version": "v1.1.2"}, "symfony/validator": {"version": "v3.3.10"}, "symfony/var-dumper": {"version": "v3.4.2"}, "symfony/var-exporter": {"version": "v4.3.0"}, "symfony/yaml": {"version": "v3.3.10"}, "thecodingmachine/safe": {"version": "v0.1.15"}, "theseer/tokenizer": {"version": "1.1.0"}, "useless-soft/http-constants": {"version": "0.1"}, "useless-soft/queries": {"version": "v0.1.0"}, "useless-soft/queries-bundle": {"version": "v0.3.0"}, "useless-soft/queries-doctrine": {"version": "v0.3.0"}, "webmozart/assert": {"version": "1.2.0"}}