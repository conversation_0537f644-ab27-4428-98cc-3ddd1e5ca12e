{"name": "cdn77/mon-master-detector", "description": "MON master detector", "type": "library", "require": {"php": "^7.4|^8.0"}, "require-dev": {"adlawson/vfs": "^0.12.1", "cdn77/coding-standard": "^6.0", "phpunit/phpunit": "^9.0"}, "autoload": {"psr-4": {"Cdn77\\MonMasterDetector\\": "src/"}}, "autoload-dev": {"psr-4": {"Cdn77\\MonMasterDetector\\Tests\\": "tests/"}}, "config": {"sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "repositories": [{"type": "composer", "url": "https://composer.cdn77.eu"}]}