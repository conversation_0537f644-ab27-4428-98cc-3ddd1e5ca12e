{"name": "cdn77/mon-master-detector-bundle", "type": "symfony-bundle", "description": "Symfony integration for MON master detector", "license": "proprietary", "require": {"php": "^8.0", "cdn77/mon-master-detector": "^0.2", "symfony/config": "^5.0|^6.0", "symfony/dependency-injection": "^5.0|^6.0", "symfony/http-kernel": "^5.0|^6.0"}, "require-dev": {"adlawson/vfs": "^0.12.1", "cdn77/coding-standard": "^6.0", "phpunit/phpunit": "^9.0", "symfony/yaml": "^5.0"}, "autoload": {"psr-4": {"Cdn77\\MonMasterDetectorBundle\\": "src/"}}, "autoload-dev": {"psr-4": {"Cdn77\\MonMasterDetectorBundle\\Tests\\": "tests/"}}, "config": {"sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "repositories": [{"type": "composer", "url": "https://composer.cdn77.eu"}]}