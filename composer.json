{"name": "cdn77/nxg-api", "description": "NXG API", "license": "proprietary", "type": "project", "require": {"php": "^8.3", "ext-ds": "^1.3", "ext-intl": "*", "ext-json": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-pdo_pgsql": "*", "ext-rdkafka": "*", "ext-sockets": "*", "cache/psr-6-doctrine-bridge": "^3.2", "cdn77/mon-master-detector-bundle": "^0.4", "cebe/php-openapi": "^1.7", "doctrine/annotations": "^2.0.2", "doctrine/collections": "^2.2.2", "doctrine/common": "^3.4.4", "doctrine/dbal": "^3.9.1", "doctrine/doctrine-bundle": "2.11", "doctrine/orm": "2.14", "jms/serializer": "^3.30", "jms/serializer-bundle": "^5.4", "league/flysystem": "^3.29", "league/tactician": "^1.1", "league/tactician-bundle": "^1.5.1", "monolog/monolog": "^2.9.3", "myclabs/php-enum": "^1.8.4", "oneup/flysystem-bundle": "^4.12.2", "psr/log": "^3.0.2", "ramsey/uuid": "^4.7.6", "ramsey/uuid-doctrine": "^2.1", "s1lentium/iptools": "^1.2.0", "sentry/sentry": "^4.9", "sentry/sentry-symfony": "^5.0.1", "simpod/kafka": "^0.2.2", "simpod/kafka-bundle": "^0.6.1", "symfony/cache": "^6.4.12", "symfony/clock": "^7.3", "symfony/config": "^6.4.8", "symfony/console": "^6.4.12", "symfony/dependency-injection": "^6.4.12", "symfony/dotenv": "^6.4.12", "symfony/event-dispatcher": "^6.4.8", "symfony/expression-language": "^6.4.11", "symfony/filesystem": "^6.4.12", "symfony/finder": "^6.4.11", "symfony/framework-bundle": "^6.4.12", "symfony/http-client": "^6.4.12", "symfony/http-foundation": "^6.4.12", "symfony/http-kernel": "^6.4.12", "symfony/intl": "^7.1.5", "symfony/monolog-bundle": "^3.10", "symfony/property-access": "^6.4.11", "symfony/routing": "^6.4.12", "symfony/uid": "^7.3", "symfony/validator": "^6.4.12", "symfony/yaml": "^6.4.12", "thecodingmachine/safe": "^2.5.0", "webmozart/assert": "^1.11"}, "require-dev": {"cdn77/coding-standard": "^7.2.10", "cdn77/test-utils": "^0.2.2", "dama/doctrine-test-bundle": "^6.7.5", "ergebnis/composer-normalize": "^2.44", "icanhazstring/composer-unused": "^0.7.12", "mockery/mockery": "^1.6.12", "phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^1.12.5", "phpstan/phpstan-beberlei-assert": "^1.1.3", "phpstan/phpstan-deprecation-rules": "^1.2.1", "phpstan/phpstan-doctrine": "^1.5.3", "phpstan/phpstan-mockery": "^1.1.3", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-strict-rules": "^1.6.1", "phpstan/phpstan-symfony": "^1.4.10", "phpstan/phpstan-webmozart-assert": "^1.2.11", "phpunit/phpunit": "^9.6.21", "react/socket": "^1.16", "roave/security-advisories": "dev-latest", "symfony/browser-kit": "^6.4.8", "symfony/var-dumper": "^6.4.11", "thecodingmachine/phpstan-safe-rule": "^1.2", "ticketswap/phpstan-error-formatter": "^1.1.2"}, "provide": {"psr/cache-implementation": "1.0"}, "repositories": [{"type": "composer", "url": "https://composer.cdn77.eu"}, {"type": "vcs", "url": "https://github.com/simpod/JMSSerializerBundle", "canonical": false}], "autoload": {"psr-4": {"Cdn77\\NxgApi\\": "src"}}, "autoload-dev": {"psr-4": {"Cdn77\\NxgApi\\Tests\\": "tests"}}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "dealerdirect/phpcodesniffer-composer-installer": true, "ergebnis/composer-normalize": true, "icanhazstring/composer-unused": true, "php-http/discovery": true, "phpstan/extension-installer": true}, "preferred-install": {"*": "dist"}, "sort-packages": true}, "extra": {"unused": ["ext-ds", "ext-intl", "ext-sockets", "ext-pdo_pgsql", "ext-rdkafka", "cache/psr-6-doctrine-bridge", "league/flysystem-memory", "symfony/http-client", "symfony/intl"]}}