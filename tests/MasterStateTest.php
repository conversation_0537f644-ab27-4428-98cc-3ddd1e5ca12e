<?php

declare(strict_types=1);

namespace Cdn77\MonMasterDetector\Tests;

use Cdn77\MonMasterDetector\MasterState;
use DateTimeImmutable;
use PHPUnit\Framework\TestCase;

class MasterStateTest extends TestCase
{
    public function test(): void
    {
        $checkStamp = new DateTimeImmutable();
        $masterState = new MasterState(true, false, $checkStamp);

        $this->assertSame(true, $masterState->isMaster());
        $this->assertSame(false, $masterState->isForced());
        $this->assertSame($checkStamp, $masterState->getCheckStamp());
    }
}
