<?php

declare(strict_types=1);

namespace Cdn77\MonMasterDetectorBundle\Tests\Detector;

use Cdn77\MonMasterDetectorBundle\Detector\ConstantDetector;
use DateTimeImmutable;
use PHPUnit\Framework\TestCase;

class ConstantDetectorTest extends TestCase
{
    public function testState(): void
    {
        $detector = new ConstantDetector(true, false);

        $state = $detector->getState();

        $this->assertTrue($state->isMaster());
        $this->assertFalse($state->isForced());
        $this->assertLessThan(new DateTimeImmutable(), $state->getCheckStamp());
    }
}
