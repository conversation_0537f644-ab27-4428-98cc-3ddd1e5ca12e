<?php

declare(strict_types=1);

namespace Cdn77\MonMasterDetectorBundle\Tests\Detector;

use Cdn77\MonMasterDetectorBundle\Detector\MutableDetector;
use DateTimeImmutable;
use PHPUnit\Framework\TestCase;

class MutableDetectorTest extends TestCase
{
    public function testState(): void
    {
        $detector = new MutableDetector(true, false);

        $state = $detector->getState();

        $this->assertTrue($state->isMaster());
        $this->assertFalse($state->isForced());
        $this->assertLessThan(new DateTimeImmutable(), $state->getCheckStamp());
    }

    public function testBecomeMaster(): void
    {
        $detector = new MutableDetector(false, false);
        $detector->becomeMaster();

        $state = $detector->getState();

        $this->assertTrue($state->isMaster());
        $this->assertFalse($state->isForced());
    }

    public function testBecomeForcedMaster(): void
    {
        $detector = new MutableDetector(false, false);
        $detector->becomeForcedMaster();

        $state = $detector->getState();

        $this->assertTrue($state->isMaster());
        $this->assertTrue($state->isForced());
    }

    public function testAbandonMaster(): void
    {
        $detector = new MutableDetector(true, true);
        $detector->abandonMaster();

        $state = $detector->getState();

        $this->assertFalse($state->isMaster());
        $this->assertFalse($state->isForced());
    }
}
