<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests;

use Cdn77\NxgApi\Kernel;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Dotenv\Dotenv;
use Webmozart\Assert\Assert;

use function getenv;

require __DIR__ . '/bootstrap.php';

// The check is to ensure we don't use .env in production
if (getenv('APP_ENV') === false) {
    (new Dotenv())->load(__DIR__ . '/../.env');
}

$kernel = new Kernel($_SERVER['APP_ENV'] ?? 'test', (bool) ($_SERVER['APP_DEBUG'] ?? false));
$kernel->boot();

$managerRegistry = $kernel->getContainer()->get('doctrine');
Assert::isInstanceOf($managerRegistry, ManagerRegistry::class);

return $managerRegistry->getManager();
