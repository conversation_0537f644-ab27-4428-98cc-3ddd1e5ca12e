<?php

declare(strict_types=1);

namespace Cdn77\MonMasterDetector\Tests\DependencyInjection;

use Cdn77\MonMasterDetector\Authority\FileAuthorityDetector;
use Cdn77\MonMasterDetector\MasterStateDetectorInterface;
use Cdn77\MonMasterDetectorBundle\DependencyInjection\MonMasterDetectorExtension;
use Cdn77\MonMasterDetectorBundle\Detector\ConstantDetector;
use Cdn77\MonMasterDetectorBundle\Detector\MutableDetector;
use InvalidArgumentException;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Config\Definition\Exception\InvalidConfigurationException;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;
use Vfs\FileSystem;
use Vfs\FileSystemInterface;
use Vfs\Node\File;

class MonMasterDetectorExtensionTest extends TestCase
{
    private const SCHEME = 'test';

    private FileSystemInterface $fileSystemMock;

    private MonMasterDetectorExtension $extension;

    private ContainerBuilder $container;

    public function testTypeIsRequired(): void
    {
        $this->expectException(InvalidConfigurationException::class);
        $this->expectExceptionMessage('The child config "type" under "mon_master_detector" must be configured.');

        $this->container->loadFromExtension($this->extension->getAlias());
        $this->loadConfiguration('typeIsRequired');
        $this->container->compile();
    }

    public function testInvalidType(): void
    {
        $this->expectException(InvalidConfigurationException::class);
        $this->expectExceptionMessage('Type should be one of: file, constant, mutable.');

        $this->container->loadFromExtension($this->extension->getAlias());
        $this->loadConfiguration('invalidType');
        $this->container->compile();
    }

    public function testConstantRequiresMasterAndForced(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Constant or mutable types require a master & forced to be explicitly defined.');

        $this->container->loadFromExtension($this->extension->getAlias());
        $this->loadConfiguration('constantRequiresMasterAndForced');
        $this->container->compile();
    }

    public function testMutableRequiresMasterAndForced(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Constant or mutable types require a master & forced to be explicitly defined.');

        $this->container->loadFromExtension($this->extension->getAlias());
        $this->loadConfiguration('mutableRequiresMasterAndForced');
        $this->container->compile();
    }

    public function testMasterAndForcedMismatch(): void
    {
        $this->expectExceptionMessage('Setting forced to true implies master to be true.');
        $this->expectException(InvalidArgumentException::class);

        $this->container->loadFromExtension($this->extension->getAlias());
        $this->loadConfiguration('masterAndForcedMismatch');
        $this->container->compile();
    }

    public function testFileDetector(): void
    {
        $this->fileSystemMock->get('/')->add('is_master_forced', new File('1'));

        $this->container->loadFromExtension($this->extension->getAlias());
        $this->loadConfiguration('file');
        $this->container->compile();

        $this->assertInstanceOf(FileAuthorityDetector::class, $this->getProvider());

        $state = $this->getProvider()->getState();
        $this->assertTrue($state->isMaster());
        $this->assertTrue($state->isForced());
    }

    public function testConstantDetector(): void
    {
        $this->container->loadFromExtension($this->extension->getAlias());
        $this->loadConfiguration('constant');
        $this->container->compile();

        $this->assertInstanceOf(ConstantDetector::class, $this->getProvider());

        $state = $this->getProvider()->getState();
        $this->assertTrue($state->isMaster());
        $this->assertFalse($state->isForced());
    }

    public function testMutableDetector(): void
    {
        $this->container->loadFromExtension($this->extension->getAlias());
        $this->loadConfiguration('mutable');
        $this->container->compile();

        $this->assertInstanceOf(MutableDetector::class, $this->getProvider());

        $this->getProvider()->abandonMaster();

        $state = $this->getProvider()->getState();
        $this->assertFalse($state->isMaster());
        $this->assertFalse($state->isForced());
    }

    protected function setUp(): void
    {
        $this->fileSystemMock = FileSystem::factory(self::SCHEME);
        $this->fileSystemMock->mount();

        $this->extension = new MonMasterDetectorExtension();

        $this->container = new ContainerBuilder();
        $this->container->registerExtension($this->extension);
    }

    protected function tearDown(): void
    {
        $this->fileSystemMock->unmount();
    }

    private function loadConfiguration(string $fixtureName): void
    {
        $loader = new YamlFileLoader($this->container, new FileLocator(__DIR__ . '/fixtures/'));
        $loader->load($fixtureName . '.yaml');
    }

    private function getProvider(): MasterStateDetectorInterface
    {
        return $this->container->get(MasterStateDetectorInterface::class);
    }
}
