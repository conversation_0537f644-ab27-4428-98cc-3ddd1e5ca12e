<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Core\Domain\Value;

use Cdn77\NxgApi\Core\Domain\Value\Cidr;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Generator;
use Throwable;

final class CidrTest extends TestCase
{
    /** @return Generator<string, array{string}> */
    public static function providerInvalidCidrValues(): Generator
    {
        yield 'invalid IP no mask' => ['abcdefgh'];
        yield 'invalid IP with mask' => ['abcdefhj/32'];
        yield 'valid IPv4, invalid mask' => ['********/33'];
        yield 'valid IPv6, invalid mask' => ['fd00:1234:5678::1/129'];
    }

    /** @return Generator<string, array<string>> */
    public static function providerCidrValidValues(): Generator
    {
        yield '0.0.0.0/32' => ['0.0.0.0/32'];
        yield '***********/24' => ['***********/24'];
        yield '10.0.0.0/8' => ['10.0.0.0/8'];
        yield '***********/32' => ['***********/32'];
        yield '::/0' => ['::/0'];
        yield '2001:db8::/48' => ['2001:db8::/48'];
        yield '2400:cb00:2048:1::c629:d7b8/128' => ['2400:cb00:2048:1::c629:d7b8/128'];
    }

    /** @dataProvider providerCidrValidValues */
    public function testValidCidrFormat(string $cidrRaw): void
    {
        $cidr = Cidr::fromCidr($cidrRaw);

        self::assertSame($cidrRaw, $cidr->toString());
    }

    /** @dataProvider providerInvalidCidrValues */
    public function testInvalidCidrFormat(string $cidrRaw): void
    {
        $this->expectException(Throwable::class);

        Cidr::fromCidr($cidrRaw);
    }
}
