<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\LetsEncrypt;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\LetsEncrypt\Request;
use Cdn77\NxgApi\Entity\LetsEncrypt\RequestState;
use Cdn77\NxgApi\LetsEncrypt\Domain\Value\RequestStateReason;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;
use Mockery;

class RequestTest extends TestCase
{
    use TemporaryData;

    public function testInterfaceWorks(): void
    {
        $resourceMock = Mockery::mock(CdnResource::class);
        $createdAtMock = Mockery::mock(DateTimeImmutable::class);
        $domains = ['foo', 'bar'];

        $request = new Request($resourceMock, $domains, $createdAtMock, RequestState::getPending());

        self::assertSame($resourceMock, $request->getResource());
        self::assertSame($domains, $request->getDomains());
        self::assertSame($createdAtMock, $request->getCreatedAt());
        self::assertSame($createdAtMock, $request->getUpdatedAt());
        self::assertSame(RequestState::getPending()->getValue(), $request->getState()->getValue());
    }

    public function testComplete(): void
    {
        $request = $this->createRequest();
        $nowMock = Mockery::mock(DateTimeImmutable::class);

        $request->complete($nowMock);
        self::assertTrue($request->getState()->isCompleted());

        self::assertSame($nowMock, $request->getUpdatedAt());
    }

    public function testCancel(): void
    {
        $request = $this->createRequest();
        $nowMock = Mockery::mock(DateTimeImmutable::class);

        $request->cancel($nowMock, RequestStateReason::CancelledInstantSslDisabled);
        self::assertTrue($request->getState()->isCancelled());
        self::assertSame($request->getStateReason(), RequestStateReason::CancelledInstantSslDisabled);

        self::assertSame($nowMock, $request->getUpdatedAt());
    }

    private function createRequest(): Request
    {
        return new Request(
            Mockery::mock(CdnResource::class),
            ['foo'],
            Mockery::mock(DateTimeImmutable::class),
            RequestState::getPending(),
        );
    }
}
