<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\LetsEncrypt;

use Cdn77\NxgApi\Entity\LetsEncrypt\Request;
use Cdn77\NxgApi\Entity\LetsEncrypt\Result;
use Cdn77\NxgApi\Entity\LetsEncrypt\ResultStatus;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;
use Mockery;

class ResultTest extends TestCase
{
    public function testInterfaceWorks(): void
    {
        $requestMock = Mockery::mock(Request::class);
        $requestedAtMock = Mockery::mock(DateTimeImmutable::class);
        $runAtMock = Mockery::mock(DateTimeImmutable::class);
        $completedAtMock = Mockery::mock(DateTimeImmutable::class);
        $status = ResultStatus::getError();
        $description = 'Random error';

        $result = new Result(
            $requestMock,
            $requestedAtMock,
            $runAtMock,
            $completedAtMock,
            $status,
            $description,
        );

        self::assertSame($requestMock, $result->getRequest());
        self::assertSame($requestedAtMock, $result->getRequestedAt());
        self::assertSame($runAtMock, $result->getRunAt());
        self::assertSame($completedAtMock, $result->getCompletedAt());
        self::assertSame($status->getValue(), $result->getStatus()->getValue());
        self::assertSame($description, $result->getDescription());
    }
}
