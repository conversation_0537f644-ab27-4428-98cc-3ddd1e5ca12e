<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\LetsEncrypt;

use Cdn77\NxgApi\Entity\LetsEncrypt\Request;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;
use Mockery;

class TaskTest extends TestCase
{
    public function testInterfaceWorks(): void
    {
        $requestMock = Mockery::mock(Request::class);
        $createdAtMock = Mockery::mock(DateTimeImmutable::class);
        $runAtMock = Mockery::mock(DateTimeImmutable::class);

        $task = new Task($requestMock, $createdAtMock, $runAtMock);

        self::assertSame($requestMock, $task->getRequest());
        self::assertSame($createdAtMock, $task->getCreatedAt());
        self::assertSame($runAtMock, $task->getRunAt());
    }
}
