<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\IgnoredQueryParam;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;
use ReflectionProperty;

class IgnoredQueryParamTest extends TestCase
{
    public function test(): void
    {
        $resourceMock = Mockery::mock(CdnResource::class);

        $entity = new IgnoredQueryParam($resourceMock, 'foo');

        self::assertSame($resourceMock, $entity->getResource());
        self::assertSame('foo', $entity->getName());

        $idReflection = new ReflectionProperty($entity, 'id');
        $idReflection->setAccessible(true);
        $idReflection->setValue($entity, 123);
        self::assertSame(123, $entity->getId());
    }
}
