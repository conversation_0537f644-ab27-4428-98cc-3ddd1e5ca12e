<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\Ip;
use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class IpTest extends TestCase
{
    /** @var Ip */
    private $entity;

    public function testGettersAndSetters(): void
    {
        // FIXME - skipped field $id due to missing setter.
        $value = 'test-ip';
        $this->entity->setIp($value);
        self::assertSame($value, $this->entity->getIp());
        $value = true;
        $this->entity->setPrimary($value);
        self::assertSame($value, $this->entity->isPrimary());
        $value = true;
        $this->entity->setUp($value);
        self::assertSame($value, $this->entity->isUp());
        self::assertSame(! $value, $this->entity->isDown());

        $target = Mockery::mock(Server::class);
        $this->entity->setServer($target);
        self::assertSame($target, $this->entity->getServer());
    }

    protected function setUp(): void
    {
        $this->entity = new Ip();
    }
}
