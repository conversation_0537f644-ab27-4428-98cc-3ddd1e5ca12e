<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtectionAddress;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class ResourceRefererProtectionAddressTest extends TestCase
{
    public function test(): void
    {
        $refererMock = Mockery::mock(ResourceRefererProtection::class);

        $entity = new ResourceRefererProtectionAddress($refererMock, 'foo.bar');

        self::assertSame($refererMock, $entity->getReferer());
        self::assertSame('foo.bar', $entity->getDomain());
    }
}
