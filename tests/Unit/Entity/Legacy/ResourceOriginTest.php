<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;
use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Cdn77\TestUtils\Stub;
use Generator;
use Mockery;
use Ramsey\Uuid\Uuid;

class ResourceOriginTest extends TestCase
{
    public function test(): void
    {
        $origin = new ResourceOrigin(
            Uuid::uuid4(),
            Mockery::mock(CdnResource::class),
            1,
            'foo.bar',
            ResourceOrigin::SCHEME_HTTPS,
        );

        self::assertSame('foo.bar', $origin->getHost());
        self::assertSame(ResourceOrigin::SCHEME_HTTPS, $origin->getScheme());
        self::assertNull($origin->getPort());
        self::assertNull($origin->getBasedir());
        self::assertNull($origin->getTimeout());
    }

    public function testInvalidOriginScheme(): void
    {
        $this->expectException(InvalidArgument::class);
        new ResourceOrigin(
            Uuid::uuid4(),
            Mockery::mock(CdnResource::class),
            1,
            'foo.bar',
            'blah',
        );
    }

    public function testInvalidOriginPort(): void
    {
        $this->expectException(InvalidArgument::class);
        new ResourceOrigin(
            Uuid::uuid4(),
            Mockery::mock(CdnResource::class),
            1,
            'foo.bar',
            ResourceOrigin::SCHEME_HTTP,
            999999, //@phpstan-ignore-line
        );
    }

    public function testInvalidTimeout(): void
    {
        $this->expectException(InvalidArgument::class);
        new ResourceOrigin(
            Uuid::uuid4(),
            Mockery::mock(CdnResource::class),
            1,
            'foo.bar',
            ResourceOrigin::SCHEME_HTTP,
            8080,
            '',
            -5, //@phpstan-ignore-line
        );
    }

    /** @dataProvider isStreamingOriginDataProvider */
    public function testIsStreamingOrigin(string $host, bool $expectedResult): void
    {
        $origin = Stub::create(ResourceOrigin::class, [ResourceOrigin::FIELD_HOST => $host]);

        self::assertSame($expectedResult, $origin->isStreamingOrigin());
    }

    /** @return Generator<int, array{string, bool}> */
    public function isStreamingOriginDataProvider(): Generator
    {
        yield ['prg-1.s.cdn77.com', true];
        yield ['www-1234.s.cdn77.com', true];
        yield ['1234.s.cdn77.com', true];
        yield ['1234.s.cdn77.eu', true];

        yield ['livesport-flus.cdn77.eu', false];
        yield ['1234.s.cdn77.com.eu', false];
        yield ['www.s.cdn77.com', false];
        yield ['s.cdn77.com', false];
        yield ['cdn77.com', false];
        yield ['www.example-1234.s.cdn77.net', false];
        yield ['www.example_1234.s.cdn77.com', false];
        yield ['www.-1234.s.cdn77.com', false];
        yield ['www.example-.s.cdn77.com', false];
        yield ['www.example-1234.s.cdn77.com', false];
    }
}
