<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\Account;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\LocationGroup;
use Cdn77\NxgApi\Entity\Legacy\ResourceCaching;
use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;
use Mockery;
use Mockery\MockInterface;

use function array_diff;
use function array_values;
use function count;

class ResourceTest extends TestCase
{
    /** @var CdnResource */
    private $entity;

    /** @var MockInterface|DateTimeImmutable */
    private $inceptionTimeMock;

    public function testInitialTime(): void
    {
        self::assertSame($this->inceptionTimeMock, $this->entity->getCreated());
        self::assertSame($this->inceptionTimeMock, $this->entity->getUpdated());
        self::assertSame($this->inceptionTimeMock, $this->entity->getGroupUpdated());
    }

    public function testGettersAndSetters(): void
    {
        $value = Mockery::mock(ResourceOrigin::class);
        $this->entity->addOrigin($value);
        self::assertSame($value, $this->entity->getMainOrigin());
        $value = 'test-cdnUrl';
        $this->entity->setCdnUrl($value);
        self::assertSame($value, $this->entity->getCdnUrl());
        $value = 'test-cnames';
        $this->entity->setCnames([$value]);
        self::assertSame([$value], $this->entity->getCnames());
        $value = Mockery::mock(DateTimeImmutable::class);
        $this->entity->setGroupUpdated($value);
        self::assertSame($value, $this->entity->getGroupUpdated());
        $value = 23791;
        $this->entity->setPurgeAllKey($value);
        self::assertSame($value, $this->entity->getPurgeAllKey());
        self::assertFalse($this->entity->isDisableQueryString());
        self::assertFalse($this->entity->isMp4PseudoStreaming());
        $value = false;
        $this->entity->setFlvPseudoStreaming($value);
        self::assertSame($value, $this->entity->isFlvPseudoStreaming());
        self::assertFalse($this->entity->isIgnoreSetCookie());
        // FIXME - skipped field $created due to missing setter.
        $value = Mockery::mock(DateTimeImmutable::class);
        $this->entity->setUpdated($value);
        self::assertSame($value, $this->entity->getUpdated());
        $value = Mockery::mock(DateTimeImmutable::class);
        $this->entity->setSuspended($value);
        self::assertSame($value, $this->entity->getSuspended());
        $value = Mockery::mock(DateTimeImmutable::class);
        self::assertNull($this->entity->getDeleted());
        self::assertFalse($this->entity->isDeleted());
        $this->entity->setDeleted($value);
        self::assertSame($value, $this->entity->getDeleted());
        self::assertTrue($this->entity->isDeleted());
        self::assertNull($this->entity->getHttpsRedirectCode());
        self::assertFalse($this->entity->hasStreamingPlaylistBypass());
        $value = Mockery::mock(ResourceCaching::class);
        $this->entity->setCaching($value);
        self::assertSame($value, $this->entity->getCaching());
        $value = 5;
        $this->entity->setUpstreamFailTimeout($value);
        self::assertSame($value, $this->entity->getUpstreamFailTimeout());
        $value = 7;
        $this->entity->setUpstreamNextAttempts($value);
        self::assertSame($value, $this->entity->getUpstreamNextAttempts());

        $target = Mockery::mock(LocationGroup::class);
        $this->entity->setGroup($target);
        self::assertSame($target, $this->entity->getGroup());
    }

    public function testgetCnamesWithNoCname(): void
    {
        $this->entity->setCnames([]);
        self::assertSame([], $this->entity->getCnames());

        $this->entity->setCnames(['foo.test']);
        self::assertSame(['foo.test'], $this->entity->getCnames());

        $this->entity->setCnames(['foo.test', 'bar.test']);
        self::assertSame(['foo.test', 'bar.test'], $this->entity->getCnames());
    }

    public function testAddCname(): void
    {
        $this->entity->setCnames([]);

        $this->entity->addCname('foo.test');
        self::assertSame(['foo.test'], $this->entity->getCnames());

        $this->entity->addCname('bar.test');
        self::assertSame(['foo.test', 'bar.test'], $this->entity->getCnames());
    }

    public function testCnamesAreCaseInsensitive(): void
    {
        $this->entity->setCnames(['Test.FOO']);
        self::assertSame(['test.foo'], $this->entity->getCnames());

        $this->entity->setCnames(['test.foo', 'Test.bar', 'TEST.BAZ']);
        self::assertSame(['test.foo', 'test.bar', 'test.baz'], $this->entity->getCnames());

        $this->entity->setCnames(['test.foo', 'Test.foo']);
        self::assertSame(['test.foo'], $this->entity->getCnames());
    }

    /**
     * @param string[] $cnames
     * @param string[] $expected
     *
     * @dataProvider customCnamesProvider
     */
    public function testGetCustomAndSharedCnames(array $cnames, array $expected): void
    {
        $this->entity->setCnames($cnames);
        self::assertSame($expected, $this->entity->getCustomCnames());
        self::assertSame(array_values(array_diff($cnames, $expected)), $this->entity->getSharedCnames());
    }

    /**
     * @param string[] $cnames
     * @param string[] $expected
     *
     * @dataProvider customCnamesProvider
     */
    public function testHasCustomAndSharedCnames(array $cnames, array $expected): void
    {
        $this->entity->setCnames($cnames);
        self::assertSame(count($expected) > 0, $this->entity->hasCustomCnames());
        self::assertSame(count(array_diff($cnames, $expected)) > 0, $this->entity->hasSharedCnames());
    }

    public function testAccount(): void
    {
        $account = Mockery::mock(Account::class);
        $this->entity->setAccount($account);
        self::assertSame($account, $this->entity->getAccount());
    }

    /** @return string[][][] */
    public function customCnamesProvider(): iterable
    {
        yield [
            [],
            [],
        ];

        yield [
            ['foo' . CdnResource::SHARED_SSL_URL_SUFFIX],
            [],
        ];

        yield [
            ['foo' . CdnResource::LEGACY_URL_SUFFIX],
            [],
        ];

        yield [
            ['foo' . CdnResource::LEGACY_SSL_URL_SUFFIX],
            [],
        ];

        yield [
            ['foo.bar'],
            ['foo.bar'],
        ];

        yield [
            ['foo' . CdnResource::LEGACY_SSL_URL_SUFFIX, 'bar.baz'],
            ['bar.baz'],
        ];

        yield [
            ['foo' . CdnResource::SHARED_SSL_URL_SUFFIX, 'bar' . CdnResource::LEGACY_SSL_URL_SUFFIX, 'baz.xyz'],
            ['baz.xyz'],
        ];
    }

    protected function setUp(): void
    {
        $this->inceptionTimeMock = Mockery::mock(DateTimeImmutable::class);
        $this->entity = new CdnResource($this->inceptionTimeMock);
    }
}
