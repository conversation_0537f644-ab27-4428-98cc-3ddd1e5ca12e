<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\LocationGroup;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class LocationGroupTest extends TestCase
{
    /** @var LocationGroup */
    private $entity;

    public function testGettersAndSetters(): void
    {
        // FIXME - skipped field $id due to missing setter.
        $value = 'test-name';
        $this->entity->setName($value);
        self::assertSame($value, $this->entity->getName());
        $value = true;
        $this->entity->setPrimary($value);
        self::assertSame($value, $this->entity->isPrimary());
        $value = false;
        $this->entity->setAllowBackupPops($value);
        self::assertSame($value, $this->entity->isAllowBackupPops());

        self::assertEmpty($this->entity->getPops());
    }

    protected function setUp(): void
    {
        $this->entity = new LocationGroup();
    }
}
