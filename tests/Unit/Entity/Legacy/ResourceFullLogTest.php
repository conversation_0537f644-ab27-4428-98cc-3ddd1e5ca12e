<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceFullLog;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class ResourceFullLogTest extends TestCase
{
    public function testInterfaceWorks(): void
    {
        $resourceMock = Mockery::mock(CdnResource::class);

        $entity = new ResourceFullLog($resourceMock);
        self::assertSame($resourceMock, $entity->getResource());
    }
}
