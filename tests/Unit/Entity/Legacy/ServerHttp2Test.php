<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Entity\Legacy\ServerHttp2;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class ServerHttp2Test extends TestCase
{
    /** @var ServerHttp2 */
    private $entity;

    public function testGettersAndSetters(): void
    {
        $target = Mockery::mock(Server::class);
        $this->entity->setServer($target);
        self::assertSame($target, $this->entity->getServer());
    }

    protected function setUp(): void
    {
        $this->entity = new ServerHttp2();
    }
}
