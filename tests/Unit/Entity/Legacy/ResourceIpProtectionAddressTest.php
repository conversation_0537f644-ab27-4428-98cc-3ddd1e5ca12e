<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\ResourceIpProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceIpProtectionAddress;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class ResourceIpProtectionAddressTest extends TestCase
{
    public function test(): void
    {
        $protectionMock = Mockery::mock(ResourceIpProtection::class);
        $entity = new ResourceIpProtectionAddress($protectionMock, '*******/32');

        self::assertSame($protectionMock, $entity->getIpProtection());
        self::assertSame('*******/32', $entity->getAddress());
    }
}
