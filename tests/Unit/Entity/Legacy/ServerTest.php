<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\Pop;
use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class ServerTest extends TestCase
{
    /** @var Server */
    private $entity;

    public function testGettersAndSetters(): void
    {
        // FIXME - skipped field $id due to missing setter.
        $value = 94365;
        $this->entity->setMaxBandwidth($value);
        self::assertSame($value, $this->entity->getMaxBandwidth());
        $value = false;
        $this->entity->setPaused($value);
        self::assertSame($value, $this->entity->isPaused());
        $value = 1230348460599678991;
        $this->entity->setMaxCacheSize($value);
        self::assertSame($value, $this->entity->getMaxCacheSize());
        $value = false;
        $this->entity->setForcedState($value);
        self::assertSame($value, $this->entity->getForcedState());
        $value = 5101884139296497423;
        $this->entity->setKeysSize($value);
        self::assertSame($value, $this->entity->getKeysSize());
        $value = 6288595637679530767;
        $this->entity->setUid($value);
        self::assertSame($value, $this->entity->getUid());
        $value = 123;
        $this->entity->setWorkerCount($value);
        self::assertSame($value, $this->entity->getWorkerCount());
        $value = 123;
        $this->entity->setDriveCount($value);
        self::assertSame($value, $this->entity->getDriveCount());

        $target = Mockery::mock(Pop::class);
        $this->entity->setPop($target);
        self::assertSame($target, $this->entity->getPop());
        self::assertEmpty($this->entity->getIps());
    }

    protected function setUp(): void
    {
        $this->entity = new Server();
    }
}
