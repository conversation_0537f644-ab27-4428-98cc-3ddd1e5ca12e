<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\LocationCoordinate;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class LocationCoordinateTest extends TestCase
{
    /** @var LocationCoordinate */
    private $entity;

    public function testGettersAndSetters(): void
    {
        $value = 9674907.4000000004;
        $this->entity->setLongitude($value);
        self::assertSame($value, $this->entity->getLongitude());
        $value = 3112931.585457;
        $this->entity->setLatitude($value);
        self::assertSame($value, $this->entity->getLatitude());
    }

    protected function setUp(): void
    {
        $this->entity = new LocationCoordinate();
    }
}
