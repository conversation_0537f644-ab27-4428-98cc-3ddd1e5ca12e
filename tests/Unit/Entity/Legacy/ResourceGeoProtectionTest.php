<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtection;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class ResourceGeoProtectionTest extends TestCase
{
    public function test(): void
    {
        $resourceMock = Mockery::mock(CdnResource::class);
        $entity = new ResourceGeoProtection($resourceMock, ResourceGeoProtection::TYPE_WHITELIST);

        self::assertSame($resourceMock, $entity->getResource());
        self::assertSame(ResourceGeoProtection::TYPE_WHITELIST, $entity->getType());

        $entity->setType(ResourceGeoProtection::TYPE_BLACKLIST);
        self::assertSame(ResourceGeoProtection::TYPE_BLACKLIST, $entity->getType());
    }
}
