<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Entity\Legacy\ServerLastDownStatus;
use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;
use Mockery;

class ServerLastDownStatusTest extends TestCase
{
    public function test(): void
    {
        $serverMock = Mockery::mock(Server::class);
        $timeMock = Mockery::mock(DateTimeImmutable::class);

        $status = new ServerLastDownStatus($serverMock, $timeMock, ServerLastDownStatus::REASON_AUTO);

        self::assertSame($serverMock, $status->getServer());
        self::assertSame($timeMock, $status->getWentDownAt());
        self::assertSame(ServerLastDownStatus::REASON_AUTO, $status->getReason());

        $updateMock = Mockery::mock(DateTimeImmutable::class);
        $status->update($updateMock, ServerLastDownStatus::REASON_FORCED);

        self::assertSame($updateMock, $status->getWentDownAt());
        self::assertSame(ServerLastDownStatus::REASON_FORCED, $status->getReason());
    }

    public function testInvalidReason(): void
    {
        $this->expectException(InvalidArgument::class);
        $serverMock = Mockery::mock(Server::class);
        $timeMock = Mockery::mock(DateTimeImmutable::class);

        new ServerLastDownStatus($serverMock, $timeMock, 'blah');
    }
}
