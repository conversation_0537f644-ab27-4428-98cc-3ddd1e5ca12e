<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtectionLocation;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class ResourceGeoProtectionLocationTest extends TestCase
{
    public function test(): void
    {
        $protectionMock = Mockery::mock(ResourceGeoProtection::class);
        $entity = new ResourceGeoProtectionLocation($protectionMock, 'UK');

        self::assertSame($protectionMock, $entity->getGeoProtection());
        self::assertSame('UK', $entity->getCountry());
    }
}
