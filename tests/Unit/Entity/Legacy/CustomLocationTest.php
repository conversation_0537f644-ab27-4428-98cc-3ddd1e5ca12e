<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\CustomLocation;
use Cdn77\NxgApi\Entity\Legacy\LocationGroup;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class CustomLocationTest extends TestCase
{
    /** @var CustomLocation */
    private $entity;

    public function testGettersAndSetters(): void
    {
        $target = Mockery::mock(CdnResource::class);
        $this->entity->setResource($target);
        self::assertSame($target, $this->entity->getResource());
        $target = Mockery::mock(LocationGroup::class);
        $this->entity->setGroup($target);
        self::assertSame($target, $this->entity->getGroup());
        self::assertEmpty($this->entity->getPops());
    }

    protected function setUp(): void
    {
        $this->entity = new CustomLocation();
    }
}
