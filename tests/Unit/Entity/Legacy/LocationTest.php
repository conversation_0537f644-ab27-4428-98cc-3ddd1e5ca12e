<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\Location;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class LocationTest extends TestCase
{
    /** @var Location */
    private $entity;

    public function testGettersAndSetters(): void
    {
        $value = 'test-id';
        $this->entity->setId($value);
        self::assertSame($value, $this->entity->getId());
        $value = 'test-city';
        $this->entity->setCity($value);
        self::assertSame($value, $this->entity->getCity());
        $value = 'test-countryCode';
        $this->entity->setCountryCode($value);
        self::assertSame($value, $this->entity->getCountryCode());
        $value = 'test-regionCode';
        $this->entity->setRegionCode($value);
        self::assertSame($value, $this->entity->getRegionCode());
        $value = 'test-continent';
        $this->entity->setContinent($value);
        self::assertSame($value, $this->entity->getContinent());
        // FIXME - skipped field $coordinate.longitude due to missing getter.
        // FIXME - skipped field $coordinate.latitude due to missing getter.

        self::assertEmpty($this->entity->getPops());
    }

    protected function setUp(): void
    {
        $this->entity = new Location();
    }
}
