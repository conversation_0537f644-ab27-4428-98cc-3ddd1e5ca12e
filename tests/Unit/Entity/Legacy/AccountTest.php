<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\Account;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class AccountTest extends TestCase
{
    public function testEmpty(): void
    {
        $account = new Account(123);
        self::assertSame(123, $account->getId());
        self::assertCount(0, $account->getResources());
    }

    public function testWithResource(): void
    {
        $resource = Mockery::mock(CdnResource::class);

        $account = new Account(123, [$resource]);
        self::assertSame(123, $account->getId());
        self::assertCount(1, $account->getResources());
        self::assertSame([$resource], $account->getResources()->toArray());
    }
}
