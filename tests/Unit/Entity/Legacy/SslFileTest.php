<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;
use Mockery;
use Mockery\MockInterface;

class SslFileTest extends TestCase
{
    /** @var SslFile */
    private $entity;

    /** @var MockInterface|DateTimeImmutable */
    private $createdAtMock;

    public function testGettersAndSetters(): void
    {
        // FIXME - skipped field $id due to missing setter.
        $value = 80993;
        $this->entity->setIndex($value);
        self::assertSame($value, $this->entity->getIndex());

        self::assertSame($this->createdAtMock, $this->entity->getCreatedAt());

        $expiresAtMock = Mockery::mock(DateTimeImmutable::class);
        $this->entity->setExpiresAt($expiresAtMock);
        self::assertSame($expiresAtMock, $this->entity->getExpiresAt());

        $this->entity->setDomains(['foo.bar', '*.xyz']);
        self::assertSame(['foo.bar', '*.xyz'], $this->entity->getDomains());

        $target = Mockery::mock(Ssl::class);
        $this->entity->setSsl($target);
        self::assertSame($target, $this->entity->getSsl());
    }

    protected function setUp(): void
    {
        $this->createdAtMock = Mockery::mock(DateTimeImmutable::class);
        $this->entity = new SslFile(
            $this->createdAtMock,
            [],
            $this->createdAtMock,
            0,
            Mockery::mock(Ssl::class),
            SslFile::TYPE_CUSTOM,
        );
    }
}
