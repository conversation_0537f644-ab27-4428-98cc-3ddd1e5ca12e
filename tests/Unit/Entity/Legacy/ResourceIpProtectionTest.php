<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceIpProtection;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class ResourceIpProtectionTest extends TestCase
{
    public function test(): void
    {
        $resourceMock = Mockery::mock(CdnResource::class);
        $entity = new ResourceIpProtection($resourceMock, ResourceIpProtection::TYPE_WHITELIST);

        self::assertSame($resourceMock, $entity->getResource());
        self::assertSame(ResourceIpProtection::TYPE_WHITELIST, $entity->getType());

        $entity->setType(ResourceIpProtection::TYPE_BLACKLIST);
        self::assertSame(ResourceIpProtection::TYPE_BLACKLIST, $entity->getType());
    }
}
