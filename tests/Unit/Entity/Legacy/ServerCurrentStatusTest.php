<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Entity\Legacy\ServerCurrentStatus;
use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;
use Mockery;

class ServerCurrentStatusTest extends TestCase
{
    public function test(): void
    {
        $serverMock = Mockery::mock(Server::class);
        $timeMock = Mockery::mock(DateTimeImmutable::class);

        $status = new ServerCurrentStatus($serverMock, $timeMock, true, ServerCurrentStatus::REASON_AUTO);

        self::assertSame($serverMock, $status->getServer());
        self::assertSame($timeMock, $status->getLastUpdateAt());
        self::assertTrue($status->isUp());
        self::assertSame(ServerCurrentStatus::REASON_AUTO, $status->getReason());

        $updateMock = Mockery::mock(DateTimeImmutable::class);
        $status->update($updateMock, false, ServerCurrentStatus::REASON_FORCED);

        self::assertSame($updateMock, $status->getLastUpdateAt());
        self::assertFalse($status->isUp());
        self::assertSame(ServerCurrentStatus::REASON_FORCED, $status->getReason());
    }

    public function testInvalidReason(): void
    {
        $this->expectException(InvalidArgument::class);
        $serverMock = Mockery::mock(Server::class);
        $timeMock = Mockery::mock(DateTimeImmutable::class);

        new ServerCurrentStatus($serverMock, $timeMock, true, 'blah');
    }
}
