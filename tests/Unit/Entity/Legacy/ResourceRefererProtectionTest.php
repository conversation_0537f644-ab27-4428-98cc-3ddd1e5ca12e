<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtectionAddress;
use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class ResourceRefererProtectionTest extends TestCase
{
    public function test(): void
    {
        $resourceMock = Mockery::mock(CdnResource::class);

        $entity = new ResourceRefererProtection($resourceMock, ResourceRefererProtection::TYPE_WHITELIST, true);

        self::assertSame($resourceMock, $entity->getResource());
        self::assertSame(ResourceRefererProtection::TYPE_WHITELIST, $entity->getType());
        self::assertTrue($entity->isEmptyDenied());
        self::assertCount(0, $entity->getAddresses());

        $addressMock = Mockery::mock(ResourceRefererProtectionAddress::class);
        $entity->addAddress($addressMock);

        self::assertCount(1, $entity->getAddresses());
        self::assertSame($addressMock, $entity->getAddresses()->first());

        $entity->removeAddress($addressMock);

        self::assertCount(0, $entity->getAddresses());
    }

    public function testInvalidType(): void
    {
        $this->expectException(InvalidArgument::class);
        new ResourceRefererProtection(Mockery::mock(CdnResource::class), 'invalid', true);
    }
}
