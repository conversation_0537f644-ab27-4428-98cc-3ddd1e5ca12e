<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;
use Mockery;

class SslTest extends TestCase
{
    public function testGettersAndSetters(): void
    {
        $entity = new Ssl();
        $value = 68239;
        $entity->setAssignedIndex($value);
        self::assertSame($value, $entity->getAssignedIndex());
        $value = Mockery::mock(DateTimeImmutable::class);
        $entity->setAssignedAt($value);
        self::assertSame($value, $entity->getAssignedAt());

        $target = Mockery::mock(CdnResource::class);
        $entity->setResource($target);
        self::assertSame($target, $entity->getResource());
    }
}
