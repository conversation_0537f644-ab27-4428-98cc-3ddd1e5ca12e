<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Entity\Legacy;

use Cdn77\NxgApi\Entity\Legacy\Location;
use Cdn77\NxgApi\Entity\Legacy\Pop;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class PopTest extends TestCase
{
    /** @var Pop */
    private $entity;

    public function testGettersAndSetters(): void
    {
        // FIXME - skipped field $id due to missing setter.
        $value = true;
        $this->entity->setUseIp($value);
        self::assertSame($value, $this->entity->getUseIp());
        $value = 'test-description--test-description--test-description--test-description--test-description';
        $this->entity->setDescription($value);
        self::assertSame($value, $this->entity->getDescription());
        $value = false;
        $this->entity->setBackup($value);
        self::assertSame($value, $this->entity->getBackup());
        $value = 'tagtag';
        $this->entity->setTag($value);
        self::assertSame($value, $this->entity->getTag());
        $value = true;
        $this->entity->setQat($value);
        self::assertSame($value, $this->entity->getQat());
        $value = true;
        $this->entity->setNoDedup($value);
        self::assertSame($value, $this->entity->getNoDedup());

        $target = Mockery::mock(Location::class);
        $this->entity->setLocation($target);
        self::assertSame($target, $this->entity->getLocation());
        self::assertEmpty($this->entity->getCustomLocations());
        self::assertEmpty($this->entity->getGroups());
        // FIXME - skipped association $servers due to missing getter.
    }

    protected function setUp(): void
    {
        $this->entity = new Pop();
    }
}
