<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Types;

use Cdn77\NxgApi\Types\ResourceCnamesArrayType;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\TypeRegistry;
use Mockery;
use Mo<PERSON>y\Adapter\Phpunit\MockeryPHPUnitIntegration;
use Mockery\MockInterface;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

use function array_reverse;

class ResourceCnamesArrayTypeTest extends TestCase
{
    use MockeryPHPUnitIntegration;

    public function testName(): void
    {
        $registry = new TypeRegistry();
        $type = $this->createType();

        $registry->register(ResourceCnamesArrayType::NAME, $type);
        $typeName = $registry->lookupName($type);

        self::assertSame(ResourceCnamesArrayType::NAME, $typeName);
    }

    /**
     * @param string[]|null $value
     *
     * @dataProvider convertToDatabaseValueProvider
     */
    public function testConvertToDatabaseValue(array|null $value, string|null $expected): void
    {
        $converted = $this->createType()->convertToDatabaseValue($value, $this->createPlatformMock());
        self::assertSame($expected, $converted);
    }

    /**
     * @param string[]|null $expected
     *
     * @dataProvider convertToPHPValueProvider
     */
    public function testConvertToPHPValue(string|null $value, array|null $expected): void
    {
        $converted = $this->createType()->convertToPHPValue($value, $this->createPlatformMock());
        self::assertSame($expected, $converted);
    }

    public function testGetSQLDeclaration(): void
    {
        $platform = $this->createPlatformMock();
        $platform->shouldReceive('getClobTypeDeclarationSQL')
            ->andReturn('test');

        self::assertSame('test', $this->createType()->getSQLDeclaration([], $platform));
    }

    /** @return mixed[][] */
    public function convertToDatabaseValueProvider(): iterable
    {
        yield [null, null];
        yield [[], ''];
        yield [['foo'], 'foo'];
        yield [['foo', 'bar'], 'foo bar'];
        yield [['foo', 'bar', 'baz'], 'foo bar baz'];
    }

    /** @return mixed[][] */
    public function convertToPHPValueProvider(): iterable
    {
        foreach ($this->convertToDatabaseValueProvider() as $set) {
            yield array_reverse($set);
        }
    }

    private function createType(): ResourceCnamesArrayType
    {
        return (new ReflectionClass(ResourceCnamesArrayType::class))->newInstanceWithoutConstructor();
    }

    /** @return MockInterface|AbstractPlatform */
    private function createPlatformMock(): MockInterface
    {
        return Mockery::mock(AbstractPlatform::class);
    }
}
