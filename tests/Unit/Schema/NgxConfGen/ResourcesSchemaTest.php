<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\NgxConfGen;

use Cdn77\NxgApi\NgxConfGen\Application\Payload\ResourceSchema;
use Cdn77\NxgApi\NgxConfGen\Application\Payload\ResourcesSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Cdn77\TestUtils\Stub;

class ResourcesSchemaTest extends TestCase
{
    public function testInterfaceWorks(): void
    {
        $detailFooMock = Stub::create(ResourceSchema::class);
        $detailBarMock = Stub::create(ResourceSchema::class);

        $schema = new ResourcesSchema([$detailFooMock, $detailBarMock]);
        self::assertSame([$detailFooMock, $detailBarMock], $schema->resources);
    }
}
