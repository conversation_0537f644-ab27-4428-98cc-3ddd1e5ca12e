<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Dns\Generator;

use Cdn77\NxgApi\Schema\Dns\Generator\LocationGroupDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class LocationGroupDetailTest extends TestCase
{
    public function test(): void
    {
        $detail = new LocationGroupDetail(
            123,
            false,
            [123, 456],
        );

        self::assertSame(123, $detail->getId());
        self::assertFalse($detail->hasBackup());
        self::assertSame([123, 456], $detail->getPopIds());
    }
}
