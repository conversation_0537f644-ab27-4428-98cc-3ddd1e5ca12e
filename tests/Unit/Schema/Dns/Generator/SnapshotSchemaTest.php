<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Dns\Generator;

use Cdn77\NxgApi\Schema\Dns\Generator\CustomLocationDetail;
use Cdn77\NxgApi\Schema\Dns\Generator\LocationDetail;
use Cdn77\NxgApi\Schema\Dns\Generator\LocationGroupDetail;
use Cdn77\NxgApi\Schema\Dns\Generator\PopDetail;
use Cdn77\NxgApi\Schema\Dns\Generator\ResourceDetail;
use Cdn77\NxgApi\Schema\Dns\Generator\ServerDetail;
use Cdn77\NxgApi\Schema\Dns\Generator\SnapshotSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;
use Mockery;

class SnapshotSchemaTest extends TestCase
{
    public function test(): void
    {
        $timeMock = Mockery::mock(DateTimeImmutable::class);
        $serverMock = Mockery::mock(ServerDetail::class);
        $popMock = Mockery::mock(PopDetail::class);
        $locationMock = Mockery::mock(LocationDetail::class);
        $locationGroupMock = Mockery::mock(LocationGroupDetail::class);
        $resourceMock = Mockery::mock(ResourceDetail::class);
        $customLocationMock = Mockery::mock(CustomLocationDetail::class);

        $schema = new SnapshotSchema(
            $timeMock,
            [$serverMock],
            [$popMock],
            [$locationMock],
            [$locationGroupMock],
            [$resourceMock],
            [$customLocationMock],
        );

        self::assertSame($timeMock, $schema->getSnapshotTime());
        self::assertSame([$serverMock], $schema->getServers());
        self::assertSame([$popMock], $schema->getPops());
        self::assertSame([$locationMock], $schema->getLocations());
        self::assertSame([$locationGroupMock], $schema->getLocationGroups());
        self::assertSame([$resourceMock], $schema->getResources());
        self::assertSame([$customLocationMock], $schema->getCustomLocations());
    }
}
