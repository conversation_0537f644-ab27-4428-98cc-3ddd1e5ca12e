<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Dns\Generator;

use Cdn77\NxgApi\Schema\Dns\Generator\CustomLocationDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class CustomLocationDetailTest extends TestCase
{
    public function test(): void
    {
        $detail = new CustomLocationDetail(
            123456789,
            123,
            [456, 789],
        );

        self::assertSame(123456789, $detail->getResourceId());
        self::assertSame(123, $detail->getGroupId());
        self::assertSame([456, 789], $detail->getPopIds());
    }
}
