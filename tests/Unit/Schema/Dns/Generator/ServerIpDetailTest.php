<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Dns\Generator;

use Cdn77\NxgApi\Schema\Dns\Generator\ServerIpDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class ServerIpDetailTest extends TestCase
{
    public function test(): void
    {
        $detail = new ServerIpDetail('*******', true, false, [123, 456]);

        self::assertSame('*******', $detail->getIp());
        self::assertTrue($detail->getPrimary());
        self::assertFalse($detail->getUp());
        self::assertSame([123, 456], $detail->getAssignedAccountIds());
    }
}
