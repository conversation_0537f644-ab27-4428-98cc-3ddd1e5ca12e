<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Dns\Generator;

use Cdn77\NxgApi\Schema\Dns\Generator\ServerDetail;
use Cdn77\NxgApi\Schema\Dns\Generator\ServerIpDetail;
use Cdn77\NxgApi\Schema\Dns\Generator\ServerLastDownDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class ServerDetailTest extends TestCase
{
    /**
     * @param mixed ...$args
     *
     * @dataProvider valuesProvider
     */
    public function test(...$args): void
    {
        $detail = new ServerDetail(...$args);

        self::assertSame($args[$i = 0], $detail->getId());
        self::assertSame($args[++$i], $detail->getOldId());
        self::assertSame($args[++$i], $detail->isPaused());
        self::assertSame($args[++$i], $detail->getForced());
        self::assertSame($args[++$i], $detail->getPopId());
        self::assertSame($args[++$i], $detail->getIpAddresses());
        self::assertSame($args[++$i], $detail->getLastDown());
    }

    /** @return mixed[][] */
    public function valuesProvider(): iterable
    {
        yield [
            123,
            234,
            false,
            null,
            456,
            [Mockery::mock(ServerIpDetail::class)],
            null,
        ];

        yield [
            123,
            234,
            true,
            false,
            456,
            [Mockery::mock(ServerIpDetail::class), Mockery::mock(ServerIpDetail::class)],
            Mockery::mock(ServerLastDownDetail::class),
        ];
    }
}
