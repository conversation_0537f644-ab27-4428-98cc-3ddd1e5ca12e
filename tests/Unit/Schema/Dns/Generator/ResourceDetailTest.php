<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Dns\Generator;

use Cdn77\NxgApi\Schema\Dns\Generator\ResourceDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class ResourceDetailTest extends TestCase
{
    public function test(): void
    {
        $detail = new ResourceDetail(
            *********,
            123,
            456,
        );

        self::assertSame(*********, $detail->getId());
        self::assertSame(123, $detail->getAccountId());
        self::assertSame(456, $detail->getGroupId());
    }
}
