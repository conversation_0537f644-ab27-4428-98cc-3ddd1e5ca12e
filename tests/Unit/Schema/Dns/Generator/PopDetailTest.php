<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Dns\Generator;

use Cdn77\NxgApi\Schema\Dns\Generator\PopDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class PopDetailTest extends TestCase
{
    public function test(): void
    {
        $detail = new PopDetail(
            123,
            'test',
            true,
            'testXY',
        );

        self::assertSame(123, $detail->getId());
        self::assertSame('test', $detail->getDescription());
        self::assertTrue($detail->hasBackup());
        self::assertSame('testXY', $detail->getLocationId());
    }
}
