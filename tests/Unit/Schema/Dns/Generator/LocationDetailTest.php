<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Dns\Generator;

use Cdn77\NxgApi\Schema\Dns\Generator\LocationDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class LocationDetailTest extends TestCase
{
    public function test(): void
    {
        $detail = new LocationDetail(
            'fooCZ',
            12.3456,
            -34.5678,
            'EU',
        );

        self::assertSame('fooCZ', $detail->getId());
        self::assertEqualsWithDelta(12.3456, $detail->getLatitude(), 1e-5);
        self::assertEqualsWithDelta(-34.5678, $detail->getLongitude(), 1e-5);
        self::assertSame('EU', $detail->getContinent());
    }
}
