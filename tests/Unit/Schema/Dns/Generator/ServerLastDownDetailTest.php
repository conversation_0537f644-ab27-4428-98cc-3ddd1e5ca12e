<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Dns\Generator;

use Cdn77\NxgApi\Schema\Dns\Generator\ServerLastDownDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;
use Mockery;

class ServerLastDownDetailTest extends TestCase
{
    public function test(): void
    {
        $timeMock = Mockery::mock(DateTimeImmutable::class);

        $detail = new ServerLastDownDetail(
            $timeMock,
            'forced',
        );

        self::assertSame($timeMock, $detail->getLastDownAt());
        self::assertSame('forced', $detail->getReason());
    }
}
