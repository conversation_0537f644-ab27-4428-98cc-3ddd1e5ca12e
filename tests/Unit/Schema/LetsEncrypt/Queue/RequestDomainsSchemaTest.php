<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\LetsEncrypt\Queue;

use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\LetsEncrypt\Application\Payload\RequestDomainsSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Ramsey\Uuid\Uuid;

class RequestDomainsSchemaTest extends TestCase
{
    public function testInterfaceWorks(): void
    {
        $args = [
            Uuid::uuid4(),
            ['foo', 'bar'],
        ];
        $item = new RequestDomainsSchema(...$args);

        self::assertSame($args[0], $item->requestId);
        self::assertSame($args[1], $item->domains);
    }

    public function testRejectsEmptyDomains(): void
    {
        $this->expectException(InvalidArgument::class);
        $args = [
            Uuid::uuid4(),
            [],
        ];
        new RequestDomainsSchema(...$args);
    }
}
