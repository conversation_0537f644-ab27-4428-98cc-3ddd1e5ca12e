<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\LetsEncrypt\Queue;

use Cdn77\NxgApi\LetsEncrypt\Application\Payload\RequestDomainsSchema;
use Cdn77\NxgApi\LetsEncrypt\Application\Payload\RequestsWithDomainsListSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Ramsey\Uuid\Uuid;

class RequestsWithDomainsListSchemaTest extends TestCase
{
    public function testInterfaceWorks(): void
    {
        $schema = new RequestsWithDomainsListSchema([]);
        self::assertSame([], $schema->tasks);

        $itemMocks = [
            new RequestDomainsSchema(Uuid::fromString('b28a88c9-f820-4c51-8191-c3914661c558'), ['foo', 'bar']),
            new RequestDomainsSchema(Uuid::fromString('6e9db255-a501-4b1d-916d-9ce2d1bf3ad8'), ['baz']),
        ];

        $schema = new RequestsWithDomainsListSchema($itemMocks);
        self::assertSame($itemMocks, $schema->tasks);

        $schema = new RequestsWithDomainsListSchema([$itemMocks[0]]);
        self::assertSame([$itemMocks[0]], $schema->tasks);
    }
}
