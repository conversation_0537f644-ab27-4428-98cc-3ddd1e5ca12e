<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Internal\Resource\Servers;

use Cdn77\NxgApi\Core\Application\Payload\ServerStatusSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceServersSchema;
use Cdn77\NxgApi\Schema\Internal\Resource\Servers\ResourceDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class ResourceServersSchemaTest extends TestCase
{
    public function test(): void
    {
        $resourceMock = Mockery::mock(ResourceDetail::class);
        $serverMockA = Mockery::mock(ServerStatusSchema::class);

        $schema = new ResourceServersSchema($resourceMock, [$serverMockA]);
        self::assertSame($resourceMock, $schema->resource);
        self::assertSame([$serverMockA], $schema->servers);
    }
}
