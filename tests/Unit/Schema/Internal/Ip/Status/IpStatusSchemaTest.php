<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Internal\Ip\Status;

use Cdn77\NxgApi\Core\Application\Payload\ServerStatusSchema;
use Cdn77\NxgApi\Schema\Internal\Ip\Status\IpStatusSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class IpStatusSchemaTest extends TestCase
{
    public function test(): void
    {
        $serverStatusMock = Mockery::mock(ServerStatusSchema::class);

        $schema = new IpStatusSchema('*******', true, false, $serverStatusMock);

        self::assertSame('*******', $schema->getIp());
        self::assertTrue($schema->isUp());
        self::assertFalse($schema->isPrimary());
        self::assertSame($serverStatusMock, $schema->getServer());
    }
}
