<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Internal\Server;

use Cdn77\NxgApi\Schema\Internal\Server\ServerPopDetail;
use Cdn77\NxgApi\Schema\Internal\Server\ServerPopLocationDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class ServerPopDetailTest extends TestCase
{
    public function test(): void
    {
        $locationMock = Mockery::mock(ServerPopLocationDetail::class);
        $schema = new ServerPopDetail(123, $locationMock);

        self::assertSame(123, $schema->id);
        self::assertSame($locationMock, $schema->location);
    }
}
