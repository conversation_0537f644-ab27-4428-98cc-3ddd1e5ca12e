<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Internal\Server\Status;

use Cdn77\NxgApi\Core\Application\Payload\ServerStatusSchema;
use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class ServerStatusSchemaTest extends TestCase
{
    public function test(): void
    {
        $schema = new ServerStatusSchema(123456789, true, false, true, ServerStatusSchema::FORCED_UP);

        self::assertSame(123456789, $schema->getUid());
        self::assertTrue($schema->getAutoUp());
        self::assertFalse($schema->isUp());
        self::assertTrue($schema->isPaused());
        self::assertSame(ServerStatusSchema::FORCED_UP, $schema->getForced());
    }

    public function testInvalidForced(): void
    {
        $this->expectException(InvalidArgument::class);
        new ServerStatusSchema(123456789, true, true, true, 'blah');
    }
}
