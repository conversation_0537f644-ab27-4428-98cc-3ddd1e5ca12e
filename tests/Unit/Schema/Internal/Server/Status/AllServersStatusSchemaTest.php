<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Internal\Server\Status;

use Cdn77\NxgApi\Core\Application\Payload\ServerStatusSchema;
use Cdn77\NxgApi\Server\Application\Payload\AllServersStatusSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class AllServersStatusSchemaTest extends TestCase
{
    public function test(): void
    {
        $statusMock = Mockery::mock(ServerStatusSchema::class);
        $schema = new AllServersStatusSchema([$statusMock]);

        self::assertSame([$statusMock], $schema->servers);
    }
}
