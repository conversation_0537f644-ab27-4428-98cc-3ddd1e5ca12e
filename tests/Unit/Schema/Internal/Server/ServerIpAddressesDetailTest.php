<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Internal\Server;

use Cdn77\NxgApi\Entity\Legacy\Ip;
use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Schema\Internal\Server\ServerIpAddressesDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Cdn77\TestUtils\Stub;
use Doctrine\Common\Collections\ArrayCollection;

class ServerIpAddressesDetailTest extends TestCase
{
    public function test(): void
    {
        $primaryIp = Stub::create(Ip::class, [
            'primary' => true,
            'ip' => '*******',
        ]);
        $secondaryIp1 = Stub::create(Ip::class, [
            'primary' => false,
            'ip' => '*******',
        ]);
        $secondaryIp2 = Stub::create(Ip::class, [
            'primary' => false,
            'ip' => '*******',
        ]);
        $server = Stub::create(Server::class, [
            'ips' => new ArrayCollection([$primaryIp, $secondaryIp1, $secondaryIp2]),
        ]);
        $schema = ServerIpAddressesDetail::fromServer($server);

        self::assertSame('*******', $schema->primary);
        self::assertSame(['*******', '*******'], $schema->secondary);
    }

    public function testNullable(): void
    {
        $server = Stub::create(Server::class, ['ips' => new ArrayCollection()]);

        $schema = ServerIpAddressesDetail::fromServer($server);

        self::assertNull($schema->primary);
        self::assertSame([], $schema->secondary);
    }
}
