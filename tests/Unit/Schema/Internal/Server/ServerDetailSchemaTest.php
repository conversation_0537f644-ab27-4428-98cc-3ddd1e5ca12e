<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Internal\Server;

use Cdn77\NxgApi\Entity\Legacy\Location;
use Cdn77\NxgApi\Entity\Legacy\Pop;
use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Schema\Internal\Server\ServerDetailSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Cdn77\TestUtils\Stub;
use Doctrine\Common\Collections\ArrayCollection;

class ServerDetailSchemaTest extends TestCase
{
    public function test(): void
    {
        $server = Stub::create(Server::class, [
            'uid' => 123456789,
            'ips' => new ArrayCollection(),
            'ipv6Addresses' => new ArrayCollection(),
            'maxBandwidth' => 111222333,
            'maxCacheSize' => ************,
            'keysSize' => ************,
            'workerCount' => 123,
            'driveCount' => 12,
            'pop' => Stub::create(Pop::class, [
                'id' => 1,
                'location' => Stub::create(Location::class, ['id' => 'test']),
            ]),
        ]);

        $schema = ServerDetailSchema::fromServer($server);

        self::assertSame(123456789, $schema->uid);
        self::assertNull($schema->ipAddresses->primary);
        self::assertSame([], $schema->ipAddresses->secondary);
        self::assertSame(111222333, $schema->maxBandwidth);
        self::assertSame(************, $schema->maxCacheSize);
        self::assertSame(************, $schema->keysSize);
        self::assertSame(123, $schema->workerCount);
        self::assertSame(12, $schema->driveCount);
        self::assertNotNull($schema->pop);
        self::assertSame(1, $schema->pop->id);
        self::assertSame('test', $schema->pop->location->id);
    }

    public function testNullable(): void
    {
        $server = Stub::create(Server::class, [
            'uid' => 987654321,
            'ips' => new ArrayCollection(),
            'ipv6Addresses' => new ArrayCollection(),
            'maxBandwidth' => 111222333,
            'maxCacheSize' => null,
            'keysSize' => 12345,
            'workerCount' => 0,
            'driveCount' => 2,
            'pop' => Stub::create(Pop::class, [
                'id' => 1,
                'location' => Stub::create(Location::class, ['id' => 'test']),
            ]),
        ]);

        $schema = ServerDetailSchema::fromServer($server);

        self::assertSame(987654321, $schema->uid);
        self::assertNull($schema->ipAddresses->primary);
        self::assertSame([], $schema->ipAddresses->secondary);
        self::assertSame(111222333, $schema->maxBandwidth);
        self::assertNull($schema->maxCacheSize);
        self::assertSame(12345, $schema->keysSize);
        self::assertSame(0, $schema->workerCount);
        self::assertSame(2, $schema->driveCount);
        self::assertNotNull($schema->pop);
        self::assertIsInt($schema->pop->id);
        self::assertIsString($schema->pop->location->id);
    }
}
