<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Internal\Server;

use Cdn77\NxgApi\Entity\Legacy\Location;
use Cdn77\NxgApi\Entity\Legacy\Pop;
use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Schema\Internal\Server\ServerListSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Cdn77\TestUtils\Stub;
use Doctrine\Common\Collections\ArrayCollection;

class ServerListSchemaTest extends TestCase
{
    public function test(): void
    {
        $serverMock1 = Stub::create(Server::class, [
            'uid' => 1,
            'ips' => new ArrayCollection(),
            'ipv6Addresses' => new ArrayCollection(),
            'maxBandwidth' => 100,
            'maxCacheSize' => null,
            'keysSize' => 1,
            'workerCount' => 1,
            'driveCount' => 1,
            'pop' => Stub::create(Pop::class, [
                'id' => 1,
                'location' => Stub::create(Location::class, ['id' => 'test']),
            ]),
        ]);
        $serverMock2 = Stub::create(Server::class, [
            'uid' => 2,
            'ips' => new ArrayCollection(),
            'ipv6Addresses' => new ArrayCollection(),
            'maxBandwidth' => 200,
            'maxCacheSize' => null,
            'keysSize' => 1,
            'workerCount' => 1,
            'driveCount' => 1,
            'pop' => Stub::create(Pop::class, [
                'id' => 1,
                'location' => Stub::create(Location::class, ['id' => 'test']),
            ]),
        ]);

        $schema = ServerListSchema::fromServers([$serverMock1, $serverMock2]);

        self::assertCount(2, $schema->servers);
    }
}
