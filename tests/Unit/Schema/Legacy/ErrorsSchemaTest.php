<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy;

use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class ErrorsSchemaTest extends TestCase
{
    public function testWithEmptyErrors(): void
    {
        $errors = new ErrorsSchema();

        self::assertSame([], $errors->getErrors());
        self::assertSame(0, $errors->getErrorsCount());
        self::assertFalse($errors->hasErrors());
    }

    public function testWithErrorsPassedViaConstructor(): void
    {
        $errors = new ErrorsSchema(['foo', 'bar']);

        self::assertSame(['foo', 'bar'], $errors->getErrors());
        self::assertSame(2, $errors->getErrorsCount());
        self::assertTrue($errors->hasErrors());
    }

    public function testAddingErrors(): void
    {
        $errors = new ErrorsSchema();

        $errors->addError('foo');
        self::assertSame(['foo'], $errors->getErrors());

        $errors->addError('bar');
        self::assertSame(['foo', 'bar'], $errors->getErrors());
    }
}
