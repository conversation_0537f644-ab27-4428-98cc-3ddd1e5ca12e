<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Groups;

use Cdn77\NxgApi\Location\Application\Payload\GroupListEntryDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class GroupListEntryDetailTest extends TestCase
{
    public function testFunctionality(): void
    {
        $detail = new GroupListEntryDetail(123, 'foo');

        self::assertSame(123, $detail->id);
        self::assertSame('foo', $detail->name);
    }
}
