<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Groups;

use Cdn77\NxgApi\Location\Application\Payload\GroupListEntry;
use Cdn77\NxgApi\Location\Application\Payload\GroupListEntryDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class GroupListEntryTest extends TestCase
{
    public function testFunctionality(): void
    {
        $cdnGroup = new GroupListEntryDetail(1, 'TEST Name');
        $entry = new GroupListEntry($cdnGroup);

        self::assertSame($cdnGroup, $entry->cdnGroup);
    }
}
