<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Groups;

use Cdn77\NxgApi\Location\Application\Payload\GroupList;
use Cdn77\NxgApi\Location\Application\Payload\GroupListEntry;
use Cdn77\NxgApi\Location\Application\Payload\GroupListEntryDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class GroupListTest extends TestCase
{
    public function testFunctionality(): void
    {
        $fooGroup = new GroupListEntry(new GroupListEntryDetail(1, 'foo'));
        $barGroup = new GroupListEntry(new GroupListEntryDetail(2, 'bar'));

        $list = new GroupList([$fooGroup, $barGroup]);
        self::assertSame([$fooGroup, $barGroup], $list->groups);
    }
}
