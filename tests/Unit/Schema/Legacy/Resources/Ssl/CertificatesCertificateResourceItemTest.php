<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources\Ssl;

use Cdn77\NxgApi\Schema\Legacy\Resources\Ssl\CertificatesCertificateResourceItem;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class CertificatesCertificateResourceItemTest extends TestCase
{
    public function test(): void
    {
        $detail = new CertificatesCertificateResourceItem(1234567890);
        self::assertSame(1234567890, $detail->id);
    }
}
