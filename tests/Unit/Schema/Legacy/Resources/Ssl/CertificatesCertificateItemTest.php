<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources\Ssl;

use Cdn77\NxgApi\Schema\Legacy\Resources\Ssl\CertificatesCertificateItem;
use Cdn77\NxgApi\Schema\Legacy\Resources\Ssl\CertificatesCertificateResourceItem;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;
use Mockery;

class CertificatesCertificateItemTest extends TestCase
{
    public function test(): void
    {
        $resourceMock = new CertificatesCertificateResourceItem(1);
        $dateMock = Mockery::mock(DateTimeImmutable::class);

        $detail = new CertificatesCertificateItem(
            $resourceMock,
            'custom',
            $dateMock,
            ['foo.bar'],
        );

        self::assertSame($resourceMock, $detail->resource);
        self::assertSame('custom', $detail->type);
        self::assertSame($dateMock, $detail->expiresAt);
        self::assertSame(['foo.bar'], $detail->domains);
    }
}
