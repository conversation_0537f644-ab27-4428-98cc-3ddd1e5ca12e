<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources\Ssl;

use Cdn77\NxgApi\Schema\Legacy\Resources\Ssl\CertificatesCertificateItem;
use Cdn77\NxgApi\Schema\Legacy\Resources\Ssl\CertificatesCertificateResourceItem;
use Cdn77\NxgApi\Schema\Legacy\Resources\Ssl\CertificatesSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;

class CertificatesSchemaTest extends TestCase
{
    public function test(): void
    {
        $detailMockFoo = new CertificatesCertificateItem(
            new CertificatesCertificateResourceItem(1),
            'custom',
            new DateTimeImmutable('2021-01-01T12:50:01'),
            [],
        );
        $detailMockBar = new CertificatesCertificateItem(
            new CertificatesCertificateResourceItem(2),
            'instant_ssl',
            new DateTimeImmutable('2021-02-01T12:50:01'),
            [],
        );

        $schema = new CertificatesSchema([$detailMockFoo, $detailMockBar]);

        self::assertSame([$detailMockFoo, $detailMockBar], $schema->certificates);
    }
}
