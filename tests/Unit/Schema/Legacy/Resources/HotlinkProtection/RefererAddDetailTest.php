<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources\HotlinkProtection;

use Cdn77\NxgApi\Schema\Legacy\Resources\HotlinkProtection\RefererAddressDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class RefererAddDetailTest extends TestCase
{
    public function test(): void
    {
        $detail = new RefererAddressDetail('foo.bar');
        self::assertSame('foo.bar', $detail->getDomain());
    }
}
