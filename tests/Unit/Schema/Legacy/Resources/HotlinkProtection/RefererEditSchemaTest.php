<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources\HotlinkProtection;

use Cdn77\NxgApi\Schema\Legacy\Resources\HotlinkProtection\RefererAddressDetail;
use Cdn77\NxgApi\Schema\Legacy\Resources\HotlinkProtection\RefererEditSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class RefererEditSchemaTest extends TestCase
{
    public function test(): void
    {
        $addressMock = Mockery::mock(RefererAddressDetail::class);

        $schema = new RefererEditSchema('whitelist', false, [$addressMock]);

        self::assertSame('whitelist', $schema->getType());
        self::assertFalse($schema->isEmptyDenied());
        self::assertSame([$addressMock], $schema->getAddresses());
    }

    public function testWithNull(): void
    {
        $schema = new RefererEditSchema(null, null, null);

        self::assertNull($schema->getType());
        self::assertNull($schema->isEmptyDenied());
        self::assertNull($schema->getAddresses());
    }
}
