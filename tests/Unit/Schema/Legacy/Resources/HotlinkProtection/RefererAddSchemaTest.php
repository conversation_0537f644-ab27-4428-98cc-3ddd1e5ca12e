<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources\HotlinkProtection;

use Cdn77\NxgApi\Schema\Legacy\Resources\HotlinkProtection\RefererAddressDetail;
use Cdn77\NxgApi\Schema\Legacy\Resources\HotlinkProtection\RefererAddSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class RefererAddSchemaTest extends TestCase
{
    public function test(): void
    {
        $addressMock = Mockery::mock(RefererAddressDetail::class);

        $schema = new RefererAddSchema('whitelist', false, [$addressMock]);

        self::assertSame('whitelist', $schema->getType());
        self::assertFalse($schema->isEmptyDenied());
        self::assertSame([$addressMock], $schema->getAddresses());
    }
}
