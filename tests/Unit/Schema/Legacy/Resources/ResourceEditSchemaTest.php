<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources;

use Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeadersSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Resource\Domain\DTO\EditedResource;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class ResourceEditSchemaTest extends TestCase
{
    public function testFunctionality(): void
    {
        $infoMock = Mockery::mock(ResourceEditInfo::class);

        $infoMock
            ->shouldReceive('s3Connection')
            ->twice()
            ->andReturnNull();

        $infoMock->originUrl = 'example.com';
        $infoMock->sslVerifyDisable = true;
        $infoMock->responseHeaders = ResponseHeadersSchema::fromArray(['Access-Control-Allow-Origin' => 'some.value']);

        $schema = new ResourceEditSchema($infoMock);
        $editedResource = EditedResource::fromSchema($schema);

        self::assertEquals($editedResource, $schema->resourceDto());
    }
}
