<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources;

use Cdn77\NxgApi\Schema\Legacy\Resources\ResourceDetail;
use Cdn77\NxgApi\Schema\Legacy\Resources\ResourceDetailInfo;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Cdn77\TestUtils\Stub;

class ResourceDetailTest extends TestCase
{
    public function testFunctionality(): void
    {
        $detailMock = Stub::create(ResourceDetailInfo::class, []);

        $schema = new ResourceDetail($detailMock);
        self::assertSame($detailMock, $schema->getResource());
    }
}
