<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources;

use Cdn77\NxgApi\Schema\Legacy\Resources\ResourceFullLogStatusSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class ResourceFullLogStatusSchemaTest extends TestCase
{
    public function testConstruct(): void
    {
        $schema = new ResourceFullLogStatusSchema(false);
        self::assertSame(0, $schema->enabled);
    }
}
