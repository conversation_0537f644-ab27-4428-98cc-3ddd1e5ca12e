<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources\IpProtection;

use Cdn77\NxgApi\Schema\Legacy\Resources\IpProtection\IpProtectionAddressDetail;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class IpProtectionAddressDetailTest extends TestCase
{
    public function test(): void
    {
        $schema = new IpProtectionAddressDetail('*******/32');

        self::assertSame('*******/32', $schema->getNetwork());
    }
}
