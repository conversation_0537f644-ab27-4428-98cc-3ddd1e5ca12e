<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources\IpProtection;

use Cdn77\NxgApi\Schema\Legacy\Resources\IpProtection\IpProtectionAddSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class IpProtectionAddSchemaTest extends TestCase
{
    public function test(): void
    {
        $schema = new IpProtectionAddSchema(IpProtectionAddSchema::TYPE_WHITELIST, []);

        self::assertSame(IpProtectionAddSchema::TYPE_WHITELIST, $schema->getType());
        self::assertSame([], $schema->getAddresses());
    }
}
