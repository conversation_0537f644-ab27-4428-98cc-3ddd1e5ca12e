<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources\IpProtection;

use Cdn77\NxgApi\Schema\Legacy\Resources\IpProtection\IpProtectionDetailSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class IpProtectionDetailSchemaTest extends TestCase
{
    public function test(): void
    {
        $schema = new IpProtectionDetailSchema(IpProtectionDetailSchema::TYPE_WHITELIST, []);

        self::assertSame(IpProtectionDetailSchema::TYPE_WHITELIST, $schema->getType());
        self::assertSame([], $schema->getAddresses());
    }
}
