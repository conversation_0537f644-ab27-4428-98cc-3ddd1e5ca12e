<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources\IpProtection;

use Cdn77\NxgApi\Schema\Legacy\Resources\IpProtection\IpProtectionEditSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class IpProtectionEditSchemaTest extends TestCase
{
    public function test(): void
    {
        $schema = new IpProtectionEditSchema(IpProtectionEditSchema::TYPE_WHITELIST, []);

        self::assertSame(IpProtectionEditSchema::TYPE_WHITELIST, $schema->getType());
        self::assertSame([], $schema->getAddresses());
    }

    public function testNull(): void
    {
        $schema = new IpProtectionEditSchema(null, null);

        self::assertNull($schema->getType());
        self::assertNull($schema->getAddresses());
    }
}
