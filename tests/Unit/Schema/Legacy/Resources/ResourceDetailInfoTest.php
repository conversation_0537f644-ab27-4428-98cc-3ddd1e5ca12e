<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources;

use Cdn77\NxgApi\Entity\Legacy\Account;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\IgnoredQueryParam;
use Cdn77\NxgApi\Entity\Legacy\LocationGroup;
use Cdn77\NxgApi\Entity\Legacy\ResourceCaching;
use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;
use Cdn77\NxgApi\Entity\Legacy\SecureToken;
use Cdn77\NxgApi\Resource\Application\Payload\FollowRedirectSchema;
use Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeadersSchema;
use Cdn77\NxgApi\Resource\Application\Payload\SecureTokenDetailSchema;
use Cdn77\NxgApi\Schema\Legacy\Resources\ResourceDetailInfo;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Mockery;
use Ramsey\Uuid\Uuid;

use function assert;

class ResourceDetailInfoTest extends TestCase
{
    use TemporaryData;

    public function testNewInstance(): void
    {
        $createdAtMock = Mockery::mock(DateTimeImmutable::class);
        $updatedAtMock = Mockery::mock(DateTimeImmutable::class);
        $secureToken = Mockery::mock(SecureTokenDetailSchema::class);
        $followRedirect = Mockery::mock(FollowRedirectSchema::class);

        $originHeaders = ['one' => 'two', 'three' => 'four'];
        $responseHeaders = ResponseHeadersSchema::fromArray(['Access-Control-Allow-Origin' => 'some.value']);

        $info = new ResourceDetailInfo(
            123,
            456,
            ['foo.example.com', 'bar.example.com'],
            $createdAtMock,
            $updatedAtMock,
            null,
            'src.example.com',
            ResourceOrigin::SCHEME_HTTPS,
            8080,
            '123.rsc.cdn77.org',
            [456],
            555555,
            1000,
            1,
            0,
            1,
            1,
            ['foo', 'bar'],
            301,
            'foo',
            0,
            0,
            null,
            null,
            0,
            0,
            0,
            false,
            true,
            null,
            null,
            null,
            null,
            null,
            true,
            true,
            false,
            $originHeaders,
            $responseHeaders,
            $secureToken,
            $followRedirect,
        );

        self::assertSame(123, $info->cdnReference);
        self::assertSame(456, $info->accountId);
        self::assertSame(['foo.example.com', 'bar.example.com'], $info->cnames);
        self::assertSame($createdAtMock, $info->createdAt);
        self::assertSame($updatedAtMock, $info->updatedAt);
        self::assertNull($info->suspendedAt);
        self::assertSame('src.example.com', $info->originUrl);
        self::assertSame(ResourceOrigin::SCHEME_HTTPS, $info->originScheme);
        self::assertSame(8080, $info->originPort);
        self::assertSame('123.rsc.cdn77.org', $info->cdnUrl);
        self::assertSame([456], $info->groupId);
        self::assertSame(555555, $info->cacheExpiry);
        self::assertSame(1000, $info->cacheExpiry404);
        self::assertSame(1, $info->disableQueryString);
        self::assertSame(0, $info->ignoreSetCookie);
        self::assertSame(1, $info->mp4PseudoOn);
        self::assertSame(1, $info->instantSsl);
        self::assertSame(['foo', 'bar'], $info->ignoredQueryParams);
        self::assertSame(301, $info->httpsRedirectCode);
        self::assertSame('foo', $info->originBasedir);
        self::assertSame(0, $info->forwardHostHeader);
        self::assertSame(0, $info->streamingPlaylistBypass);
        self::assertNull($info->originTimeout);
        self::assertNull($info->customData);
        self::assertSame(0, $info->waf);
        self::assertSame(0, $info->quic);
        self::assertSame(0, $info->corsOriginHeader);
        self::assertFalse($info->corsTimingEnabled);
        self::assertTrue($info->corsWildcardEnabled);
        self::assertNull($info->awsAccessKeyId);
        self::assertNull($info->awsSecret);
        self::assertNull($info->awsRegion);
        self::assertNull($info->s3BucketName);
        self::assertNull($info->s3Type);
        self::assertTrue($info->sslVerifyDisable);
        self::assertTrue($info->rateLimit);
        self::assertFalse($info->contentDispositionByParam);
        self::assertSame($originHeaders, $info->originHeaders);
        self::assertSame($responseHeaders, $info->responseHeaders);
        self::assertSame($secureToken, $info->secureToken);
        self::assertSame($followRedirect, $info->followRedirect);
    }

    public function testFromResource(): void
    {
        $resource = $this->createResourceMockWithMockData(false, false, false, true);

        assert($resource instanceof CdnResource);

        $info = ResourceDetailInfo::fromResource($resource);

        self::assertSame($resource->getId(), $info->cdnReference);
        self::assertSame($resource->getAccount()->getId(), $info->accountId);
        self::assertSame($resource->getCnames(), $info->cnames);
        self::assertSame($resource->getCreated(), $info->createdAt);
        self::assertSame($resource->getUpdated(), $info->updatedAt);
        self::assertSame($resource->getMainOrigin()->getHost(), $info->originUrl);
        self::assertSame($resource->getMainOrigin()->getScheme(), $info->originScheme);
        self::assertSame($resource->getMainOrigin()->getPort(), $info->originPort);
        self::assertSame($resource->getCdnUrl(), $info->cdnUrl);
        self::assertSame([$resource->getGroup()->getId()], $info->groupId);
        self::assertSame($resource->getCaching()->getExpiry(), $info->cacheExpiry);
        self::assertSame($resource->getCaching()->getExpiry404(), $info->cacheExpiry404);
        self::assertSame($resource->isDisableQueryString(), (bool) $info->disableQueryString);
        self::assertSame($resource->isIgnoreSetCookie(), (bool) $info->ignoreSetCookie);
        self::assertSame($resource->isMp4PseudoStreaming(), (bool) $info->mp4PseudoOn);
        self::assertSame($resource->hasInstantSsl(), (bool) $info->instantSsl);
        self::assertSame(['foo'], $info->ignoredQueryParams);
        self::assertSame($resource->getHttpsRedirectCode(), $info->httpsRedirectCode);
        self::assertSame($resource->getMainOrigin()->getBasedir(), $info->originBasedir);
        self::assertSame($resource->getMainOrigin()->hasForwardHostHeader(), (bool) $info->forwardHostHeader);
        self::assertSame($resource->hasStreamingPlaylistBypass(), (bool) $info->streamingPlaylistBypass);
        self::assertSame($resource->getMainOrigin()->getTimeout(), $info->originTimeout);
        self::assertSame($resource->hasWaf(), (bool) $info->waf);
        self::assertSame($resource->hasQuic(), (bool) $info->quic);
        self::assertSame($resource->hasCorsOriginHeader(), (bool) $info->corsOriginHeader);
        self::assertSame($resource->hasCorsTimingEnabled(), $info->corsTimingEnabled);
        self::assertSame($resource->hasCorsWildcardEnabled(), $info->corsWildcardEnabled);
        self::assertSame($resource->getMainOrigin()->hasSslVerifyDisable(), $info->sslVerifyDisable);
        self::assertSame($resource->hasRateLimit(), $info->rateLimit);
        self::assertSame($resource->hasContentDispositionByParam(), $info->contentDispositionByParam);
        self::assertSame($resource->getMainOrigin()->getOriginHeaders(), $info->originHeaders);
        self::assertSame($resource->getMainOrigin()->hasFollowRedirectOrigin(), $info->followRedirect->enabled);
        self::assertSame($resource->getMainOrigin()->getFollowRedirectCodes(), $info->followRedirect->codes);
        self::assertSame(
            $resource->getResourceSecureToken()->getType(),
            $info->secureToken->secureTokenType,
        );
        self::assertSame(
            $resource->getResourceSecureToken()->getValue(),
            $info->secureToken->secureTokenValue,
        );
        self::assertSame(
            $resource->getResourceSecureToken()->getSecureLinkExpiryParam(),
            $info->secureToken->secureLinkExpiryParam,
        );
        self::assertSame(
            $resource->getResourceSecureToken()->getSecureLinkTokenParam(),
            $info->secureToken->secureLinkTokenParam,
        );
        self::assertSame(
            $resource->getResourceSecureToken()->getSecureLinkPathlenParam(),
            $info->secureToken->secureLinkPathlenParam,
        );
        self::assertSame(
            $resource->getResourceSecureToken()->getSecureLinkSecretParam(),
            $info->secureToken->secureLinkSecretParam,
        );
        self::assertSame(
            $resource->getResourceSecureToken()->hasSecureLinkRewritePlaylist(),
            $info->secureToken->secureLinkRewritePlaylist,
        );
    }

    public function testFromResourceWithNoAccount(): void
    {
        $followRedirect = Mockery::mock(FollowRedirectSchema::class);
        $s3BucketName = 'bucket';
        $s3Type = 'external-s3';
        $info = new ResourceDetailInfo(
            123,
            null,
            [],
            Mockery::mock(DateTimeImmutable::class),
            Mockery::mock(DateTimeImmutable::class),
            null,
            'src.example.com',
            ResourceOrigin::SCHEME_HTTP,
            null,
            '123.rsc.cdn77.org',
            [456],
            555555,
            100,
            1,
            0,
            1,
            1,
            ['foo', 'bar'],
            301,
            'foo',
            1,
            1,
            10,
            ['boolean' => true, 'string' => 'text', 'integer' => 666, 'float' => 88.8],
            0,
            0,
            0,
            true,
            false,
            null,
            null,
            null,
            $s3BucketName,
            $s3Type,
            false,
            false,
            true,
            ['one' => 'two', 'three' => 'four'],
            ResponseHeadersSchema::fromArray(['Access-Control-Allow-Origin' => 'some.value']),
            null,
            $followRedirect,
        );

        self::assertNull($info->accountId);
        self::assertSame($s3BucketName, $info->s3BucketName);
        self::assertSame($s3Type, $info->s3Type);
    }

    public function testFromResourceWithWafQuicCorsOriginHeaderVerifyOriginSsl(): void
    {
        $resource = $this->createResourceMockWithMockData(true, true, true, false);

        assert($resource instanceof CdnResource);

        $info = ResourceDetailInfo::fromResource($resource);

        self::assertSame($resource->hasWaf(), (bool) $info->waf);
        self::assertSame($resource->hasQuic(), (bool) $info->quic);
        self::assertSame($resource->hasCorsOriginHeader(), (bool) $info->corsOriginHeader);
        self::assertSame($resource->getMainOrigin()->hasSslVerifyDisable(), $info->sslVerifyDisable);
    }

    private function createResourceMockWithMockData(
        bool $isWaf,
        bool $isQuic,
        bool $isCorsOriginHeader,
        bool $sslVerifyDisable,
    ): CdnResource {
        $caching = (new ResourceCaching())->withExpiry(123)->withBypass(true)->withMinUses(456);
        $origin = new ResourceOrigin(
            Uuid::uuid4(),
            Mockery::mock(CdnResource::class),
            1,
            'abc.def',
            ResourceOrigin::SCHEME_HTTPS,
            8080,
            'foo',
            10,
        );
        $secureToken = Stub::create(SecureToken::class, [
            'resource' => Stub::create(CdnResource::class),
            'type' => 'type',
            'value' => 'value',
            'secureLinkExpiryParam' => 'secureLinkExpiryParam',
            'secureLinkTokenParam' => 'secureLinkTokenParam',
            'secureLinkPathlenParam' => 'secureLinkPathlenParam',
            'secureLinkSecretParam' => 'secureLinkSecretParam',
            'secureLinkRewritePlaylist' => true,
        ]);

        return Stub::create(CdnResource::class, [
            'account' => Stub::create(Account::class, ['id' => 456]),
            'caching' => $caching,
            'cdnUrl' => '********.rsc.cdn77.org',
            'cnames' => ['foo.bar'],
            'corsOriginHeader' => $isCorsOriginHeader,
            'created' => new DateTimeImmutable(),
            'group' => Stub::create(LocationGroup::class, ['id' => 123]),
            'httpsRedirectCode' => 301,
            'id' => ********,
            'ignoredQueryParams' => new ArrayCollection([Stub::create(IgnoredQueryParam::class, ['name' => 'foo'])]),
            'origins' => new ArrayCollection([$origin]),
            'quic' => $isQuic,
            'streamingPlaylistBypass' => true,
            'suspended' => new DateTimeImmutable(),
            'updated' => new DateTimeImmutable(),
            'waf' => $isWaf,
            'rateLimit' => true,
            'contentDispositionByParam' => false,
            'resourceSecureToken' => $secureToken,
        ]);
    }
}
