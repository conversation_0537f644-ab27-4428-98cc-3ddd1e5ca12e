<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Schema\Legacy\Resources;

use Cdn77\NxgApi\Schema\Legacy\Resources\ResourceDetailInfo;
use Cdn77\NxgApi\Schema\Legacy\Resources\ResourceList;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Cdn77\TestUtils\Stub;

class ResourceListTest extends TestCase
{
    public function testFunctionality(): void
    {
        $fooInfoMock = Stub::create(ResourceDetailInfo::class, []);
        $barInfoMock = Stub::create(ResourceDetailInfo::class);

        $list = new ResourceList([$fooInfoMock]);
        self::assertSame([$fooInfoMock], $list->resources);

        $list = new ResourceList([$fooInfoMock, $barInfoMock]);
        self::assertSame([$fooInfoMock, $barInfoMock], $list->resources);
    }
}
