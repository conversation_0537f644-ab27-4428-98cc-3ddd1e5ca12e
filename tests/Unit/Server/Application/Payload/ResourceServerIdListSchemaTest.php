<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Server\Application\Payload;

use Cdn77\NxgApi\Server\Application\Payload\ResourceServerIdListSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use <PERSON><PERSON>\Serializer\SerializerBuilder;

final class ResourceServerIdListSchemaTest extends TestCase
{
    public function testSerialize(): void
    {
        $data = [
            111122220 => [
                111234,
                112234,
                113234,
            ],
            111200001 => [
                111345,
                112345,
                113345,
            ],
        ];

        $serializerBuilder = SerializerBuilder::create();
        $serializer = $serializerBuilder->build();

        $schema = new ResourceServerIdListSchema($data);
        $serializedSchema = $serializer->serialize($schema, 'json');

        self::assertJsonStringEqualsJsonString(
            '{"111122220":[111234,112234,113234],"111200001":[111345,112345,113345]}',
            $serializedSchema,
        );
    }
}
