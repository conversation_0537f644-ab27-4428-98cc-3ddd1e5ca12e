<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Server\Application\Payload;

use Cdn77\NxgApi\Server\Application\Payload\ResourceIdsSchema;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use <PERSON><PERSON>\Serializer\SerializerBuilder;

final class ResourceIdsSchemaTest extends TestCase
{
    public function testDeserialize(): void
    {
        $data = '[111122220, 111200001]';

        $serializerBuilder = SerializerBuilder::create();
        $serializer = $serializerBuilder->build();

        $schema = $serializer->deserialize($data, ResourceIdsSchema::class, 'json');

        self::assertInstanceOf(ResourceIdsSchema::class, $schema);
        self::assertSame([111122220, 111200001], $schema->resourceIds);
    }
}
