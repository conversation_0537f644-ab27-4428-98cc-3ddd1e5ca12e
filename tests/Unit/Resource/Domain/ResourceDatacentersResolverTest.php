<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Resource\Domain;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Repository\Legacy\CustomLocationRepository;
use Cdn77\NxgApi\Repository\Legacy\LocationRepository;
use Cdn77\NxgApi\Resource\Domain\DTO\ResourceDatacenterLocation;
use Cdn77\NxgApi\Resource\Domain\ResourceDatacentersResolver;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Cdn77\TestUtils\Stub;
use Mockery;

final class ResourceDatacentersResolverTest extends TestCase
{
    public function testResolveForResource(): void
    {
        $resourceId = 1231231230;
        $resource = Stub::create(CdnResource::class, ['id' => $resourceId]);

        $cdnRepository = Mockery::mock(CdnResourceRepository::class);
        $customLocationRepository = Mockery::mock(CustomLocationRepository::class);
        $locationRepository = Mockery::mock(LocationRepository::class);

        $locations = [
            Stub::create(ResourceDatacenterLocation::class, ['city' => 'chicago', 'isEnabled' => true]),
            Stub::create(ResourceDatacenterLocation::class, ['city' => 'shanghai', 'isEnabled' => false]),
        ];

        $cdnRepository->shouldReceive('get')
            ->once()
            ->with($resourceId)
            ->andReturn($resource);
        $customLocationRepository->shouldReceive('hasResourceCustomLocation')
            ->once()
            ->with($resource)
            ->andReturn(false);
        $locationRepository->shouldReceive('findDatacenterLocationsForResource')
            ->once()
            ->with($resource)
            ->andReturn($locations);

        $resolver = new ResourceDatacentersResolver($cdnRepository, $customLocationRepository, $locationRepository);

        $resolver->resolve($resourceId);
    }

    public function testResolveForResourceWithCustomLocations(): void
    {
        $resourceId = 1231231230;
        $resource = Stub::create(CdnResource::class, ['id' => $resourceId]);

        $cdnRepository = Mockery::mock(CdnResourceRepository::class);
        $customLocationRepository = Mockery::mock(CustomLocationRepository::class);
        $locationRepository = Mockery::mock(LocationRepository::class);

        $locations = [
            Stub::create(ResourceDatacenterLocation::class, ['city' => 'prague', 'isEnabled' => true]),
            Stub::create(ResourceDatacenterLocation::class, ['city' => 'los angeles', 'isEnabled' => false]),
        ];

        $cdnRepository->shouldReceive('get')
            ->once()
            ->with($resourceId)
            ->andReturn($resource);
        $customLocationRepository->shouldReceive('hasResourceCustomLocation')
            ->once()
            ->with($resource)
            ->andReturn(true);
        $locationRepository->shouldReceive('findCustomDatacenterLocationsForResource')
            ->once()
            ->with($resource)
            ->andReturn($locations);

        $resolver = new ResourceDatacentersResolver($cdnRepository, $customLocationRepository, $locationRepository);

        $resolver->resolve($resourceId);
    }
}
