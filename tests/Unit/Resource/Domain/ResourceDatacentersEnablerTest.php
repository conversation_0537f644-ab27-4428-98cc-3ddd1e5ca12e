<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Resource\Domain;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Location;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Repository\Legacy\LocationRepository;
use Cdn77\NxgApi\Resource\Domain\ResourceDatacentersEnabler;
use Cdn77\NxgApi\Service\Legacy\Locations\Exception\NotEnoughActiveLocations;
use Cdn77\NxgApi\Service\Legacy\Locations\ResourceLocationsChanger;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Cdn77\TestUtils\Stub;
use Mockery;

use function array_map;

final class ResourceDatacentersEnablerTest extends TestCase
{
    public function testEnableLocations(): void
    {
        $resourceId = 1234567890;
        $resource = Stub::create(CdnResource::class, ['id' => $resourceId]);
        $locationCities = ['prague'];
        $locations = array_map(
            static fn (string $city): Location => Stub::create(Location::class, ['city' => $city]),
            $locationCities,
        );

        $cdnRepository = Mockery::mock(CdnResourceRepository::class);
        $locationRepository = Mockery::mock(LocationRepository::class);
        $resourceLocationsChanger = Mockery::mock(ResourceLocationsChanger::class);

        $cdnRepository->shouldReceive('get')
            ->once()
            ->with($resourceId)
            ->andReturn($resource);

        $locationRepository->shouldReceive('findAllForCities')
            ->once()
            ->with($locationCities)
            ->andReturn($locations);

        $resourceLocationsChanger->shouldReceive('setCustomLocations')
            ->once()
            ->with($resource, $locations, 1);

        $enabler = new ResourceDatacentersEnabler($cdnRepository, $locationRepository, $resourceLocationsChanger);

        $enabler->enable($resourceId, $locationCities);
    }

    public function testEnableNoLocations(): void
    {
        $resourceId = 1234567890;
        $resource = Stub::create(CdnResource::class, ['id' => $resourceId]);
        $locationCities = [];
        $locations = [];

        $cdnRepository = Mockery::mock(CdnResourceRepository::class);
        $locationRepository = Mockery::mock(LocationRepository::class);
        $resourceLocationsChanger = Mockery::mock(ResourceLocationsChanger::class);

        $cdnRepository->shouldReceive('get')
            ->once()
            ->with($resourceId)
            ->andReturn($resource);

        $locationRepository->shouldReceive('findAllForCities')
            ->once()
            ->with($locationCities)
            ->andReturn($locations);

        $resourceLocationsChanger->shouldReceive('setCustomLocations')
            ->once()
            ->with($resource, $locations, 1)
            ->andThrow(new NotEnoughActiveLocations(0, ''));

        $enabler = new ResourceDatacentersEnabler($cdnRepository, $locationRepository, $resourceLocationsChanger);

        self::expectException(NotEnoughActiveLocations::class);

        $enabler->enable($resourceId, $locationCities);
    }
}
