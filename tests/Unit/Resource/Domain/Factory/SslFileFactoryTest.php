<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Resource\Domain\Factory;

use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Resource\Domain\Finder\SslFileIndexFinder;
use Cdn77\NxgApi\Resource\Domain\SslFileFactory;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateMetadata;
use Cdn77\NxgApi\Tests\Generators\CertificateDefinitions;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use Mockery;

use function Safe\date;

final class SslFileFactoryTest extends TestCase
{
    use CertificateDefinitions;

    public function testCreateForSsl(): void
    {
        $validUntil = new DateTimeImmutable('+60 days');
        $certificateMetada = Stub::create(
            CertificateMetadata::class,
            ['validUntil' => $validUntil, 'alternativeNames' => [], 'commonName' => 'example.org'],
        );
        $ssl = Stub::create(Ssl::class, ['resource' => 12345678]);

        $sslFileIndexFinder = Mockery::mock(SslFileIndexFinder::class);
        $sslFileIndexFinder->shouldReceive('getNextForSsl')
            ->once()
            ->with($ssl)
            ->andReturn(3);

        $sslFile = (new SslFileFactory(
            $sslFileIndexFinder,
        ))->createForSsl($certificateMetada, $ssl, SslFile::TYPE_CUSTOM);

        self::assertEquals($sslFile->getCreatedAt()->format('Y-m-d'), date('Y-m-d'));
        self::assertEquals($sslFile->getSsl(), $ssl);
        self::assertEquals($sslFile->getType(), SslFile::TYPE_CUSTOM);
        self::assertEquals($sslFile->getExpiresAt(), $validUntil);
        self::assertEquals($sslFile->getIndex(), 3);
        self::assertEquals($sslFile->getDomains(), ['example.org']);
    }
}
