<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Ip;

use Cdn77\NxgApi\Validator\Constraints\Ip\Cidr;
use Cdn77\NxgApi\Validator\Constraints\Ip\CidrValidator;
use Generator;
use Mockery;
use Mockery\Adapter\Phpunit\MockeryPHPUnitIntegration;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

class CidrValidatorTest extends ConstraintValidatorTestCase
{
    use MockeryPHPUnitIntegration;

    public function testAllowsNull(): void
    {
        $this->validator->validate(null, new Cidr());
        self::assertNoViolation();
    }

    public function testRejectsInvalidConstraint(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate(null, Mockery::mock(Constraint::class));
    }

    public function testRejectsInvalidType(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate([], new Cidr());
    }

    /** @dataProvider validFormatsProvider */
    public function testValidFormat(string $network): void
    {
        $this->validator->validate($network, new Cidr());
        self::assertNoViolation();
    }

    /** @dataProvider invalidFormatsProvider */
    public function testInvalidFormat(string $network): void
    {
        $constraint = new Cidr();
        $this->validator->validate($network, $constraint);

        $this->buildViolation($constraint->invalidFormatMessage)
            ->assertRaised();
    }

    /** @return Generator<string, list<string>> */
    public function validFormatsProvider(): Generator
    {
        yield '0.0.0.0/0' => ['0.0.0.0/0'];
        yield '*******/32' => ['*******/32'];
        yield '10.20.30.40/32' => ['10.20.30.40/32'];
        yield '127.0.0.0/8' => ['127.0.0.0/8'];
        yield '**********/32' => ['**********/32'];
        yield '*********/25' => ['*********/25'];
        yield '********/26' => ['********/26'];
        yield '*******/30' => ['*******/30'];
        yield '2000::/8' => ['2000::/8'];
        yield '2a02:6ea0:dc0d::/64' => ['2a02:6ea0:d50a::/64'];
        yield '2a02:6ea0:dc0d::1335/128' => ['2a02:6ea0:dc0d::1335/128'];
    }

    /** @return Generator<string, list<string>> */
    public function invalidFormatsProvider(): Generator
    {
        yield 'nonip format' => ['crap'];
        yield 'invalid address part' => ['256.0.0.0/24'];
        yield 'invalid mask part' => ['*******/33'];
        yield 'incorrect ip part format' => ['1..2.3.4/32'];
        yield 'incorrect ip part format #2' => ['1.2.3./32'];
        yield 'incorrect ip part format #3' => ['.2.3.4/32'];
        yield 'invalid ipv6 suffix suffix "::/"' => ['2001:0db8:85a3:::/64'];
        yield 'invalid ipv6 mask 129' => ['2001:0db8::/129'];
        yield 'list' => ['*********** ***************'];
        yield 'range' => ['***********-***************'];
        yield 'bits outside of mask' => ['2a02:6ea0:dc0d::1336/64'];
    }

    protected function createValidator(): CidrValidator
    {
        return new CidrValidator();
    }
}
