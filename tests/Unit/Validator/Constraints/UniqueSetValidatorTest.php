<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints;

use Cdn77\NxgApi\Validator\Constraints\UniqueSet;
use Cdn77\NxgApi\Validator\Constraints\UniqueSetValidator;
use Generator;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

class UniqueSetValidatorTest extends ConstraintValidatorTestCase
{
    public function testInvalidConstraint(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate('foo', new NotBlank());
    }

    public function testAcceptsNull(): void
    {
        $this->validator->validate(null, new UniqueSet());
        self::assertNoViolation();
    }

    public function testEmptyArray(): void
    {
        $this->validator->validate([], new UniqueSet());
        self::assertNoViolation();
    }

    /**
     * @param string[] $value
     *
     * @dataProvider validProvider
     */
    public function testValid(array $value, bool $caseSensitive): void
    {
        $constraint = new UniqueSet();
        $constraint->caseSensitive = $caseSensitive;

        $this->validator->validate($value, $constraint);

        self::assertNoViolation();
    }

    /**
     * @param list<string|int> $value
     * @param list<list<string|int>> $expectedDuplicates
     *
     * @dataProvider invalidProvider
     */
    public function testInvalid(array $value, array $expectedDuplicates, bool $caseSensitive): void
    {
        $constraint = new UniqueSet();
        $constraint->caseSensitive = $caseSensitive;

        $this->validator->validate($value, $constraint);

        $violationBuilder = null;
        foreach ($expectedDuplicates as $violation) {
            $violationBuilder = $violationBuilder === null
                ? $this->buildViolation($constraint->duplicateFoundMessage)
                : $violationBuilder->buildNextViolation($constraint->duplicateFoundMessage);

            $violationBuilder->setParameter('%found%', (string) $violation[0])
                ->setParameter('%ref%', (string) $violation[1])
                ->atPath('property.path[' . $violation[2] . ']');
        }

        self::assertNotNull($violationBuilder);
        $violationBuilder->assertRaised();
    }

    /** @return mixed[][] */
    public function validProvider(): iterable
    {
        yield [
            [
                'foo',
                'bar',
            ],
            true,
        ];

        yield [
            [
                'foo',
                'Foo',
                'FOO',
            ],
            true,
        ];

        yield [
            [
                'foo',
                'bar',
                'Baz',
            ],
            true,
        ];

        yield [
            [
                'foo',
                'bar',
            ],
            false,
        ];

        yield [
            [
                123,
                456,
            ],
            true,
        ];
    }

    /** @return Generator<array{
     *     list<string|int>,
     *     list<list<string|int>>,
     *     bool
     *   }> */
    public function invalidProvider(): Generator
    {
        yield [
            [
                'foo',
                'foo',
            ],
            [
                ['foo', 'foo', 1],
            ],
            true,
        ];

        yield [
            [
                'foo',
                'foo',
                'foo',
            ],
            [
                ['foo', 'foo', 1],
                ['foo', 'foo', 2],
            ],
            true,
        ];

        yield [
            [
                'foo',
                'Foo',
                'FOO',
            ],
            [
                ['Foo', 'foo', 1],
                ['FOO', 'foo', 2],
            ],
            false,
        ];

        yield [
            [
                'foo',
                'Bar',
                'Foo',
                'baz',
                'bar',
                'ABC',
            ],
            [
                ['Foo', 'foo', 2],
                ['bar', 'Bar', 4],
            ],
            false,
        ];

        yield [
            [
                123,
                456,
                123,
            ],
            [
                [123, 123, 2],
            ],
            true,
        ];
    }

    protected function createValidator(): UniqueSetValidator
    {
        return new UniqueSetValidator();
    }
}
