<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Resource;

use Cdn77\NxgApi\Validator\Constraints\Resource\AllowedCnameDomain;
use Cdn77\NxgApi\Validator\Constraints\Resource\AllowedCnameDomainValidator;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

final class AllowedCnameDomainValidatorTest extends ConstraintValidatorTestCase
{
    public function testInvalidConstraint(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate('foo', new NotBlank());
    }

    public function testAcceptsNull(): void
    {
        $this->validator->validate(null, new AllowedCnameDomain());
        $this->assertNoViolation();
    }

    /** @dataProvider validValuesProvider */
    public function testValidValues(string $value): void
    {
        $this->validator->validate($value, new AllowedCnameDomain());
        $this->assertNoViolation();
    }

    /** @dataProvider invalidValuesProvider */
    public function testInvalidValues(string $value, string $expectedFailedDomain): void
    {
        $constraint = new AllowedCnameDomain();
        $this->validator->validate($value, $constraint);

        $this->buildViolation($constraint->message)
            ->setParameter('%domain%', $expectedFailedDomain)
            ->assertRaised();
    }

    /** @return string[][] */
    public function validValuesProvider(): iterable
    {
        yield ['example.com'];
        yield ['www.example.com'];
        yield ['foo.bar.example.com'];
        yield ['foo.bar.baz.example.com'];
    }

    /** @return string[][] */
    public function invalidValuesProvider(): iterable
    {
        yield ['x.ripe.cdn77.org', 'ripe.cdn77.org'];
        yield ['foo.ripe.cdn77.org', 'ripe.cdn77.org'];
        yield ['foo.bar.ripe.cdn77.org', 'ripe.cdn77.org'];
        yield ['x.rsc.cdn77.org', 'rsc.cdn77.org'];
        yield ['foo.rsc.cdn77.org', 'rsc.cdn77.org'];
        yield ['foo.bar.rsc.cdn77.org', 'rsc.cdn77.org'];
        yield ['x.cdn77.dev', 'cdn77.dev'];
        yield ['x.cdn77.com', 'cdn77.com'];
        yield ['foo.cdn77.com', 'cdn77.com'];
        yield ['foo.bar.cdn77.com', 'cdn77.com'];
        yield ['cdn77.com', 'cdn77.com'];
        yield ['cdn77.eu', 'cdn77.eu'];
        yield ['cdn77.dev', 'cdn77.dev'];
        yield ['cdn77-storage.com', 'cdn77-storage.com'];
        yield ['test-1.cdn77-storage.com', 'cdn77-storage.com'];
    }

    protected function createValidator(): AllowedCnameDomainValidator
    {
        return new AllowedCnameDomainValidator();
    }
}
