<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Resource;

use Cdn77\NxgApi\Validator\Constraints\Resource\QueryParameterFormat;
use Cdn77\NxgApi\Validator\Constraints\Resource\QueryParameterFormatValidator;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

class QueryParameterFormatValidatorTest extends ConstraintValidatorTestCase
{
    public function testInvalidConstraint(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate('foo', new NotBlank());
    }

    public function testAcceptsNull(): void
    {
        $this->validator->validate(null, new QueryParameterFormat());
        self::assertNoViolation();
    }

    /** @dataProvider validValuesProvider */
    public function testValidValues(string $value): void
    {
        $this->validator->validate($value, new QueryParameterFormat());
        self::assertNoViolation();
    }

    /** @dataProvider invalidValuesProvider */
    public function testInvalidValues(string $value): void
    {
        $constraint = new QueryParameterFormat();

        $this->validator->validate($value, $constraint);
        $this->buildViolation($constraint->message)
            ->setParameter('{{ value }}', '"' . $value . '"')
            ->setParameter('{{ pattern }}', QueryParameterFormat::ALLOWED_FORMAT_RE)
            ->setCode(Regex::REGEX_FAILED_ERROR)
            ->assertRaised();
    }

    /** @return string[][] */
    public function validValuesProvider(): iterable
    {
        yield ['x'];
        yield ['_'];
        yield ['foo'];
        yield ['foo_bar'];
        yield ['foo-bar'];
        yield ['Foo'];
        yield ['foo[]'];
        yield ['foo[][]'];
        yield ['foo[x]'];
        yield ['foo[bar]'];
        yield ['foo[bar][baz]'];
    }

    /** @return string[][] */
    public function invalidValuesProvider(): iterable
    {
        yield ["\n"];
        yield ["foo\nbar"];
        yield ['[]'];
        yield ['foo['];
        yield ['foo[[]'];
        yield ['foo[bar[]]'];
    }

    protected function createValidator(): QueryParameterFormatValidator
    {
        return new QueryParameterFormatValidator();
    }
}
