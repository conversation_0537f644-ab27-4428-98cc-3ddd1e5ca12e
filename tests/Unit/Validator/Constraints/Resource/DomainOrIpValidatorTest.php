<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Resource;

use Cdn77\NxgApi\Validator\Constraints\Resource\DomainOrIp;
use Cdn77\NxgApi\Validator\Constraints\Resource\DomainOrIpValidator;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

class DomainOrIpValidatorTest extends ConstraintValidatorTestCase
{
    public function testInvalidConstraint(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate('foo', new NotBlank());
    }

    public function testAcceptsNull(): void
    {
        $this->validator->validate(null, new DomainOrIp());
        self::assertNoViolation();
    }

    /** @dataProvider getValidDomainsAndIps */
    public function testValidDomain(string $domainOrIp): void
    {
        $this->validator->validate($domainOrIp, new DomainOrIp());

        self::assertNoViolation();
    }

    /** @dataProvider getInvalidIps */
    public function testInvalidDomainsOrIps(string $domainOrIp): void
    {
        $this->validator->validate($domainOrIp, new DomainOrIp());

        $this->buildDomainOrIpViolation($domainOrIp);
    }

    /** @return string[][] */
    public function getValidDomainsAndIps(): array
    {
        return [
            ['*************'],
            ['**********'],
            ['*********'],
            ['google.com'],
            ['test.google.com'],
            ['test.test.google.com'],
        ];
    }

    /** @return string[][] */
    public function getInvalidIps(): array
    {
        return [
            ['0'],
            ['0.0'],
            ['0.0.0'],
            ['256.0.0.0'],
            ['0.256.0.0'],
            ['0.0.256.0'],
            ['-*******'],
            ['foobar'],
        ];
    }

    protected function createValidator(): DomainOrIpValidator
    {
        return new DomainOrIpValidator();
    }

    private function buildDomainOrIpViolation(string $domainOrIp): void
    {
        $this->buildViolation('%string% is not domain or IP.')
            ->setParameter('%string%', $domainOrIp)
            ->assertRaised();
    }
}
