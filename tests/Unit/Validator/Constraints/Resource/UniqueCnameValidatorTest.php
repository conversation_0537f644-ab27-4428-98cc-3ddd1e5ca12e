<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Resource;

use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Validator\Constraints\Resource\UniqueCname;
use Cdn77\NxgApi\Validator\Constraints\Resource\UniqueCnameValidator;
use Mockery;
use Mockery\Adapter\Phpunit\MockeryPHPUnitIntegration;
use Mockery\MockInterface;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

class UniqueCnameValidatorTest extends ConstraintValidatorTestCase
{
    use MockeryPHPUnitIntegration;

    /** @var MockInterface|CdnResourceRepository */
    private $repositoryMock;

    public function testInvalidConstraint(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate('foo', new NotBlank());
    }

    public function testAcceptsNull(): void
    {
        $this->validator->validate(null, new UniqueCname());
        self::assertNoViolation();
    }

    public function testWorksWithNoCnames(): void
    {
        $this->validator->validate([], new UniqueCname());
        self::assertNoViolation();
    }

    public function testWorksWithNullAsCnames(): void
    {
        $this->validator->validate(null, new UniqueCname());
        self::assertNoViolation();
    }

    public function testWithNoExistingCnames(): void
    {
        $testedCnames = ['foo.bar'];
        $existingCnames = [];
        $this->configureRepositoryMock($testedCnames, $existingCnames);

        $this->validator->validate($testedCnames, new UniqueCname());
        self::assertNoViolation();
    }

    public function testWithOneExistingCname(): void
    {
        $testedCnames = ['foo.bar', 'bar.baz'];
        $existingCnames = ['foo.bar'];
        $this->configureRepositoryMock($testedCnames, $existingCnames);

        $constraint = new UniqueCname();
        $this->validator->validate($testedCnames, $constraint);
        $this->buildViolation($constraint->message)
            ->setParameter('%string%', $testedCnames[0])
            ->atPath('property.path[0]') // FIXME this seems to be broken in SF?
            ->assertRaised();
    }

    public function testWithMultipleExistingCnames(): void
    {
        $testedCnames = ['foo.bar', 'bar.baz', '123.baz'];
        $existingCnames = ['foo.bar', '123.baz'];
        $this->configureRepositoryMock($testedCnames, $existingCnames);

        $constraint = new UniqueCname();
        $this->validator->validate($testedCnames, $constraint);
        $this->buildViolation($constraint->message)
            ->setParameter('%string%', $testedCnames[0])
            ->atPath('property.path[0]') // FIXME this seems to be broken in SF?
            ->buildNextViolation($constraint->message)
            ->setParameter('%string%', $testedCnames[2])
            ->atPath('property.path[2]') // FIXME this seems to be broken in SF?
            ->assertRaised();
    }

    protected function setUp(): void
    {
        $this->repositoryMock = Mockery::mock(CdnResourceRepository::class);

        parent::setUp();
    }

    protected function createValidator(): UniqueCnameValidator
    {
        return new UniqueCnameValidator($this->repositoryMock);
    }

    /**
     * @param string[] $testedCnames
     * @param string[] $existingCnames
     */
    private function configureRepositoryMock(array $testedCnames, array $existingCnames): void
    {
        $this->repositoryMock
            ->shouldReceive('findUsedCnames')
            ->once()
            ->with($testedCnames)
            ->andReturn($existingCnames);
    }
}
