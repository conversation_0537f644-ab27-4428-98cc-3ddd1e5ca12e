<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Resource;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Validator\Constraints\Resource\Exists;
use Cdn77\NxgApi\Validator\Constraints\Resource\ExistsValidator;
use Mockery;
use Mockery\Adapter\Phpunit\MockeryPHPUnitIntegration;
use Mockery\MockInterface;
use Symfony\Component\Validator\Constraints\NotNull;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

class ExistsValidatorTest extends ConstraintValidatorTestCase
{
    use MockeryPHPUnitIntegration;

    /** @var CdnResourceRepository|MockInterface */
    private $resourceRepositoryMock;

    public function testInvalidConstraint(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate(null, new NotNull());
    }

    public function testAcceptsNull(): void
    {
        $this->validator->validate(null, new Exists());
        self::assertNoViolation();
    }

    public function testExistingResource(): void
    {
        $this->resourceRepositoryMock->shouldReceive('find')
            ->with(1234567890)
            ->andReturn(Mockery::mock(CdnResource::class));

        $this->validator->validate(1234567890, new Exists());
        self::assertNoViolation();
    }

    public function testInvalidResource(): void
    {
        $this->resourceRepositoryMock->shouldReceive('find')
            ->with(1234567890)
            ->andReturnNull();

        $constraint = new Exists();
        $this->validator->validate(1234567890, $constraint);
        $this->buildViolation($constraint->message)
            ->setParameter('{{ id }}', '1234567890')
            ->assertRaised();
    }

    protected function setUp(): void
    {
        $this->resourceRepositoryMock = Mockery::mock(CdnResourceRepository::class);

        parent::setUp();
    }

    protected function createValidator(): ExistsValidator
    {
        return new ExistsValidator($this->resourceRepositoryMock);
    }
}
