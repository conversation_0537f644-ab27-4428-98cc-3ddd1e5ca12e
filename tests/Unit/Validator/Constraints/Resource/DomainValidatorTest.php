<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Resource;

use Cdn77\NxgApi\Validator\Constraints\Resource\Domain;
use Cdn77\NxgApi\Validator\Constraints\Resource\DomainValidator;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

use function str_repeat;

class DomainValidatorTest extends ConstraintValidatorTestCase
{
    public function testInvalidConstraint(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate('foo', new NotBlank());
    }

    public function testAcceptsNull(): void
    {
        $this->validator->validate(null, new Domain());
        self::assertNoViolation();
    }

    /** @dataProvider validFormatsProvider */
    public function testValidFormats(string $value): void
    {
        $this->validator->validate($value, new Domain());
        self::assertNoViolation();
    }

    /** @dataProvider invalidFormatsProvider */
    public function testInvalidFormats(string $value): void
    {
        $constraint = new Domain();
        $this->validator->validate($value, $constraint);
        $this->buildViolation($constraint->message)
            ->setParameter('%string%', $value)
            ->assertRaised();
    }

    /** @return string[][] */
    public function validFormatsProvider(): iterable
    {
        yield ['cdn77.com'];
        yield ['CDN77.COM'];
        yield ['www.cdn77.com'];
        yield ['i.cz'];
        yield ['www.i.cz'];
        yield ['1.2.3.foo.bar'];
        yield ['1.2.3.4.5.foo'];
        yield ['x.y.z.a.b.c.foo'];
        yield ['111.com'];
        yield ['www.111.com'];
        yield [str_repeat('x', 63) . '.foo.bar'];
        yield [str_repeat(str_repeat('x', 63) . '.', 3) . str_repeat('y', 57) . '.foo'];
        yield [str_repeat('x.', 125) . 'yy'];
        yield ['xn--99zt52a.w3.mag.keio.ac.jp'];
        yield ['xn--ss-uia6e4a.com'];
        yield ['xn--v8jxj3d1dzdz08w.com'];
        yield ['foo.xn--fiqz9s'];
        yield ['xn--ss-uia6e4a.xn--fiqz9s'];
    }

    /** @return string[][] */
    public function invalidFormatsProvider(): iterable
    {
        yield ['foo'];
        yield ['foo.1'];
        yield ['-.foo'];
        yield ['-foo.bar'];
        yield ['foo-.bar'];
        yield ['foo.-bar'];
        yield [str_repeat(str_repeat('x', 63) . '.', 5) . '.foo'];
        yield [str_repeat('x.', 127) . '.foo'];
        yield ['_http.foo.bar'];
        yield ['-.foo.bar'];
    }

    protected function createValidator(): DomainValidator
    {
        return new DomainValidator();
    }
}
