<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Resource\Referer;

use Cdn77\NxgApi\Validator\Constraints\Resource\Referer\DomainFormat;
use Cdn77\NxgApi\Validator\Constraints\Resource\Referer\DomainFormatValidator;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

use function str_repeat;

/**
 * Similar to resource's DomainValidator, but with allowed asterisk at the beginning/end
 */
class DomainFormatValidatorTest extends ConstraintValidatorTestCase
{
    public function testInvalidConstraint(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate('foo', new NotBlank());
    }

    public function testAcceptsNull(): void
    {
        $this->validator->validate(null, new DomainFormat());
        self::assertNoViolation();
    }

    /** @dataProvider validFormatsProvider */
    public function testValidFormats(string $value): void
    {
        $this->validator->validate($value, new DomainFormat());
        self::assertNoViolation();
    }

    /** @dataProvider invalidFormatsProvider */
    public function testInvalidFormats(string $value): void
    {
        $constraint = new DomainFormat();
        $this->validator->validate($value, $constraint);
        $this->buildViolation($constraint->message)
            ->setParameter('%string%', $value)
            ->assertRaised();
    }

    /** @return string[][] */
    public function validFormatsProvider(): iterable
    {
        yield ['cdn77.com'];
        yield ['CDN77.COM'];
        yield ['www.cdn77.com'];
        yield ['i.cz'];
        yield ['www.i.cz'];
        yield ['1.2.3.foo.bar'];
        yield ['1.2.3.4.5.foo'];
        yield ['x.y.z.a.b.c.foo'];
        yield ['111.com'];
        yield ['www.111.com'];
        yield [str_repeat('x', 63) . '.foo.bar'];
        yield [str_repeat(str_repeat('x', 63) . '.', 3) . str_repeat('y', 57) . '.foo'];
        yield [str_repeat('x.', 125) . 'yy'];
        yield ['xn--99zt52a.w3.mag.keio.ac.jp'];
        yield ['xn--ss-uia6e4a.com'];
        yield ['xn--v8jxj3d1dzdz08w.com'];
        yield ['foo.xn--fiqz9s'];
        yield ['xn--ss-uia6e4a.xn--fiqz9s'];
        yield ['*.example'];
        yield ['example.*'];
        yield ['*.example.com'];
        yield ['www.example.*'];
        yield [str_repeat('x', 63) . '.foo.*'];
        yield ['*.' . str_repeat('x', 63) . '.foo'];
        yield [str_repeat(str_repeat('x', 63) . '.', 3) . str_repeat('y', 59) . '.*'];
        yield ['*.' . str_repeat(str_repeat('x', 63) . '.', 3) . str_repeat('y', 55) . '.foo'];
        yield [str_repeat('x.', 125) . '*'];
        yield ['*.' . str_repeat('x.', 124) . 'yy'];
        yield ['localhost'];
        yield ['localhost:123'];
        yield ['example.com:80'];
        yield ['bs-local.com:3000'];
    }

    /** @return string[][] */
    public function invalidFormatsProvider(): iterable
    {
        yield ['foo'];
        yield ['foo.1'];
        yield ['-.foo'];
        yield ['-foo.bar'];
        yield ['foo-.bar'];
        yield ['foo.-bar'];
        yield [str_repeat(str_repeat('x', 63) . '.', 5) . '.foo'];
        yield [str_repeat('x.', 127) . '.foo'];
        yield ['_http.foo.bar'];
        yield ['-.foo.bar'];
        yield ['**.foo'];
        yield ['foo.**'];
        yield ['foo.bar*'];
        yield ['*foo.bar'];
        yield ['*.foo.*'];
        yield ['www.*.foo'];
        yield ['_-localhost.com'];
        yield ['localhost:70000'];
    }

    protected function createValidator(): DomainFormatValidator
    {
        return new DomainFormatValidator();
    }
}
