<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Resource;

use Cdn77\NxgApi\Validator\Constraints\Resource\OriginBaseDir;
use Cdn77\NxgApi\Validator\Constraints\Resource\OriginBaseDirValidator;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

final class OriginBaseDirValidatorTest extends ConstraintValidatorTestCase
{
    public function testInvalidBaseDirWithSlashAtEnd(): void
    {
        $constraint = new OriginBaseDir();

        $this->validator->validate('foo/bar/', $constraint);
        $this->buildViolation($constraint->message)->assertRaised();
    }

    public function testInvalidBaseDirWithSlashAtStart(): void
    {
        $constraint = new OriginBaseDir();

        $this->validator->validate('/foo/bar', $constraint);
        $this->buildViolation($constraint->message)->assertRaised();
    }

    protected function createValidator(): OriginBaseDirValidator
    {
        return new OriginBaseDirValidator();
    }
}
