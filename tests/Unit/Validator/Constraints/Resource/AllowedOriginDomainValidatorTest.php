<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Resource;

use Cdn77\NxgApi\Validator\Constraints\Resource\AllowedOriginDomain;
use Cdn77\NxgApi\Validator\Constraints\Resource\AllowedOriginDomainValidator;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

final class AllowedOriginDomainValidatorTest extends ConstraintValidatorTestCase
{
    public function testInvalidConstraint(): void
    {
        $this->expectException(UnexpectedTypeException::class);
        $this->validator->validate('foo', new NotBlank());
    }

    public function testAcceptsNull(): void
    {
        $this->validator->validate(null, new AllowedOriginDomain());
        $this->assertNoViolation();
    }

    /** @dataProvider validValuesProvider */
    public function testValidValues(string $value): void
    {
        $this->validator->validate($value, new AllowedOriginDomain());
        $this->assertNoViolation();
    }

    /** @dataProvider invalidValuesProvider */
    public function testInvalidValues(string $value, string $expectedFailedDomain): void
    {
        $constraint = new AllowedOriginDomain();
        $this->validator->validate($value, $constraint);

        $this->buildViolation($constraint->message)
            ->setParameter('%domain%', $expectedFailedDomain)
            ->assertRaised();
    }

    /** @return string[][] */
    public function validValuesProvider(): iterable
    {
        yield ['example.com'];
        yield ['www.example.com'];
        yield ['foo.bar.example.com'];
        yield ['foo.bar.baz.example.com'];
        yield ['1.s.cdn77.com'];
        yield ['111.s.cdn77.com'];
        yield ['prg-1.s.cdn77.com'];
        yield ['prg-111.s.cdn77.com'];
        yield ['1.s.cdn77.eu'];
        yield ['111.s.cdn77.eu'];
        yield ['prg-1.s.cdn77.eu'];
        yield ['prg-111.s.cdn77.eu'];
        yield ['abc.push-1.cdn77.com'];
        yield ['abc.push-111.cdn77.com'];
        yield ['test-1.cdn77-storage.com'];
    }

    /** @return string[][] */
    public function invalidValuesProvider(): iterable
    {
        yield ['x.ripe.cdn77.org', 'ripe.cdn77.org'];
        yield ['foo.ripe.cdn77.org', 'ripe.cdn77.org'];
        yield ['foo.bar.ripe.cdn77.org', 'ripe.cdn77.org'];
        yield ['x.rsc.cdn77.org', 'rsc.cdn77.org'];
        yield ['foo.rsc.cdn77.org', 'rsc.cdn77.org'];
        yield ['foo.bar.rsc.cdn77.org', 'rsc.cdn77.org'];
        yield ['x.cdn77.dev', 'cdn77.dev'];
        yield ['x.cdn77.com', 'cdn77.com'];
        yield ['foo.cdn77.com', 'cdn77.com'];
        yield ['foo.bar.cdn77.com', 'cdn77.com'];
        yield ['cdn77.dev', 'cdn77.dev'];
        yield ['cdn77.com', 'cdn77.com'];
        yield ['cdn77.eu', 'cdn77.eu'];
        yield ['foo.cdn77.eu', 'cdn77.eu'];
        yield ['x.foo.cdn77.eu', 'cdn77.eu'];
    }

    protected function createValidator(): AllowedOriginDomainValidator
    {
        return new AllowedOriginDomainValidator();
    }
}
