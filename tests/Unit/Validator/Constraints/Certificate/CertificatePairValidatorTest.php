<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Certificate;

use Cdn77\NxgApi\Entity\LetsEncrypt;
use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use Cdn77\NxgApi\Validator\Constraints\Certificate\CertificatePair;
use Cdn77\NxgApi\Validator\Constraints\Certificate\CertificatePairValidator;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

/** @extends ConstraintValidatorTestCase<CertificatePairValidator> */
class CertificatePairValidatorTest extends ConstraintValidatorTestCase
{
    /** @dataProvider getValidCertificatePairsProvider */
    public function testValidCertificatePair(LetsEncrypt\CertificatePair $certificatePair): void
    {
        $constraint = new CertificatePair();

        $this->validator->validate($certificatePair, $constraint);

        self::assertNoViolation();
    }

    /** @dataProvider getInvalidCertificatePairsProvider */
    public function testInvalidCertificatePair(LetsEncrypt\CertificatePair $certificatePair): void
    {
        $constraint = new CertificatePair();

        $this->validator->validate($certificatePair, $constraint);

        $this->buildViolation($constraint->messageCertificatePairMustBeValid)->assertRaised();
    }

    /** @return CertificatePair[][] */
    public function getValidCertificatePairsProvider(): iterable
    {
        yield [(new CertificatePairGenerator())->generateRandomCertificatePair()];
    }

    /** @return CertificatePair[][] */
    public function getInvalidCertificatePairsProvider(): iterable
    {
        yield [(new CertificatePairGenerator())->generateRandomInvalidCertificatePair()];
    }

    protected function createValidator(): CertificatePairValidator
    {
        return new CertificatePairValidator();
    }
}
