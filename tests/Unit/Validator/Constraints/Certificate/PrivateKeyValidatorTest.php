<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Certificate;

use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use Cdn77\NxgApi\Validator\Constraints\Certificate\PrivateKey;
use Cdn77\NxgApi\Validator\Constraints\Certificate\PrivateKeyValidator;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

class PrivateKeyValidatorTest extends ConstraintValidatorTestCase
{
    /** @dataProvider getValidPrivateKeysProvider */
    public function testValidPrivateKey(string $certificate): void
    {
        $constraint = new PrivateKey();

        $this->validator->validate($certificate, $constraint);

        self::assertNoViolation();
    }

    /** @dataProvider getEmptyPrivateKeysProvider */
    public function testEmptyPrivateKey(string $certificate): void
    {
        $constraint = new PrivateKey();

        $this->validator->validate($certificate, $constraint);

        $this
            ->buildViolation($constraint->messagePrivateKeyCannotBeEmpty)
            ->buildNextViolation($constraint->messagePrivateKeyMustBeValid)
            ->assertRaised();
    }

    /** @dataProvider getInvalidPrivateKeysProvider */
    public function testInvalidPrivateKey(string $certificate): void
    {
        $constraint = new PrivateKey();

        $this->validator->validate($certificate, $constraint);

        $this->buildViolation($constraint->messagePrivateKeyMustBeValid)->assertRaised();
    }

    /** @return string[][] */
    public function getValidPrivateKeysProvider(): iterable
    {
        yield [(new CertificatePairGenerator())->generateRandomPrivateKey()];
    }

    /** @return string[][] */
    public function getEmptyPrivateKeysProvider(): iterable
    {
        yield [''];
    }

    /** @return string[][] */
    public function getInvalidPrivateKeysProvider(): iterable
    {
        yield ['invalidPrivateKey'];
        yield ["-----BEGIN RSA PRIVATE KEY-----\n-----END RSA PRIVATE KEY-----\n"];
        yield ["-----BEGIN RSA PRIVATE KEY-----\ninvalidPrivateKey\n-----END RSA PRIVATE KEY-----\n"];
    }

    protected function createValidator(): PrivateKeyValidator
    {
        return new PrivateKeyValidator();
    }
}
