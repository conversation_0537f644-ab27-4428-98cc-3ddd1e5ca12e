<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Certificate;

use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use Cdn77\NxgApi\Validator\Constraints\Certificate\CertificateChain;
use Cdn77\NxgApi\Validator\Constraints\Certificate\CertificateChainValidator;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

use function array_shift;
use function random_int;

/** @extends ConstraintValidatorTestCase<CertificateChainValidator> */
class CertificateChainValidatorTest extends ConstraintValidatorTestCase
{
    public function testNoChain(): void
    {
        $this->validator->validate($this->createValidCertificate(), new CertificateChain());
        self::assertNoViolation();
    }

    /** @dataProvider validChainProvider */
    public function testValidChain(string $certificate): void
    {
        $this->validator->validate($certificate, new CertificateChain());
        self::assertNoViolation();
    }

    /**
     * @param int[] $violationOffsets
     *
     * @dataProvider invalidChainProvider
     */
    public function testInvalidChain(string $certificate, array $violationOffsets): void
    {
        $constraint = new CertificateChain();

        $this->validator->validate($certificate, $constraint);

        $violationChain = $this->buildViolation($constraint->message)
            ->setParameter('{{ position }}', (string) array_shift($violationOffsets));

        foreach ($violationOffsets as $violationOffset) {
            $violationChain = $violationChain->buildNextViolation($constraint->message)
                ->setParameter('{{ position }}', (string) $violationOffset);
        }

        $violationChain->assertRaised();
    }

    /** @return string[][] */
    public function validChainProvider(): iterable
    {
        yield [
            $this->createValidCertificate()
            . "\n"
            . $this->createValidCertificate(),
        ];

        yield [
            $this->createValidCertificate()
            . "\n"
            . $this->createValidCertificate()
            . "\n"
            . $this->createValidCertificate(),
        ];

        yield [
            $this->createValidCertificate()
            . "\n"
            . $this->createValidCertificate()
            . "\n"
            . $this->createValidCertificate()
            . "\n"
            . $this->createValidCertificate(),
        ];
    }

    /** @return mixed[][] */
    public function invalidChainProvider(): iterable
    {
        yield [
            $this->createValidCertificate()
            . $this->createInvalidCertificate(),
            [1],
        ];

        yield [
            $this->createValidCertificate()
            . $this->createInvalidCertificate()
            . $this->createInvalidCertificate(),
            [1, 2],
        ];

        yield [
            $this->createValidCertificate()
            . $this->createInvalidCertificate()
            . $this->createInvalidCertificate()
            . $this->createInvalidCertificate(),
            [1, 2, 3],
        ];

        yield [
            $this->createValidCertificate()
            . $this->createValidCertificate()
            . $this->createInvalidCertificate()
            . $this->createValidCertificate()
            . $this->createInvalidCertificate(),
            [2, 4],
        ];
    }

    protected function createValidator(): CertificateChainValidator
    {
        return new CertificateChainValidator();
    }

    private function createValidCertificate(): string
    {
        return (new CertificatePairGenerator())->generateRandomCertificate();
    }

    private function createInvalidCertificate(): string
    {
        return "-----BEGIN CERTIFICATE-----\ninvalidCertificate" . random_int(
            1,
            9999,
        ) . "\n-----END CERTIFICATE-----\n";
    }
}
