<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Certificate;

use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use Cdn77\NxgApi\Validator\Constraints\Certificate\Certificate;
use Cdn77\NxgApi\Validator\Constraints\Certificate\CertificateValidator;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

class CertificateValidatorTest extends ConstraintValidatorTestCase
{
    /** @dataProvider getValidCertificatesProvider */
    public function testValidCertificate(string $certificate): void
    {
        $constraint = new Certificate();

        $this->validator->validate($certificate, $constraint);

        self::assertNoViolation();
    }

    /** @dataProvider getEmptyCertificatesProvider */
    public function testEmptyCertificate(string $certificate): void
    {
        $constraint = new Certificate();

        $this->validator->validate($certificate, $constraint);

        $this
            ->buildViolation($constraint->messageCertificateCannotBeEmpty)
            ->buildNextViolation($constraint->messageCertificateMustBeValid)
            ->assertRaised();
    }

    /** @dataProvider getInvalidCertificatesProvider */
    public function testInvalidCertificate(string $certificate): void
    {
        $constraint = new Certificate();

        $this->validator->validate($certificate, $constraint);

        $this->buildViolation($constraint->messageCertificateMustBeValid)->assertRaised();
    }

    /** @return string[][] */
    public function getValidCertificatesProvider(): iterable
    {
        yield [(new CertificatePairGenerator())->generateRandomCertificate()];
    }

    /** @return string[][] */
    public function getEmptyCertificatesProvider(): iterable
    {
        yield [''];
    }

    /** @return string[][] */
    public function getInvalidCertificatesProvider(): iterable
    {
        yield ['invalidCertificate'];
        yield ["-----BEGIN CERTIFICATE-----\n-----END CERTIFICATE-----\n"];
        yield ["-----BEGIN CERTIFICATE-----\ninvalidCertificate\n-----END CERTIFICATE-----\n"];
    }

    protected function createValidator(): CertificateValidator
    {
        return new CertificateValidator();
    }
}
