<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\IpProtection\Domain;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceIpProtection;
use Cdn77\NxgApi\IpProtection\Application\Payload\IpProtectionInfo;
use Cdn77\NxgApi\IpProtection\Domain\SetupIpProtection;
use Cdn77\NxgApi\IpProtection\Domain\Value\IpProtectionType;
use Cdn77\NxgApi\Schema\Legacy\Resources\IpProtection\IpProtectionAddressDetail;
use Cdn77\NxgApi\Service\Legacy\IpProtection\Exception\IpProtectionNotEnabled;
use Cdn77\NxgApi\Service\Legacy\IpProtection\IpProtectionManager;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Cdn77\TestUtils\Stub;
use Mockery;

final class SetupIpProtectionTest extends TestCase
{
    public function testEnable(): void
    {
        $cdnResource = Stub::create(CdnResource::class);
        $ipProtectionInfo = new IpProtectionInfo(
            IpProtectionType::TYPE_WHITELIST,
            [new IpProtectionAddressDetail('*******/32')],
        );

        $ipProtectionManager = Mockery::mock(IpProtectionManager::class);

        $ipProtectionManager
            ->shouldReceive('get')
            ->once()
            ->with($cdnResource)
            ->andThrow(IpProtectionNotEnabled::class);
        $ipProtectionManager
            ->shouldReceive('enable')
            ->once();

        $setupIpProtection = new SetupIpProtection($ipProtectionManager);

        $setupIpProtection->setup($cdnResource, $ipProtectionInfo);
    }

    public function testDisable(): void
    {
        $cdnResource = Stub::create(CdnResource::class);
        $ipProtectionInfo = new IpProtectionInfo(IpProtectionType::TYPE_DISABLED, []);
        $resourceIpProtection = Stub::create(ResourceIpProtection::class);

        $ipProtectionManager = Mockery::mock(IpProtectionManager::class);

        $ipProtectionManager
            ->shouldReceive('get')
            ->once()
            ->with($cdnResource)
            ->andReturn($resourceIpProtection);
        $ipProtectionManager
            ->shouldReceive('disable')
            ->once()
            ->with($cdnResource);

        $setupIpProtection = new SetupIpProtection($ipProtectionManager);

        $setupIpProtection->setup($cdnResource, $ipProtectionInfo);
    }

    public function testUpdate(): void
    {
        $cdnResource = Stub::create(CdnResource::class);
        $ipProtectionInfo = new IpProtectionInfo(
            IpProtectionType::TYPE_BLACKLIST,
            [new IpProtectionAddressDetail('*******/32')],
        );
        $resourceIpProtection = Stub::create(ResourceIpProtection::class);

        $ipProtectionManager = Mockery::mock(IpProtectionManager::class);

        $ipProtectionManager
            ->shouldReceive('get')
            ->once()
            ->with($cdnResource)
            ->andReturn($resourceIpProtection);
        $ipProtectionManager
            ->shouldReceive('modify')
            ->once();

        $setupIpProtection = new SetupIpProtection($ipProtectionManager);

        $setupIpProtection->setup($cdnResource, $ipProtectionInfo);
    }
}
