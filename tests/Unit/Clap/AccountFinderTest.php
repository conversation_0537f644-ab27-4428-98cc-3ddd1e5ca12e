<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Clap;

use Cdn77\NxgApi\Clap\Domain\Client;
use Cdn77\NxgApi\Clap\Infrastructure\GraphQLAccountFinder;
use Cdn77\NxgApi\Tests\Functional\KernelTestCase;
use Mo<PERSON>y;

final class AccountFinderTest extends KernelTestCase
{
    public function testBothTypesSet(): void
    {
        $accountFinder = $this->prepareAccountFinder(
            [['node' => ['oldId' => '1']], ['node' => ['oldId' => '2']]],
            [['node' => ['oldId' => '3']]],
        );

        $result = $accountFinder->findVipAndTop();

        self::assertEquals([1, 2, 3], $result);
    }

    public function testOnlyVip(): void
    {
        $accountFinder = $this->prepareAccountFinder(
            [['node' => ['oldId' => '1']], ['node' => ['oldId' => '2']]],
            [],
        );

        $result = $accountFinder->findVipAndTop();

        self::assertEquals([1, 2], $result);
    }

    public function testMinimumAccounts(): void
    {
        $accountFinder = $this->prepareAccountFinder(
            [['node' => ['oldId' => '1']], ['node' => ['oldId' => '2']]],
            [],
            3,
        );

        self::expectExceptionMessage('Not enough accounts found');
        $accountFinder->findVipAndTop();
    }

    /**
     * @param array<int, array<string, array<string, string>>> $vip
     * @param array<int, array<string, array<string, string>>> $top
     */
    private function prepareAccountFinder(array $vip, array $top, int $minimumAccounts = 1): GraphQLAccountFinder
    {
        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('query')
            ->andReturn(['data' => ['vip' => ['edges' => $vip], 'top' => ['edges' => $top]]]);

        return new GraphQLAccountFinder($clientMock, $minimumAccounts);
    }
}
