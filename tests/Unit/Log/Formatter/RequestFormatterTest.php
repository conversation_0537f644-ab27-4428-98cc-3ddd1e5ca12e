<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Log\Formatter;

use Cdn77\NxgApi\Log\Formatter\RequestsFormatter;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTime;
use Monolog\Logger;

use function Safe\json_encode;

class RequestFormatterTest extends TestCase
{
    /** @return string[] */
    private static function getTestPrivateKeys(): array
    {
        return [
            '-----BEGIN RSA PRIVATE KEY-----
nTPJwOyXi5IV9LMndVtmKGISKIFEx0FjfE0DSxpYUyfWgHP0q/MUWfbEHEsyoUMr
CcAovr+cntIF9xDLXKhfJ02WMXJx4S4QbWDbPxHcj9G8JAjT57wTG93Lh6Ab3/zu
E5KG66UB9rxbeX6bUZvDUGZRMOaXMlAfVfWmFhf8HbIvcDxBLG4k4hayRG6zn+eh
dXgFCFCYIEGjnuY4v89sUAhiAdeNzw82VZYBcoD8W61qhxNZhdBdBAbpnJ607zWk
F/FUbNQm1LtWTucSw772KTPBlvjjgTIhr+c9rZFMGwPEBQuL1VZv3gOTa04jVxN1
5M9a2uHDKPjmOeXgvOnBpKzYLv/QqznTer3/SUpIgsGXdHXG9OzZ5mfwkHE5X3kH
CBUjzKh2mfjTZw3bueTZYlK5wHlg2SkpnYtaw5YZk8gn9YTInmUY0Z2L6YGez3e0
4/imxAkQuDo3nt+qLJqSYQH6pb9OWxihfy4mnKeisEeFxkqjDxhBVv0T1+dYCmTw
eNQX0p6eWQHgbqhpjZonM++EAfmb10EMqvQ140MGXqu5B63zdFpoYCGio90gGs4d
KhJUoKkiihqrbrixcoxwyPljMWnVGu/rB5aJk21FRy0e6DHjHaDYObISIXR5xb/2
3fxvzPqAa33tau7rV42KInN6GHINzka0hTlDXFtbeMglOfo73H95Z9KdKGtumLT5
IXuxzp5U0IvxZX3Z83+Asjio5Ky06t6OFY3Ea8EvuBwx1kZyoVQOwRrLhXrBVOEO
/H4OvjR1k13gf1k8jYXWbb02YDYTS6BQUqcV79VT1IoGHmm/EqA9WQ==
-----END RSA PRIVATE KEY-----',
            '-----BEGIN PRIVATE KEY-----
nTPJwOyXi5IV9LMndVtmKGISKIFEx0FjfE0DSxpYUyfWgHP0q/MUWfbEHEsyoUMr
CcAovr+cntIF9xDLXKhfJ02WMXJx4S4QbWDbPxHcj9G8JAjT57wTG93Lh6Ab3/zu
E5KG66UB9rxbeX6bUZvDUGZRMOaXMlAfVfWmFhf8HbIvcDxBLG4k4hayRG6zn+eh
dXgFCFCYIEGjnuY4v89sUAhiAdeNzw82VZYBcoD8W61qhxNZhdBdBAbpnJ607zWk
F/FUbNQm1LtWTucSw772KTPBlvjjgTIhr+c9rZFMGwPEBQuL1VZv3gOTa04jVxN1
5M9a2uHDKPjmOeXgvOnBpKzYLv/QqznTer3/SUpIgsGXdHXG9OzZ5mfwkHE5X3kH
CBUjzKh2mfjTZw3bueTZYlK5wHlg2SkpnYtaw5YZk8gn9YTInmUY0Z2L6YGez3e0
4/imxAkQuDo3nt+qLJqSYQH6pb9OWxihfy4mnKeisEeFxkqjDxhBVv0T1+dYCmTw
eNQX0p6eWQHgbqhpjZonM++EAfmb10EMqvQ140MGXqu5B63zdFpoYCGio90gGs4d
KhJUoKkiihqrbrixcoxwyPljMWnVGu/rB5aJk21FRy0e6DHjHaDYObISIXR5xb/2
3fxvzPqAa33tau7rV42KInN6GHINzka0hTlDXFtbeMglOfo73H95Z9KdKGtumLT5
IXuxzp5U0IvxZX3Z83+Asjio5Ky06t6OFY3Ea8EvuBwx1kZyoVQOwRrLhXrBVOEO
/H4OvjR1k13gf1k8jYXWbb02YDYTS6BQUqcV79VT1IoGHmm/EqA9WQ
-----END PRIVATE KEY-----',
        ];
    }

    public function testBasicFormatting(): void
    {
        $formatter = $this->createFormatter();
        $message = $this->createDefaultMessage();

        self::assertSame(
            "[1970-01-01T00:00:00+00:00 @ test-id] test:\n\n=========================\n",
            $formatter->format($message),
        );

        $message['context']['payload'] = json_encode(['foo', 'bar']);
        self::assertSame(
            "[1970-01-01T00:00:00+00:00 @ test-id] test:\n[\"foo\",\"bar\"]\n=========================\n",
            $formatter->format($message),
        );

        $message['context']['payload'] = json_encode(['foo' => 'bar']);
        self::assertSame(
            "[1970-01-01T00:00:00+00:00 @ test-id] test:\n{\"foo\":\"bar\"}\n=========================\n",
            $formatter->format($message),
        );
    }

    public function testStrippingCertificates(): void
    {
        $formatter = $this->createFormatter();
        $message = $this->createDefaultMessage();

        $message['context']['payload'] = json_encode(['foo' => 'bar', 'key' => static::getTestPrivateKeys()[0]]);
        self::assertSame(
            "[1970-01-01T00:00:00+00:00 @ test-id] test:\n{\"foo\":\"bar\",\"key\":\"" .
            "-----BEGIN RSA PRIVATE KEY-----\\n<stripped>\\n-----END RSA PRIVATE KEY-----" .
            "\"}\n=========================\n",
            $formatter->format($message),
        );

        $message['context']['payload'] = json_encode(['key' => static::getTestPrivateKeys()[1]]);
        self::assertSame(
            "[1970-01-01T00:00:00+00:00 @ test-id] test:\n{\"key\":\"" .
            "-----BEGIN PRIVATE KEY-----\\n<stripped>\\n-----END PRIVATE KEY-----\"}\n=========================\n",
            $formatter->format($message),
        );
    }

    /** @return mixed[] */
    private function createDefaultMessage(): array
    {
        return [
            'level' => Logger::ERROR,
            'level_name' => 'ERROR',
            'channel' => 'test',
            'context' => ['payload' => ''],
            'datetime' => new DateTime('@0'),
            'extra' => ['request_id' => 'test-id'],
            'message' => 'test',
        ];
    }

    private function createFormatter(): RequestsFormatter
    {
        return new RequestsFormatter();
    }
}
