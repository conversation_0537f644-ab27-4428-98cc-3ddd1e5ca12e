<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Filter\Resource;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Filter\Resource\DeletedResourceFilter;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Mapping\ClassMetadata;
use Mockery;

class DeletedResourceFilterTest extends TestCase
{
    /** @var DeletedResourceFilter */
    private $filter;

    public function testNotResource(): void
    {
        self::assertSame(
            '',
            $this->filter->addFilterConstraint($this->createMetadataMock('Foo'), 'r'),
        );
    }

    public function testResource(): void
    {
        self::assertSame(
            'r.deleted IS NULL',
            $this->filter->addFilterConstraint($this->createMetadataMock(CdnResource::class), 'r'),
        );
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->filter = new DeletedResourceFilter(Mockery::mock(EntityManagerInterface::class));
    }

    private function createMetadataMock(string $className): ClassMetadata
    {
        $metadata = Mockery::mock(ClassMetadata::class);
        $metadata->shouldReceive('getName')
            ->withNoArgs()
            ->andReturn($className);

        return $metadata;
    }
}
