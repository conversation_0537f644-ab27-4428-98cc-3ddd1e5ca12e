<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Service\LetsEncrypt\DomainChooser;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser\DefaultDomainChooser;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class DefaultDomainChooserTest extends TestCase
{
    /** @var DefaultDomainChooser */
    private $chooser;

    public function testSupports(): void
    {
        self::assertTrue($this->chooser->supports(Mockery::mock(CdnResource::class)));
    }

    public function testChoose(): void
    {
        $resource = Mockery::mock(CdnResource::class);
        $resource->shouldReceive('getCdnUrl')
            ->andReturn('1234567890.rsc.cdn77.org');
        $resource->shouldReceive('getCnames')
            ->andReturn(['foo.bar']);

        self::assertSame(['1234567890.rsc.cdn77.org', 'foo.bar'], $this->chooser->choose($resource));
    }

    public function testChooseWithNoCnames(): void
    {
        $this->expectExceptionMessage('The resource has no cnames.');
        $this->expectException(InvalidArgument::class);
        $resource = Mockery::mock(CdnResource::class);
        $resource->shouldReceive('getCdnUrl')
            ->andReturn('1234567890.rsc.cdn77.org');
        $resource->shouldReceive('getCnames')
            ->andReturn([]);

        $this->chooser->choose($resource);
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->chooser = new DefaultDomainChooser();
    }
}
