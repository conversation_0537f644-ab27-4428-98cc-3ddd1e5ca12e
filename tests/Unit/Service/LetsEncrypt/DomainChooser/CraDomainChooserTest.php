<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Service\LetsEncrypt\DomainChooser;

use Cdn77\NxgApi\Entity\Legacy\Account;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser\CraDomainChooser;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class CraDomainChooserTest extends TestCase
{
    /** @var CraDomainChooser */
    private $chooser;

    public function testSupportsWithDifferentAccount(): void
    {
        $resource = Mockery::mock(CdnResource::class);
        $account = Mockery::mock(Account::class);
        $resource->shouldReceive('getAccount')
            ->andReturn($account);
        $account->shouldReceive('getId')
            ->andReturn(11111);

        self::assertFalse($this->chooser->supports($resource));
    }

    public function testSupportsWithCraAccount(): void
    {
        $resource = Mockery::mock(CdnResource::class);
        $account = Mockery::mock(Account::class);
        $resource->shouldReceive('getAccount')
            ->andReturn($account);
        $account->shouldReceive('getId')
            ->andReturn(CraDomainChooser::ACCOUNT_IDS[0]);

        self::assertTrue($this->chooser->supports($resource));
    }

    public function testChoose(): void
    {
        $resource = Mockery::mock(CdnResource::class);
        $resource->shouldReceive('getId')
            ->andReturn(**********);
        $resource->shouldReceive('getCnames')
            ->andReturn(['**********.ssl.cdn.cra.cz', 'foo.bar']);

        self::assertSame(['**********.ssl.cdn.cra.cz', 'foo.bar'], $this->chooser->choose($resource));
    }

    public function testChooseWithMissingCraCname(): void
    {
        $this->expectExceptionMessage('Incorrectly configured CRA resource.');
        $this->expectException(InvalidArgument::class);
        $resource = Mockery::mock(CdnResource::class);
        $resource->shouldReceive('getId')
            ->andReturn(**********);
        $resource->shouldReceive('getCnames')
            ->andReturn(['foo.bar']);

        self::assertSame(['**********.rsc.cdn77.org', 'foo.bar'], $this->chooser->choose($resource));
    }

    public function testChooseWithNoCnames(): void
    {
        $this->expectExceptionMessage('CRA resource must have a cname.');
        $this->expectException(InvalidArgument::class);
        $resource = Mockery::mock(CdnResource::class);
        $resource->shouldReceive('getCnames')
            ->andReturn([]);

        $this->chooser->choose($resource);
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->chooser = new CraDomainChooser();
    }
}
