<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Service\LetsEncrypt\DomainChooser;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser\DomainChooser;
use Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser\DomainChooserChain;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;
use Mockery\MockInterface;

class DomainChooserChainTest extends TestCase
{
    /** @var DomainChooserChain */
    private $chain;

    public function testSupports(): void
    {
        self::assertTrue($this->chain->supports(Mockery::mock(CdnResource::class)));
    }

    public function testChoose(): void
    {
        $resource = Mockery::mock(CdnResource::class);

        $chooserA = Mockery::mock(DomainChooser::class);
        $chooserB = Mockery::mock(DomainChooser::class);

        $this->expectSupportsCall($chooserA, $resource, false);
        $this->expectSupportsCall($chooserB, $resource, true);
        $this->expectChooseCall($chooserB, $resource, ['foo.bar']);

        $this->chain->add($chooserA);
        $this->chain->add($chooserB);

        self::assertSame(['foo.bar'], $this->chain->choose($resource));
    }

    public function testAdd(): void
    {
        $chooserA = Mockery::mock(DomainChooser::class);
        $chooserB = Mockery::mock(DomainChooser::class);
        $chooserC = Mockery::mock(DomainChooser::class);

        $this->chain->add($chooserA, 100);
        $this->chain->add($chooserB, 100);
        $this->chain->add($chooserC, 200);

        self::assertSame(
            [
                100 => [
                    $chooserA,
                    $chooserB,
                ],
                200 => [$chooserC],
            ],
            $this->chain->getPrioritizedChoosers(),
        );
    }

    public function testGetOrderedChoosers(): void
    {
        $chooserA = Mockery::mock(DomainChooser::class);
        $chooserB = Mockery::mock(DomainChooser::class);
        $chooserC = Mockery::mock(DomainChooser::class);

        $this->chain->add($chooserA, 100);
        $this->chain->add($chooserB, 100);
        $this->chain->add($chooserC, 200);

        self::assertSame(
            [
                $chooserC,
                $chooserA,
                $chooserB,
            ],
            $this->chain->getOrderedChoosers(),
        );
    }

    public function testChooseWithPriorities(): void
    {
        $resource = Mockery::mock(CdnResource::class);

        $chooserA = Mockery::mock(DomainChooser::class);
        $chooserB = Mockery::mock(DomainChooser::class);
        $chooserC = Mockery::mock(DomainChooser::class);

        $this->expectSupportsCall($chooserA, $resource, false);
        $this->expectSupportsCall($chooserB, $resource, true);
        $this->expectSupportsCall($chooserC, $resource, false);
        $this->expectChooseCall($chooserB, $resource, ['foo.bar']);

        $this->chain->add($chooserA, 100);
        $this->chain->add($chooserB, 100);
        $this->chain->add($chooserC, 200);

        self::assertSame(['foo.bar'], $this->chain->choose($resource));
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->chain = new DomainChooserChain();
    }

    /** @param MockInterface|DomainChooser $chooser */
    private function expectSupportsCall(MockInterface $chooser, CdnResource $resource, bool $supports): void
    {
        $chooser->shouldReceive('supports')
            ->with($resource)
            ->andReturn($supports);
    }

    /**
     * @param MockInterface|DomainChooser $chooser
     * @param string[]                    $domains
     */
    private function expectChooseCall(MockInterface $chooser, CdnResource $resource, array $domains): void
    {
        $chooser->shouldReceive('choose')
            ->with($resource)
            ->andReturn($domains);
    }
}
