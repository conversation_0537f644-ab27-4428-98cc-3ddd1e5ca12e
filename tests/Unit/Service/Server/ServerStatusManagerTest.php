<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Service\Server;

use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Entity\Legacy\ServerCurrentStatus;
use Cdn77\NxgApi\Entity\Legacy\ServerLastDownStatus;
use Cdn77\NxgApi\Repository\Legacy\ServerCurrentStatusRepository;
use Cdn77\NxgApi\Repository\Legacy\ServerLastDownStatusRepository;
use Cdn77\NxgApi\Service\Server\ServerStatus;
use Cdn77\NxgApi\Service\Server\ServerStatusManager;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;
use Doctrine\DBAL\LockMode;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Mockery;
use Mo<PERSON>y\Matcher\Closure;
use Mockery\MockInterface;

class ServerStatusManagerTest extends TestCase
{
    /** @var MockInterface|EntityManagerInterface */
    private $entityManagerMock;

    /** @var MockInterface|ServerCurrentStatusRepository */
    private $currentStatusRepositoryMock;

    /** @var MockInterface|ServerLastDownStatusRepository */
    private $lastDownStatusRepositoryMock;

    /** @var MockInterface|Server */
    private $serverMock;

    /** @var MockInterface[]|EntityRepository[] */
    private $repositoryTypeMap;

    private Closure $nowMock;

    public function testWithNoExistingRecordsCreatesEntries(): void
    {
        $oldStatus = new ServerStatus($this->serverMock, true, true, true, true);
        $newStatus = new ServerStatus($this->serverMock, true, false, false, false);

        foreach ([ServerCurrentStatus::class, ServerLastDownStatus::class] as $type) {
            $this->expectRepositoryFind($type, null);
            $this->expectEntityManagerPersistType($type);
        }

        $this->createManager()->handleStatusChange($this->serverMock, $oldStatus, $newStatus);
    }

    public function testWithExistingRecordsDoesNotAttemptToCreateAnother(): void
    {
        $oldStatus = new ServerStatus($this->serverMock, true, true, true, true);
        $newStatus = new ServerStatus($this->serverMock, true, false, false, false);

        $typeMocks = [
            ServerCurrentStatus::class => Mockery::mock(ServerCurrentStatus::class),
            ServerLastDownStatus::class => Mockery::mock(ServerLastDownStatus::class),
        ];

        foreach ([ServerCurrentStatus::class, ServerLastDownStatus::class] as $type) {
            $this->expectRepositoryFind($type, $typeMocks[$type]);
            $this->expectEntityManagerNoPersistType($type);
        }

        $typeMocks[ServerCurrentStatus::class]->shouldReceive('update')
            ->with(
                $this->nowMock,
                false,
                ServerCurrentStatus::REASON_FORCED,
            );
        $typeMocks[ServerLastDownStatus::class]->shouldReceive('update')
            ->with(
                $this->nowMock,
                ServerLastDownStatus::REASON_FORCED,
            );

        $this->createManager()->handleStatusChange($this->serverMock, $oldStatus, $newStatus);
    }

    public function testBeingUpDoesNotGenerateLastDownEntry(): void
    {
        $oldStatus = new ServerStatus($this->serverMock, true, true, false, true);
        $newStatus = new ServerStatus($this->serverMock, true, true, false, null);

        foreach ([ServerCurrentStatus::class, ServerLastDownStatus::class] as $type) {
            $this->expectRepositoryFind($type, null);
        }

        $this->expectEntityManagerPersistType(ServerCurrentStatus::class);
        $this->expectEntityManagerNoPersistType(ServerLastDownStatus::class);

        $this->createManager()->handleStatusChange($this->serverMock, $oldStatus, $newStatus);
    }

    public function testBeingDownDoesNotUpdateWentDownAtButUpdatesReason(): void
    {
        $oldStatus = new ServerStatus($this->serverMock, false, false, false, true);
        $newStatus = new ServerStatus($this->serverMock, false, false, false, null);

        $typeMocks = [
            ServerCurrentStatus::class => Mockery::mock(ServerCurrentStatus::class),
            ServerLastDownStatus::class => Mockery::mock(ServerLastDownStatus::class),
        ];

        $typeMocks[ServerLastDownStatus::class]->shouldReceive('getWentDownAt')
            ->andReturn(Mockery::mock(DateTimeImmutable::class));

        foreach ([ServerCurrentStatus::class, ServerLastDownStatus::class] as $type) {
            $this->expectRepositoryFind($type, $typeMocks[$type]);
            $this->expectEntityManagerNoPersistType($type);
        }

        $typeMocks[ServerCurrentStatus::class]->shouldReceive('update')
            ->with(
                $this->nowMock,
                false,
                ServerCurrentStatus::REASON_AUTO,
            );
        $typeMocks[ServerLastDownStatus::class]->shouldReceive('update')
            ->with(
                $typeMocks[ServerLastDownStatus::class]->getWentDownAt(),
                ServerLastDownStatus::REASON_AUTO,
            );

        $this->createManager()->handleStatusChange($this->serverMock, $oldStatus, $newStatus);
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->entityManagerMock = Mockery::mock(EntityManagerInterface::class);
        $this->currentStatusRepositoryMock = Mockery::mock(ServerCurrentStatusRepository::class);
        $this->lastDownStatusRepositoryMock = Mockery::mock(ServerLastDownStatusRepository::class);
        $this->serverMock = Mockery::mock(Server::class);

        $this->entityManagerMock->shouldReceive('beginTransaction');
        $this->entityManagerMock->shouldReceive('commit');
        $this->entityManagerMock->shouldReceive('rollback');
        $this->entityManagerMock->shouldReceive('flush');

        $this->repositoryTypeMap = [
            ServerCurrentStatus::class => $this->currentStatusRepositoryMock,
            ServerLastDownStatus::class => $this->lastDownStatusRepositoryMock,
        ];

        $this->nowMock = Mockery::on(static fn ($arg) => $arg instanceof DateTimeImmutable);
    }

    private function expectRepositoryFind(string $type, object|null $returnValue): void
    {
        $this->repositoryTypeMap[$type]->shouldReceive('find')
            ->with($this->serverMock, LockMode::PESSIMISTIC_WRITE)
            ->andReturn($returnValue);
    }

    private function expectEntityManagerPersistType(string $type, int $times = 1): void
    {
        $this->entityManagerMock->shouldReceive('persist')
            ->with(
                Mockery::on(
                    static fn ($arg) => $arg instanceof $type,
                ),
            )
            ->times($times);
    }

    private function expectEntityManagerNoPersistType(string $type): void
    {
        $this->expectEntityManagerPersistType($type, 0);
    }

    private function createManager(): ServerStatusManager
    {
        return new ServerStatusManager(
            $this->entityManagerMock,
            $this->currentStatusRepositoryMock,
            $this->lastDownStatusRepositoryMock,
        );
    }
}
