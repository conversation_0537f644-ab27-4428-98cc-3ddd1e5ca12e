<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Service\Legacy\Edit\Constraints;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Service\Legacy\Edit\Constraints\UniqueCname;
use Cdn77\NxgApi\Service\Legacy\Edit\Constraints\UniqueCnameValidator;
use Mockery;
use Mockery\Adapter\Phpunit\MockeryPHPUnitIntegration;
use Mockery\MockInterface;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;

use function array_intersect;
use function array_map;
use function array_slice;

class UniqueCnamesValidatorTest extends ConstraintValidatorTestCase
{
    use MockeryPHPUnitIntegration;

    /** @var MockInterface|CdnResourceRepository */
    private $repositoryMock;

    /**
     * @param string[] $resourceCnames
     * @param string[] $existingCnames
     *
     * @dataProvider validCombinationsProvider
     */
    public function testValidCombinations(array $resourceCnames, array $existingCnames): void
    {
        $resource = $this->createResourceMock($resourceCnames);
        $this->prepareRepositoryMock($resource, $existingCnames);

        $this->validator->validate($resource, new UniqueCname());

        self::assertNoViolation();
    }

    /**
     * @param string[] $resourceCnames
     * @param string[] $existingCnames
     * @param string[] $invalidCnames
     *
     * @dataProvider invalidCombinationsProvider
     */
    public function testInvalidCombinations(array $resourceCnames, array $existingCnames, array $invalidCnames): void
    {
        $resource = $this->createResourceMock($resourceCnames);
        $this->prepareRepositoryMock($resource, $existingCnames);

        $constraint = new UniqueCname();
        $this->validator->validate($resource, $constraint);

        $violations = $this->buildViolation($constraint->message)
            ->setParameter('%string%', $invalidCnames[0]);

        foreach (array_slice($invalidCnames, 1) as $invalidCname) {
            $violations = $violations->buildNextViolation($constraint->message)
                ->setParameter('%string%', $invalidCname);
        }

        $violations->assertRaised();
    }

    /** @return string[][][] */
    public function validCombinationsProvider(): iterable
    {
        yield [['foo.bar'], ['baz.xyz']];
        yield [['1.foo', '2.bar'], ['3.baz', '4.xyz']];
    }

    /** @return string[][][] */
    public function invalidCombinationsProvider(): iterable
    {
        yield [['foo.bar'], ['foo.bar'], ['foo.bar']];
        yield [['1.foo', '2.bar'], ['2.bar', '3.baz', '4.xyz'], ['2.bar']];
        yield [['1.foo', '2.bar', '3.baz'], ['2.bar', '3.baz', '4.xyz'], ['2.bar', '3.baz']];
    }

    protected function setUp(): void
    {
        $this->repositoryMock = Mockery::mock(CdnResourceRepository::class);

        parent::setUp();
    }

    protected function createValidator(): UniqueCnameValidator
    {
        return new UniqueCnameValidator($this->repositoryMock);
    }

    /** @param string[] $existingCnames */
    private function prepareRepositoryMock(CdnResource $resource, array $existingCnames): void
    {
        $this->repositoryMock->shouldReceive('findUsedCnamesExcludingResource')
            ->once()
            ->with($resource->getCnames(), $resource)
            ->andReturn(
                array_intersect(
                    array_map('strtolower', $resource->getCnames()),
                    array_map('strtolower', $existingCnames),
                ),
            );
    }

    /** @param string[] $cnames */
    private function createResourceMock(array $cnames): CdnResource
    {
        $resourceMock = Mockery::mock(CdnResource::class);
        $resourceMock->shouldReceive('getCnames')
            ->atLeast()
            ->once()
            ->withNoArgs()
            ->andReturn($cnames);

        return $resourceMock;
    }
}
