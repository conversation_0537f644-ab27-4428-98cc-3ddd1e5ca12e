<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Service\Legacy\Certificate;

use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateMetadata;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;
use Mockery;

class CertificateMetadataTest extends TestCase
{
    public function testWithAllValuesSet(): void
    {
        $validSinceMock = Mockery::mock(DateTimeImmutable::class);
        $validUntilMock = Mockery::mock(DateTimeImmutable::class);

        $metadata = new CertificateMetadata(
            '1a2b3',
            $validSinceMock,
            $validUntilMock,
            'test.foo',
            ['bar.foo'],
        );

        self::assertSame('1a2b3', $metadata->getSerial());
        self::assertSame($validSinceMock, $metadata->getValidSince());
        self::assertSame($validUntilMock, $metadata->getValidUntil());
        self::assertSame('test.foo', $metadata->getCommonName());
        self::assertSame(['bar.foo'], $metadata->getAlternativeNames());
    }

    public function testWithNullableValues(): void
    {
        $validSinceMock = Mockery::mock(DateTimeImmutable::class);
        $validUntilMock = Mockery::mock(DateTimeImmutable::class);

        $metadata = new CertificateMetadata(
            '1a2b3',
            $validSinceMock,
            $validUntilMock,
            null,
            [],
        );

        self::assertSame('1a2b3', $metadata->getSerial());
        self::assertSame($validSinceMock, $metadata->getValidSince());
        self::assertSame($validUntilMock, $metadata->getValidUntil());
        self::assertNull($metadata->getCommonName());
        self::assertSame([], $metadata->getAlternativeNames());
    }
}
