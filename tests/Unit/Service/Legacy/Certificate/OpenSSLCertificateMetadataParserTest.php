<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Service\Legacy\Certificate;

use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Service\Legacy\Certificate\OpenSSLCertificateMetadataParser;
use Cdn77\NxgApi\Tests\Generators\CertificateDefinitions;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;

class OpenSSLCertificateMetadataParserTest extends TestCase
{
    use CertificateDefinitions;

    /**
     * @param string[] $domains
     *
     * @dataProvider certificatesProvider
     */
    public function testWithMultipleCertificateConfigurations(
        CertificatePair $certificatePair,
        array $domains,
        DateTimeImmutable $expiration,
    ): void {
        $metadata = (new OpenSSLCertificateMetadataParser())->getMetadata($certificatePair);

        self::assertSame($domains, $metadata->getAlternativeNames() ?: (array) $metadata->getCommonName());
        self::assertSame($expiration->getTimestamp(), $metadata->getValidUntil()->getTimestamp());
    }

    /** @return mixed[][] */
    public function certificatesProvider(): iterable
    {
        yield $this->getCertificateWithCommonNameAlternativeNames();
        yield $this->getCertificateWithCommonNameAndOneDifferentAlternativeName();
        yield $this->getCertificateWithCommonNameAndMultipleDifferentAternativeNames();
        yield $this->getCertificateWithCommonNameAndMatchingAlternativeNames();
        yield $this->getCertificateWithCommonNameAndMultipleAndNonDnsAlternativeNames();
    }
}
