<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Service\Legacy\Certificate;

use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Resource\Application\Payload\Ssl\CertificateStatusSchema;
use Cdn77\NxgApi\Resource\Domain\Enum\CertificateStatus;
use Cdn77\NxgApi\Resource\Domain\Enum\CertificateStatusDescription;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateActualStatus;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use DateTimeImmutable;
use Mockery;

class CertificateStatusTest extends TestCase
{
    public function testWithAllValuesSet(): void
    {
        $lastChangeMock = Mockery::mock(DateTimeImmutable::class);
        $active = CertificateStatus::active();

        $status = new CertificateStatusSchema(
            $active,
            CertificateStatusDescription::ACTIVE,
            $lastChangeMock,
            null,
            null,
        );

        self::assertSame(CertificateStatus::ACTIVE, $status->status);
        self::assertSame(CertificateStatusDescription::ACTIVE, $status->statusDescription);
        self::assertSame($lastChangeMock, $status->lastChangedAt);
        self::assertNull($status->actual);
        self::assertNull($status->requested);
    }

    public function testWIthNullableValues(): void
    {
        $lastChangedAt = Mockery::mock(DateTimeImmutable::class);
        $sslFile = Mockery::mock(SslFile::class);
        $status = new CertificateActualStatus($lastChangedAt, $sslFile);

        self::assertSame($lastChangedAt, $status->lastChangeAt);
        self::assertSame($sslFile, $status->activeFile);
    }
}
