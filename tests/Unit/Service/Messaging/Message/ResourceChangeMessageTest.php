<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Service\Messaging\Message;

use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Service\Messaging\Message\ResourceChangeMessage;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class ResourceChangeMessageTest extends TestCase
{
    public function testCorrect(): void
    {
        $message = new ResourceChangeMessage(
            1234567890,
            ResourceChangeMessage::TYPE_CREATED,
            '2017-01-01T00:00:00Z',
        );

        self::assertSame(1234567890, $message->getId());
        self::assertSame(ResourceChangeMessage::TYPE_CREATED, $message->getType());
        self::assertSame('2017-01-01T00:00:00Z', $message->getDate());
    }

    public function testInvalidType(): void
    {
        $this->expectExceptionMessage('Invalid resource change type supplied.');
        $this->expectException(InvalidArgument::class);
        new ResourceChangeMessage(1234567890, 'blah', '2017-01-01T00:00:00Z');
    }
}
