<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\HotlinkProtection\Domain;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtectionAddress;
use Cdn77\NxgApi\HotlinkProtection\Application\Payload\HotlinkProtectionInfo;
use Cdn77\NxgApi\HotlinkProtection\Domain\SetupHotlinkProtection;
use Cdn77\NxgApi\HotlinkProtection\Domain\Value\HotlinkProtectionType;
use Cdn77\NxgApi\Schema\Legacy\Resources\HotlinkProtection\RefererAddressDetail;
use Cdn77\NxgApi\Service\Legacy\HotlinkProtection\Exception\RefererNotFound;
use Cdn77\NxgApi\Service\Legacy\HotlinkProtection\RefererManager;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Cdn77\TestUtils\Stub;
use Doctrine\Common\Collections\ArrayCollection;
use Mockery;

final class SetupHotlinkProtectionTest extends TestCase
{
    public function testEnable(): void
    {
        $cdnResource = Stub::create(CdnResource::class);
        $hotlinkProtectionInfo = new HotlinkProtectionInfo(
            HotlinkProtectionType::TYPE_WHITELIST,
            true,
            [new RefererAddressDetail('domain.co')],
        );

        $hotlinkProtectionManager = Mockery::mock(RefererManager::class);

        $hotlinkProtectionManager
            ->shouldReceive('getReferer')
            ->once()
            ->with($cdnResource)
            ->andThrow(RefererNotFound::class);
        $hotlinkProtectionManager
            ->shouldReceive('enable')
            ->with($cdnResource, HotlinkProtectionType::TYPE_WHITELIST, true, ['domain.co'])
            ->once();

        $setupHotlinkProtection = new SetupHotlinkProtection($hotlinkProtectionManager);

        $setupHotlinkProtection->setup($cdnResource, $hotlinkProtectionInfo);
    }

    public function testDisable(): void
    {
        $cdnResource = Stub::create(CdnResource::class);
        $resourceHotlinkProtection = Stub::create(ResourceRefererProtection::class);
        $hotlinkProtectionInfo = new HotlinkProtectionInfo(
            HotlinkProtectionType::TYPE_DISABLED,
            false,
            [],
        );

        $hotlinkProtectionManager = Mockery::mock(RefererManager::class);

        $hotlinkProtectionManager
            ->shouldReceive('getReferer')
            ->once()
            ->with($cdnResource)
            ->andReturn($resourceHotlinkProtection);
        $hotlinkProtectionManager
            ->shouldReceive('disable')
            ->once();

        $setupHotlinkProtection = new SetupHotlinkProtection($hotlinkProtectionManager);

        $setupHotlinkProtection->setup($cdnResource, $hotlinkProtectionInfo);
    }

    public function testUpdateDisabled(): void
    {
        $cdnResource = Stub::create(CdnResource::class);
        $resourceHotlinkProtection = Stub::create(ResourceRefererProtection::class, [
            'type' => HotlinkProtectionType::TYPE_WHITELIST,
            'denyEmpty' => true,
        ]);
        $hotlinkProtectionInfo = new HotlinkProtectionInfo(
            HotlinkProtectionType::TYPE_DISABLED,
            true,
            [],
        );

        $hotlinkProtectionManager = Mockery::mock(RefererManager::class);

        $hotlinkProtectionManager
            ->shouldReceive('getReferer')
            ->once()
            ->with($cdnResource)
            ->andReturn($resourceHotlinkProtection);
        $hotlinkProtectionManager
            ->shouldReceive('update')
            ->with($cdnResource, HotlinkProtectionType::TYPE_DISABLED, true, [])
            ->once();

        $setupHotlinkProtection = new SetupHotlinkProtection($hotlinkProtectionManager);

        $setupHotlinkProtection->setup($cdnResource, $hotlinkProtectionInfo);
    }

    public function testUpdate(): void
    {
        $cdnResource = Stub::create(CdnResource::class);
        $resourceHotlinkProtection = Stub::create(ResourceRefererProtection::class);
        $hotlinkProtectionInfo = new HotlinkProtectionInfo(
            HotlinkProtectionType::TYPE_WHITELIST,
            false,
            [new RefererAddressDetail('domain.co')],
        );

        $hotlinkProtectionManager = Mockery::mock(RefererManager::class);

        $hotlinkProtectionManager
            ->shouldReceive('getReferer')
            ->once()
            ->with($cdnResource)
            ->andReturn($resourceHotlinkProtection);
        $hotlinkProtectionManager
            ->shouldReceive('update')
            ->with($cdnResource, HotlinkProtectionType::TYPE_WHITELIST, false, ['domain.co'])
            ->once();

        $setupHotlinkProtection = new SetupHotlinkProtection($hotlinkProtectionManager);

        $setupHotlinkProtection->setup($cdnResource, $hotlinkProtectionInfo);
    }

    public function testEnableDisabledDenyEmptyOnly(): void
    {
        $cdnResource = Stub::create(CdnResource::class);
        $hotlinkProtectionInfo = new HotlinkProtectionInfo(
            HotlinkProtectionType::TYPE_DISABLED,
            true,
            [],
        );

        $hotlinkProtectionManager = Mockery::mock(RefererManager::class);

        $hotlinkProtectionManager
            ->shouldReceive('getReferer')
            ->once()
            ->with($cdnResource)
            ->andThrow(RefererNotFound::class);
        $hotlinkProtectionManager
            ->shouldReceive('enable')
            ->with($cdnResource, HotlinkProtectionType::TYPE_DISABLED, true, [])
            ->once();

        $setupHotlinkProtection = new SetupHotlinkProtection($hotlinkProtectionManager);

        $setupHotlinkProtection->setup($cdnResource, $hotlinkProtectionInfo);
    }

    public function testDisableEmptyReferer(): void
    {
        $currentBlacklistedDomain = 'domain.co';

        $cdnResource = Stub::create(CdnResource::class);
        $resourceHotlinkProtection = Stub::create(ResourceRefererProtection::class, [
            ResourceRefererProtection::FIELD_ADDRESSES => new ArrayCollection([
                Stub::create(
                    ResourceRefererProtectionAddress::class,
                    [ResourceRefererProtectionAddress::FIELD_DOMAIN => $currentBlacklistedDomain],
                ),
            ]),
            ResourceRefererProtection::FIELD_DENY_EMPTY => true,
            ResourceRefererProtection::FIELD_TYPE => HotlinkProtectionType::TYPE_BLACKLIST,
        ]);
        $hotlinkProtectionInfo = new HotlinkProtectionInfo(
            HotlinkProtectionType::TYPE_BLACKLIST,
            false,
            [new RefererAddressDetail($currentBlacklistedDomain)],
        );

        $hotlinkProtectionManager = Mockery::mock(RefererManager::class);

        $hotlinkProtectionManager
            ->shouldReceive('getReferer')
            ->once()
            ->with($cdnResource)
            ->andReturn($resourceHotlinkProtection);
        $hotlinkProtectionManager
            ->shouldReceive('update')
            ->with($cdnResource, HotlinkProtectionType::TYPE_BLACKLIST, false, [$currentBlacklistedDomain])
            ->once()
            ->andReturn($resourceHotlinkProtection);

        $setupHotlinkProtection = new SetupHotlinkProtection($hotlinkProtectionManager);

        $setupHotlinkProtection->setup($cdnResource, $hotlinkProtectionInfo);
    }
}
