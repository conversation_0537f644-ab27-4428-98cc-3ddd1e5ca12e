<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\DTO\NxgConfGen;

use Cdn77\NxgApi\DTO\NgxConfGen\ServerDTO;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class ServerDTOTest extends TestCase
{
    public function testInterfaceWorks(): void
    {
        $dto = new ServerDTO(
            'foo',
            '*******',
            123456789123456,
            123456789,
            9876543210987,
            false,
            5,
            2,
            true,
        );

        self::assertSame('foo', $dto->getLocationId());
        self::assertSame('*******', $dto->getIp());
        self::assertSame(123456789123456, $dto->getMaxCacheSize());
        self::assertSame(123456789, $dto->getKeysSize());
        self::assertSame(9876543210987, $dto->getUid());
        self::assertFalse($dto->hasBackup());
        self::assertSame(5, $dto->getWorkerCount());
        self::assertSame(2, $dto->getDriveCount());
        self::assertTrue($dto->hasHttp2());
    }
}
