<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\DTO\NxgConfGen;

use Cdn77\NxgApi\NgxConfGen\Domain\Value\ResourceConfiguration;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class ResourceDTOTest extends TestCase
{
    /**
     * @param mixed ...$args
     *
     * @dataProvider valuesProvider
     */
    public function testInterfaceWorks(...$args): void
    {
        $dto = new ResourceConfiguration(...$args);

        self::assertSame($args[$i = 0], $dto->id);
        self::assertSame($args[++$i], $dto->accountId);
        self::assertSame($args[++$i], $dto->cdnUrl);
        self::assertSame($args[++$i], $dto->groupId);
        self::assertSame($args[++$i], $dto->cnames);
        self::assertSame($args[++$i], $dto->suspended);
        self::assertSame($args[++$i], $dto->disableQueryString);
        self::assertSame($args[++$i], $dto->ignoreSetCookie);
        self::assertSame($args[++$i], $dto->mp4PseudoStreaming);
        self::assertSame($args[++$i], $dto->cacheExpiry);
        self::assertSame($args[++$i], $dto->cacheExpiry404);
        self::assertSame($args[++$i], $dto->cacheBypass);
        self::assertSame($args[++$i], $dto->purgeAllKey);
        self::assertSame($args[++$i], $dto->sslFileIndex);
        self::assertSame($args[++$i], $dto->fullLogs);
        self::assertSame($args[++$i], $dto->ignoredQueryParams);
        self::assertSame($args[++$i], $dto->httpsRedirectCode);
        self::assertSame($args[++$i], $dto->refererDenyEmpty);
        self::assertSame($args[++$i], $dto->refererType);
        self::assertSame($args[++$i], $dto->refererDomains);
        self::assertSame($args[++$i], $dto->ipProtectionType);
        self::assertSame($args[++$i], $dto->ipProtectionAddresses);
        self::assertSame($args[++$i], $dto->geoProtectionType);
        self::assertSame($args[++$i], $dto->geoProtectionCountries);
        self::assertSame($args[++$i], $dto->cacheMinUses);
        self::assertSame($args[++$i], $dto->streamingPlaylistBypass);
        self::assertSame($args[++$i], $dto->cacheLockAge);
        self::assertSame($args[++$i], $dto->cacheLockTimeout);
        self::assertSame($args[++$i], $dto->cacheContentLengthLimit);
        self::assertSame($args[++$i], $dto->cacheNoContentLengthLimit);
        self::assertSame($args[++$i], $dto->upstreamFailTimeout);
        self::assertSame($args[++$i], $dto->upstreamNextAttempts);
        self::assertSame($args[++$i], $dto->customData);
        self::assertSame($args[++$i], $dto->quic);
        self::assertSame($args[++$i], $dto->waf);
        self::assertSame($args[++$i], $dto->corsOriginHeader);
        self::assertSame($args[++$i], $dto->corsTimingEnabled);
        self::assertSame($args[++$i], $dto->corsWildcardEnabled);
        self::assertSame($args[++$i], $dto->rateLimit);
        self::assertSame($args[++$i], $dto->contentDispositionByParam);
        self::assertSame($args[++$i], $dto->secureTokenType);
        self::assertSame($args[++$i], $dto->secureTokenValue);
        self::assertSame($args[++$i], $dto->secureLinkExpiryParam);
        self::assertSame($args[++$i], $dto->secureLinkTokenParam);
        self::assertSame($args[++$i], $dto->secureLinkPathlenParam);
        self::assertSame($args[++$i], $dto->secureLinkSecretParam);
        self::assertSame($args[++$i], $dto->secureLinkRewritePlaylist);
    }

    /** @return mixed[][] */
    public function valuesProvider(): iterable
    {
        yield [
            12345,
            123,
            '12345.rsc.cdn77.org',
            123,
            ['123.foo', '456.bar'],
            null,
            true,
            false,
            false,
            123,
            123,
            false,
            123,
            2,
            false,
            null,
            302,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            false,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            false,
            false,
            false,
            false,
            false,
            true,
            true,
            'highwinds',
            'secureTokenValue',
            'secureLinkExpiryParam',
            'secureLinkTokenParam',
            'secureLinkPathlenParam',
            'secureLinkSecretParam',
            true,
            [],
            [],
        ];

        yield [
            12345,
            123,
            '12345.rsc.cdn77.org',
            123,
            ['123.foo', '456.bar'],
            null,
            true,
            false,
            false,
            123,
            123,
            true,
            123,
            2,
            false,
            ['foo'],
            302,
            true,
            'blacklist',
            ['foo.bar'],
            'whitelist',
            ['1.2.3.4/32', '2.0.0.0/8'],
            'blacklist',
            ['US', 'UK'],
            5,
            true,
            12,
            34,
            56,
            78,
            10,
            2,
            ['boolean' => true, 'string' => 'text', 'integer' => 666, 'float' => 88.8],
            true,
            true,
            true,
            true,
            true,
            false,
            false,
            null,
            null,
            null,
            null,
            null,
            null,
            false,
            [
                [
                    'priority' => 1,
                    'host' => 'foo.bar',
                    'scheme' => 'https',
                    'basedir' => 'foo/bar',
                    'port' => 123,
                    'timeout' => 456,
                    's3_access_key_id' => null,
                    's3_secret' => null,
                    's3_region' => null,
                    's3_bucket_name' => null,
                    's3_type' => null,
                    'forward_host_header' => true,
                    'ssl_verify_disable' => false,
                    'origin_headers' => ['foo' => 'bar'],
                    'follow_redirect_origin' => true,
                    'follow_redirect_codes' => [301, 302],
                ],
            ],
            ['Access-Control-Allow-Origin' => 'some.value'],
        ];
    }
}
