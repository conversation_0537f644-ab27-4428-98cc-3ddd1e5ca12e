<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\DTO\Legacy;

use Cdn77\NxgApi\DTO\Legacy\ServerUpIpListDTO;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class ServerUpIpListDTOTest extends TestCase
{
    public function testInterfaceWorks(): void
    {
        $dto = new ServerUpIpListDTO(
            123,
            456,
            true,
            false,
            true,
        );

        self::assertSame(123, $dto->getLocationId());
        self::assertSame(456, $dto->getServerId());
        self::assertTrue($dto->isInDns());
        self::assertFalse($dto->isOk());
        self::assertTrue($dto->isUp());
    }
}
