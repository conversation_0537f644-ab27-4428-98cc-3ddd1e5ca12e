<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\DTO\Legacy;

use Cdn77\NxgApi\DTO\Legacy\IpServerStatusDTO;
use Cdn77\NxgApi\Entity\Legacy\Ip;
use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class IpServerStatusDTOTest extends TestCase
{
    public function testValues(): void
    {
        $ipMock = Mockery::mock(Ip::class);
        $serverMock = Mockery::mock(Server::class);
        $ipMock->shouldReceive('getServer')->andReturn($serverMock);

        $dto = new IpServerStatusDTO($ipMock, true, true, false, true, null);
        self::assertTrue($dto->isUp());
        self::assertTrue($dto->isPaused());
        self::assertFalse($dto->isForced());
        self::assertTrue($dto->isAutoUp());
        self::assertNull($dto->getForcedUp());
    }
}
