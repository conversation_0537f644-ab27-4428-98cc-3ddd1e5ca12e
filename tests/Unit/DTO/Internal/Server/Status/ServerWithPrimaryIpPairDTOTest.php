<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\DTO\Internal\Server\Status;

use Cdn77\NxgApi\DTO\Internal\Server\ServerWithPrimaryIpPairDTO;
use Cdn77\NxgApi\Entity\Legacy\Ip;
use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Tests\Unit\TestCase;
use Mockery;

class ServerWithPrimaryIpPairDTOTest extends TestCase
{
    public function test(): void
    {
        $serverMock = Mockery::mock(Server::class);
        $ipMock = Mockery::mock(Ip::class);

        $dto = new ServerWithPrimaryIpPairDTO($serverMock, $ipMock);

        self::assertSame($serverMock, $dto->getServer());
        self::assertSame($ipMock, $dto->getPrimaryIp());
    }
}
