<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\DTO\Internal\Server\Certificates;

use Cdn77\NxgApi\DTO\Internal\Server\ServerCertificateStatusDTO;
use Cdn77\NxgApi\Tests\Unit\TestCase;

class ServerCertificateStatusDTOTest extends TestCase
{
    public function test(): void
    {
        $dto = new ServerCertificateStatusDTO(123456789, 2, true);

        self::assertSame(123456789, $dto->getResourceId());
        self::assertSame(2, $dto->getFileIndex());
        self::assertTrue($dto->isReady());
    }
}
