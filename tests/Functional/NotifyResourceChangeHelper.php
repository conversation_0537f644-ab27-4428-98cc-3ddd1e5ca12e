<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional;

use Cdn77\NxgApi\Service\Messaging\Message\ResourceChangeMessage;

trait NotifyResourceChangeHelper
{
    /** @var string|null */
    private $resourceChangeQueueName;

    /** @param mixed[][] $messages array of tuples of [resourceId, type] */
    public function assertMultipleMessagesInQueue(array $messages): void
    {
        return;

        /* @phpstan-ignore-next-line */
        foreach ($messages as [$resourceId, $type]) {
            self::assertMessageInQueue($resourceId, $type);
        }

        self::assertNoMessageInQueue();
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->setUpNotifyResourceChangeHelper();
    }

    private function setUpNotifyResourceChangeHelper(): void
    {
        return;

        /* @phpstan-ignore-next-line */
        $this->resourceChangeQueueName = $this->createAnonymousQueueForResourceChange();
    }

    private function assertNoMessageInQueue(): void
    {
        return;

        /* @phpstan-ignore-next-line */
        self::assertNull($this->getMessageFromResourceChangeQueue(), 'Expected no change message in queue.');
    }

    private function assertMessageInQueue(int $resourceId, string $type): void
    {
        return;

        /* @phpstan-ignore-next-line */
        $message = $this->getMessageFromResourceChangeQueue();

        self::assertNotNull($message, 'Expected change message in queue, none found.');
        self::assertSame($resourceId, $message->getId());
        self::assertSame($type, $message->getType());
    }

    private function assertSingleMessageInQueue(int $resourceId, string $type): void
    {
        return;

        /* @phpstan-ignore-next-line */
        self::assertMessageInQueue($resourceId, $type);
        self::assertNoMessageInQueue();
    }

    private function assertCreatingMessageInQueue(int $resourceId): void
    {
        return;

        /* @phpstan-ignore-next-line */
        self::assertMessageInQueue($resourceId, ResourceChangeMessage::TYPE_CREATED);
    }

    private function assertUpdatingMessageInQueue(int $resourceId): void
    {
        return;

        /* @phpstan-ignore-next-line */
        self::assertMessageInQueue($resourceId, ResourceChangeMessage::TYPE_UPDATED);
    }

    private function assertDeletingMessageInQueue(int $resourceId): void
    {
        self::assertMessageInQueue($resourceId, ResourceChangeMessage::TYPE_DELETED);
    }

    private function assertRestoringMessageInQueue(int $resourceId): void
    {
        self::assertMessageInQueue($resourceId, ResourceChangeMessage::TYPE_RESTORED);
    }

    private function assertSingleCreatingMessageInQueue(int $resourceId): void
    {
        return;

        /* @phpstan-ignore-next-line */
        self::assertSingleMessageInQueue($resourceId, ResourceChangeMessage::TYPE_CREATED);
    }

    private function assertSingleUpdatingMessageInQueue(int $resourceId): void
    {
        return;

        /* @phpstan-ignore-next-line */
        self::assertSingleMessageInQueue($resourceId, ResourceChangeMessage::TYPE_UPDATED);
    }

    private function assertSingleDeletingMessageInQueue(int $resourceId): void
    {
        return;

        /* @phpstan-ignore-next-line */
        self::assertSingleMessageInQueue($resourceId, ResourceChangeMessage::TYPE_DELETED);
    }

    private function assertSingleRestoringMessageInQueue(int $resourceId): void
    {
        return;

        /* @phpstan-ignore-next-line */
        self::assertSingleMessageInQueue($resourceId, ResourceChangeMessage::TYPE_RESTORED);
    }
}
