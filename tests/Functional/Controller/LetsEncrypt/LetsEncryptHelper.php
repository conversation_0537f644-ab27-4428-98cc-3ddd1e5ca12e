<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\LetsEncrypt;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\LetsEncrypt\Request as LetsEncryptRequest;
use Cdn77\NxgApi\Entity\LetsEncrypt\RequestState;
use Cdn77\NxgApi\Entity\LetsEncrypt\Result;
use Cdn77\NxgApi\Entity\LetsEncrypt\ResultStatus;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\LetsEncrypt\Domain\Value\RequestStateReason;
use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request as HttpRequest;
use Symfony\Component\HttpFoundation\Response;

use function array_merge;
use function base64_encode;
use function Safe\json_encode;

trait LetsEncryptHelper
{
    /** @return mixed[] */
    private function prepareData(
        EntityManagerInterface $entityManager,
        DateTimeImmutable $requestCreatedAt = new DateTimeImmutable('- 10 minutes'),
    ): array {
        $resource = $this->createTemporaryResource();
        $request = $this->prepareRequest(
            $entityManager,
            $resource,
            RequestState::getPending(),
            createdAt: $requestCreatedAt,
        );
        $this->prepareTask($entityManager, $request);

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();

        return [$resource, $request, $certificatePair];
    }

    private function prepareRequest(
        EntityManagerInterface $entityManager,
        CdnResource $resource,
        RequestState $requestState,
        RequestStateReason|null $requestStateReason = null,
        DatetimeImmutable $createdAt = new DateTimeImmutable('- 10 minutes'),
    ): LetsEncryptRequest {
        $request = new LetsEncryptRequest(
            $resource,
            array_merge([$resource->getCdnUrl()], $resource->getCnames()),
            $createdAt,
            $requestState,
            $requestStateReason,
        );

        $entityManager->persist($request);
        $entityManager->flush();

        return $request;
    }

    private function prepareTask(EntityManagerInterface $entityManager, LetsEncryptRequest $request): Task
    {
        $task = new Task($request, new DateTimeImmutable('- 10 minutes'), new DateTimeImmutable('- 10 minutes'));

        $entityManager->persist($task);
        $entityManager->flush();

        return $task;
    }

    private function prepareResult(
        EntityManagerInterface $entityManager,
        LetsEncryptRequest $request,
        DateTimeImmutable $completedAt,
        ResultStatus $resultStatus,
    ): void {
        $result = new Result($request, $completedAt, $completedAt, $completedAt, $resultStatus, null);

        $entityManager->persist($result);
        $entityManager->flush();
    }

    private function makeRequest(
        string $requestId,
        string|null $certificate = '',
        string|null $privateKey = '',
        string|null $validationError = null,
        string|null $error = null,
    ): Response {
        $this->client->request(
            HttpRequest::METHOD_POST,
            '/letsencrypt/result',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                'request_id' => $requestId,
                'certificate' => $certificate === null ? null : base64_encode($certificate),
                'private_key' => $privateKey === null ? null : base64_encode($privateKey),
                'validation_error' => $validationError,
                'error' => $error,
            ]),
        );

        return $this->client->getResponse();
    }

    /** @return array<LetsEncryptRequest> */
    private function findAllPendingCertificateRequests(EntityManagerInterface $entityManager): array
    {
        return $entityManager->createQueryBuilder()
            ->from(LetsEncryptRequest::class, 'r')
            ->select('r')
            ->where('r.state = :pending')
            ->setParameter('pending', RequestState::PENDING)
            ->orderBy('r.createdAt')
            ->getQuery()
            ->getResult();
    }
}
