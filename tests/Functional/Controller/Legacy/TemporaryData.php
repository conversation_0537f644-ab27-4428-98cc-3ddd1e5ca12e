<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\Legacy;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Entity\Legacy\Account;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\CustomLocation;
use Cdn77\NxgApi\Entity\Legacy\IgnoredQueryParam;
use Cdn77\NxgApi\Entity\Legacy\Ip;
use Cdn77\NxgApi\Entity\Legacy\Location;
use Cdn77\NxgApi\Entity\Legacy\LocationGroup;
use Cdn77\NxgApi\Entity\Legacy\Pop;
use Cdn77\NxgApi\Entity\Legacy\ResourceFullLog;
use Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtectionLocation;
use Cdn77\NxgApi\Entity\Legacy\ResourceIpProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceIpProtectionAddress;
use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;
use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtectionAddress;
use Cdn77\NxgApi\Entity\Legacy\SecureToken;
use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Entity\Legacy\ServerCurrentStatus;
use Cdn77\NxgApi\Entity\Legacy\ServerHttp2;
use Cdn77\NxgApi\Entity\Legacy\ServerLastDownStatus;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Entity\LetsEncrypt\Request;
use Cdn77\NxgApi\Entity\LetsEncrypt\RequestState;
use Cdn77\NxgApi\Entity\LetsEncrypt\Result;
use Cdn77\NxgApi\Entity\LetsEncrypt\ResultStatus;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\Entity\PopRedirection\Redirection;
use Cdn77\NxgApi\Resource\Application\Payload\FollowRedirectSchema;
use Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeaderSchema;
use Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeadersSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Tests\Utils\DateFormatter;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use DateTimeZone;
use Doctrine\Bundle\DoctrineBundle\Registry;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpFoundation\Response;

use function array_filter;
use function array_key_exists;
use function array_map;
use function array_pop;
use function array_values;
use function count;
use function in_array;
use function is_array;
use function ksort;
use function openssl_x509_parse;
use function rand;
use function random_int;
use function round;
use function Safe\json_decode;
use function Safe\preg_split;
use function sprintf;
use function strncmp;
use function substr;

trait TemporaryData
{
    public function createAccount(int|null $id = null): Account
    {
        static $used = [];

        if ($id === null) {
            do {
                $id = random_int(10000, 99999);
            } while (isset($used[$id]));
        }

        $account = new Account($id);

        $this->getEntityManager()->persist($account);
        $this->getEntityManager()->flush();

        return $account;
    }

    public function createLocation(
        string $id,
        string $city,
        string $country,
        string $continent,
        string|null $region = null,
    ): Location {
        $location = new Location();
        $location->setId($id);
        $location->setCity($city);
        $location->setCountryCode($country);
        $location->setContinent($continent);
        $location->setRegionCode($region);
        $location->getCoordinate()->setLatitude(round(rand(200000, 600000) / 10000, 4));
        $location->getCoordinate()->setLongitude(round(rand(-500000, -100000) / 10000, 4));

        $this->getEntityManager()->persist($location);
        $this->getEntityManager()->flush();

        return $location;
    }

    public function createCustomLocation(LocationGroup $group, CdnResource $resource): CustomLocation
    {
        $customLocation = new CustomLocation();
        $customLocation->setGroup($group);
        $customLocation->setResource($resource);

        $this->getEntityManager()->persist($customLocation);
        $this->getEntityManager()->flush();

        return $customLocation;
    }

    public function createCustomLocationWithPops(
        LocationGroup $group,
        CdnResource $resource,
        Pop ...$pops,
    ): CustomLocation {
        $customLocation = $this->createCustomLocation($group, $resource);

        foreach ($pops as $pop) {
            $customLocation->getPops()->add($pop);
            $pop->getCustomLocations()->add($customLocation);
        }

        $this->getEntityManager()->flush();

        return $customLocation;
    }

    /** @param string[] $addresses */
    public function createResourceIpProtection(
        CdnResource $resource,
        string $type,
        array $addresses,
    ): ResourceIpProtection {
        $protection = new ResourceIpProtection($resource, $type);
        $this->getEntityManager()->persist($protection);

        foreach ($addresses as $address) {
            $protectionAddress = new ResourceIpProtectionAddress($protection, $address);
            $protection->getAddresses()->add($protectionAddress);
            $this->getEntityManager()->persist($protectionAddress);
        }

        $this->getEntityManager()->flush();

        return $protection;
    }

    /** @param string[] $locations */
    public function createResourceGeoProtection(
        CdnResource $resource,
        string $type,
        array $locations,
    ): ResourceGeoProtection {
        $protection = new ResourceGeoProtection($resource, $type);
        $this->getEntityManager()->persist($protection);

        foreach ($locations as $location) {
            $protectionLocation = new ResourceGeoProtectionLocation($protection, $location);
            $protection->getLocations()->add($protectionLocation);
            $this->getEntityManager()->persist($protectionLocation);
        }

        $this->getEntityManager()->flush();

        return $protection;
    }

    public function createPop(
        Location $location,
        bool $useIp = true,
        bool $backup = true,
        string|null $description = null,
        string|null $tag = null,
        bool $qat = false,
        bool $noDedup = false,
    ): Pop {
        $pop = new Pop();
        $pop->setLocation($location);
        $pop->setUseIp($useIp);
        $pop->setBackup($backup);
        $pop->setDescription($description);
        $pop->setTag($tag);
        $pop->setQat($qat);
        $pop->setNoDedup($noDedup);

        $this->getEntityManager()->persist($pop);
        $this->getEntityManager()->flush();

        return $pop;
    }

    protected function getEntityManager(): EntityManagerInterface
    {
        $doctrine = static::$kernel->getContainer()->get('doctrine');
        self::assertInstanceOf(Registry::class, $doctrine);

        $entityManager = $doctrine->getManager('mc_legacy');
        self::assertInstanceOf(EntityManagerInterface::class, $entityManager);

        return $entityManager;
    }

    /** @return CdnResource[] */
    private function createTemporaryResources(bool $withSameAccount = false): array
    {
        $resourceA = $this->createTemporaryResource();

        $extend = [];
        if ($withSameAccount) {
            $extend = ['account' => $resourceA->getAccount()];
        }

        $resourceB = $this->createTemporaryResource($extend);
        $this->createResourceIgnoredParameter($resourceB, 'foo');
        $this->createResourceIgnoredParameter($resourceB, 'Bar');

        $this->getEntityManager()->flush();

        return [
            $resourceA->getId() => $resourceA,
            $resourceB->getId() => $resourceB,
        ];
    }

    /**
     * @param array<string, mixed> $extend
     * @param array<string, mixed> $extendOrigin
     */
    private function createTemporaryResource(array $extend = [], array $extendOrigin = []): CdnResource
    {
        $account = $this->createAccount();
        $group = $this->createLocationGroup();

        return $this->createResource($account, $group, $extend, $extendOrigin);
    }

    private function createResourceWithTwoOrigins(): CdnResource
    {
        $resource = $this->createTemporaryResource();
        $originB = $this->createResourceOrigin($resource, 2);
        $resource->getOrigins()->add($originB);
        $this->getEntityManager()->refresh($resource);

        return $resource;
    }

    private function createResourceWithAccount(LocationGroup $group): CdnResource
    {
        $account = $this->createAccount();

        return $this->createResource($account, $group);
    }

    private function createLocationGroup(
        string $name = 'foo',
        bool $primary = true,
        bool $backup = true,
        int|null $id = null,
    ): LocationGroup {
        $group = new LocationGroup();

        $group->setName($name);
        $group->setPrimary($primary);
        $group->setAllowBackupPops($backup);

        $this->getEntityManager()->persist($group);
        $this->getEntityManager()->flush();

        if ($id !== null) {
            $group->setId($id);
            $this->getEntityManager()->persist($group);
            FlushAndClear::do($this->getEntityManager());
        }

        return $group;
    }

    private function createLocationGroupWithPops(
        string $name = 'foo',
        bool $primary = true,
        bool $backup = true,
        Pop ...$pops,
    ): LocationGroup {
        $group = $this->createLocationGroup($name, $primary, $backup);

        foreach ($pops as $pop) {
            $group->getPops()->add($pop);
            $pop->getGroups()->add($group);
        }

        $this->getEntityManager()->flush();

        return $group;
    }

    /**
     * @param array<string, mixed> $extend
     * @param array<string, mixed> $extendOrigin
     */
    private function createResource(
        Account $account,
        LocationGroup $group,
        array $extend = [],
        array $extendOrigin = [],
    ): CdnResource {
        $i = random_int(100000, 999999);
        $i2 = random_int(100000, 999999);
        $resource = new CdnResource(new DateTimeImmutable());
        $resource->setAccount($account);
        $resource->setCnames([$i . '-cname.test.com', $i2 . '-cname.test.com']);
        $resource->setGroup($group);
        $resource->setCaching($resource->getCaching()->withExpiry(123));
        $resource->setCustomData(['boolean' => true, 'string' => 'text', 'integer' => 666, 'float' => 88.8]);

        $extended = Stub::extend($resource, $extend);

        $this->getEntityManager()->persist($extended);

        $origin = $this->createResourceOrigin($extended, 1, $i . '-test.com', $extendOrigin);

        $extended->getOrigins()->add($origin);

        $this->getEntityManager()->persist($extended);
        $this->getEntityManager()->flush();

        return $extended;
    }

    /**
     * @param array<string, mixed> $extend
     * @param int<1, 100> $priority
     */
    private function createResourceOrigin(
        CdnResource $resource,
        int $priority = 1,
        string $host = 'host.com',
        array $extend = [],
    ): ResourceOrigin {
        $origin = new ResourceOrigin(Uuid::uuid4(), $resource, $priority, $host);

        $extended = Stub::extend($origin, $extend);

        $this->getEntityManager()->persist($extended);
        $this->getEntityManager()->flush();

        return $extended;
    }

    private function createServer(bool $paused, Pop|null $pop = null): Server
    {
        if ($pop === null) {
            $pop = $this->createPop($this->getLocation(), true, false);
        }

        $server = new Server();
        $server->setKeysSize(rand(10000, 999999));
        $server->setMaxBandwidth(rand(10000, 999999));
        $server->setUid(rand(1, 999999));
        $server->setPaused($paused);
        $server->setWorkerCount(rand(1, 10));
        $server->setDriveCount(rand(1, 5));
        $server->setPop($pop);

        $this->getEntityManager()->persist($server);
        $this->getEntityManager()->flush();

        return $server;
    }

    private function getLocation(): Location
    {
        $locations = $this->getEntityManager()->getRepository(Location::class)->findAll();

        return array_pop($locations) ?? $this->createLocation('fooPopLocId', 'foo', 'XY', 'Xy');
    }

    private function createIp(Server $server, bool $up, bool $primary = true): Ip
    {
        $ip = new Ip();
        $ip->setServer($server);
        $ip->setIp($this->generateRandomIpAddress());
        $ip->setUp($up);
        $ip->setPrimary($primary);

        $server->getIps()->add($ip);

        $this->getEntityManager()->persist($ip);
        $this->getEntityManager()->flush();

        return $ip;
    }

    private function generateRandomIpAddress(): string
    {
        static $allocated = [];

        do {
            $ip = rand(1, 254) . '.0.0.' . rand(2, 254);
        } while (isset($allocated[$ip]));

        $allocated[$ip] = true;

        return $ip;
    }

    private function createServerWithIp(bool $paused, bool $ipUp, bool $ipPrimary = true, Pop|null $pop = null): Server
    {
        $server = $this->createServer($paused, $pop);
        $this->createIp($server, $ipUp, $ipPrimary);

        return $server;
    }

    private function assignServerPop(Server $server, Pop $pop): void
    {
        $server->setPop($pop);
        $pop->getServers()->add($server);

        $this->getEntityManager()->flush();
    }

    private function enableServerHttp2(Server $server): ServerHttp2
    {
        $http2 = new ServerHttp2();
        $http2->setServer($server);

        $this->getEntityManager()->persist($http2);
        $this->getEntityManager()->flush();

        return $http2;
    }

    /**
     * @param CdnResource[] $resources
     *
     * @return mixed[][]
     */
    private function getValidResponseForList(array $resources): array
    {
        $validResourcesData = [];
        foreach ($resources as $resource) {
            $validResourcesData[] = $this->getValidDataFromResource($resource);
        }

        return $validResourcesData;
    }

    /** @return mixed[] */
    private function getValidResponseForDetail(CdnResource $resource): array
    {
        return [
            'cdn_resource' => $this->getValidDataFromResource($resource),
        ];
    }

    /** @return mixed[] */
    private function getValidDataFromResource(CdnResource $resource): array
    {
        $customData = $resource->getCustomData();
        if ($customData !== null) {
            ksort($customData);
        }

        $data = [
            'id' => $resource->getId(),
            'cdn_reference' => $resource->getId(),
            'account_id' => $resource->getAccount()->getId(),
            'cnames' => $resource->getCnames(),
            'created_at' => $resource->getCreated()->format('c'),
            'resource_type' => 'HTTP_PULL',
            'updated_at' => $resource->getUpdated()->format('c'),
            'origin_url' => $resource->getMainOrigin()->getHost(),
            'origin_scheme' => $resource->getMainOrigin()->getScheme(),
            'cdn_url' => $resource->getCdnUrl(),
            'group_id' => [$resource->getGroup()->getId()],
            'cache_expiry' => $resource->getCaching()->getExpiry(),
            'disable_query_string' => 0,
            'ignore_set_cookie' => 0,
            'mp4_pseudo_on' => 0,
            'instant_ssl' => (int) $resource->hasInstantSsl(),
            'ignored_query_params' => $resource->getIgnoredQueryParams()->map(
                static fn (IgnoredQueryParam $param) => $param->getName(),
            )->getValues(),
            'https_redirect_code' => $resource->getHttpsRedirectCode() ?? 0,
            'origin_basedir' => $resource->getMainOrigin()->getBasedir() === null ? ''
                : $resource->getMainOrigin()->getBasedir(),
            'forward_host_header' => (int) $resource->getMainOrigin()->hasForwardHostHeader(),
            'streaming_playlist_bypass' => (int) $resource->hasStreamingPlaylistBypass(),
            'custom_data' => $customData,
            'waf' => 0,
            'quic' => 0,
            'cors_origin_header' => 0,
            'cors_timing_enabled' => $resource->hasCorsTimingEnabled(),
            'cors_wildcard_enabled' => $resource->hasCorsWildcardEnabled(),
            'ssl_verify_disable' => $resource->getMainOrigin()->hasSslVerifyDisable(),
            'rate_limit' => $resource->hasRateLimit(),
            'content_disposition_by_param' => $resource->hasContentDispositionByParam(),
            'response_headers' => [],
            'follow_redirect' => [
                FollowRedirectSchema::FIELD_ENABLED => $resource->getMainOrigin()->hasFollowRedirectOrigin(),
            ],
        ];

        if ($resource->getCaching()->getExpiry404() !== null) {
            $data['cache_expiry_404'] = $resource->getCaching()->getExpiry404();
        }

        if ($resource->getSuspended() !== null) {
            $data['suspended_at'] = $resource->getSuspended()->format('c');
        }

        if ($resource->getMainOrigin()->getPort() !== null) {
            $data['origin_port'] = $resource->getMainOrigin()->getPort();
        }

        if ($resource->getMainOrigin()->getTimeout() !== null) {
            $data['origin_timeout'] = $resource->getMainOrigin()->getTimeout();
        }

        if ($resource->getMainOrigin()->getOriginHeaders() !== null) {
            $data['origin_headers'] = $resource->getMainOrigin()->getOriginHeaders();
        }

        if ($resource->getMainOrigin()->hasFollowRedirectOrigin()) {
            $data['follow_redirect'][FollowRedirectSchema::FIELD_CODES]
                = $resource->getMainOrigin()->getFollowRedirectCodes();
        }

        return $data;
    }

    /** @return mixed[] */
    private function prepareResourceFromResponse(Response $response): array
    {
        $content = $response->getContent();
        self::assertIsString($content);

        $responseContent = json_decode($content, true);

        $customData = $responseContent['cdn_resource']['custom_data'] ?? [];
        ksort($customData);
        $responseContent['cdn_resource']['custom_data'] = $customData;

        return $responseContent;
    }

    /**
     * @param mixed[] $resources
     *
     * @return mixed[]
     */
    private function sortCustomDataForMultipleResources(array $resources): array
    {
        foreach ($resources as &$resource) {
            if (! is_array($resource['custom_data'])) {
                continue;
            }

            ksort($resource['custom_data']);
        }

        return $resources;
    }

    /** @return mixed[] */
    private function getValidDataFromResourceInNgxFormat(
        CdnResource $resource,
        ResourceGeoProtection|null $geoProtection = null,
        ResourceIpProtection|null $ipProtection = null,
        ResourceRefererProtection|null $refererProtection = null,
        Ssl|null $ssl = null,
        ResourceFullLog|null $fullLog = null,
    ): array {
        $ignoredQueryParams = [];
        foreach ($resource->getIgnoredQueryParams() as $ignoredQueryParam) {
            $ignoredQueryParams[] = $ignoredQueryParam->getName();
        }

        $secureToken = $resource->getResourceSecureToken();

        $oldSecureToken = '';
        $oldSecureTokenPath = '';

        if ($secureToken !== null) {
            $oldSecureToken = in_array(
                $secureToken->getType(),
                [CdnResource::SECURE_TOKEN_TYPE_PARAMETER, CdnResource::SECURE_TOKEN_TYPE_HIGHWINDS],
                true,
            ) ? $secureToken->getValue() : '';

            $oldSecureTokenPath = $secureToken->getType() === CdnResource::SECURE_TOKEN_TYPE_PATH
                ? $secureToken->getValue() : '';
        }

        $resourceOrigin = $resource->getMainOrigin();
        $resourceCaching = $resource->getCaching();

        return [
            'id' => $resource->getId(),
            'account_id' => $resource->getAccount()->getId(),
            'cdn_url' => $resource->getCdnUrl(),
            'group_id' => $resource->getGroup()->getId(),
            'origin_url' => $resourceOrigin->getHost(),
            'origin_scheme' => $resourceOrigin->getScheme(),
            'cnames' => $resource->getCnames(),
            'suspended' => $resource->getSuspended() === null
                ? null
                : DateFormatter::formatDate($resource->getSuspended()),
            'disable_query_string' => $resource->isDisableQueryString(),
            'ignore_set_cookie' => $resource->isIgnoreSetCookie(),
            'mp4_pseudo_streaming' => $resource->isMp4PseudoStreaming(),
            'cache_expiry' => $resourceCaching->getExpiry(),
            'cache_expiry_404' => $resourceCaching->getExpiry404(),
            'cache_bypass' => $resourceCaching->isBypassed(),
            'secure_token' => $oldSecureToken,
            'secure_token_path' => $oldSecureTokenPath,
            'purge_all_key' => $resource->getPurgeAllKey(),
            'cache_stat_key' => $resource->getId(),
            'ssl_file_index' => $ssl?->getAssignedIndex(),
            'full_logs' => ! ($fullLog === null),
            'ignored_query_params' => count($ignoredQueryParams) > 0 ? $ignoredQueryParams : null,
            'https_redirect_code' => $resource->getHttpsRedirectCode(),
            'origin_basedir' => $resourceOrigin->getBasedir(),
            'referer_deny_empty' => $refererProtection?->isEmptyDenied(),
            'referer_type' => $refererProtection?->getType(),
            'referer_domains' => $refererProtection === null ? null : array_map(
                static fn (ResourceRefererProtectionAddress $address) => $address->getDomain(),
                $refererProtection->getAddresses()->toArray(),
            ),
            'origin_port' => $resourceOrigin->getPort(),
            'origin_proxy_ip' => null,
            'origin_proxy_cached' => false,
            'ip_protection_type' => $ipProtection?->getType(),
            'ip_protection_addresses' => $ipProtection === null ? null : array_map(
                static fn (ResourceIpProtectionAddress $address) => $address->getAddress(),
                $ipProtection->getAddresses()->toArray(),
            ),
            'geo_protection_type' => $geoProtection?->getType(),
            'geo_protection_countries' => $geoProtection === null ? null : array_map(
                static fn (ResourceGeoProtectionLocation $location) => $location->getCountry(),
                $geoProtection->getLocations()->toArray(),
            ),
            'secure_token_ua_bypass' => null,
            'origin_timeout' => $resourceOrigin->getTimeout(),
            'cache_min_uses' => $resourceCaching->getMinUses(),
            'forward_host_header' => $resourceOrigin->hasForwardHostHeader(),
            'streaming_playlist_bypass' => $resource->hasStreamingPlaylistBypass(),
            'cache_lock_age' => $resourceCaching->getLockAge(),
            'cache_lock_timeout' => $resourceCaching->getLockTimeout(),
            'cache_content_length_limit' => $resourceCaching->getContentLengthLimit(),
            'cache_no_content_length_limit' => $resourceCaching->getMissingContentLengthLimit(),
            'upstream_fail_timeout' => $resource->getUpstreamFailTimeout(),
            'upstream_next_attempts' => $resource->getUpstreamNextAttempts(),
            'custom_data' => ['boolean' => true, 'float' => 88.8, 'integer' => 666, 'string' => 'text'],
            'quic' => $resource->hasQuic(),
            'waf' => $resource->hasWaf(),
            'cors_origin_header' => $resource->hasCorsOriginHeader(),
            'cors_timing_enabled' => $resource->hasCorsTimingEnabled(),
            'cors_wildcard_enabled' => $resource->hasCorsWildcardEnabled(),
            'aws_access_key_id'
                => $resourceOrigin->getS3()->getAccessKeyId(),
            's3_access_key_id' => $resourceOrigin->getS3()->getAccessKeyId(),
            'aws_secret' => $resourceOrigin->getS3()->getSecret(),
            's3_secret' => $resourceOrigin->getS3()->getSecret(),
            'aws_region' => $resourceOrigin->getS3()->getRegion(),
            's3_region' => $resourceOrigin->getS3()->getRegion(),
            's3_bucket_name' => $resourceOrigin->getS3()->getBucketName(),
            's3_type' => $resourceOrigin->getS3()->getType(),
            'rate_limit' => false,
            'content_disposition_by_param' => false,
            'origin_headers' => $resourceOrigin->getOriginHeaders(),
            'secure_token_type' => $secureToken?->getType(),
            'secure_token_value' => $secureToken?->getValue(),
            'secure_link_expiry_param' => $secureToken?->getSecureLinkExpiryParam(),
            'secure_link_token_param' => $secureToken?->getSecureLinkTokenParam(),
            'secure_link_pathlen_param' => $secureToken?->getSecureLinkPathlenParam(),
            'secure_link_secret_param' => $secureToken?->getSecureLinkSecretParam(),
            'secure_link_rewrite_playlist' => $secureToken === null
                ? false : $secureToken->hasSecureLinkRewritePlaylist(),
            'follow_redirect_origin' => $resourceOrigin->hasFollowRedirectOrigin(),
            'follow_redirect_codes' => ValueReplacer::emptyArrayToNull($resourceOrigin->getFollowRedirectCodes()),
            'ssl_verify_disable' => $resourceOrigin->hasSslVerifyDisable(),
            'origins' => $this->prepareOriginsInNxgFormat($resource),
            'response_headers' => ValueReplacer::emptyArrayToNull($resource->getResponseHeaders()->toArray()),
        ];
    }

    /** @return array<array<string, mixed>> */
    private function prepareOriginsInNxgFormat(CdnResource $resource): array
    {
        $result = [];

        foreach ($resource->getOrigins() as $origin) {
            $result[$origin->getPriority() - 1] = [
                'priority' => $origin->getPriority(),
                'host' => $origin->getHost(),
                'scheme' => $origin->getScheme(),
                'basedir' => $origin->getBasedir(),
                'port' => $origin->getPort(),
                'timeout' => $origin->getTimeout(),
                's3_access_key_id' => $origin->getS3()->getAccessKeyId(),
                's3_secret' => $origin->getS3()->getSecret(),
                's3_region' => $origin->getS3()->getRegion(),
                's3_bucket_name' => $origin->getS3()->getBucketName(),
                's3_type' => $origin->getS3()->getType(),
                'forward_host_header' => $origin->hasForwardHostHeader(),
                'ssl_verify_disable' => $origin->hasSslVerifyDisable(),
                'origin_headers' => $origin->getOriginHeaders(),
                'follow_redirect_origin' => $origin->hasFollowRedirectOrigin(),
                'follow_redirect_codes' => ValueReplacer::emptyArrayToNull($origin->getFollowRedirectCodes()),
            ];
        }

        return $result;
    }

    /**
     * @param array<int> $followRedirectCodes
     * @param array<string, string> $originHeaders
     *
     * @return array<string, mixed>
     */
    private function prepareOriginArray(
        string $host,
        string $scheme,
        string|null $basedir = null,
        int|null $port = null,
        int|null $timeout = null,
        string|null $s3AccessKeyId = null,
        string|null $s3Secret = null,
        string|null $s3Region = null,
        string|null $s3BucketName = null,
        string|null $s3Type = null,
        bool|null $forwardHostHeader = null,
        bool|null $sslVerifyDisable = null,
        array|null $originHeaders = null,
        bool|null $followRedirectOrigin = null,
        array|null $followRedirectCodes = null,
    ): array {
        return [
            'host' => $host,
            'scheme' => $scheme,
            'basedir' => $basedir,
            'port' => $port,
            'timeout' => $timeout,
            's3_access_key_id' => $s3AccessKeyId,
            's3_secret' => $s3Secret,
            's3_region' => $s3Region,
            's3_bucket_name' => $s3BucketName,
            's3_type' => $s3Type,
            'forward_host_header' => $forwardHostHeader,
            'ssl_verify_disable' => $sslVerifyDisable,
            'origin_headers' => $originHeaders,
            'follow_redirect_origin' => $followRedirectOrigin,
            'follow_redirect_codes' => $followRedirectCodes,
        ];
    }

    /**
     * @param Collection<int, ResourceOrigin> $origins
     *
     * @return array<int<0, max>, array<string, mixed>>
     */
    private function prepareOriginsFromEntity(Collection $origins): array
    {
        $result = [];

        foreach ($origins as $origin) {
            $result[] = [
                'clap_origin_id' => $origin->getClapOriginId()?->toString(),
                'priority' => $origin->getPriority(),
                'url' => [
                    'host' => $origin->getHost(),
                    'scheme' => $origin->getScheme(),
                    'basedir' => $origin->getBasedir(),
                    'port' => $origin->getPort(),
                ],
                'timeout' => $origin->getTimeout(),
                's3' => [
                    'access_key_id' => $origin->getS3()->getAccessKeyId(),
                    'secret' => $origin->getS3()->getSecret(),
                    'region' => $origin->getS3()->getRegion(),
                    'bucket_name' => $origin->getS3()->getBucketName(),
                    'type' => $origin->getS3()->getType(),
                ],
                'forward_host_header' => $origin->hasForwardHostHeader(),
                'ssl_verify_disable' => $origin->hasSslVerifyDisable(),
                'origin_headers' => $origin->getOriginHeaders(),
                'follow_redirect' => [
                    'enabled' => $origin->hasFollowRedirectOrigin(),
                    'codes' => ValueReplacer::emptyArrayToNull($origin->getFollowRedirectCodes()),
                ],
            ];
        }

        return $result;
    }

    private function formatToZuluDate(DateTimeImmutable|null $date): string|null
    {
        return $date?->format('Y-m-d\TH:i:s\Z');
    }

    /** @return mixed[] */
    private function prepareDataForNewCdn(): array
    {
        $group = $this->createLocationGroup();

        return [
            'account_id' => random_int(10000, 99999),
            'origin_url' => 'test-add-resource.com',
            'origin_scheme' => ResourceOrigin::SCHEME_HTTPS,
            'cnames' => ['c1.test.com', 'c2.test.com'],
            'resource_type' => 'HTTP_PULL',
            'group_id' => [$group->getId()],
            'cache_expiry' => 666,
            'cache_expiry_404' => 666,
            'disable_query_string' => 1,
            'ignore_set_cookie' => 0,
            'mp4_pseudo_streaming' => 1,
            'instant_ssl' => 0,
            'ignored_query_params' => [],
            'https_redirect_code' => 0,
            'origin_timeout' => 13,
            'origin_basedir' => '',
            'origin_port' => 88,
            'forward_host_header' => 0,
            'streaming_playlist_bypass' => 0,
            'custom_data' => ['test' => 1],
            'waf' => 0,
            'quic' => 0,
            'cors_origin_header' => 0,
            'cors_timing_enabled' => false,
            'cors_wildcard_enabled' => false,
            'ssl_verify_disable' => true,
            'rate_limit' => 1,
            'content_disposition_by_param' => 1,
            'origin_headers' => ['one' => 'two', 'three' => 'four'],
            'response_headers' => ResponseHeadersSchema::fromArray(
                ['Access-Control-Allow-Origin' => 'some.value'],
            )->headers,
            'follow_redirect' => [
                FollowRedirectSchema::FIELD_ENABLED => true,
                FollowRedirectSchema::FIELD_CODES => [301, 302],
            ],
        ];
    }

    /** @return array<string, string|int|bool|array<string, mixed>> */
    private function prepareDataForOrigin(int $priority = 1): array
    {
        return [
            'clap_origin_id' => Uuid::uuid4()->toString(),
            'priority' => $priority,
            'url' => [
                'host' => 'eu-1.cdn77-storage.com',
                'scheme' => ResourceOrigin::SCHEME_HTTPS,
                'basedir' => 'basedir',
                'port' => 88,
            ],
            'timeout' => 10,
            's3' => [
                'access_key_id' => 's3_access_key_id',
                'secret' => 's3_secret',
                'region' => 's3_region',
                'bucket_name' => 's3_bucket_name',
                'type' => 'cdn77-ceph-rgw',
            ],
            'forward_host_header' => true,
            'ssl_verify_disable' => true,
            'origin_headers' => ['one' => 'two', 'three' => 'four'],
            'follow_redirect' => [
                'enabled' => true,
                'codes' => [301, 302],
            ],
        ];
    }

    /**
     * @param array<string, string|int|bool|array<int|string, ResponseHeaderSchema|int|float|string|bool|null>|null> $cdnResourceRequestData
     * @param array<string> $propertiesToExclude
     *
     * @return array<string, mixed>
     */
    private function addRequiredData(
        CdnResource $resource,
        array $cdnResourceRequestData,
        array $propertiesToExclude = [],
    ): array {
        if ($this->shouldBeAdded('account_id', $cdnResourceRequestData, $propertiesToExclude)) {
            $cdnResourceRequestData['account_id'] = $resource->getAccount()->getId();
        }

        if ($this->shouldBeAdded('origin_url', $cdnResourceRequestData, $propertiesToExclude)) {
            $cdnResourceRequestData['origin_url'] = $resource->getMainOrigin()->getHost();
        }

        if ($this->shouldBeAdded('origin_scheme', $cdnResourceRequestData, $propertiesToExclude)) {
            $cdnResourceRequestData['origin_scheme'] = $resource->getMainOrigin()->getScheme();
        }

        if ($this->shouldBeAdded('origin_port', $cdnResourceRequestData, $propertiesToExclude)) {
            $cdnResourceRequestData['origin_port'] = $resource->getMainOrigin()->getPort() ?? 0;
        }

        if ($this->shouldBeAdded('origin_timeout', $cdnResourceRequestData, $propertiesToExclude)) {
            $cdnResourceRequestData['origin_timeout'] = $resource->getMainOrigin()->getTimeout() ?? 0;
        }

        if ($this->shouldBeAdded('origin_headers', $cdnResourceRequestData, $propertiesToExclude)) {
            $cdnResourceRequestData['origin_headers']
                = ValueReplacer::nullToEmptyArray($resource->getMainOrigin()->getOriginHeaders());
        }

        if ($this->shouldBeAdded('response_headers', $cdnResourceRequestData, $propertiesToExclude)) {
            $cdnResourceRequestData['response_headers']
                = ValueReplacer::nullToEmptyArray($resource->getResponseHeaders()->toArray());
        }

        if ($this->shouldBeAdded('follow_redirect', $cdnResourceRequestData, $propertiesToExclude)) {
            $cdnResourceRequestData['follow_redirect'] = [
                'enabled' => $resource->getMainOrigin()->hasFollowRedirectOrigin(),
                'codes' => ValueReplacer::nullToEmptyArray($resource->getMainOrigin()->getFollowRedirectCodes()),
            ];
        }

        if ($this->shouldBeAdded('forward_host_header', $cdnResourceRequestData, $propertiesToExclude)) {
            $cdnResourceRequestData['forward_host_header'] = $resource->getMainOrigin()->hasForwardHostHeader();
        }

        if (
            $this->shouldBeAdded(
                ResourceEditInfo::FIELD_SSL_VERIFY_DISABLE,
                $cdnResourceRequestData,
                $propertiesToExclude,
            )
        ) {
            $cdnResourceRequestData[ResourceEditInfo::FIELD_SSL_VERIFY_DISABLE]
                = $resource->getMainOrigin()->hasSslVerifyDisable();
        }

        if (
            $this->shouldBeAdded(
                ResourceEditInfo::FIELD_ORIGIN_BASEDIR,
                $cdnResourceRequestData,
                $propertiesToExclude,
            )
        ) {
            $cdnResourceRequestData[ResourceEditInfo::FIELD_ORIGIN_BASEDIR]
                = $resource->getMainOrigin()->getBasedir() === null ? '' : $resource->getMainOrigin()->getBasedir();
        }

        return $cdnResourceRequestData;
    }

    /**
     * @param array<string, string|int|bool|array<int|string, int|float|string|bool|null>|null> $requestData
     * @param array<string> $propertiesToExclude
     *
     * @return array<string, mixed>
     */
    private function addRequiredOriginData(
        ResourceOrigin $origin,
        array $requestData,
        array $propertiesToExclude = [],
    ): array {
        if ($this->shouldBeAdded('clap_origin_id', $requestData, $propertiesToExclude)) {
            $requestData['clap_origin_id']
                = $origin->getClapOriginId()?->toString();
        }

        if ($this->shouldBeAdded('priority', $requestData, $propertiesToExclude)) {
            $requestData['priority'] = $origin->getPriority();
        }

        if ($this->shouldBeAdded('timeout', $requestData, $propertiesToExclude)) {
            $requestData['timeout'] = ValueReplacer::nullToZero($origin->getTimeout());
        }

        if ($this->shouldBeAdded('forward_host_header', $requestData, $propertiesToExclude)) {
            $requestData['forward_host_header'] = $origin->hasForwardHostHeader();
        }

        if ($this->shouldBeAdded('ssl_verify_disable', $requestData, $propertiesToExclude)) {
            $requestData['ssl_verify_disable'] = $origin->hasSslVerifyDisable();
        }

        if ($this->shouldBeAdded('origin_headers', $requestData, $propertiesToExclude)) {
            $requestData['origin_headers'] = ValueReplacer::nullToEmptyArray($origin->getOriginHeaders());
        }

        if ($this->shouldBeAdded('url', $requestData, $propertiesToExclude)) {
            $requestData['url'] = [
                'host' => $origin->getHost(),
                'scheme' => $origin->getScheme(),
                'basedir' => ValueReplacer::nullToEmptyString($origin->getBasedir()),
                'port' => ValueReplacer::nullToZero($origin->getPort()),
            ];
        }

        if ($this->shouldBeAdded('s3', $requestData, $propertiesToExclude)) {
            $requestData['s3'] = [
                'access_key_id' => $origin->getS3()->getAccessKeyId(),
                'secret' => $origin->getS3()->getSecret(),
                'region' => $origin->getS3()->getRegion(),
                'bucket_name' => $origin->getS3()->getBucketName(),
                'type' => $origin->getS3()->getType(),
            ];
        }

        if ($this->shouldBeAdded('follow_redirect', $requestData, $propertiesToExclude)) {
            $requestData['follow_redirect'] = [
                'enabled' => $origin->hasFollowRedirectOrigin(),
                'codes' => ValueReplacer::nullToEmptyArray($origin->getFollowRedirectCodes()),
            ];
        }

        return $requestData;
    }

    /**
     * @param array<string, mixed> $cdnResourceRequestData
     * @param array<string> $propertiesToExclude
     */
    private function shouldBeAdded(
        string $propertyName,
        array $cdnResourceRequestData,
        array $propertiesToExclude,
    ): bool {
        if (in_array($propertyName, $propertiesToExclude, true)) {
            return false;
        }

        return ! array_key_exists($propertyName, $cdnResourceRequestData);
    }

    private function createResourceIgnoredParameter(CdnResource $resource, string $name): IgnoredQueryParam
    {
        $parameter = new IgnoredQueryParam($resource, $name);

        $resource->getIgnoredQueryParams()[$name] = $parameter;
        $this->getEntityManager()->flush();

        return $parameter;
    }

    private function createLetsEncryptRequestForResource(
        CdnResource $resource,
        RequestState $requestState,
        DateTimeImmutable|null $createdAt = null,
    ): Request {
        $createdAt ??= new DateTimeImmutable();

        $request = new Request($resource, $resource->getCnames(), $createdAt, $requestState);

        $this->getEntityManager()->getRepository(Task::class)->add(
            new Task($request, $createdAt, new DateTimeImmutable()),
        );

        $this->getEntityManager()->persist($request);
        $this->getEntityManager()->flush();

        return $request;
    }

    private function createLetsEncryptResultForRequest(
        Request $request,
        DateTimeImmutable $runAt,
        DateTimeImmutable $completedAt,
        ResultStatus $resultStatus,
        string|null $description = null,
    ): Result {
        $result = new Result(
            $request,
            $request->getCreatedAt(),
            $runAt,
            $completedAt,
            $resultStatus,
            $description,
        );

        $this->getEntityManager()->persist($result);
        $this->getEntityManager()->flush();

        return $result;
    }

    private function enableResourceFullLog(CdnResource $resource): ResourceFullLog
    {
        $fullLog = new ResourceFullLog($resource);

        $this->getEntityManager()->persist($fullLog);
        $this->getEntityManager()->flush();

        return $fullLog;
    }

    private function enableResourceSsl(CdnResource $resource): Ssl
    {
        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resource->getId());
        if ($ssl !== null) {
            return $ssl;
        }

        $ssl = new Ssl();

        $ssl->setResource($resource);
        $ssl->setAssignedAt(new DateTimeImmutable('- 1 minute'));
        $ssl->setAssignedIndex(1);

        $this->getEntityManager()->persist($ssl);
        $this->getEntityManager()->flush();

        return $ssl;
    }

    private function enableResourceSecureToken(
        CdnResource $resource,
        string $secureTokenType = 'highwinds',
        string $secureTokenValue = 'asdfghjkl',
        string|null $secureLinkExpiryParam = 'secureLinkExpiryParam',
        string|null $secureLinkTokenParam = 'secureLinkTokenParam',
        string|null $secureLinkPathlenParam = 'secureLinkPathlenParam',
        string|null $secureLinkSecretParam = 'secureLinkSecretParam',
        bool $secureLinkRewritePlaylist = false,
    ): SecureToken {
        $st = new SecureToken(
            $resource,
            $secureTokenType,
            $secureTokenValue,
            $secureLinkExpiryParam,
            $secureLinkTokenParam,
            $secureLinkPathlenParam,
            $secureLinkSecretParam,
            $secureLinkRewritePlaylist,
        );
        $resource->setResourceSecureToken($st);

        $this->getEntityManager()->persist($st);
        $this->getEntityManager()->flush();

        return $st;
    }

    /** @param string[] $domains */
    private function addResourceSslFile(
        CdnResource $resource,
        Ssl|null $ssl = null,
        int|null $index = null,
        DateTimeImmutable|null $createdAtAndExpiration = null,
        array|null $domains = [],
        string $type = SslFile::TYPE_CUSTOM,
    ): SslFile {
        $ssl ??= $this->enableResourceSsl($resource);
        $index = $index ?? $ssl->getFiles()->count() + 1;
        $ssl->setAssignedIndex($index);
        $ssl->setAssignedAt($createdAtAndExpiration ?? new DateTimeImmutable());

        $sslFile = new SslFile(
            $createdAtAndExpiration ?? new DateTimeImmutable(),
            $domains ?? [sprintf('dom-%d-1.com', $resource->getId()), sprintf('dom-%d-2.com', $resource->getId())],
            $createdAtAndExpiration ?? new DateTimeImmutable('+1 year'),
            $index,
            $ssl,
            $type,
        );

        if ($type === SslFile::TYPE_LETSENCRYPT) {
            $resource->setInstantSsl(true);
        } else {
            $resource->setInstantSsl(false);
        }

        $ssl->getFiles()->add($sslFile);

        $this->getEntityManager()->persist($resource);
        $this->getEntityManager()->persist($ssl);
        $this->getEntityManager()->persist($sslFile);
        $this->getEntityManager()->flush();

        return $sslFile;
    }

    private function extractCertificateExpiration(CertificatePair $certificatePair): DateTimeImmutable
    {
        $data = @openssl_x509_parse($certificatePair->getCertificate(), false);

        self::assertIsArray($data);
        self::assertArrayHasKey('validTo', $data);

        $date = DateTimeImmutable::createFromFormat(
            'ymdHis\Z',
            $data['validTo'],
            new DateTimeZone('UTC'),
        );

        self::assertInstanceOf(DateTimeImmutable::class, $date);

        return $date;
    }

    /** @return string[] */
    private function extractCertificateDomains(CertificatePair $certificatePair): array
    {
        $certificate = @openssl_x509_parse($certificatePair->getCertificate(), false);

        $alternativeNames = array_values(
            array_map(
                static fn (string $entry): string => substr($entry, 4),
                array_filter(
                    preg_split('~,\s*~', $certificate['extensions']['subjectAltName'] ?? ''),
                    static fn (string $entry): bool => strncmp($entry, 'DNS:', 4) === 0,
                ),
            ),
        );

        return count($alternativeNames) > 0
            ? $alternativeNames : (array) ($certificate['subject']['commonName'] ?? null);
    }

    /** @param string[] $domains */
    private function createResourceRefererProtection(
        CdnResource $resource,
        string $type,
        bool $denyEmpty,
        array $domains = [],
    ): ResourceRefererProtection {
        $referer = new ResourceRefererProtection($resource, $type, $denyEmpty);
        $this->getEntityManager()->persist($referer);

        foreach ($domains as $domain) {
            $referer->addAddress(new ResourceRefererProtectionAddress($referer, $domain));
        }

        $this->getEntityManager()->flush();

        return $referer;
    }

    private function createServerCurrentStatus(
        Server $server,
        DateTimeImmutable $lastUpdateAt,
        bool $up,
        string $reason,
    ): ServerCurrentStatus {
        $currentStatus = new ServerCurrentStatus($server, $lastUpdateAt, $up, $reason);

        $this->getEntityManager()->persist($currentStatus);
        $this->getEntityManager()->flush();

        return $currentStatus;
    }

    private function createServerLastDown(Server $server, DateTimeImmutable $at, string $reason): ServerLastDownStatus
    {
        $lastDown = new ServerLastDownStatus($server, $at, $reason);

        $this->getEntityManager()->persist($lastDown);
        $this->getEntityManager()->flush();

        return $lastDown;
    }

    /** @param Pop[] $targets */
    private function createPopRedirection(
        Pop $pop,
        float $probability,
        bool $useNextBestPop,
        array $targets = [],
    ): Redirection {
        $redirection = new Redirection($pop, $probability, $useNextBestPop, new DateTimeImmutable());
        $this->getEntityManager()->persist($redirection);

        foreach ($targets as $target) {
            $redirection->addTarget($target, new DateTimeImmutable());
        }

        if (count($targets) > 0) {
            $redirection->setActive(true);
        }

        $this->getEntityManager()->flush();

        return $redirection;
    }

    private function nullingEmptyString(string $value): string|null
    {
        return $value === '' ? null : $value;
    }
}
