<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\Legacy\Resources;

use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_decode;
use function sprintf;

class DetailControllerTest extends WebTestCase
{
    use TemporaryData;

    public function testExistingResource(): void
    {
        $resource = $this->createTemporaryResource();
        $validResource = $this->getValidResponseForDetail($resource);

        $this->client->request(
            Request::METHOD_GET,
            '/cdn_resources/' . $resource->getId() . '.json',
            [],
            [],
            $this->getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        $actual = $this->prepareResourceFromResponse($response);

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame($validResource, $actual);
    }

    public function testNonExistentResource(): void
    {
        $this->client->request(
            Request::METHOD_GET,
            '/cdn_resources/123456789.json',
            [],
            [],
            $this->getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['Requested object could not be found.'],
            ],
            json_decode($response->getContent(), true),
        );
    }

    public function testWithIgnoredQueryParams(): void
    {
        $resource = $this->createTemporaryResource();
        $this->createResourceIgnoredParameter($resource, 'foo');
        $this->createResourceIgnoredParameter($resource, 'Bar');
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_GET,
            sprintf('/cdn_resources/%d.json', $resource->getId()),
            [],
            [],
            $this->getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        $actual = $this->prepareResourceFromResponse($response);

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame(
            $this->getValidResponseForDetail($resource),
            $actual,
        );
    }
}
