<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\Legacy\Resources;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NgxConfGen\Application\Controller\SortResourceData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use DateTimeImmutable;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function array_merge;
use function assert;
use function count;
use function Safe\json_decode;

class ListControllerTest extends WebTestCase
{
    use TemporaryData;
    use SortResourceData;

    public function testWithNoResources(): void
    {
        $this->client->request(
            Request::METHOD_GET,
            '/cdn_resources.json',
            [],
            [],
            $this->getDefaultHeaders(),
        );
        $response = $this->client->getResponse();
        $responseContent = json_decode($response->getContent(), true);

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame([], $responseContent);
    }

    public function testAllExistingResources(): void
    {
        $resources = $this->createTemporaryResources();

        $this->createSuspendedResource();

        $validData = $this->getValidResponseForList($resources);

        $this->client->request(
            Request::METHOD_GET,
            '/cdn_resources.json',
            [],
            [],
            $this->getDefaultHeaders(),
        );
        $response = $this->client->getResponse();
        $responseContent = $this->sortCustomDataForMultipleResources(json_decode($response->getContent(), true));

        $this->sortResourcesById($validData, $responseContent, 'cdn_reference');

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame(2, count($responseContent));
        self::assertSame($validData, $responseContent);
    }

    public function testAllExistingResourcesWithSuspended(): void
    {
        $resources = $this->createTemporaryResources();
        $suspendedResource = $this->createSuspendedResource();

        $validData = $this->getValidResponseForList(array_merge($resources, [$suspendedResource]));

        $this->client->request(
            Request::METHOD_GET,
            '/cdn_resources.json',
            ['excludeSuspended' => '0'],
            [],
            $this->getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        $responseContent = json_decode($response->getContent(), true);
        $this->sortResourcesById($validData, $responseContent, 'cdn_reference');

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame(3, count($responseContent));
        self::assertEquals($validData, $responseContent);
    }

    private function createSuspendedResource(): CdnResource
    {
        $resource = $this->createTemporaryResource();
        assert($resource instanceof CdnResource);
        $resource->setSuspended(new DateTimeImmutable('-666 days'));
        $em = $this->getEntityManager();
        $em->persist($resource);
        $em->flush();

        return $resource;
    }
}
