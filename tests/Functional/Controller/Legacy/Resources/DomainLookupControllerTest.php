<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\Legacy\Resources;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_decode;
use function sprintf;

class DomainLookupControllerTest extends WebTestCase
{
    use TemporaryData;

    public function testWithMalformedDomain(): void
    {
        $response = $this->makeRequest('x0x0^_+.tld');

        self::assertSame(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['The domain "x0x0^_+.tld" is not in valid format.'],
            ],
            json_decode($response->getContent(), true),
        );
    }

    public function testWithNotFoundDomain(): void
    {
        $this->createTemporaryResource();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest('foo.test');

        self::assertSame(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['No resource found matching domain "foo.test".'],
            ],
            json_decode($response->getContent(), true),
        );
    }

    public function testLookupByCdnUrl(): void
    {
        $resource = $this->createTemporaryResource();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource->getCdnUrl());

        $resource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());

        $actual = $this->prepareResourceFromResponse($response);

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame($this->formatExpectedResponse($resource), $actual);
    }

    public function testLookupByCname(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setCnames(['foo.test', 'bar.test']);

        $this->createTemporaryResource();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest('foo.test');

        $resource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());

        $actual = $this->prepareResourceFromResponse($response);

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame($this->formatExpectedResponse($resource), $actual);
    }

    /** @return mixed[][] */
    private function formatExpectedResponse(CdnResource $resource): array
    {
        return [
            'cdn_resource' => $this->getValidDataFromResource($resource),
        ];
    }

    private function makeRequest(string $domain): Response
    {
        $this->client->request(
            Request::METHOD_GET,
            sprintf('/resource/domain-lookup/%s.json', $domain),
            [],
            [],
            static::getDefaultHeaders(),
        );

        return $this->client->getResponse();
    }
}
