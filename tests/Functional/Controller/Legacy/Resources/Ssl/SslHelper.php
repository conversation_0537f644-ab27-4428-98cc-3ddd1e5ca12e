<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\Legacy\Resources\Ssl;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use DateTime;
use DateTimeImmutable;
use Webmozart\Assert\Assert;

trait SslHelper
{
    /**
     * @param string[] $domains
     *
     * @return array{CdnResource, Ssl, SslFile}
     */
    private function createResourceAndSslSet(
        string $type = SslFile::TYPE_CUSTOM,
        string $expiration = '2035-01-01T00:00:00+0000',
        array $domains = ['foo.test'],
    ): array {
        $createdAtAndExpiration = DateTimeImmutable::createFromFormat(DateTime::ISO8601, $expiration);
        Assert::isInstanceOf($createdAtAndExpiration, DateTimeImmutable::class);

        $resource = $this->createTemporaryResource();
        $ssl = $this->enableResourceSsl($resource);

        $file = $this->addResourceSslFile(
            $resource,
            $ssl,
            1,
            $createdAtAndExpiration,
            $domains,
            $type,
        );

        return [$resource, $ssl, $file];
    }
}
