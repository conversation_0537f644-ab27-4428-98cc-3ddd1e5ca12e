<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\Legacy\Resources\Ssl;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Generators\CertificateDefinitions;
use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use Cdn77\NxgApi\Tests\Utils\DateFormatter;
use DateTimeImmutable;
use ReflectionProperty;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function assert;
use function Safe\json_decode;
use function Safe\json_encode;
use function sprintf;

class SetControllerTest extends WebTestCase
{
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use CertificateDefinitions;

    public function testWithNoPreviousCertificateSet(): void
    {
        $now = new DateTimeImmutable();
        $resource = $this->createTemporaryResource();

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();

        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource, $certificatePair);

        self::assertSame(Response::HTTP_ACCEPTED, $response->getStatusCode());

        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resource->getId());
        assert($ssl instanceof Ssl);
        self::assertNotNull($ssl);

        self::assertSame(1, $ssl->getFiles()->count());

        $file = $ssl->getFiles()->first();
        assert($file instanceof SslFile);
        self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
        self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
        self::assertSame($ssl->getAssignedIndex(), 1);
        self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());
        self::assertSame(
            $file->getExpiresAt()->getTimestamp(),
            $this->extractCertificateExpiration($certificatePair)->getTimestamp(),
        );
        self::assertSame([], $this->extractCertificateDomains($certificatePair));

        self::assertSame(
            [
                'id' => $file->getId(),
                'type' => 'custom',
                'index' => 1,
                'created_at' => DateFormatter::formatDate($file->getCreatedAt()),
            ],
            json_decode($response->getContent(), true),
        );

        self::assertSingleUpdatingMessageInQueue($resource->getId());
    }

    public function testWithExistingPreviousCertificateFiles(): void
    {
        $now = new DateTimeImmutable();
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(true);

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();

        $ssl = $this->enableResourceSsl($resource);

        for ($i = 1; $i <= 3; $i++) {
            $sslFile = $this->addResourceSslFile(
                $resource,
                $ssl,
                $i,
                new DateTimeImmutable(),
                ['foo.bar'],
                SslFile::TYPE_LETSENCRYPT,
            );

            $rp = new ReflectionProperty(SslFile::class, 'createdAt');
            $rp->setAccessible(true);
            $rp->setValue($sslFile, new DateTimeImmutable('- ' . ((3 - $i) * 10) . ' minutes'));
        }

        $ssl->setAssignedIndex(2);
        $ssl->setAssignedAt(new DateTimeImmutable('- 15 minutes'));

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource, $certificatePair);

        self::assertSame(Response::HTTP_ACCEPTED, $response->getStatusCode());

        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resource->getId());
        assert($ssl instanceof Ssl);
        self::assertNotNull($ssl);

        self::assertSame(4, $ssl->getFiles()->count());

        $file = $ssl->getFiles()->get(4);
        assert($file instanceof SslFile);
        self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
        self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
        self::assertSame($ssl->getAssignedIndex(), 4);
        self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());

        self::assertSame(
            [
                'id' => $file->getId(),
                'type' => 'custom',
                'index' => 4,
                'created_at' => DateFormatter::formatDate($file->getCreatedAt()),
            ],
            json_decode($response->getContent(), true),
        );

        self::assertSingleUpdatingMessageInQueue($resource->getId());

        $updatedResource = $this->getEntityManager()->getRepository(CdnResource::class)->find($resource->getId());
        self::assertFalse($updatedResource->hasInstantSsl());
    }

    public function testWithExistingSslFilesAndMissingSsl(): void
    {
        $now = new DateTimeImmutable();
        $resource = $this->createTemporaryResource();

        $ssl = $this->enableResourceSsl($resource);

        for ($i = 1; $i <= 2; $i++) {
            $this->addResourceSslFile(
                $resource,
                $ssl,
                $i,
                new DateTimeImmutable(),
                ['foo.bar'],
                SslFile::TYPE_CUSTOM,
            );
        }

        $this->getEntityManager()->flush();

        $this->getEntityManager()->createQueryBuilder()
            ->delete(Ssl::class, 's')
            ->where('s.resource = :resource')
            ->setParameter('resource', $ssl->getResource())
            ->getQuery()
            ->execute();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();

        $response = $this->makeRequest($resource, $certificatePair);

        self::assertSame(Response::HTTP_ACCEPTED, $response->getStatusCode());

        $this->getEntityManager()->clear();

        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resource->getId());
        assert($ssl instanceof Ssl);
        self::assertNotNull($ssl);

        $sslFile = $ssl->getFiles()[3];

        self::assertSame(3, $ssl->getFiles()->count());
        self::assertSame($ssl->getAssignedIndex(), $sslFile->getIndex());
        self::assertSame($ssl->getAssignedIndex(), 3);
        self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());

        self::assertSame(
            [
                'id' => $sslFile->getId(),
                'type' => 'custom',
                'index' => 3,
                'created_at' => DateFormatter::formatDate($sslFile->getCreatedAt()),
            ],
            json_decode($response->getContent(), true),
        );

        self::assertSingleUpdatingMessageInQueue($resource->getId());
    }

    public function testWithInvalidCertificatePair(): void
    {
        $resource = $this->createTemporaryResource();

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomInvalidCertificatePair();

        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource, $certificatePair);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertNoMessageInQueue();
    }

    /**
     * @param string[] $domains
     *
     * @dataProvider certificatesProvider
     */
    public function testWithMultipleCertificateConfigurations(
        CertificatePair $certificatePair,
        array $domains,
        DateTimeImmutable $expiration,
    ): void {
        $now = new DateTimeImmutable();
        $resource = $this->createTemporaryResource();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource, $certificatePair);

        self::assertSame(Response::HTTP_ACCEPTED, $response->getStatusCode());

        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resource->getId());
        assert($ssl instanceof Ssl);
        self::assertNotNull($ssl);

        self::assertSame(1, $ssl->getFiles()->count());

        $file = $ssl->getFiles()->first();
        assert($file instanceof SslFile);
        self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
        self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
        self::assertSame($ssl->getAssignedIndex(), 1);
        self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());

        self::assertSame(
            $expiration->getTimestamp(),
            $this->extractCertificateExpiration($certificatePair)->getTimestamp(),
        );
        self::assertSame($file->getExpiresAt()->getTimestamp(), $expiration->getTimestamp());

        self::assertSame($domains, $this->extractCertificateDomains($certificatePair));
        self::assertSame($domains, $file->getDomains());
        self::assertSingleUpdatingMessageInQueue($resource->getId());
    }

    public function testWithInvalidCertificateChain(): void
    {
        $resource = $this->createTemporaryResource();

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();

        $certificatePairChain = new CertificatePair(
            $certificatePair->getCertificate()
            . $certificatePairGenerator->generateRandomCertificate()
            . "-----BEGIN CERTIFICATE-----\ninvalidCertificate\n-----END CERTIFICATE-----\n"
            . $certificatePairGenerator->generateRandomCertificate(),
            $certificatePair->getPrivateKey(),
        );

        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource, $certificatePairChain);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['The certificate chain is malformed, probably 2. intermediate is corrupted.'],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    /** @return mixed[][] */
    public function certificatesProvider(): iterable
    {
        yield $this->getCertificateWithCommonNameAlternativeNames();
        yield $this->getCertificateWithCommonNameAndOneDifferentAlternativeName();
        yield $this->getCertificateWithCommonNameAndMultipleDifferentAternativeNames();
        yield $this->getCertificateWithCommonNameAndMatchingAlternativeNames();
        yield $this->getCertificateWithCommonNameAndMultipleAndNonDnsAlternativeNames();
    }

    private function makeRequest(CdnResource $resource, CertificatePair $certificatePair): Response
    {
        $this->client->request(
            Request::METHOD_POST,
            sprintf('/cdn_resources/%d/certificates.json', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    'certificate' => $certificatePair->getCertificate(),
                    'key' => $certificatePair->getPrivateKey(),
                ],
            ),
        );

        return $this->client->getResponse();
    }
}
