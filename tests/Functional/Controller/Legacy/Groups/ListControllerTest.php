<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\Legacy\Groups;

use Cdn77\NxgApi\Entity\Legacy\LocationGroup;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function count;
use function Safe\json_decode;

class ListControllerTest extends WebTestCase
{
    public function testWorksWithNoGroups(): void
    {
        $this->client->request(
            Request::METHOD_GET,
            '/cdn_groups.json',
            [],
            [],
            $this->getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame(
            [],
            json_decode($response->getContent(), true),
        );
    }

    public function testWorksWithExistingGroups(): void
    {
        $groupFoo = new LocationGroup();
        $groupFoo->setName('foo');
        $groupFoo->setPrimary(true);
        $groupFoo->setAllowBackupPops(true);

        $groupBar = new LocationGroup();
        $groupBar->setName('bar');
        $groupBar->setPrimary(false);
        $groupBar->setAllowBackupPops(true);

        $this->getEntityManager()->wrapInTransaction(
            static function (EntityManagerInterface $entityManager) use ($groupFoo, $groupBar): void {
                $entityManager->persist($groupFoo);
                $entityManager->persist($groupBar);
                $entityManager->flush();
                $entityManager->clear();
            },
        );

        $this->client->request(
            Request::METHOD_GET,
            '/cdn_groups.json',
            [],
            [],
            $this->getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        self::assertSame(2, count($content));
        self::assertSame(
            [
                [
                    'cdn_group' => [
                        'id' => $groupFoo->getId(),
                        'name' => $groupFoo->getName(),
                    ],
                ],
                [
                    'cdn_group' => [
                        'id' => $groupBar->getId(),
                        'name' => $groupBar->getName(),
                    ],
                ],
            ],
            $content,
        );
    }

    private function getEntityManager(): EntityManagerInterface
    {
        return static::$kernel->getContainer()->get('doctrine')->getManager('mc_legacy');
    }
}
