<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\Internal\Server;

use Cdn77\NxgApi\Entity\Legacy\Ip;
use Cdn77\NxgApi\Entity\Legacy\Ipv6;
use Cdn77\NxgApi\Entity\Legacy\Server;

use function array_map;
use function array_values;
use function assert;

trait ServerDetailResponseFactory
{
    /** @return mixed[] */
    private function createExpectedServerResponse(int $serverId): array
    {
        $server = $this->getEntityManager()->find(Server::class, $serverId);
        assert($server instanceof Server);

        $primaryIp = $server->getPrimaryIp();
        $secondaryIps = $server->getSecondaryIps();

        $primaryIpv6 = $server->getPrimaryIpv6();
        $secondaryIpv6Addresses = $server->getSecondaryIpv6Addresses();

        $ipAddresses = [
            'secondary' => array_values(array_map(
                static fn (Ip $ip) => $ip->getIp(),
                $secondaryIps,
            )),
        ];

        if ($primaryIp !== null) {
            $ipAddresses['primary'] = $primaryIp->getIp();
        }

        $ipv6Addresses = [
            'secondary' => array_values(array_map(
                static fn (Ipv6 $ipv6) => $ipv6->getIp(),
                $secondaryIpv6Addresses,
            )),
        ];

        if ($primaryIpv6 !== null) {
            $ipv6Addresses['primary'] = $primaryIpv6->getIp();
        }

        $expectedServer = [
            'uid' => $server->getUid(),
            'ip_addresses' => $ipAddresses,
            'ipv6_addresses' => $ipv6Addresses,
            'max_bandwidth' => $server->getMaxBandwidth(),
            'keys_size' => $server->getKeysSize(),
            'worker_count' => $server->getWorkerCount(),
            'drive_count' => $server->getDriveCount(),
        ];

        if ($server->getMaxCacheSize() !== null) {
            $expectedServer['max_cache_size'] = $server->getMaxCacheSize();
        }

        $pop = $server->getPop();
        $expectedServer['pop'] = [
            'id' => $pop->getId(),
            'location' => [
                'id' => $pop->getLocation()->getId(),
            ],
        ];

        return $expectedServer;
    }
}
