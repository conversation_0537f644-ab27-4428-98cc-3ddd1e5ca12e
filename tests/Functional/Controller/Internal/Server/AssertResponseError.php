<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\Internal\Server;

use Symfony\Component\HttpFoundation\Response;

use function Safe\json_decode;

trait AssertResponseError
{
    private function assertResponseError(Response $response, string $error): void
    {
        self::assertSame(
            [
                'errors' => [$error],
            ],
            json_decode($response->getContent(), true),
        );
    }
}
