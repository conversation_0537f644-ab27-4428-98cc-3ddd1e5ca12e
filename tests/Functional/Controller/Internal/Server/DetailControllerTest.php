<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\Internal\Server;

use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_decode;
use function sprintf;

class DetailControllerTest extends WebTestCase
{
    use AssertResponseError;
    use TemporaryData;
    use ServerDetailResponseFactory;

    public function testWithNonExistentServer(): void
    {
        $response = $this->makeRequest(123);

        self::assertSame(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        self::assertResponseError($response, 'Server with uid #123 does not exist.');
    }

    public function testWithNoIp(): void
    {
        $server = $this->createServer(false);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($server->getUid());

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertServerResponse($response, $server->getId());
    }

    public function testWithMultipleIpAddresses(): void
    {
        $server = $this->createServer(false);
        $this->createIp($server, true, true);
        $this->createIp($server, true, false);
        $this->createIp($server, true, false);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($server->getUid());

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertServerResponse($response, $server->getId());
    }

    private function assertServerResponse(Response $response, int $id): void
    {
        self::assertEquals(
            $this->createExpectedServerResponse($id),
            json_decode($response->getContent(), true),
        );
    }

    private function makeRequest(int $uid): Response
    {
        $this->client->request(
            Request::METHOD_GET,
            sprintf('/internal/server/%d', $uid),
            [],
            [],
            static::getDefaultHeaders(),
        );

        return $this->client->getResponse();
    }
}
