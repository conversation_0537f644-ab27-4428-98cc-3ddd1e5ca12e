<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\Internal\Server;

use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_decode;
use function usort;

class ListControllerTest extends WebTestCase
{
    use TemporaryData;
    use ServerDetailResponseFactory;

    public function testWithNoServers(): void
    {
        $response = $this->makeRequest();

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame([], json_decode($response->getContent(), true));
    }

    public function testWithMultipleServers(): void
    {
        $serverA = $this->createServerWithIp(false, true, true);
        $serverB = $this->createServerWithIp(false, true, true);
        $serverC = $this->createServerWithIp(true, true, true);
        $serverD = $this->createServerWithIp(false, false, false);
        $locationA = $this->createLocation('fooXY', 'foo', 'XY', 'Xy');
        $locationB = $this->createLocation('barAB', 'bar', 'AB', 'Ab');
        $popA = $this->createPop($locationA, true, false);
        $popB = $this->createPop($locationB, true, false);
        $popC = $this->createPop($locationB, true, false);

        $serverA->setPop($popA);
        $popA->getServers()->add($serverA);
        $serverB->setPop($popB);
        $popB->getServers()->add($serverB);
        $serverC->setPop($popC);
        $popC->getServers()->add($serverC);
        $serverD->setPop($popC);
        $popC->getServers()->add($serverD);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest();

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertServersResponse(
            $response,
            [$serverA->getId(), $serverB->getId(), $serverC->getId(), $serverD->getId()],
        );
    }

    /** @param int[] $ids */
    private function assertServersResponse(Response $response, array $ids): void
    {
        $serversExpectations = [];
        foreach ($ids as $id) {
            $serversExpectations[] = $this->createExpectedServerResponse($id);
        }

        $serversResponses = json_decode($response->getContent(), true);

        $sorter = static function (&$servers): void {
            usort($servers, static fn (array $a, array $b): int => $a['uid'] <=> $b['uid']);
        };

        $sorter($serversExpectations);
        $sorter($serversResponses);

        self::assertEquals($serversExpectations, $serversResponses);
    }

    private function makeRequest(): Response
    {
        $this->client->request(
            Request::METHOD_GET,
            '/internal/server',
            [],
            [],
            static::getDefaultHeaders(),
        );

        return $this->client->getResponse();
    }
}
