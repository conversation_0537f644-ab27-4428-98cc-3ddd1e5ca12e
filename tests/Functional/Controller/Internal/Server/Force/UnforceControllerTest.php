<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\Internal\Server\Force;

use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Entity\Legacy\ServerCurrentStatus;
use Cdn77\NxgApi\Entity\Legacy\ServerLastDownStatus;
use Cdn77\NxgApi\Tests\Functional\Controller\Internal\Server\AssertServerStatus;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use DateTimeImmutable;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_decode;
use function sprintf;

class UnforceControllerTest extends WebTestCase
{
    use TemporaryData;
    use AssertServerStatus;

    public function testUnforceAction(): void
    {
        $server = $this->createServerWithIp(false, true, true);
        $server->setForcedState(true);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/unforce', $server->getUid()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $this->getEntityManager()->clear();

        $server = $this->getEntityManager()->find(Server::class, $server->getId());
        self::assertNull($server->getForcedState());
        self::assertCurrentServerStatus($server, new DateTimeImmutable(), true, ServerCurrentStatus::REASON_AUTO);
        self::assertNoServerLastDownStatus($server);
    }

    public function testUnforceActionUpdatesExistingStatus(): void
    {
        $server = $this->createServerWithIp(false, true, true);
        $server->setForcedState(false);
        $this->createServerLastDown($server, new DateTimeImmutable('- 1 day'), ServerLastDownStatus::REASON_FORCED);
        $this->createServerCurrentStatus(
            $server,
            new DateTimeImmutable('- 1 day'),
            false,
            ServerCurrentStatus::REASON_FORCED,
        );

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/unforce', $server->getUid()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $this->getEntityManager()->clear();

        $server = $this->getEntityManager()->find(Server::class, $server->getId());
        self::assertNull($server->getForcedState());
        self::assertCurrentServerStatus($server, new DateTimeImmutable(), true, ServerCurrentStatus::REASON_AUTO);
        self::assertServerLastDownStatus(
            $server,
            new DateTimeImmutable('- 1 day'),
            ServerLastDownStatus::REASON_FORCED,
        );
    }

    public function testUnforceActionWithForcedUpServer(): void
    {
        $server = $this->createServerWithIp(true, true, true);

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/unforce', $server->getUid()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_CONFLICT, $response->getStatusCode());

        $this->getEntityManager()->clear();

        $server = $this->getEntityManager()->find(Server::class, $server->getId());
        self::assertNull($server->getForcedState()); // still not forced
    }

    public function testUnforceActionWithNonExistentServer(): void
    {
        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/unforce', 123),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['Server with uid #123 does not exist.'],
            ],
            json_decode($response->getContent(), true),
        );
    }
}
