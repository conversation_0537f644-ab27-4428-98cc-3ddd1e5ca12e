<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\Internal\Server\Force;

use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Entity\Legacy\ServerCurrentStatus;
use Cdn77\NxgApi\Entity\Legacy\ServerLastDownStatus;
use Cdn77\NxgApi\Tests\Functional\Controller\Internal\Server\AssertServerStatus;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use DateTimeImmutable;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_decode;
use function sprintf;

class ForceDownControllerTest extends WebTestCase
{
    use TemporaryData;
    use AssertServerStatus;

    public function testForceDownAction(): void
    {
        $server = $this->createServerWithIp(false, true, true);

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/force-down', $server->getUid()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $this->getEntityManager()->clear();

        $server = $this->getEntityManager()->find(Server::class, $server->getId());
        self::assertFalse($server->getForcedState());
        self::assertCurrentServerStatus($server, new DateTimeImmutable(), false, ServerCurrentStatus::REASON_FORCED);
        self::assertServerLastDownStatus($server, new DateTimeImmutable(), ServerLastDownStatus::REASON_FORCED);
    }

    public function testForceDownActionUpdatesExistingStatus(): void
    {
        $server = $this->createServerWithIp(false, true, true);
        $this->createServerCurrentStatus(
            $server,
            new DateTimeImmutable('-1 day'),
            true,
            ServerCurrentStatus::REASON_AUTO,
        );
        $this->createServerLastDown($server, new DateTimeImmutable('- 2 days'), ServerLastDownStatus::REASON_AUTO);

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/force-down', $server->getUid()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $this->getEntityManager()->clear();

        $server = $this->getEntityManager()->find(Server::class, $server->getId());
        self::assertFalse($server->getForcedState());
        self::assertCurrentServerStatus($server, new DateTimeImmutable(), false, ServerCurrentStatus::REASON_FORCED);
        self::assertServerLastDownStatus($server, new DateTimeImmutable(), ServerLastDownStatus::REASON_FORCED);
    }

    public function testForceDownActionWithForcedDownServer(): void
    {
        $server = $this->createServerWithIp(true, true, true);
        $server->setForcedState(false);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/force-down', $server->getUid()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_CONFLICT, $response->getStatusCode());

        $this->getEntityManager()->clear();

        $server = $this->getEntityManager()->find(Server::class, $server->getId());
        self::assertFalse($server->getForcedState()); // still forced down
    }

    public function testForceDownActionWithNonExistentServer(): void
    {
        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/force-down', 123),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['Server with uid #123 does not exist.'],
            ],
            json_decode($response->getContent(), true),
        );
    }
}
