<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Controller\Internal\Server;

use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Entity\Legacy\ServerCurrentStatus;
use Cdn77\NxgApi\Entity\Legacy\ServerLastDownStatus;
use Cdn77\NxgApi\Repository\Legacy\ServerCurrentStatusRepository;
use Cdn77\NxgApi\Repository\Legacy\ServerLastDownStatusRepository;
use DateTimeImmutable;

use function abs;

trait AssertServerStatus
{
    /**
     * Allowed delta for date comparison in seconds.
     *
     * @var int
     */
    private $timeEpsilon = 2;

    private function assertCurrentServerStatus(
        Server $server,
        DateTimeImmutable $expectedTime,
        bool $expectedUp,
        string $expectedReason,
    ): void {
        $status = $this->getCurrentStatusRepository()->find($server);

        self::assertInstanceOf(ServerCurrentStatus::class, $status);
        self::assertTimeWithEpsilon($expectedTime, $status->getLastUpdateAt());
        self::assertSame($expectedUp, $status->isUp());
        self::assertSame($expectedReason, $status->getReason());
    }

    private function assertServerLastDownStatus(
        Server $server,
        DateTimeImmutable $expectedTime,
        string $expectedReason,
    ): void {
        $status = $this->getLastDownStatusRepository()->find($server);

        self::assertInstanceOf(ServerLastDownStatus::class, $status);
        self::assertTimeWithEpsilon($expectedTime, $status->getWentDownAt());
        self::assertSame($expectedReason, $status->getReason());
    }

    private function assertNoServerLastDownStatus(Server $server): void
    {
        self::assertNull($this->getLastDownStatusRepository()->find($server));
    }

    private function assertTimeWithEpsilon(DateTimeImmutable $expectedTime, DateTimeImmutable $time): void
    {
        self::assertTrue(abs($expectedTime->getTimestamp() - $time->getTimestamp()) <= $this->timeEpsilon);
    }

    private function getCurrentStatusRepository(): ServerCurrentStatusRepository
    {
        return $this->getEntityManager()->getRepository(ServerCurrentStatus::class);
    }

    private function getLastDownStatusRepository(): ServerLastDownStatusRepository
    {
        return $this->getEntityManager()->getRepository(ServerLastDownStatus::class);
    }
}
