<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\FullLogs\Application\Controller;

use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_decode;
use function sprintf;

class StatusControllerTest extends WebTestCase
{
    use NotifyResourceChangeHelper;
    use TemporaryData;

    public function testStatusDisabled(): void
    {
        $resource = $this->createTemporaryResource();

        $this->client->request(
            Request::METHOD_GET,
            sprintf('/cdn_resources/%d/full_log.json', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame(
            ['enabled' => 0],
            json_decode($response->getContent(), true),
        );
    }

    public function testStatusEnabled(): void
    {
        $resource = $this->createTemporaryResource();
        $this->enableResourceFullLog($resource);

        $this->client->request(
            Request::METHOD_GET,
            sprintf('/cdn_resources/%d/full_log.json', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame(
            ['enabled' => 1],
            json_decode($response->getContent(), true),
        );
    }

    public function testResourceNotFound(): void
    {
        $this->client->request(
            Request::METHOD_GET,
            '/cdn_resources/123456/full-log',
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NOT_FOUND, $response->getStatusCode());
    }
}
