<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\FullLogs\Application\Controller;

use Cdn77\NxgApi\Entity\Legacy\ResourceFullLog;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function sprintf;

final class DisableControllerTest extends WebTestCase
{
    use NotifyResourceChangeHelper;
    use TemporaryData;

    public function testDisable(): void
    {
        $resource = $this->createTemporaryResource();
        $this->enableResourceFullLog($resource);

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/cdn_resources/%d/full_log/disable.json', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertNull($this->getEntityManager()->find(ResourceFullLog::class, $resource->getId()));
        self::assertSingleUpdatingMessageInQueue($resource->getId());
    }

    public function testDisableOnAlreadyDisabled(): void
    {
        $resource = $this->createTemporaryResource();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/cdn_resources/%d/full_log/disable.json', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertNull($this->getEntityManager()->find(ResourceFullLog::class, $resource->getId()));
        self::assertNoMessageInQueue();
    }

    public function testResourceNotFound(): void
    {
        $this->client->request(
            Request::METHOD_PUT,
            '/cdn_resources/123456/full-log/disable',
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NOT_FOUND, $response->getStatusCode());
    }
}
