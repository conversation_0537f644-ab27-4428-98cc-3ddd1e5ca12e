<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\LetsEncrypt\Request;
use Webmozart\Assert\Assert;

trait EntityGetter
{
    private function getResource(int $id): CdnResource
    {
        $resource = $this->getEntityManager()->find(CdnResource::class, $id);
        Assert::isInstanceOf($resource, CdnResource::class);

        return $resource;
    }

    private function getSsl(CdnResource $resource): Ssl
    {
        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resource->getId());
        Assert::isInstanceOf($ssl, Ssl::class);

        return $ssl;
    }

    private function getRequest(string $id): Request
    {
        $request = $this->getEntityManager()->find(Request::class, $id);
        Assert::isInstanceOf($request, Request::class);

        return $request;
    }
}
