<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional;

use Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeaderSchema;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

use function array_combine;
use function array_keys;
use function array_map;
use function count;
use function is_array;
use function Safe\json_decode;
use function sprintf;

trait ResponseEvaluateHelper
{
    /** @param Response|ResponseDecoded $response */
    private function evaluateUnprocessableEntityResponse(
        $response,
        string $errorMsg,
        string $field,
        string $fieldPrefix = 'cdn_resource',
    ): void {
        if ($response instanceof Response) {
            $response = ResponseDecoded::fromResponse($response);
        }

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);

        $validResponse = $this->prepareFailedResponse([$errorMsg], [$field => [$errorMsg]], $fieldPrefix);

        self::assertEquals($validResponse, $response->decodedContent);

        self::assertNoMessageInQueue();
    }

    /** @param array<string> $errors */
    private function evaluateFailedResponse(ResponseDecoded $response, array $errors, int $statusCode): void
    {
        self::assertSame($statusCode, $response->statusCode);

        self::assertEquals($this->prepareFailedResponse($errors), $response->decodedContent);
    }

    /**
     * @param array<string> $errors
     * @param array<string, array<string>> $fields
     *
     * @return array<string, array<array<string>|string>|false>
     */
    private function prepareFailedResponse(
        array $errors,
        array $fields = [],
        string $fieldsPrefix = 'cdn_resource',
    ): array {
        return ['errors' => $errors]
            + (
            count($fields) > 0
                ? [
                    'fields' => array_combine(
                        array_map(
                            static fn (string $s): string => $fieldsPrefix === ''
                                ? $s
                                : sprintf('%s.%s', $fieldsPrefix, $s),
                            array_keys($fields),
                        ),
                        $fields,
                    ),
                ]
                : []
            );
    }

    /**
     * @param array<ResponseHeaderSchema> $responseHeaders
     *
     * @return array<array<string, string>>
     */
    private function convertResponseHeadersToArray(array $responseHeaders): array
    {
        return array_map(
            static fn (ResponseHeaderSchema $header) => ['name' => $header->name, 'value' => $header->value],
            $responseHeaders,
        );
    }
}
