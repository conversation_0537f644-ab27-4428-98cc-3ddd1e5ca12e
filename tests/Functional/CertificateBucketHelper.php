<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional;

use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\CertificateBucket;
use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use League\Flysystem\FilesystemOperator;
use Throwable;
use Webmozart\Assert\Assert;

trait CertificateBucketHelper
{
    private const string CERT_1_PEM = 'cert1';
    private const string CERT_1_KEY = 'key1';

    private function cleanBucketAndPrepareCertificate1(): void
    {
        $filesystem = self::$container->get(FilesystemOperator::class);
        Assert::isInstanceOf($filesystem, FilesystemOperator::class);

        $this->clean($filesystem);

        $filesystem->write('0000000001_1.pem', self::CERT_1_PEM);
        $filesystem->write('0000000001_1.key', self::CERT_1_KEY);
    }

    public function clean(FilesystemOperator $filesystem): void
    {
        $files = $filesystem->listContents('')->toArray();
        foreach ($files as $file) {
            try {
                $filesystem->delete($file['path']);
            } catch (Throwable) {
                // Ignore deletion errors
            }
        }
    }

    private function createCertificatePairAndSaveToBucket(SslFile $sslFile): CertificatePair
    {
        $certificatePair = (new CertificatePairGenerator())->generateRandomCertificatePair();

        $bucket = self::$container->get(CertificateBucket::class);
        Assert::isInstanceOf($bucket, CertificateBucket::class);
        $bucket->save($sslFile, $certificatePair);

        return $certificatePair;
    }
}
