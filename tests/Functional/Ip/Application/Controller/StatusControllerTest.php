<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Ip\Application\Controller;

use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_decode;
use function sprintf;

class StatusControllerTest extends WebTestCase
{
    use TemporaryData;

    public function testWithNonExistentIp(): void
    {
        $response = $this->makeRequest('*******');

        self::assertSame(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['IP "*******" does not exist.'],
            ],
            json_decode($response->getContent(), true),
        );
    }

    public function testWithExistingIp(): void
    {
        $server = $this->createServer(false);
        $server->setForcedState(null);

        $ip = $this->createIp($server, true, true);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($ip->getIp());

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertEquals(
            [
                'ip' => $ip->getIp(),
                'up' => $ip->isUp(),
                'primary' => $ip->isPrimary(),
                'server' => [
                    'uid' => $server->getUid(),
                    'auto_up' => true,
                    'up' => true,
                    'paused' => false,
                ],
            ],
            json_decode($response->getContent(), true),
        );
    }

    private function makeRequest(string $ip): Response
    {
        $this->client->request(
            Request::METHOD_GET,
            sprintf('/internal/ip/%s', $ip),
            [],
            [],
            static::getDefaultHeaders(),
        );

        return $this->client->getResponse();
    }
}
