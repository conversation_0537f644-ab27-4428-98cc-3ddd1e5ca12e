<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional;

use Symfony\Component\HttpFoundation\Response;

use function is_array;
use function Safe\json_decode;

final class ResponseDecoded
{
    /** @var array<mixed> mixed */
    public array $decodedContent;
    public int $statusCode;

    public Response $response;

    /** @param array<mixed> $decodedContent */
    public function __construct(Response $response, int $statusCode, array $decodedContent)
    {
        $this->response = $response;
        $this->statusCode = $statusCode;
        $this->decodedContent = $decodedContent;
    }

    public static function fromResponse(Response $response): self
    {
        $decodedContent = $response->getContent() === '' ? [] : json_decode((string) $response->getContent(), true);

        return new self(
            $response,
            $response->getStatusCode(),
            is_array($decodedContent) ? $decodedContent : [],
        );
    }
}
