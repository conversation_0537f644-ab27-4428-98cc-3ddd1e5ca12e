<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Server\Application\Controller;

use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function ksort;
use function Safe\json_decode;
use function Safe\json_encode;
use function sort;

final class ServerIdListControllerTest extends WebTestCase
{
    use TemporaryData;

    public function testListResponseForResourceIds(): void
    {
        $locationA = $this->createLocation('tmpCZ', 'tmp', 'CZ', 'EU');
        $locationB = $this->createLocation('tmpDE', 'tmpzie', 'DE', 'EU');

        $popA = $this->createPop($locationA);
        $popB = $this->createPop($locationB);
        $popC = $this->createPop($locationB);
        $popD = $this->createPop($locationB);

        $groupA = $this->createLocationGroup();
        $groupA->getPops()->add($popA);
        $groupA->getPops()->add($popB);
        $groupB = $this->createLocationGroup();
        $groupB->getPops()->add($popB);
        $groupC = $this->createLocationGroup();
        $groupC->getPops()->add($popC);
        $groupC->getPops()->add($popD);

        $serverA1 = $this->createServer(false);
        $serverA1->setPop($popA);
        $this->createIp($serverA1, true);
        $serverA2 = $this->createServer(false);
        $serverA2->setPop($popA);
        $this->createIp($serverA2, true);

        $serverB = $this->createServer(false);
        $serverB->setPop($popB);
        $this->createIp($serverB, true);
        $this->createIp($serverB, true, false);

        $serverC = $this->createServer(false);
        $serverC->setPop($popC);
        $this->createIp($serverC, true);

        $serverD = $this->createServer(false);
        $serverD->setPop($popD);
        $this->createIp($serverD, true, false);

        $serverWithoutIp = $this->createServer(false);
        $serverWithoutIp->setPop($popB);

        $resourceA = $this->createResourceWithAccount($groupA);
        $customLocationA = $this->createCustomLocation($groupA, $resourceA);
        $customLocationA->getPops()->add($popA);

        $resourceB = $this->createResourceWithAccount($groupB);
        $resourceC = $this->createResourceWithAccount($groupC);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_POST,
            '/internal/resource/servers',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([$resourceA->getId(), $resourceB->getId(), $resourceC->getId()]),
        );
        $response = $this->client->getResponse();

        $resourceAServers = [$serverA1->getUid(), $serverA2->getUid()];
        $resourceBServers = [$serverB->getUid()];
        $resourceCServers = [$serverC->getUid()];

        sort($resourceAServers);
        sort($resourceBServers);
        sort($resourceCServers);

        $expectedResponse = [
            $resourceA->getId() => $resourceAServers,
            $resourceB->getId() => $resourceBServers,
            $resourceC->getId() => $resourceCServers,
        ];
        ksort($expectedResponse);

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());

        $response = json_decode($response->getContent(), true);
        ksort($response);

        self::assertSame($expectedResponse, $response);
    }

    public function testBadRequest(): void
    {
        $this->client->request(
            Request::METHOD_POST,
            '/internal/resource/servers',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([]),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
    }
}
