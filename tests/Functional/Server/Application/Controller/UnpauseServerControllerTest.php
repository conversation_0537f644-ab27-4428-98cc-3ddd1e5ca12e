<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Server\Application\Controller;

use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Entity\Legacy\ServerCurrentStatus;
use Cdn77\NxgApi\Entity\Legacy\ServerLastDownStatus;
use Cdn77\NxgApi\Tests\Functional\Controller\Internal\Server\AssertResponseError;
use Cdn77\NxgApi\Tests\Functional\Controller\Internal\Server\AssertServerStatus;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use DateTimeImmutable;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function sprintf;

class UnpauseServerControllerTest extends WebTestCase
{
    use TemporaryData;
    use AssertResponseError;
    use AssertServerStatus;

    public function testUnpauseAction(): void
    {
        $server = $this->createServerWithIp(true, true, true);

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/unpause', $server->getUid()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $this->getEntityManager()->clear();

        $server = $this->getEntityManager()->find(Server::class, $server);
        self::assertFalse($server->isPaused());
        self::assertCurrentServerStatus($server, new DateTimeImmutable(), true, ServerCurrentStatus::REASON_AUTO);
        self::assertNoServerLastDownStatus($server);
    }

    public function testUnpauseActionUpdatesExistingStatus(): void
    {
        $server = $this->createServerWithIp(true, true, true);
        $this->createServerCurrentStatus(
            $server,
            new DateTimeImmutable('- 1 day'),
            false,
            ServerCurrentStatus::REASON_FORCED,
        );
        $this->createServerLastDown($server, new DateTimeImmutable('- 1 day'), ServerLastDownStatus::REASON_FORCED);

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/unpause', $server->getUid()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $this->getEntityManager()->clear();

        $server = $this->getEntityManager()->find(Server::class, $server);
        self::assertFalse($server->isPaused());
        self::assertCurrentServerStatus($server, new DateTimeImmutable(), true, ServerCurrentStatus::REASON_AUTO);
        self::assertServerLastDownStatus(
            $server,
            new DateTimeImmutable('- 1 day'),
            ServerLastDownStatus::REASON_FORCED,
        );
    }

    public function testUnpauseActionWithUnpausedServer(): void
    {
        $server = $this->createServerWithIp(false, true, true);

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/unpause', $server->getUid()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_CONFLICT, $response->getStatusCode());
        self::assertResponseError($response, sprintf('Server #%s is already unpaused.', $server->getUid()));

        $this->getEntityManager()->clear();

        $server = $this->getEntityManager()->find(Server::class, $server->getId());
        self::assertFalse($server->isPaused()); // still unpaused
    }

    public function testWithNonExistentServer(): void
    {
        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/unpause', 123),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        self::assertResponseError($response, 'Server with uid #123 does not exist.');
    }
}
