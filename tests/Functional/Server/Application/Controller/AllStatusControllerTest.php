<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Server\Application\Controller;

use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\Ip\Application\Controller\StatusControllerTest;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_decode;
use function usort;

/** @see StatusControllerTest */
class AllStatusControllerTest extends WebTestCase
{
    use TemporaryData;

    public function testWithNoServers(): void
    {
        $response = $this->makeRequest();

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertEquals(
            [],
            json_decode($response->getContent(), true),
        );
    }

    public function testWithMultipleServers(): void
    {
        $serverA = $this->createServer(false); // no IP - won't be present

        $serverB = $this->createServer(false); // no primary IP - won't be present
        $this->createIp($serverB, true, false);

        $serverC = $this->createServer(false);
        $this->createIp($serverC, true, true);

        $serverD = $this->createServer(false);
        $this->createIp($serverD, true, true);
        $serverD->setForcedState(true);

        $serverE = $this->createServer(true);
        $this->createIp($serverE, true, true);
        $serverE->setForcedState(false);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest();

        $expectedData = [
            [
                'uid' => $serverC->getUid(),
                'auto_up' => true,
                'up' => true,
                'paused' => false,
            ],
            [
                'uid' => $serverD->getUid(),
                'auto_up' => true,
                'up' => true,
                'paused' => false,
                'forced' => 'up',
            ],
            [
                'uid' => $serverE->getUid(),
                'auto_up' => true,
                'up' => false,
                'paused' => true,
                'forced' => 'down',
            ],
        ];
        $responseData = json_decode($response->getContent(), true);

        $this->sortData($expectedData);
        $this->sortData($responseData);

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertEquals($expectedData, $responseData);
    }

    /** @param mixed[] $data */
    private function sortData(array &$data): void
    {
        usort($data, static fn (array $a, array $b): int => $a['uid'] <=> $b['uid']);
    }

    private function makeRequest(): Response
    {
        $this->client->request(
            Request::METHOD_GET,
            '/internal/servers/statuses',
            [],
            [],
            static::getDefaultHeaders(),
        );

        return $this->client->getResponse();
    }
}
