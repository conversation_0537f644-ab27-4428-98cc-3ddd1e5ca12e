<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Server\Application\Controller;

use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Entity\Legacy\ServerCurrentStatus;
use Cdn77\NxgApi\Entity\Legacy\ServerLastDownStatus;
use Cdn77\NxgApi\Tests\Functional\Controller\Internal\Server\AssertResponseError;
use Cdn77\NxgApi\Tests\Functional\Controller\Internal\Server\AssertServerStatus;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use DateTimeImmutable;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function sprintf;

class PauseServerControllerTest extends WebTestCase
{
    use TemporaryData;
    use AssertResponseError;
    use AssertServerStatus;

    public function testPauseAction(): void
    {
        $server = $this->createServerWithIp(false, true, true);

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/pause', $server->getUid()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $this->getEntityManager()->clear();

        $server = $this->getEntityManager()->find(Server::class, $server->getId());
        self::assertTrue($server->isPaused());
        self::assertCurrentServerStatus($server, new DateTimeImmutable(), false, ServerCurrentStatus::REASON_FORCED);
        self::assertServerLastDownStatus($server, new DateTimeImmutable(), ServerLastDownStatus::REASON_FORCED);
    }

    public function testPauseActionUpdatesExistingStatus(): void
    {
        $server = $this->createServerWithIp(false, true, true);
        $this->createServerCurrentStatus(
            $server,
            new DateTimeImmutable('- 1 day'),
            true,
            ServerCurrentStatus::REASON_AUTO,
        );
        $this->createServerLastDown($server, new DateTimeImmutable('- 2 day'), ServerLastDownStatus::REASON_AUTO);

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/pause', $server->getUid()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $this->getEntityManager()->clear();

        $server = $this->getEntityManager()->find(Server::class, $server->getId());
        self::assertTrue($server->isPaused());
        self::assertCurrentServerStatus($server, new DateTimeImmutable(), false, ServerCurrentStatus::REASON_FORCED);
        self::assertServerLastDownStatus($server, new DateTimeImmutable(), ServerLastDownStatus::REASON_FORCED);
    }

    public function testPauseActionWithPausedServer(): void
    {
        $server = $this->createServerWithIp(true, true, true);

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/pause', $server->getUid()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_CONFLICT, $response->getStatusCode());
        self::assertResponseError($response, sprintf('Server #%s is already paused.', $server->getUid()));

        $this->getEntityManager()->clear();

        $server = $this->getEntityManager()->find(Server::class, $server->getId());
        self::assertTrue($server->isPaused()); // still paused
    }

    public function testWithNonExistentServer(): void
    {
        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/internal/server/%d/pause', 123),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        self::assertResponseError($response, 'Server with uid #123 does not exist.');
    }
}
