<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Server\Domain;

use Cdn77\NxgApi\Server\Domain\ServerPauser;
use Cdn77\NxgApi\Service\Server\Exception\ServerAlreadyPaused;
use Cdn77\NxgApi\Service\Server\Exception\ServerAlreadyUnpaused;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\KernelTestCase;

class ServerPauserTest extends KernelTestCase
{
    use TemporaryData;

    public function testPause(): void
    {
        $server = $this->createServerWithIp(false, true, true);
        $pauser = $this->getPauser();

        $pauser->pause($server);

        $this->getEntityManager()->refresh($server);

        self::assertTrue($server->isPaused());
    }

    public function testUnpause(): void
    {
        $server = $this->createServerWithIp(true, true, true);
        $pauser = $this->getPauser();

        $pauser->unpause($server);

        $this->getEntityManager()->refresh($server);

        self::assertFalse($server->isPaused());
    }

    public function testPauseWithPausedServer(): void
    {
        $this->expectException(ServerAlreadyPaused::class);
        $server = $this->createServerWithIp(true, true, true);
        $this->getPauser()->pause($server);
    }

    public function testUnpauseWithUnpausedServer(): void
    {
        $this->expectException(ServerAlreadyUnpaused::class);
        $this->getPauser()->unpause($this->createServer(false));
    }

    private function getPauser(): ServerPauser
    {
        return static::$kernel->getContainer()->get(ServerPauser::class);
    }
}
