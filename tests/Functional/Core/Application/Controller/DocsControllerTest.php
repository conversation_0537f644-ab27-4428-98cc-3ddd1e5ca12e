<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Core\Application\Controller;

use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class DocsControllerTest extends WebTestCase
{
    public function testGetOpenApiDocsJson(): void
    {
        $this->client->request(
            Request::METHOD_GET,
            '/docs/openapi.json',
            [],
            [],
            static::getDefaultHeaders(),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
    }
}
