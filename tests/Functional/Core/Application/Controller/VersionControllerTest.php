<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Core\Application\Controller;

use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Versioning\Versions;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class VersionControllerTest extends WebTestCase
{
    public function testWorks(): void
    {
        $this->client->request(
            Request::METHOD_GET,
            '/version',
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());

        $version = Versions::VERSION_1_0_0;
        self::assertJsonStringEqualsJsonString(
            <<<JSON
            {"version":"$version"}
            JSON,
            $response->getContent(),
        );
    }
}
