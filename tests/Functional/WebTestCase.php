<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional;

use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase as SymfonyWebTestCase;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Dotenv\Dotenv;

use function getenv;

abstract class WebTestCase extends SymfonyWebTestCase
{
    /** @var ContainerInterface */
    protected static $container;

    /** @var KernelBrowser */
    protected $client = null;

    /** @return string[] */
    protected static function getDefaultHeaders(): array
    {
        return ['CONTENT_TYPE' => 'application/json'];
    }

    protected function setUp(): void
    {
        if (! getenv('APP_ENV')) {
            (new Dotenv())->load(__DIR__ . '/../../.env.test');
        }

        parent::setUp();

        if ($this->client === null) {
            $this->client = static::createClient();
        }

        self::$container = self::$kernel->getContainer();
    }
}
