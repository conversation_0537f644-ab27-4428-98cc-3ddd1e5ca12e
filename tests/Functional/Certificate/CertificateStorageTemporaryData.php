<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Certificate;

use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use League\Flysystem\FilesystemOperator;
use Ramsey\Uuid\Uuid;

use function assert;
use function basename;
use function sprintf;
use function str_ends_with;
use function str_replace;

trait CertificateStorageTemporaryData
{
    private FilesystemOperator $certificateStorage;

    protected function setUpCertificateStorage(): void
    {
        $filesystem = self::$container->get('oneup_flysystem.certificate_storage_filesystem');
        assert($filesystem instanceof FilesystemOperator);
        $this->certificateStorage = $filesystem;
        $this->cleanCertificateStorage();
    }

    protected function cleanCertificateStorage(): void
    {
        $this->certificateStorage->deleteDirectory('');
    }

    /** @return array<string, CertificatePair> */
    protected function prepareStoredKeys(string $accountDirName, int $count = 3): array
    {
        $this->certificateStorage->createDirectory($accountDirName);

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePairs = [];

        for ($i = 0; $i < $count; $i++) {
            $uuid = Uuid::uuid4()->toString();
            $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();
            $certificatePairs[$uuid] = $certificatePair;

            $this->storePrivateKey($accountDirName, $uuid, $certificatePair);
        }

        return $certificatePairs;
    }

    protected function storePrivateKey(string|int $accountDirName, string $uuid, CertificatePair $certificatePair): void
    {
        $accountDirName = (string) $accountDirName;

        if (! $this->certificateStorage->directoryExists($accountDirName)) {
            $this->certificateStorage->createDirectory($accountDirName);
        }

        $keyFilePath = sprintf('%s/%s.key', $accountDirName, $uuid);
        $this->certificateStorage->write($keyFilePath, $certificatePair->getPrivateKey());
    }

    protected function accountDirectoryExists(string $accountId): bool
    {
        return $this->certificateStorage->directoryExists($accountId);
    }

    protected function keyFileExists(string $accountId, string $uuid): bool
    {
        return $this->certificateStorage->fileExists(sprintf('%s/%s.key', $accountId, $uuid));
    }

    /** @return list<string> */
    protected function getAccountDirectoryContents(string $accountId): array
    {
        if (! $this->accountDirectoryExists($accountId)) {
            return [];
        }

        return $this->certificateStorage->listContents($accountId)
            ->filter(static fn ($attributes) => $attributes->isFile() && str_ends_with($attributes->path(), '.key'))
            ->map(static fn ($attributes) => str_replace('.key', '', basename($attributes->path())))
            ->toArray();
    }

    protected function generateCertificatePair(): CertificatePair
    {
        return (new CertificatePairGenerator())->generateRandomCertificatePair();
    }

    protected function getStoredPrivateKeyContent(string $accountId, string $uuid): string
    {
        $privateKeyFile = sprintf('%s/%s.key', $accountId, $uuid);

        return $this->certificateStorage->read($privateKeyFile);
    }

    protected function verifyStoredKeyContent(string $accountId, string $uuid, CertificatePair $expectedPair): void
    {
        $storedPrivateKey = $this->getStoredPrivateKeyContent($accountId, $uuid);
        self::assertSame($storedPrivateKey, $expectedPair->getPrivateKey());
    }
}
