<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Certificate;

use Cdn77\NxgApi\Certificate\Domain\CertificateCleaner;
use Cdn77\NxgApi\Certificate\Domain\Enum\MoveOnlyParameter;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\KernelTestCase;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use DateTimeImmutable;
use Generator;
use League\Flysystem\DirectoryListing;
use League\Flysystem\StorageAttributes;

use function count;
use function Safe\preg_match;
use function sprintf;
use function strpos;

class CertificateCleanerTest extends KernelTestCase
{
    private const int STARTING_MESSAGES_COUNT = 5;
    use EntityGetter;
    use TemporaryData;
    use CertificateCleanerTemporaryData;

    public function testOk(): void
    {
        [$resource, $expiration] = $this->prepareResourceWithSslFiles(-3, 2);
        $ssl = $this->getSsl($resource);

        $this->certificateCleaner->clean($this->outputWriter);

        self::assertCount(5, $ssl->getFiles());

        $this->validateFilesForResourceInCold($resource, [0, 1, 2], $expiration);
        $this->validateFilesForResourceInBucket($resource, [3, 4]);

        self::assertCount(4, $this->bucketFilesystem->listContents('')->toArray());
    }

    public function testLimit(): void
    {
        [$resource, $expiration] = $this->prepareResourceWithSslFiles(-3, 2);

        $limit = 1;
        $this->certificateCleaner->clean($this->outputWriter, $limit);

        self::assertCount(5, $this->getSsl($resource)->getFiles());

        $this->validateFilesForResourceInCold($resource, [0], $expiration);
        $this->validateFilesForResourceInBucket($resource, [1, 2, 3, 4]);

        self::assertCount(8, $this->bucketFilesystem->listContents('')->toArray());

        $this->validateMsgInLogOnPosition(sprintf('Moving limit %d reached', $limit), 4);
    }

    public function testWithOnlyOneAssignedCertificate(): void
    {
        [$resource, $expiration] = $this->prepareResourceWithSslFiles(1, 2);
        $ssl = $this->getSsl($resource);

        $this->certificateCleaner->clean($this->outputWriter);

        self::assertCount(1, $ssl->getFiles());

        $this->validateFilesForResourceInCold($resource, [], $expiration);
        $this->validateFilesForResourceInBucket($resource, [0]);

        self::assertCount(2, $this->bucketFilesystem->listContents('')->toArray());

        $this->validateMsgInLogOnPosition(
            sprintf('Skipping "%d_0". Reason: Not ready to archive', $resource->getId()),
            1,
        );
    }

    public function testWithOneAssignedAndOneBackupCertificate(): void
    {
        [$resource, $expiration] = $this->prepareResourceWithSslFiles(0, 2);
        $ssl = $this->getSsl($resource);

        $this->certificateCleaner->clean($this->outputWriter);

        self::assertCount(2, $ssl->getFiles());

        $this->validateFilesForResourceInCold($resource, [], $expiration);
        $this->validateFilesForResourceInBucket($resource, [0, 1]);

        self::assertCount(4, $this->bucketFilesystem->listContents('')->toArray());
    }

    public function testWithAssignedIndexLowerThanMaxIndex(): void
    {
        [$resource, $expiration] = $this->prepareResourceWithSslFiles(-3, 3);
        $ssl = $this->getSsl($resource);
        $ssl->setAssignedIndex(3);

        $this->getEntityManager()->persist($ssl);
        $this->getEntityManager()->flush();

        $this->certificateCleaner->clean($this->outputWriter);

        self::assertCount(6, $ssl->getFiles());

        $this->validateFilesForResourceInCold($resource, [0, 1], $expiration);
        $this->validateFilesForResourceInBucket($resource, [2, 3, 4, 5]);

        self::assertCount(8, $this->bucketFilesystem->listContents('')->toArray());
    }

    public function testImportantAccountExcluded(): void
    {
        $account = $this->createAccount(1);
        $group = $this->createLocationGroup();
        $resourceOfVipAccount = $this->createResource($account, $group);

        [$resourceOfVipAccount, $expirationA] = $this->prepareResourceWithSslFiles(-2, 2, 365, $resourceOfVipAccount);
        $sslA = $this->getSsl($resourceOfVipAccount);

        [$resourceOfDefaultAccount, $expirationB] = $this->prepareResourceWithSslFiles(-3, 2, 60);
        $sslB = $this->getSsl($resourceOfDefaultAccount);

        $this->certificateCleaner->clean($this->outputWriter);

        self::assertCount(4, $sslA->getFiles());
        self::assertCount(5, $sslB->getFiles());

        $this->validateFilesForResourceInCold($resourceOfVipAccount, [], $expirationA);
        $this->validateFilesForResourceInBucket($resourceOfVipAccount, [0, 1, 2, 3]);

        $this->validateFilesForResourceInCold($resourceOfDefaultAccount, [0, 1, 2], $expirationB);
        $this->validateFilesForResourceInBucket($resourceOfDefaultAccount, [3, 4]);

        self::assertCount(12, $this->bucketFilesystem->listContents('')->toArray());

        $this->validateMsgInLog(
            sprintf('Skipping "%d_\d+"\. Reason: Owner is important account with ID 1', $resourceOfVipAccount->getId()),
            8,
        );

        $this->validateMsgInLog(
            sprintf(
                'Moving "%d_\d+". Reason: Ready to archive from account ID %d',
                $resourceOfDefaultAccount->getId(),
                $resourceOfDefaultAccount->getAccount()->getId(),
            ),
            3,
        );
    }

    public function testWithSslWithoutFiles(): void
    {
        $expiration = new DateTimeImmutable(sprintf('+%d days', 365));

        $resource = $this->createTemporaryResource();
        $ssl = $this->enableResourceSsl($resource);

        $this->certificateCleaner->clean($this->outputWriter);

        self::assertCount(0, $ssl->getFiles());

        $this->validateFilesForResourceInCold($resource, [], $expiration);
        $this->validateFilesForResourceInBucket($resource, []);

        self::assertCount(0, $this->bucketFilesystem->listContents('')->toArray());
    }

    public function testWithFilesWithoutSsl(): void
    {
        [$resourceWithInactiveSsl, $expirationInactiveSsl] = $this->prepareResourceWithSslFiles(-2, 2);
        $this->getEntityManager()->remove($this->getSsl($resourceWithInactiveSsl));

        FlushAndClear::do($this->getEntityManager());

        $movedCount = $this->certificateCleaner->clean($this->outputWriter);

        self::assertSame(2, $movedCount);

        $this->validateFilesForResourceInCold($resourceWithInactiveSsl, [0, 1], $expirationInactiveSsl);
        $this->validateFilesForResourceInBucket($resourceWithInactiveSsl, [2, 3]);

        $this->validateMsgInLog(
            sprintf(
                'Moving "%d_\d+". Reason: Ready to archive from account ID %d',
                $resourceWithInactiveSsl->getId(),
                $resourceWithInactiveSsl->getAccount()->getId(),
            ),
            2,
        );

        $this->validateMsgInLog(
            sprintf(
                'Skipping "%d_\d+". Reason: Not ready to archive',
                $resourceWithInactiveSsl->getId(),
            ),
            4,
        );
    }

    public function testWithOneFileWithoutSsl(): void
    {
        [$resourceWithInactiveSsl, $expirationInactiveSsl] = $this->prepareResourceWithSslFiles(0, 1);
        $this->getEntityManager()->remove($this->getSsl($resourceWithInactiveSsl));

        FlushAndClear::do($this->getEntityManager());

        $movedCount = $this->certificateCleaner->clean($this->outputWriter);

        self::assertSame(0, $movedCount);

        $this->validateFilesForResourceInCold($resourceWithInactiveSsl, [], $expirationInactiveSsl);
        $this->validateFilesForResourceInBucket($resourceWithInactiveSsl, [0]);

        $this->validateMsgInLog(
            sprintf(
                'Skipping "%d_\d+". Reason: Not ready to archive',
                $resourceWithInactiveSsl->getId(),
            ),
            2,
        );
    }

    public function testArchiveCertificatesForIdInUsedIds(): void
    {
        $this->addUsedResourceIdToDb((int) self::USED_RESOURCE_ID);
        $this->storeCertificatePairInBucket(sprintf('%d_1', self::USED_RESOURCE_ID));

        $this->certificateCleaner->clean($this->outputWriter);

        $this->validateMsgInLog('No errors occurred');
        $this->validateMsgInLog(sprintf('Moving "%d_\d+". Reason: ID in used resource IDs', self::USED_RESOURCE_ID));
        $this->validateMsgInLog(sprintf('File "%d_\d+\.(pem|key)" moved to .*', self::USED_RESOURCE_ID), 2);
    }

    public function testError(): void
    {
        $this->addUsedResourceIdToDb((int) self::USED_RESOURCE_ID);

        $filename = sprintf('%d_1', self::USED_RESOURCE_ID);
        $this->bucketFilesystem->write(sprintf('%s.pem', $filename), 'not_valid_certificate');

        $this->certificateCleaner->clean($this->outputWriter);

        $this->validateMsgInLog('Errors count: 1');
        $this->validateMsgInLog(sprintf(
            'Moving failed for file "%s". Exception: Failed to parse certificate from file %s',
            sprintf('%s.pem', $filename),
            $filename,
        ), 2);
    }

    public function testArchiveOnlyResourcesInUsedIds(): void
    {
        [$resourceNotInUsed, $expiration] = $this->prepareResourceWithSslFiles(-2, 1);

        $this->addUsedResourceIdToDb((int) self::USED_RESOURCE_ID);
        $this->storeCertificatePairInBucket(sprintf('%d_1', self::USED_RESOURCE_ID));

        $this->certificateCleaner->clean($this->outputWriter, moveOnly: MoveOnlyParameter::UsedIds);

        $this->validateFilesForResourceInCold($resourceNotInUsed, [], $expiration);
        $this->validateFilesForResourceInBucket($resourceNotInUsed, [0, 1, 2]);

        $this->validateMsgInLog(
            sprintf(
                'Skipping "%d_0". Reason: Not in used resource IDs and moveOnlyUsedResourceIds is enabled',
                $resourceNotInUsed->getId(),
            ),
            2,
        );

        $this->validateMsgInLog(sprintf('Moving "%d_\d+". Reason: ID in used resource IDs', self::USED_RESOURCE_ID));
        $this->validateMsgInLog(sprintf('File "%d_\d+\.(pem|key)" moved to .*', self::USED_RESOURCE_ID), 2);
    }

    public function testDryRun(): void
    {
        [$resource, $expiration] = $this->prepareResourceWithSslFiles(-1, 2);

        $this->certificateCleaner->clean($this->outputWriter, 10, true);

        $this->validateFilesForResourceInCold($resource, [], $expiration);
        $this->validateFilesForResourceInBucket($resource, [0, 1, 2]);

        $this->validateMsgInLog(
            sprintf(
                'Dry run for file "%d_\d+\.(pem|key)". File is readable. Target location would be',
                $resource->getId(),
            ),
            2,
        );
    }

    /** @dataProvider providerNames */
    public function testIgnoringFilesForResource1AndUnknownFilenames(
        string $filename,
        string $expectedLogMsg,
        int $expectedLogCount = 1,
    ): void {
        $this->storeCertificatePairInBucket($filename);

        $this->certificateCleaner->clean($this->outputWriter);

        $files = $this->bucketFilesystem->listContents('')->sortByPath()->toArray();
        self::assertCount(2, $files);
        self::assertSame($filename . '.key', $files[0]->path());
        self::assertSame($filename . '.pem', $files[1]->path());

        $this->validateMsgInLog($expectedLogMsg, $expectedLogCount);
    }

    /** @return Generator<string, array<string|int>> */
    public static function providerNames(): Generator
    {
        yield 'resource 1 variants 1' => [
            '**********',
            'Skipping "**********.key". Reason: Special certificate for resource 1',
        ];

        yield 'resource 1 variants 2' => [
            '**********_1',
            'Skipping "**********_1.key". Reason: Special certificate for resource 1',
        ];

        yield 'resource 1 variants 3' => [
            '**********_100',
            'Skipping "**********_100.key". Reason: Special certificate for resource 1',
        ];

        yield 'resource 1 variants 4' => ['1', 'Skipping "1.key". Reason: Invalid name'];
        yield 'resource 1 variants 5' => ['1_1', 'Skipping "1_1.key". Reason: Invalid name'];
        yield 'resource 1 variants 6' => ['1_100', 'Skipping "1_100.key". Reason: Invalid name'];

        yield 'unknown name 1' => ['test', 'Skipping "test.key". Reason: Invalid name'];
        yield 'unknown name 2' => ['test_1', 'Skipping "test_1.key". Reason: Invalid name'];
        yield 'unknown name 3' => ['test_100', 'Skipping "test_100.key". Reason: Invalid name'];

        yield 'valid resource ID without any record in DB 1' => [
            '1234567890',
            'Skipping "1234567890.key". Reason: Invalid name',
        ];

        yield 'valid resource ID without any record in DB 2' => [
            '1234567890_1',
            'Skipping "1234567890_1". Reason: Not in DB files and also not in used resource IDs.',
            2,
        ];

        yield 'valid resource ID without any record in DB 3' => [
            '1234567890_100',
            'Skipping "1234567890_100". Reason: Not in DB files and also not in used resource IDs.',
            2,
        ];
    }

    public function testMultipleResources(): void
    {
        [$resourceA, $expirationA] = $this->prepareResourceWithSslFiles(-1, 2);
        $sslA = $this->getSsl($resourceA);

        [$resourceB, $expirationB] = $this->prepareResourceWithSslFiles(-3, 2, 60);
        $sslB = $this->getSsl($resourceB);

        $movedCount = $this->certificateCleaner->clean($this->outputWriter);

        self::assertSame(4, $movedCount);

        self::assertCount(3, $sslA->getFiles());
        self::assertCount(5, $sslB->getFiles());

        $this->validateFilesForResourceInCold($resourceA, [0], $expirationA);
        $this->validateFilesForResourceInBucket($resourceA, [1, 2]);

        $this->validateFilesForResourceInCold($resourceB, [0, 1, 2], $expirationB);
        $this->validateFilesForResourceInBucket($resourceB, [3, 4]);

        self::assertCount(8, $this->bucketFilesystem->listContents('')->toArray());

        $this->validateMsgInLog(
            sprintf(
                'Moving "%d_\d+". Reason: Ready to archive from account ID %d',
                $resourceA->getId(),
                $resourceA->getAccount()->getId(),
            ),
        );
        $this->validateMsgInLog(
            sprintf('Skipping "%d_\d+". Reason: Not ready to archive', $resourceA->getId()),
            4,
        );

        $this->validateMsgInLog(
            sprintf(
                'Moving "%d_\d+". Reason: Ready to archive from account ID %d',
                $resourceB->getId(),
                $resourceB->getAccount()->getId(),
            ),
            3,
        );
        $this->validateMsgInLog(
            sprintf('Skipping "%d_\d+". Reason: Not ready to archive', $resourceB->getId()),
            4,
        );
    }

    public function testOnlyExpired(): void
    {
        [$resource, $expirationBExpired] = $this->prepareResourceWithSslFiles(-6, -4, -60);
        $this->prepareResourceWithSslFiles(-1, 2, 60, $resource);
        $ssl = $this->getSsl($resource);

        $movedCount = $this->certificateCleaner->clean($this->outputWriter, moveOnly: MoveOnlyParameter::Expired);

        self::assertSame(2, $movedCount);
        self::assertCount(5, $ssl->getFiles());

        $this->validateFilesForResourceInCold($resource, [0, 1], $expirationBExpired);
        $this->validateFilesForResourceInBucket($resource, [2, 3, 4]);

        self::assertCount(6, $this->bucketFilesystem->listContents('')->toArray());

        $this->validateMsgInLog(
            sprintf(
                'Moving "%d_\d+". Reason: Certificate is expired so ready to archive for account ID %d',
                $resource->getId(),
                $resource->getAccount()->getId(),
            ),
            2,
        );
        $this->validateMsgInLog(
            sprintf('Skipping "%d_\d+". Reason: Certificate is not expired - not ready to archive', $resource->getId()),
            2,
        );
        $this->validateMsgInLog(
            sprintf('Skipping "%d_\d+". Reason: Not ready to archive', $resource->getId()),
            4,
        );
    }

    public function testOnlyInactiveResources(): void
    {
        $resourceInactiveLongSuspended
            = $this->createTemporaryResource(['suspended' => new DateTimeImmutable('-31 days')]);
        [$resourceInactiveLongSuspended, $expirationInactiveLongSuspended]
            = $this->prepareResourceWithSslFiles(-2, 2, 60, $resourceInactiveLongSuspended);

        $resourceInactiveDeleted = $this->createTemporaryResource(['deleted' => new DateTimeImmutable('-1 days')]);
        [$resourceInactiveDeleted, $expirationInactiveDeleted]
            = $this->prepareResourceWithSslFiles(-2, 2, 60, $resourceInactiveDeleted);

        $resourceSuspended = $this->createTemporaryResource(['suspended' => new DateTimeImmutable('-1 days')]);
        [$resourceSuspended, $expirationSuspended] = $this->prepareResourceWithSslFiles(-2, 2, 60, $resourceSuspended);

        [$resource, $expiration] = $this->prepareResourceWithSslFiles(-2, 2);

        $movedCount = $this->certificateCleaner->clean(
            $this->outputWriter,
            moveOnly: MoveOnlyParameter::InactiveResources,
        );

        self::assertSame(4, $movedCount);

        $this->validateFilesForResourceInCold($resourceInactiveLongSuspended, [0, 1], $expirationInactiveLongSuspended);
        $this->validateFilesForResourceInBucket($resourceInactiveLongSuspended, [2, 3]);

        $this->validateFilesForResourceInCold($resourceInactiveDeleted, [0, 1], $expirationInactiveDeleted);
        $this->validateFilesForResourceInBucket($resourceInactiveDeleted, [2, 3]);

        $this->validateFilesForResourceInCold($resourceSuspended, [], $expirationSuspended);
        $this->validateFilesForResourceInBucket($resourceSuspended, [0, 1, 2, 3]);

        $this->validateFilesForResourceInCold($resource, [], $expiration);
        $this->validateFilesForResourceInBucket($resource, [0, 1, 2, 3]);

        $this->validateMsgInLog(
            sprintf(
                'Moving "%d_\d+". Reason: Assigned to inactive resource so ready to archive for account ID %d',
                $resourceInactiveLongSuspended->getId(),
                $resourceInactiveLongSuspended->getAccount()->getId(),
            ),
            2,
        );

        $this->validateMsgInLog(
            sprintf(
                'Moving "%d_\d+". Reason: Assigned to inactive resource so ready to archive for account ID %d',
                $resourceInactiveDeleted->getId(),
                $resourceInactiveDeleted->getAccount()->getId(),
            ),
            2,
        );

        $this->validateMsgInLog(
            sprintf(
                'Skipping "%d_\d+". Reason: Certificate is not assigned to inactive resource - not ready to archive',
                $resourceSuspended->getId(),
            ),
            4,
        );

        $this->validateMsgInLog(
            sprintf(
                'Skipping "%d_\d+". Reason: Certificate is not assigned to inactive resource - not ready to archive',
                $resource->getId(),
            ),
            4,
        );
    }

    public function testOnlyResourcesWithInactiveSsl(): void
    {
        [$resourceWithInactiveSsl, $expirationInactiveSsl] = $this->prepareResourceWithSslFiles(-2, 2);
        FlushAndClear::do($this->getEntityManager());
        $this->getEntityManager()->remove($this->getSsl($resourceWithInactiveSsl));

        [$resource, $expiration] = $this->prepareResourceWithSslFiles(-2, 2);

        $movedCount = $this->certificateCleaner->clean($this->outputWriter, moveOnly: MoveOnlyParameter::InactiveSsl);

        self::assertSame(2, $movedCount);

        $this->validateFilesForResourceInCold($resourceWithInactiveSsl, [0, 1], $expirationInactiveSsl);
        $this->validateFilesForResourceInBucket($resourceWithInactiveSsl, [2, 3]);

        $this->validateFilesForResourceInCold($resource, [], $expiration);
        $this->validateFilesForResourceInBucket($resource, [0, 1, 2, 3]);

        $this->validateMsgInLog(
            sprintf(
                'Moving "%d_\d+". Reason: Assigned to resource with inactive SSL so ready to archive for account ID %d',
                $resourceWithInactiveSsl->getId(),
                $resourceWithInactiveSsl->getAccount()->getId(),
            ),
            2,
        );

        $this->validateMsgInLog(
            sprintf(
                'Skipping "%d_\d+". Reason: Certificate is not assigned to resource with inactive SSL - not ready to archive', //phpcs:ignore
                $resource->getId(),
            ),
            4,
        );
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->prepareServices();
        $this->cleanStorage();
    }

    private function validateMsgInLog(string $msg, int $expectedCount = 1): void
    {
        $count = 0;

        foreach ($this->logMessages as $message) {
            if (preg_match(sprintf('/%s/', $msg), $message) === 0) {
                continue;
            }

            $count++;
        }

        self::assertSame($expectedCount, $count);
    }

    private function validateMsgInLogOnPosition(string $msg, int $positionAfterStartingMessages): void
    {
        self::assertSame($msg, $this->logMessages[self::STARTING_MESSAGES_COUNT + $positionAfterStartingMessages]);
    }

    /** @param array<int> $expectedIndexes */
    private function validateFilesForResourceInCold(
        CdnResource $resource,
        array $expectedIndexes,
        DateTimeImmutable $expiration,
    ): void {
        $targetFolder = $this->getTargetFolder($expiration);
        $this->validateFilesForResource(
            $resource,
            $expectedIndexes,
            $this->getOnlyResourceRelatedFiles(
                $resource,
                $this->coldFilesystem->listContents($targetFolder),
            ),
            $targetFolder,
        );
    }

    /** @param array<int> $expectedIndexes */
    private function validateFilesForResourceInBucket(CdnResource $resource, array $expectedIndexes): void
    {
        $this->validateFilesForResource(
            $resource,
            $expectedIndexes,
            $this->getOnlyResourceRelatedFiles($resource, $this->bucketFilesystem->listContents('')),
        );
    }

    /**
     * @param array<int> $expectedIndexes
     * @param array<StorageAttributes> $actualFiles
     */
    private function validateFilesForResource(
        CdnResource $resource,
        array $expectedIndexes,
        array $actualFiles,
        string $folder = '',
    ): void {
        $expectedFileNames = [];
        foreach ($expectedIndexes as $index) {
            $expectedFileNames[$this->getFileName(
                $resource,
                $index,
                $folder,
                CertificateCleaner::CERTIFICATE_EXTENSION,
            )] = 1;
            $expectedFileNames[$this->getFileName(
                $resource,
                $index,
                $folder,
                CertificateCleaner::KEY_EXTENSION,
            )] = 1;
        }

        self::assertCount(count($expectedFileNames), $actualFiles);

        foreach ($actualFiles as $file) {
            self::assertArrayHasKey($file->path(), $expectedFileNames);
        }
    }

    /**
     * @param DirectoryListing<StorageAttributes> $listing
     *
     * @return array<StorageAttributes>
     */
    private function getOnlyResourceRelatedFiles(CdnResource $resource, DirectoryListing $listing): array
    {
        return $listing->filter(
            static fn ($item) => strpos($item->path(), (string) $resource->getId()) !== false,
        )->toArray();
    }

    private function getFileName(CdnResource $resource, int $index, string $folder, string $extension): string
    {
        return sprintf('%s%s_%d.%s', $folder, $resource->getId(), $index, $extension);
    }
}
