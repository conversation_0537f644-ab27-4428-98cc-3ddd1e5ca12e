<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Certificate;

use Cdn77\NxgApi\Certificate\Domain\CertificateCleaner;
use Cdn77\NxgApi\Certificate\Infrastructure\Finder\DbalInactiveFinder;
use Cdn77\NxgApi\Certificate\Infrastructure\Finder\DbalSslFilesForCleanupFinder;
use Cdn77\NxgApi\Certificate\Infrastructure\Finder\DbalUsedResourceIdsFinder;
use Cdn77\NxgApi\Clap\Domain\AccountFinder;
use Cdn77\NxgApi\Command\OutputWriter;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\CertificateBucket;
use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use DateInterval;
use DatePeriod;
use DateTimeImmutable;
use League\Flysystem\FilesystemOperator;
use Mockery;
use Webmozart\Assert\Assert;

use function getenv;
use function sprintf;

trait CertificateCleanerTemporaryData
{
    private const string USED_RESOURCE_ID = '**********';

    private FilesystemOperator $coldFilesystem;
    private FilesystemOperator $bucketFilesystem;
    private CertificateBucket $certificateBucket;
    private CertificateCleaner $certificateCleaner;
    private OutputWriter $outputWriter;

    /** @var array<string> */
    private array $logMessages = [];

    private function prepareServices(): void
    {
        $this->prepareOutputWriterMock();
        $this->prepareFilesystems();

        $this->certificateBucket = $this->getContainerService(CertificateBucket::class);

        $accountFinderMock = Mockery::mock(AccountFinder::class);
        $accountFinderMock->shouldReceive('findVipAndTop')->andReturn([1, 2]);

        $this->certificateCleaner = new CertificateCleaner(
            $this->coldFilesystem,
            $this->bucketFilesystem,
            $this->getContainerService(DbalSslFilesForCleanupFinder::class),
            $this->getContainerService(DbalUsedResourceIdsFinder::class),
            $accountFinderMock,
            $this->getContainerService(DbalInactiveFinder::class),
            (string) getenv('CERTIFICATES_PATH'),
            (string) getenv('CERTIFICATES_COLD_PATH'),
        );
    }

    /** @return array{CdnResource, DateTimeImmutable} */
    private function prepareResourceWithSslFiles(
        int $monthStart,
        int $monthEnd,
        int $daysToExpireCertificate = 365,
        CdnResource|null $resource = null,
    ): array {
        $certificateBucket = $this->getContainerService(CertificateBucket::class);
        $certificatePair = (new CertificatePairGenerator())->generateRandomCertificatePair($daysToExpireCertificate);

        $resource ??= $this->createTemporaryResource();
        $ssl = $this->enableResourceSsl($resource);

        $datePeriod = new DatePeriod(
            new DateTimeImmutable(sprintf('first day of %d months', $monthStart)),
            new DateInterval('P1M'),
            new DateTimeImmutable(sprintf('first day of %d months', $monthEnd)),
        );

        foreach ($datePeriod as $expiration) {
            $sslFile = $this->addResourceSslFile(
                $resource,
                $ssl,
                $ssl->getFiles()->count(),
                $expiration,
                ['foo.bar'],
                SslFile::TYPE_LETSENCRYPT,
            );

            $certificateBucket->save($sslFile, $certificatePair);
        }

        return [$resource, new DateTimeImmutable(sprintf('%d days', $daysToExpireCertificate))];
    }

    private function addUsedResourceIdToDb(int $resourceId): void
    {
        $this->getEntityManager()->getConnection()->insert('used_resource_id', ['id' => $resourceId]);
    }

    private function storeCertificatePairInBucket(string $filename): void
    {
        $pair = (new CertificatePairGenerator())->generateRandomCertificatePair();

        $this->bucketFilesystem->write(sprintf('%s.pem', $filename), $pair->getCertificate());
        $this->bucketFilesystem->write(sprintf('%s.key', $filename), $pair->getPrivateKey());
    }

    private function cleanStorage(): void
    {
        $this->coldFilesystem->deleteDirectory('');
        $this->bucketFilesystem->deleteDirectory('');
    }

    private function prepareOutputWriterMock(): void
    {
        $outputWriter = $this->createMock(OutputWriter::class);

        $outputWriter->method('message')->willReturnCallback(function ($message): void {
            $this->logMessages[] = $message;
        });

        $outputWriter->method('messageRed')->willReturnCallback(function ($message): void {
            $this->logMessages[] = $message;
        });

        $this->outputWriter = $outputWriter;
    }

    private function prepareFilesystems(): void
    {
        $coldFilesystem = self::getContainer()->get('oneup_flysystem.certificate_cold_filesystem');
        Assert::isInstanceOf($coldFilesystem, FilesystemOperator::class);
        $this->coldFilesystem = $coldFilesystem;

        $bucketFilesystem = self::getContainer()->get('oneup_flysystem.certificate_bucket_filesystem');
        Assert::isInstanceOf($bucketFilesystem, FilesystemOperator::class);
        $this->bucketFilesystem = $bucketFilesystem;
    }

    private function getTargetFolder(DateTimeImmutable $expiration): string
    {
        return sprintf('%s/%s/', $expiration->format('Y'), $expiration->format('m'));
    }
}
