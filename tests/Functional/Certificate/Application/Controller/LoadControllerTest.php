<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Certificate\Application\Controller;

use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Tests\Functional\Certificate\CertificateStorageTemporaryData;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpFoundation\Request as HttpRequest;
use Symfony\Component\HttpFoundation\Response;

use function array_keys;
use function sprintf;

final class LoadControllerTest extends WebTestCase
{
    use CertificateStorageTemporaryData;

    protected function setUp(): void
    {
        parent::setUp();

        $this->setUpCertificateStorage();
    }

    public function testLoadWhenNoKeysExist(): void
    {
        $response = $this->makeRequest();

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        self::assertEquals([], $response->decodedContent);
    }

    public function testLoadForMultipleStoredPrivateKeys(): void
    {
        $accountId1 = '12345';
        $accountId2 = '67890';
        $certificatePairs1 = $this->prepareStoredKeys($accountId1, 2);
        $certificatePairs2 = $this->prepareStoredKeys($accountId2, 1);

        $response = $this->makeRequest();

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        self::assertCount(3, $response->decodedContent);

        $this->validateUuids($certificatePairs1, $accountId1, $response);
        $this->validateUuids($certificatePairs2, $accountId2, $response);
    }

    public function testLoadWithEmptyKeyFile(): void
    {
        $accountId = '12345';

        $certificatePairs = $this->prepareStoredKeys($accountId, 2);

        $this->certificateStorage->createDirectory($accountId);
        $keyFilePath = sprintf('%s/%s.key', $accountId, Uuid::uuid4()->toString());
        $this->certificateStorage->write($keyFilePath, '');

        $response = $this->makeRequest();

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        self::assertCount(2, $response->decodedContent);

        $this->validateUuids($certificatePairs, $accountId, $response);
    }

    private function makeRequest(): ResponseDecoded
    {
        $this->client->request(
            HttpRequest::METHOD_GET,
            '/certificate/key',
            [],
            [],
            static::getDefaultHeaders(),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }

    /** @param array<CertificatePair> $certificatePairs */
    private function validateUuids(array $certificatePairs, string $accountId, ResponseDecoded $response): void
    {
        foreach (array_keys($certificatePairs) as $uuid) {
            self::assertArrayHasKey($uuid, $response->decodedContent);
            self::assertEquals($accountId, $response->decodedContent[$uuid]);
        }
    }
}
