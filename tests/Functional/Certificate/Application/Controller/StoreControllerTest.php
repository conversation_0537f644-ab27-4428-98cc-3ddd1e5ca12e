<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Certificate\Application\Controller;

use Cdn77\NxgApi\Certificate\Application\Payload\StorePrivateKeySchema;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Tests\Functional\Certificate\CertificateStorageTemporaryData;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Generator;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpFoundation\Request as HttpRequest;
use Symfony\Component\HttpFoundation\Response;

use function array_keys;
use function Safe\json_encode;

final class StoreControllerTest extends WebTestCase
{
    use CertificateStorageTemporaryData;

    protected function setUp(): void
    {
        parent::setUp();

        $this->setUpCertificateStorage();
    }

    public function testAddPrivateKeyToExistingAccount(): void
    {
        $accountId = '12345';
        $uuid = Uuid::uuid4()->toString();

        $existingCertificatePairs = $this->prepareStoredKeys($accountId, 1);
        $existingUuid = array_keys($existingCertificatePairs)[0];

        self::assertTrue($this->accountDirectoryExists($accountId));
        self::assertTrue($this->keyFileExists($accountId, $existingUuid));

        $newCertificatePair = $this->generateCertificatePair();
        $response = $this->makeRequest($accountId, $uuid, $newCertificatePair);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertEmpty($response->response->getContent());

        $certificates = $this->getAccountDirectoryContents($accountId);
        self::assertCount(2, $certificates);
        self::assertContains($existingUuid, $certificates);
        self::assertContains($uuid, $certificates);

        $this->verifyStoredKeyContent($accountId, $uuid, $newCertificatePair);
    }

    public function testAddPrivateKeyToNewAccount(): void
    {
        $accountId = '12345';
        $uuid = Uuid::uuid4()->toString();

        self::assertFalse($this->accountDirectoryExists($accountId));

        $certificatePair = $this->generateCertificatePair();

        $response = $this->makeRequest($accountId, $uuid, $certificatePair);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertEmpty($response->response->getContent());

        self::assertTrue($this->accountDirectoryExists($accountId));
        self::assertTrue($this->keyFileExists($accountId, $uuid));

        $keyUuids = $this->getAccountDirectoryContents($accountId);
        self::assertCount(1, $keyUuids);
        self::assertContains($uuid, $keyUuids);

        $this->verifyStoredKeyContent($accountId, $uuid, $certificatePair);
    }

    public function testRewritePrivateKeyWithSameUuid(): void
    {
        $accountId = '12345';
        $uuid = Uuid::uuid4()->toString();

        $initialCertificatePair = $this->generateCertificatePair();
        $this->storePrivateKey($accountId, $uuid, $initialCertificatePair);
        self::assertTrue($this->keyFileExists($accountId, $uuid));

        $newCertificatePair = $this->generateCertificatePair();
        $response = $this->makeRequest($accountId, $uuid, $newCertificatePair);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertEmpty($response->response->getContent());

        self::assertTrue($this->keyFileExists($accountId, $uuid));

        $keyUuids = $this->getAccountDirectoryContents($accountId);
        self::assertCount(1, $keyUuids);
        self::assertContains($uuid, $keyUuids);

        $this->verifyStoredKeyContent($accountId, $uuid, $newCertificatePair);
    }

    public function testFailAddInvalidCertificatePair(): void
    {
        $accountId = '12345';
        $uuid = Uuid::uuid4()->toString();

        $validCertificatePair1 = $this->generateCertificatePair();
        $validCertificatePair2 = $this->generateCertificatePair();

        $invalidCertificate = $validCertificatePair1->getCertificate();
        $invalidKey = $validCertificatePair2->getPrivateKey();

        $response = $this->makeRequestRaw($accountId, $uuid, $invalidCertificate, $invalidKey);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertArrayHasKey('errors', $response->decodedContent);
        self::assertNotEmpty($response->decodedContent['errors']);
        self::assertContains(
            'Certificate and private key must form a valid certificate pair.',
            $response->decodedContent['errors'],
        );

        self::assertFalse($this->accountDirectoryExists($accountId));
        self::assertFalse($this->keyFileExists($accountId, $uuid));
    }

    public function testStoreWithInvalidAccountId(): void
    {
        $response = $this->makeRequestRaw('666', Uuid::uuid4()->toString(), 'cert', 'key');

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertArrayHasKey('errors', $response->decodedContent);
    }

    /** @return Generator<string, array{string}> */
    public static function providerInvalidUuids(): Generator
    {
        yield 'empty string' => [''];
        yield 'invalid format' => ['not-a-valid-uuid'];
        yield 'uuid v1' => ['550e8400-e29b-11d4-a716-************'];
        yield 'uuid v3' => ['6ba7b810-9dad-31d1-80b4-00c04fd430c8'];
        yield 'uuid v5' => ['6ba7b811-9dad-51d1-80b4-00c04fd430c8'];
        yield 'invalid characters' => ['550e8400-e29b-11d4-a716-44665544000g'];
    }

    /** @dataProvider providerInvalidUuids */
    public function testStoreWithInvalidUuid(string $invalidUuid): void
    {
        $certificatePair = $this->generateCertificatePair();

        $response = $this->makeRequestRaw(
            '12345',
            $invalidUuid,
            $certificatePair->getCertificate(),
            $certificatePair->getPrivateKey(),
        );

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertArrayHasKey('errors', $response->decodedContent);
    }

    public function testStoreWithEmptyCertificate(): void
    {
        $certificatePair = $this->generateCertificatePair();

        $response = $this->makeRequestRaw(
            '12345',
            Uuid::uuid4()->toString(),
            '',
            $certificatePair->getPrivateKey(),
        );

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertArrayHasKey('errors', $response->decodedContent);
    }

    public function testStoreWithEmptyKey(): void
    {
        $certificatePair = $this->generateCertificatePair();

        $response = $this->makeRequestRaw(
            '12345',
            Uuid::uuid4()->toString(),
            $certificatePair->getCertificate(),
            '',
        );

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertArrayHasKey('errors', $response->decodedContent);
    }

    private function makeRequest(string $accountId, string $uuid, CertificatePair $certificatePair): ResponseDecoded
    {
        return $this->makeRequestRaw(
            $accountId,
            $uuid,
            $certificatePair->getCertificate(),
            $certificatePair->getPrivateKey(),
        );
    }

    private function makeRequestRaw(string $accountId, string $uuid, string $certificate, string $key): ResponseDecoded
    {
        $requestData = [
            StorePrivateKeySchema::FIELD_ACCOUNT_ID => (int) $accountId,
            StorePrivateKeySchema::FIELD_UUID => $uuid,
            StorePrivateKeySchema::FIELD_CERTIFICATE => $certificate,
            StorePrivateKeySchema::FIELD_PRIVATE_KEY => $key,
        ];

        $this->client->request(
            HttpRequest::METHOD_POST,
            '/certificate/key',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($requestData),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
