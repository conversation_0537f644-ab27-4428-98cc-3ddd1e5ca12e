<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Certificate\Application\Controller;

use Cdn77\NxgApi\Certificate\Application\Payload\DeletePrivateKeySchema;
use Cdn77\NxgApi\Tests\Functional\Certificate\CertificateStorageTemporaryData;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Generator;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpFoundation\Request as HttpRequest;
use Symfony\Component\HttpFoundation\Response;

use function array_keys;
use function http_build_query;
use function sprintf;

final class DeleteControllerTest extends WebTestCase
{
    use CertificateStorageTemporaryData;

    protected function setUp(): void
    {
        parent::setUp();

        $this->setUpCertificateStorage();
    }

    public function testDeleteLastAccountPrivateKey(): void
    {
        $accountId = '12345';
        $certificatePairs = $this->prepareStoredKeys($accountId, 1);

        $response = $this->makeDeleteRequest($accountId, array_keys($certificatePairs)[0]);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertFalse($this->accountDirectoryExists($accountId));
    }

    public function testDeleteOnePrivateKeyFromMultiple(): void
    {
        $accountId = '12345';
        $certificatePairs = $this->prepareStoredKeys($accountId);

        $uuidToDelete = array_keys($certificatePairs)[1];

        $response = $this->makeDeleteRequest($accountId, $uuidToDelete);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertTrue($this->accountDirectoryExists($accountId));

        self::assertCount(2, $this->getAccountDirectoryContents($accountId));
        self::assertNotContains($uuidToDelete, $this->getAccountDirectoryContents($accountId));
    }

    public function testDeleteWithInvalidAccountId(): void
    {
        $response = $this->makeDeleteRequest('666', Uuid::uuid4()->toString());

        self::assertSame(Response::HTTP_NOT_FOUND, $response->statusCode);
        self::assertArrayHasKey('error', $response->decodedContent);
    }

    public function testDeleteWithInvalidUuid(): void
    {
        $accountId = '12345';
        $this->prepareStoredKeys($accountId);
        $randomUuid = Uuid::uuid4()->toString();
        $response = $this->makeDeleteRequest($accountId, $randomUuid);

        self::assertSame(Response::HTTP_NOT_FOUND, $response->statusCode);
        self::assertSame(
            ['error' => sprintf('File "%s/%s.key" not found when deleting private key.', $accountId, $randomUuid)],
            $response->decodedContent,
        );
    }

    /** @return Generator<string, array{string}> */
    public static function providerInvalidUuids(): Generator
    {
        yield 'empty string' => [''];
        yield 'invalid format' => ['not-a-valid-uuid'];
        yield 'uuid v1' => ['550e8400-e29b-11d4-a716-************'];
        yield 'invalid characters' => ['550e8400-e29b-11d4-a716-44665544000g'];
    }

    /** @dataProvider providerInvalidUuids */
    public function testDeleteWithInvalidUuidFormat(string $invalidUuid): void
    {
        $response = $this->makeDeleteRequest('12345', $invalidUuid);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertArrayHasKey('errors', $response->decodedContent);
    }

    private function makeDeleteRequest(string $accountId, string $uuid): ResponseDecoded
    {
        $queryString = http_build_query([
            DeletePrivateKeySchema::FIELD_ACCOUNT_ID => (int) $accountId,
            DeletePrivateKeySchema::FIELD_UUID => $uuid,
        ]);

        $this->client->request(
            HttpRequest::METHOD_DELETE,
            '/certificate/key?' . $queryString,
            [],
            [],
            static::getDefaultHeaders(),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
