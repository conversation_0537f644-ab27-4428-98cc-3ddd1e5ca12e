<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional;

use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase as SymfonyKernelTestCase;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Dotenv\Dotenv;
use Webmozart\Assert\Assert;

use function getenv;

abstract class KernelTestCase extends SymfonyKernelTestCase
{
    /** @var ContainerInterface */
    protected static $container;

    protected function setUp(): void
    {
        if (getenv('APP_ENV') === false) {
            (new Dotenv())->usePutenv()->load(__DIR__ . '/../../.env.test');
        }

        parent::setUp();

        static::bootKernel();

        self::$container = self::$kernel->getContainer();
    }

    protected function getConnection(): Connection
    {
        $connection = static::$kernel->getContainer()->get('doctrine.dbal.default_connection');
        Assert::isInstanceOf($connection, Connection::class);

        return $connection;
    }

    protected function getEntityManager(): EntityManagerInterface
    {
        $entityManager = static::$kernel->getContainer()->get('doctrine')->getManager('mc_legacy');
        Assert::isInstanceOf($entityManager, EntityManagerInterface::class);

        return $entityManager;
    }

    /**
     * @param class-string<T> $id
     *
     * @return T
     *
     * @template T of object
     */
    protected function getContainerService(string $id): object
    {
        /** @phpstan-var T */
        return static::$container->get($id);
    }
}
