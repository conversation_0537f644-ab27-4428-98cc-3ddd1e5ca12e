<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Domain\Finder;

use Cdn77\NxgApi\Resource\Domain\Exception\FailedToGetCertificate;
use Cdn77\NxgApi\Resource\Infrastructure\Finder\FilesystemMainCertificateFinder;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\DefaultNamingStrategy;
use Cdn77\NxgApi\Tests\Functional\CertificateBucketHelper;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\KernelTestCase;
use League\Flysystem\FilesystemOperator;

class MainCertificateFinderTest extends KernelTestCase
{
    use CertificateBucketHelper;
    use TemporaryData;

    public function testGetLastMainCertificatePair(): void
    {
        [$certificateFinder, $filesystem] = $this->prepare();

        $filesystem->write('0000000001_001.pem', 'cert1');
        $filesystem->write('0000000001_001.key', 'key1');
        $filesystem->write('0000000001_002.pem', 'cert2');
        $filesystem->write('0000000001_002.key', 'key2');
        $filesystem->write('0000000001_123.pem', 'cert123');
        $filesystem->write('0000000001_123.key', 'key123');

        $filesystem->write('0000001_666.pem', 'cert666'); //wrong name
        $filesystem->write('0000001_666.key', 'key666'); //wrong name

        $result = $certificateFinder->findLast();

        self::assertSame('cert123', $result->getCertificate());
        self::assertSame('key123', $result->getPrivateKey());
    }

    public function testGetLastMainCertificatePairIncomplete(): void
    {
        $this->expectException(FailedToGetCertificate::class);
        $this->expectExceptionMessage('Incomplete main certificate pair for index 123');

        [$certificateFinder, $filesystem] = $this->prepare();

        $filesystem->write('0000000001_123.pem', 'cert123');

        $certificateFinder->findLast();
    }

    public function testGetLastMainCertificatePairNoFiles(): void
    {
        $this->expectException(FailedToGetCertificate::class);
        $this->expectExceptionMessage('Main certificate (0000000001) not found');

        [$certificateFinder] = $this->prepare();

        $certificateFinder->findLast();
    }

    protected function setUp(): void
    {
        parent::setUp();
    }

    /** @return array{FilesystemMainCertificateFinder, FilesystemOperator} */
    private function prepare(): array
    {
        $filesystem = $this->getContainerService(FilesystemOperator::class);
        $certificateFinder = new FilesystemMainCertificateFinder(new DefaultNamingStrategy(), $filesystem);

        $this->clean($filesystem);

        return [$certificateFinder, $filesystem];
    }
}
