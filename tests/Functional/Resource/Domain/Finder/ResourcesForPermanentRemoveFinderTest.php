<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Domain\Finder;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Resource\Domain\Finder\ResourcesForPermanentRemoveFinder;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\KernelTestCase;
use DateTimeImmutable;

class ResourcesForPermanentRemoveFinderTest extends KernelTestCase
{
    use TemporaryData;

    public function testSuspend(): void
    {
        $resourceToRemove = $this->createTemporaryResource();
        $resourceToRemove->setDeleted(new DateTimeImmutable('- 35 days'));

        $resourceNotOldEnough = $this->createTemporaryResource();
        $resourceNotOldEnough->setDeleted(new DateTimeImmutable('- 5 days'));

        $resource = $this->createTemporaryResource();

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $finder = $this->getContainerService(ResourcesForPermanentRemoveFinder::class);

        $resourceIdsRemove = $finder->findIds(10);
        self::assertCount(1, $resourceIdsRemove);
        self::assertSame($resourceToRemove->getId(), $resourceIdsRemove[0]);

        //check that DeletedResourceFilter is enabled again
        $allResources = $this->getEntityManager()->getRepository(CdnResource::class)->findAll();
        self::assertCount(1, $allResources);
        self::assertSame($resource->getId(), $allResources[0]->getId());
    }
}
