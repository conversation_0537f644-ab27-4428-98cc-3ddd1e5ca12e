<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Domain;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Resource\Domain\ResourceSuspender;
use Cdn77\NxgApi\Service\Legacy\Suspension\Exception\ResourceAlreadySuspended;
use Cdn77\NxgApi\Service\Legacy\Suspension\Exception\ResourceAlreadyUnsuspended;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\KernelTestCase;
use DateTimeImmutable;

class ResourceSuspenderTest extends KernelTestCase
{
    use TemporaryData;

    public function testSuspend(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setSuspended(null);
        $this->getEntityManager()->flush();

        $this->getSuspender()->suspend($resource);

        $resource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());

        self::assertTrue($resource->isSuspended());
    }

    public function testUnsuspend(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setSuspended(new DateTimeImmutable());
        $this->getEntityManager()->flush();

        $this->getSuspender()->unsuspend($resource);

        $resource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());

        self::assertFalse($resource->isSuspended());
    }

    public function testSuspendWithSuspendedResource(): void
    {
        $this->expectException(ResourceAlreadySuspended::class);
        $resource = $this->createTemporaryResource();
        $resource->setSuspended(new DateTimeImmutable());
        $this->getEntityManager()->flush();

        $this->getSuspender()->suspend($resource);
    }

    public function testUnsuspendWithUnsuspendedResource(): void
    {
        $this->expectException(ResourceAlreadyUnsuspended::class);
        $resource = $this->createTemporaryResource();
        $resource->setSuspended(null);
        $this->getEntityManager()->flush();

        $this->getSuspender()->unsuspend($resource);
    }

    private function getSuspender(): ResourceSuspender
    {
        return static::$kernel->getContainer()->get(ResourceSuspender::class);
    }
}
