<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Payload;

use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddSchema;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use <PERSON><PERSON>\Serializer\SerializerInterface;

class ResourceAddSchemaTest extends WebTestCase
{
    public function testDeserialization(): void
    {
        $data = '{"cdn_resource": {}}';
        $serializer = self::$container->get(SerializerInterface::class);
        $schema = $serializer->deserialize($data, ResourceAddSchema::class, 'json');

        self::assertInstanceOf(ResourceAddSchema::class, $schema);
    }
}
