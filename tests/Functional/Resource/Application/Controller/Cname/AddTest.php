<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Cname;

use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Cdn77\NxgApi\Validator\Constraints\Resource\UniqueCnameValidator;
use Doctrine\DBAL\Exception\DriverException;
use Generator;
use Mockery;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;
use function sprintf;

class AddTest extends WebTestCase
{
    use EntityGetter;
    use NotifyResourceChangeHelper;
    use ResponseEvaluateHelper;
    use TemporaryData;

    public function testOkWithCnameAsSubstring(): void
    {
        $this->createTemporaryResource(['cnames' => ['abc.cz']]);
        $this->createTemporaryResource(['cnames' => ['bc.cz']]);
    }

    public function testOkWithEmptyCname(): void
    {
        $resourceA = $this->createTemporaryResource();
        $resourceA->setCnames(['']);

        $resourceB = $this->createTemporaryResource();
        $resourceB->setCnames(['']);

        $resourceC = $this->createTemporaryResource();
        $resourceC->setCnames(['', '']);

        $resourceD = $this->createTemporaryResource();
        $resourceD->setCnames([]);

        $this->getEntityManager()->persist($resourceA);
        $this->getEntityManager()->persist($resourceB);
        $this->getEntityManager()->persist($resourceC);
        $this->getEntityManager()->persist($resourceD);

        FlushAndClear::do($this->getEntityManager());

        self::assertCount(0, $this->getResource($resourceA->getId())->getCnames());
        self::assertCount(0, $this->getResource($resourceB->getId())->getCnames());
        self::assertCount(0, $this->getResource($resourceC->getId())->getCnames());
        self::assertCount(0, $this->getResource($resourceD->getId())->getCnames());
    }

    /**
     * @param array<string> $cnamesForFirstCdn
     * @param array<string> $cnamesForSecondCdn
     *
     * @dataProvider providerFailsOnTriggerWhenAddingExistingCname
     */
    public function testFailsOnTriggerWhenAddingExistingCname(
        array $cnamesForFirstCdn,
        array $cnamesForSecondCdn,
        string $messageCname,
    ): void {
        $this->expectException(DriverException::class);
        $this->expectExceptionMessage(
            sprintf('CNAME "%s" have to be unique in all undeleted resources', $messageCname),
        );

        $this->createTemporaryResource(['cnames' => $cnamesForFirstCdn]);

        $this->createTemporaryResource(['cnames' => $cnamesForSecondCdn]);
    }

    /** @return Generator<string, array<array<string>|string>> */
    public static function providerFailsOnTriggerWhenAddingExistingCname(): Generator
    {
        yield 'test 1' => [['abc.cz', 'def.cz'], ['abc.cz'], 'abc.cz'];
        yield 'test 2' => [['abc.cz', 'def.cz'], ['abc.cz', 'def.cz'], 'abc.cz'];
        yield 'test 3' => [['abc.cz', 'def.cz'], ['qwe.cz', 'def.cz'], 'def.cz'];

        yield 'case 1' => [['abc.cz', 'def.cz'], ['Abc.cz'], 'Abc.cz'];
        yield 'case 2' => [['Abc.cz', 'def.cz'], ['abc.cz'], 'abc.cz'];

        yield 'duplicate-with-surrounding-whitespace 1' => [
            ['alpha.cz'],
            [' alpha.cz ', 'beta.cz'],
            'alpha.cz',
        ];

        yield 'duplicate-with-surrounding-whitespace 2' => [
            ['alpha.cz '],
            ['alpha.cz', 'beta.cz'],
            'alpha.cz',
        ];
    }

    public function testFailsWhenAddingExistingCnameWithDifferentCase(): void
    {
        $this->disableUniqueCnameValidator();

        $this->createTemporaryResource(['cnames' => ['test.foo', 'Test.bar']]);

        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['cnames'] = ['xyz.test', 'TEST.bar'];

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateFailedResponse(
            $response,
            ['CNAME "test.bar" have to be unique in all undeleted resources'],
            Response::HTTP_UNPROCESSABLE_ENTITY,
        );
    }

    /** @param array<string, string|bool|null> $data */
    private function callApiGetResponse(array $data): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_POST,
            '/cdn_resources.json',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(['cdn_resource' => $data]),
        );

        $response = $this->client->getResponse();

        return ResponseDecoded::fromResponse($response);
    }

    private function disableUniqueCnameValidator(): void
    {
        $validatorMock = Mockery::mock(UniqueCnameValidator::class);
        $validatorMock->shouldReceive('initialize')->once();
        $validatorMock->shouldReceive('validate');

        self::getContainer()->set(UniqueCnameValidator::class, $validatorMock);
    }
}
