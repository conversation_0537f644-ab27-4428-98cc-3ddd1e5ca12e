<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Cname;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Service\Legacy\Edit\Constraints\UniqueCnameValidator;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Doctrine\DBAL\Exception\DriverException;
use Generator;
use Mockery;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;
use function sprintf;

class EditTest extends WebTestCase
{
    use EntityGetter;
    use NotifyResourceChangeHelper;
    use ResponseEvaluateHelper;
    use TemporaryData;

    public function testOkWithCnameAsSubstring(): void
    {
        $this->createTemporaryResource(['cnames' => ['abc.cz']]);
        $resourcePrepared = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $resource = $this->getResource($resourcePrepared->getId());
        $resource->setCnames(['bc.cz']);

        $this->getEntityManager()->persist($resource);

        FlushAndClear::do($this->getEntityManager());
    }

    public function testOkWithEmptyCname(): void
    {
        $resourceA = $this->createTemporaryResource();
        $resourceA->setCnames(['', '']);
        $this->getEntityManager()->persist($resourceA);

        $resourcePrepared = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $resourceB = $this->getResource($resourcePrepared->getId());
        $resourceB->setCnames(['']);

        $this->getEntityManager()->persist($resourceB);

        FlushAndClear::do($this->getEntityManager());

        self::assertCount(0, $this->getResource($resourceA->getId())->getCnames());
        self::assertCount(0, $this->getResource($resourceB->getId())->getCnames());
    }

    /**
     * @param array<string> $cnamesForFirstCdn
     * @param array<string> $cnamesForSecondCdn
     *
     * @dataProvider providerFailsOnTriggerWhenAddingExistingCname
     */
    public function testFailsOnTriggerWhenAddingExistingCname(
        array $cnamesForFirstCdn,
        array $cnamesForSecondCdn,
        string $messageCname,
    ): void {
        $this->expectException(DriverException::class);
        $this->expectExceptionMessage(
            sprintf('CNAME "%s" have to be unique in all undeleted resources', $messageCname),
        );

        $this->createTemporaryResource(['cnames' => $cnamesForFirstCdn]);
        $resourcePrepared = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $resource = $this->getResource($resourcePrepared->getId());
        $resource->setCnames($cnamesForSecondCdn);

        $this->getEntityManager()->persist($resource);
        $this->getEntityManager()->flush();
    }

    /** @return Generator<string, array<array<string>|string>> */
    public static function providerFailsOnTriggerWhenAddingExistingCname(): Generator
    {
        yield 'test 1' => [['abc.cz', 'def.cz'], ['abc.cz'], 'abc.cz'];
        yield 'test 2' => [['abc.cz', 'def.cz'], ['abc.cz', 'def.cz'], 'abc.cz'];
        yield 'test 3' => [['abc.cz', 'def.cz'], ['qwe.cz', 'def.cz'], 'def.cz'];

        yield 'case insensitive 1' => [['abc.cz', 'def.cz'], ['Abc.cz'], 'abc.cz'];
        yield 'case insensitive 2' => [['Abc.cz', 'def.cz'], ['abc.cz'], 'abc.cz'];

        yield 'duplicate-with-surrounding-whitespace 1' => [
            ['alpha.cz'],
            [' alpha.cz ', 'beta.cz'],
            'alpha.cz',
        ];

        yield 'duplicate-with-surrounding-whitespace 2' => [
            ['alpha.cz '],
            ['alpha.cz', 'beta.cz'],
            'alpha.cz',
        ];
    }

    public function testFailsWhenAddingExistingCnameWithDifferentCase(): void
    {
        $this->disableUniqueCnameValidator();

        $this->createTemporaryResource(['cnames' => ['test.foo', 'test.bar']]);

        $resourceToEdit = $this->createTemporaryResource();

        $response = $this->callApiGetResponse($resourceToEdit, ['qwe.foo', 'TEST.bar']);

        $this->evaluateFailedResponse(
            $response,
            ['CNAME "test.bar" have to be unique in all undeleted resources'],
            Response::HTTP_UNPROCESSABLE_ENTITY,
        );
    }

    /** @param array<string> $data */
    private function callApiGetResponse(CdnResource $resource, array $data): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['cnames' => $data],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        return ResponseDecoded::fromResponse($response);
    }

    private function disableUniqueCnameValidator(): void
    {
        $validatorMock = Mockery::mock(UniqueCnameValidator::class);
        $validatorMock->shouldReceive('initialize')->once();
        $validatorMock->shouldReceive('validate');

        self::getContainer()->set(UniqueCnameValidator::class, $validatorMock);
    }
}
