<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\FollowRedirect;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Resource\Application\Payload\FollowRedirectSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Validator\Constraints\Resource\AllowedDomainValidator;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;

class AddTest extends WebTestCase
{
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use AllowedDomainValidator;
    use ResponseEvaluateHelper;

    /** @return Generator<string, array<array<int>|null>> */
    public static function providerFollowRedirect(): Generator
    {
        yield 'empty 1' => [false, []];
        yield 'empty 2' => [true, []];
        yield 'data 1' => [true, [301]];
        yield 'data 2' => [true, [301, 302]];
    }

    /** @return Generator<string, array<array<int>> */
    public static function providerFailFollowRedirect(): Generator
    {
        yield 'bad enabled 1' => ['true', [301]];
        yield 'bad enabled 2' => ['false', [301]];
        yield 'bad enabled 3' => ['string', [301]];
        yield 'bad enabled 4' => ['1', [301]];
        yield 'bad enabled 5' => ['0', [301]];
        yield 'bad enabled 6' => ['true', []];
        yield 'bad enabled 7' => ['false', []];
        yield 'bad enabled 8' => ['string', []];
        yield 'bad enabled 9' => ['1', []];
        yield 'bad enabled 10' => ['0', []];
        yield 'bad enabled 11' => [0, [301]];
        yield 'bad enabled 12' => [1, [301]];
        yield 'bad enabled 13' => [[], [301]];
        yield 'bad enabled 14' => [null, [301]];

        yield 'bad codes 1' => [true, 'string'];
        yield 'bad codes 2' => [true, 1];
        yield 'bad codes 3' => [true, 0];
        yield 'bad codes 4' => [true, true];
        yield 'bad codes 5' => [true, false];
        yield 'bad codes 6' => [true, ['301']];
        yield 'bad codes 7' => [true, ['301', '302']];
        yield 'bad codes 8' => [true, [true]];
        yield 'bad codes 9' => [true, [201]];
        yield 'bad codes 10' => [true, [400]];
        yield 'bad codes 11' => [true, [301, 402]];
        yield 'bad codes 12' => [true, [301, '302']];
        yield 'bad codes 13' => [true, null];

        yield 'bad combination 1' => [false, [301]];
        yield 'bad combination 2' => [false, [301, 302]];
    }

    /**
     * @param list<int>|null $followRedirectCodes
     *
     * @dataProvider providerFollowRedirect
     */
    public function testOk(bool $followRedirectOrigin, array $followRedirectCodes): void
    {
        $dataForNewCdn = $this->prepareRequestData($followRedirectOrigin, $followRedirectCodes);
        $response = $this->callApiGetResponse($dataForNewCdn);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        self::assertSame(
            $followRedirectOrigin,
            $response->decodedContent['cdn_resource']['follow_redirect']['enabled'],
        );
        self::assertSame($followRedirectOrigin, $resource->getMainOrigin()->hasFollowRedirectOrigin());

        $expectedCodes = ValueReplacer::emptyArrayToNull($followRedirectCodes);
        if ($followRedirectCodes !== []) {
            self::assertSame($expectedCodes, $response->decodedContent['cdn_resource']['follow_redirect']['codes']);
        }

        self::assertSame($expectedCodes, $resource->getMainOrigin()->getFollowRedirectCodes());
    }

    /**
     * @param mixed $followRedirectOrigin
     * @param mixed $followRedirectCodes
     *
     * @dataProvider providerFailFollowRedirect
     */
    public function testFail($followRedirectOrigin, $followRedirectCodes): void
    {
        $dataForNewCdn = $this->prepareRequestData($followRedirectOrigin, $followRedirectCodes);
        $response = $this->callApiGetResponse($dataForNewCdn);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertNoMessageInQueue();
    }

    public function testFailWhenFollowRedirectMissing(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        unset($dataForNewCdn['follow_redirect']);

        $response = $this->callApiGetResponse($dataForNewCdn);

        self::evaluateUnprocessableEntityResponse($response, 'This value should not be null.', 'follow_redirect');
    }

    /**
     * @param mixed $followRedirectOrigin
     * @param mixed $followRedirectCodes
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData($followRedirectOrigin, $followRedirectCodes): array
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();

        $dataForNewCdn[ResourceAddInfo::FIELD_FOLLOW_REDIRECT] = [
            FollowRedirectSchema::FIELD_ENABLED => $followRedirectOrigin,
            FollowRedirectSchema::FIELD_CODES => $followRedirectCodes,
        ];

        return $dataForNewCdn;
    }

    /** @param array<int>|null $dataForNewCdn */
    private function callApiGetResponse(array $dataForNewCdn): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_POST,
            '/cdn_resources.json',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(['cdn_resource' => $dataForNewCdn]),
        );

        $response = $this->client->getResponse();

        return ResponseDecoded::fromResponse($response);
    }
}
