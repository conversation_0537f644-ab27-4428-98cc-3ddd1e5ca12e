<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\FollowRedirect;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Resource\Application\Payload\FollowRedirectSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Generators\CertificateDefinitions;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function is_array;
use function ksort;
use function Safe\json_encode;
use function sprintf;

final class EditTest extends WebTestCase
{
    use CertificateDefinitions;
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    /** @return Generator<string, array<array<int>|null>> */
    public static function providerFollowRedirectPreparationOnly(): Generator
    {
        yield 'empty 1' => [false, []];
        yield 'empty 2' => [true, []];
        yield 'data 1' => [true, [301]];
        yield 'data 2' => [true, [301, 302]];
    }

    /** @return Generator<string, array<array<int>|bool>> */
    public static function providerFollowRedirect(): Generator
    {
        $single = [301];
        $multi = [301, 302];

        yield 'not used -> turn on enabled' => [false, true, [], []];
        yield 'not used -> turn on enabled with codes 1' => [false, true, [], $single];
        yield 'not used -> turn on enabled with codes 2' => [false, true, [], $multi];
        yield 'not used -> off' => [false, false, [], []];
        yield 'only enabled -> enabled with codes 1' => [true, true, [], $single];
        yield 'only enabled -> enabled with codes 2' => [true, true, [], $multi];
        yield 'only enabled -> off' => [true, false, [], []];
        yield 'only enabled -> only enabled' => [true, true, [], []];
        yield 'enabled with codes -> only enabled' => [true, true, $single, []];
        yield 'enabled with codes -> off' => [true, false, $multi, []];
        yield 'enabled with codes -> enabled with changed codes' => [true, true, $multi, $single];
    }

    /** @return Generator<string, array<array<int>|null, array|string|int|bool|null>> */
    public static function providerBadFollowRedirect(): Generator
    {
        $single = [301];
        $multi = [301, 302];

        yield 'not used -> bad enabled 1' => [false, 'true', [], []];
        yield 'not used -> bad enabled 2' => [false, 'false', [], []];
        yield 'not used -> bad enabled 3' => [false, 'string', [], []];
        yield 'not used -> bad enabled 4' => [false, '1', [], []];
        yield 'not used -> bad enabled 5' => [false, '0', [], []];
        yield 'not used -> bad enabled 6' => [false, 1, [], []];
        yield 'not used -> bad enabled 7' => [false, 0, [], []];
        yield 'not used -> bad enabled 8' => [false, [], [], []];
        yield 'not used -> bad enabled 9' => [false, null, [], []];

        yield 'not used -> bad codes 1' => [false, true, [], 'string'];
        yield 'not used -> bad codes 2' => [false, true, [], 1];
        yield 'not used -> bad codes 3' => [false, true, [], 0];
        yield 'not used -> bad codes 4' => [false, true, [], true];
        yield 'not used -> bad codes 5' => [false, true, [], false];
        yield 'not used -> bad codes 6' => [false, true, [], ['301']];
        yield 'not used -> bad codes 7' => [false, true, [], [true]];
        yield 'not used -> bad codes 9' => [false, true, [], [201]];
        yield 'not used -> bad codes 10' => [false, true, [], [400]];
        yield 'not used -> bad codes 11' => [false, true, [], [301, '302']];
        yield 'not used -> bad codes 12' => [false, true, [], null];

        yield 'not used -> bad combination 1' => [false, false, [], $single];

        yield 'only enabled -> bad enabled 1' => [true, 'true', [], []];
        yield 'only enabled -> bad enabled 2' => [true, 'false', [], []];
        yield 'only enabled -> bad enabled 3' => [true, 'string', [], []];
        yield 'only enabled -> bad enabled 4' => [true, '1', [], []];
        yield 'only enabled -> bad enabled 5' => [true, '0', [], []];
        yield 'only enabled -> bad enabled 6' => [true, 1, [], []];
        yield 'only enabled -> bad enabled 7' => [true, 0, [], []];
        yield 'only enabled -> bad enabled 8' => [true, [], [], []];
        yield 'only enabled -> bad enabled 9' => [true, null, [], []];

        yield 'only enabled -> bad codes 1' => [true, true, [], 'string'];
        yield 'only enabled -> bad codes 2' => [true, true, [], 1];
        yield 'only enabled -> bad codes 3' => [true, true, [], 0];
        yield 'only enabled -> bad codes 4' => [true, true, [], true];
        yield 'only enabled -> bad codes 5' => [true, true, [], false];
        yield 'only enabled -> bad codes 6' => [true, true, [], ['301']];
        yield 'only enabled -> bad codes 7' => [true, true, [], [true]];
        yield 'only enabled -> bad codes 9' => [true, true, [], [201]];
        yield 'only enabled -> bad codes 10' => [true, true, [], [400]];
        yield 'only enabled -> bad codes 11' => [true, true, [], [301, '302']];
        yield 'only enabled -> bad codes 12' => [true, true, [], null];

        yield 'only enabled -> bad combination 1' => [true, false, [], $single];

        yield 'enabled with codes -> bad enabled 1' => [true, 'true', $multi, []];
        yield 'enabled with codes -> bad enabled 2' => [true, 'false', $multi, []];
        yield 'enabled with codes -> bad enabled 3' => [true, 'string', $multi, []];
        yield 'enabled with codes -> bad enabled 4' => [true, '1', $multi, []];
        yield 'enabled with codes -> bad enabled 5' => [true, '0', $multi, []];
        yield 'enabled with codes -> bad enabled 6' => [true, 1, $multi, []];
        yield 'enabled with codes -> bad enabled 7' => [true, 0, $multi, []];
        yield 'enabled with codes -> bad enabled 8' => [true, [], $multi, []];
        yield 'enabled with codes -> bad enabled 9' => [true, null, $multi, []];

        yield 'enabled with codes -> bad codes 1' => [true, true, $multi, 'string'];
        yield 'enabled with codes -> bad codes 2' => [true, true, $multi, 1];
        yield 'enabled with codes -> bad codes 3' => [true, true, $multi, 0];
        yield 'enabled with codes -> bad codes 4' => [true, true, $multi, true];
        yield 'enabled with codes -> bad codes 5' => [true, true, $multi, false];
        yield 'enabled with codes -> bad codes 6' => [true, true, $multi, ['301']];
        yield 'enabled with codes -> bad codes 7' => [true, true, $multi, [true]];
        yield 'enabled with codes -> bad codes 9' => [true, true, $multi, [201]];
        yield 'enabled with codes -> bad codes 10' => [true, true, $multi, [400]];
        yield 'enabled with codes -> bad codes 11' => [true, true, $multi, [301, '302']];
        yield 'enabled with codes -> bad codes 12' => [true, true, $multi, null];

        yield 'origin with codes -> bad combination 1' => [true, false, $multi, $single];
    }

    /**
     * @param list<int> $codesForPreparation
     * @param list<int> $codesForRequest
     *
     * @dataProvider providerFollowRedirect
     */
    public function testOk(
        bool $enabledForPreparation,
        bool $enabledForRequest,
        array $codesForPreparation,
        array $codesForRequest,
    ): void {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setupFollowRedirect($enabledForPreparation, $codesForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $requestData = $this->prepareRequestData($enabledForRequest, $codesForRequest);
        $response = $this->callApiGetResponse($resource, $requestData);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertUpdatingMessageInQueue($resource->getId());

        $this->assertOriginAndCodes($resource->getId(), $enabledForRequest, $codesForRequest);
    }

    /**
     * @param mixed $enabledForRequest
     * @param list<int> $codesForPreparation
     * @param mixed $codesForRequest
     *
     * @dataProvider providerBadFollowRedirect
     */
    public function testFail(
        bool $enabledForPreparation,
        $enabledForRequest,
        array $codesForPreparation,
        $codesForRequest,
    ): void {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setupFollowRedirect($enabledForPreparation, $codesForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $requestData = $this->prepareRequestData($enabledForRequest, $codesForRequest);
        $response = $this->callApiGetResponse($resource, $requestData);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertNoMessageInQueue();

        $this->assertOriginAndCodes($resource->getId(), $enabledForPreparation, $codesForPreparation);
    }

    /**
     * @param list<int> $codesForPreparation
     *
     * @dataProvider providerFollowRedirectPreparationOnly
     */
    public function testFailWhenFollowRedirectMissing(bool $enabledForPreparation, array $codesForPreparation): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setupFollowRedirect($enabledForPreparation, $codesForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $response = $this->callApiGetResponse($resource, [], ['follow_redirect']);

        self::evaluateUnprocessableEntityResponse($response, 'This value should not be null.', 'follow_redirect');

        $this->assertOriginAndCodes($resource->getId(), $enabledForPreparation, $codesForPreparation);
    }

    /** @param list<int> $expectedCodes */
    private function assertOriginAndCodes(
        int $resourceId,
        bool $expectedFollowRedirectOrigin,
        array $expectedCodes,
    ): void {
        $resource = $this->getResource($resourceId);

        $actualFollowRedirectCodes = $resource->getMainOrigin()->getFollowRedirectCodes();
        if (is_array($actualFollowRedirectCodes)) {
            ksort($actualFollowRedirectCodes);
        }

        ksort($expectedCodes);

        self::assertSame($expectedFollowRedirectOrigin, $resource->getMainOrigin()->hasFollowRedirectOrigin());
        self::assertSame(ValueReplacer::emptyArrayToNull($expectedCodes), $actualFollowRedirectCodes);
    }

    /**
     * @param mixed $followRedirectOrigin
     * @param mixed $followRedirectCodes
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData($followRedirectOrigin, $followRedirectCodes): array
    {
        $data[ResourceAddInfo::FIELD_FOLLOW_REDIRECT] = [
            FollowRedirectSchema::FIELD_ENABLED => $followRedirectOrigin,
            FollowRedirectSchema::FIELD_CODES => $followRedirectCodes,
        ];

        return $data;
    }

    /**
     * @param array<string, string|null> $data
     * @param array<string> $propertiesExcluedFromRequiredData
     */
    private function callApiGetResponse(
        CdnResource $resource,
        $data,
        array $propertiesExcluedFromRequiredData = [],
    ): Response {
        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        $data,
                        $propertiesExcluedFromRequiredData,
                    ),
                ],
            ),
        );

        return $this->client->getResponse();
    }
}
