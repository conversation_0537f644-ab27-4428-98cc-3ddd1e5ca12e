<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\SecureToken;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\SecureToken;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Resource\Application\Payload\SecureTokenSchema;
use Cdn77\NxgApi\Service\Legacy\Edit\ResourceChanger;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Generators\CertificateDefinitions;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function is_array;
use function Safe\json_encode;
use function sprintf;

final class EditTest extends WebTestCase
{
    use CertificateDefinitions;
    use NotifyResourceChangeHelper;
    use TemporaryData;

    public static function providerSecureToken(): Generator
    {
        yield 'all set 1' => [
            'preparation' =>
            [
                'type' => 'path',
                'value' => 'token',
                'secureLinkExpiryParam' => '',
                'secureLinkTokenParam' => '',
                'secureLinkPathlenParam' => '',
                'secureLinkSecretParam' => '',
                'secureLinkRewritePlaylist' => false,
            ],
            'changes' => [
                'type' => 'highwinds',
                'value' => 'newToken',
                'secureLinkExpiryParam' => 'newExpiry',
                'secureLinkTokenParam' => 'newLinkToken',
                'secureLinkPathlenParam' => 'newPathlen',
                'secureLinkSecretParam' => 'newSecret',
                'secureLinkRewritePlaylist' => true,
            ],
        ];

        yield 'all set 2' => [
            'preparation' =>
            [
                'type' => 'path',
                'value' => 'token',
                'secureLinkExpiryParam' => null,
                'secureLinkTokenParam' => null,
                'secureLinkPathlenParam' => null,
                'secureLinkSecretParam' => null,
                'secureLinkRewritePlaylist' => true,
            ],
            'changes' => [
                'type' => 'highwinds',
                'value' => 'newToken',
                'secureLinkExpiryParam' => 'newExpiry',
                'secureLinkTokenParam' => 'newLinkToken',
                'secureLinkPathlenParam' => 'newPathlen',
                'secureLinkSecretParam' => 'newSecret',
                'secureLinkRewritePlaylist' => false,
            ],
        ];

        yield 'all set 3' => [
            'preparation' =>
            [
                'type' => 'highwinds',
                'value' => 'token',
                'secureLinkExpiryParam' => 'e',
                'secureLinkTokenParam' => 'lt',
                'secureLinkPathlenParam' => 'pl',
                'secureLinkSecretParam' => 'pl',
                'secureLinkRewritePlaylist' => true,
            ],
            'changes' => [
                'type' => 'highwinds',
                'value' => 'newToken',
                'secureLinkExpiryParam' => 'newExpiry',
                'secureLinkTokenParam' => 'newLinkToken',
                'secureLinkPathlenParam' => 'newPathlen',
                'secureLinkSecretParam' => 'newSecret',
                'secureLinkRewritePlaylist' => false,
            ],
        ];

        yield 'all set 4' => [
            'preparation' => null,
            'changes' => [
                'type' => 'highwinds',
                'value' => 'newToken',
                'secureLinkExpiryParam' => 'newExpiry',
                'secureLinkTokenParam' => 'newLinkToken',
                'secureLinkPathlenParam' => 'newPathlen',
                'secureLinkSecretParam' => 'newSecret',
                'secureLinkRewritePlaylist' => false,
            ],
        ];

        yield 'empty params' => [
            'preparation' =>
            [
                'type' => 'path',
                'value' => 'token',
                'secureLinkExpiryParam' => 'e',
                'secureLinkTokenParam' => 'lt',
                'secureLinkPathlenParam' => 'pl',
                'secureLinkSecretParam' => 'pl',
                'secureLinkRewritePlaylist' => true,
            ],
            'changes' => [
                'type' => 'parameter',
                'value' => 'newToken',
                'secureLinkExpiryParam' => '',
                'secureLinkTokenParam' => '',
                'secureLinkPathlenParam' => '',
                'secureLinkSecretParam' => '',
                'secureLinkRewritePlaylist' => false,
            ],
        ];

        yield 'required params' => [
            'preparation' =>
            [
                'type' => 'parameter',
                'value' => 'token',
                'secureLinkExpiryParam' => null,
                'secureLinkTokenParam' => null,
                'secureLinkPathlenParam' => null,
                'secureLinkSecretParam' => null,
                'secureLinkRewritePlaylist' => true,
            ],
            'changes' => [
                'type' => 'path',
                'value' => 'newToken',
                'secureLinkExpiryParam' => '',
                'secureLinkTokenParam' => '',
                'secureLinkPathlenParam' => '',
                'secureLinkSecretParam' => '',
                'secureLinkRewritePlaylist' => false,
            ],
        ];
    }

    /** @return Generator<string, array<string, string, string|null, string|null, string|null, string|null, bool>> */
    public static function providerFailSecureToken(): Generator
    {
        yield 'bad 1' => ['path', '', '', '', '', '', true];
        yield 'bad 2' => ['wrongType', '', '', '', '', '', true];
        yield 'bad 3' => ['', 'token', '', '', '', '', true];
        yield 'bad 4' => ['', '', '', '', '', '', true];
        yield 'bad 5' => ['path', 'token', null, '', '', '', true];
        yield 'bad 6' => ['path', 'token', '', null, '', '', true];
        yield 'bad 7' => ['path', 'token', '', '', null, '', true];
        yield 'bad 8' => ['path', 'token', '', '', '', null, true];
        yield 'bad 9' => [null, '', '', '', '', '', true];
        yield 'bad 10' => ['', null, '', '', '', '', true];
    }

    /**
     * @param array<string, string|bool>|null $preparationData
     * @param array<string, string|bool> $changes
     *
     * @dataProvider providerSecureToken
     */
    public function testSecureToken(array|null $preparationData, array $changes): void
    {
        $resource = $this->createTemporaryResource();

        if (is_array($preparationData)) {
            $this->enableResourceSecureToken(
                $resource,
                $preparationData['type'],
                $preparationData['value'],
                $preparationData['secureLinkExpiryParam'],
                $preparationData['secureLinkTokenParam'],
                $preparationData['secureLinkPathlenParam'],
                $preparationData['secureLinkSecretParam'],
                $preparationData['secureLinkRewritePlaylist'],
            );
        }

        FlushAndClear::do($this->getEntityManager());

        $response = $this->callApiGetResponse(
            $resource,
            [
                SecureTokenSchema::FIELD_SECURE_TOKEN_TYPE => $changes['type'],
                SecureTokenSchema::FIELD_SECURE_TOKEN_VALUE => $changes['value'],
                SecureTokenSchema::FIELD_SECURE_LINK_EXPIRY_PARAM => $changes['secureLinkExpiryParam'],
                SecureTokenSchema::FIELD_SECURE_LINK_TOKEN_PARAM => $changes['secureLinkTokenParam'],
                SecureTokenSchema::FIELD_SECURE_LINK_PATHLEN_PARAM => $changes['secureLinkPathlenParam'],
                SecureTokenSchema::FIELD_SECURE_LINK_SECRET_PARAM => $changes['secureLinkSecretParam'],
                SecureTokenSchema::FIELD_SECURE_LINK_REWRITE_PLAYLIST => $changes['secureLinkRewritePlaylist'],
            ],
        );

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());

        self::assertInstanceOf(SecureToken::class, $resource->getResourceSecureToken());

        self::assertSame($changes['type'], $resource->getResourceSecureToken()->getType());
        self::assertSame($changes['value'], $resource->getResourceSecureToken()->getValue());
        self::assertSame(
            $this->nullingEmptyString($changes['secureLinkExpiryParam']),
            $resource->getResourceSecureToken()->getSecureLinkExpiryParam(),
        );
        self::assertSame(
            $this->nullingEmptyString($changes['secureLinkTokenParam']),
            $resource->getResourceSecureToken()->getSecureLinkTokenParam(),
        );
        self::assertSame(
            $this->nullingEmptyString($changes['secureLinkPathlenParam']),
            $resource->getResourceSecureToken()->getSecureLinkPathlenParam(),
        );
        self::assertSame(
            $this->nullingEmptyString($changes['secureLinkSecretParam']),
            $resource->getResourceSecureToken()->getSecureLinkSecretParam(),
        );
        self::assertSame(
            $changes['secureLinkRewritePlaylist'],
            $resource->getResourceSecureToken()->hasSecureLinkRewritePlaylist(),
        );

        self::assertUpdatingMessageInQueue($resource->getId());
    }

    /** @dataProvider providerFailSecureToken */
    public function testFailOnResourceWithSecureToken(
        string|null $secureTokenType,
        string|null $secureTokenValue,
        string|null $secureLinkExpiryParam,
        string|null $secureLinkTokenParam,
        string|null $secureLinkPathlenParam,
        string|null $secureLinkSecretParam,
        bool $secureLinkRewritePlaylist,
    ): void {
        $resource = $this->createTemporaryResource();
        $secureToken = $this->enableResourceSecureToken($resource);

        $validType = $secureToken->getType();
        $validValue = $secureToken->getValue();
        $validSecureLinkExpiryParam = $secureToken->getSecureLinkExpiryParam();
        $validSecureLinkTokenParam = $secureToken->getSecureLinkTokenParam();
        $validSecureLinkPathlenParam = $secureToken->getSecureLinkPathlenParam();
        $validSecureLinkSecretParam = $secureToken->getSecureLinkSecretParam();
        $validSecureLinkRewritePlaylist = $secureToken->hasSecureLinkRewritePlaylist();

        FlushAndClear::do($this->getEntityManager());

        $response = $this->callApiGetResponse(
            $resource,
            [
                SecureTokenSchema::FIELD_SECURE_TOKEN_TYPE => $secureTokenType,
                SecureTokenSchema::FIELD_SECURE_TOKEN_VALUE => $secureTokenValue,
                SecureTokenSchema::FIELD_SECURE_LINK_EXPIRY_PARAM => $secureLinkExpiryParam,
                SecureTokenSchema::FIELD_SECURE_LINK_TOKEN_PARAM => $secureLinkTokenParam,
                SecureTokenSchema::FIELD_SECURE_LINK_PATHLEN_PARAM => $secureLinkPathlenParam,
                SecureTokenSchema::FIELD_SECURE_LINK_SECRET_PARAM => $secureLinkSecretParam,
                SecureTokenSchema::FIELD_SECURE_LINK_REWRITE_PLAYLIST => $secureLinkRewritePlaylist,
            ],
        );

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $resource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());

        self::assertInstanceOf(SecureToken::class, $resource->getResourceSecureToken());

        self::assertSame($validType, $resource->getResourceSecureToken()->getType());
        self::assertSame($validValue, $resource->getResourceSecureToken()->getValue());
        self::assertSame(
            $validSecureLinkExpiryParam,
            $resource->getResourceSecureToken()->getSecureLinkExpiryParam(),
        );
        self::assertSame(
            $validSecureLinkTokenParam,
            $resource->getResourceSecureToken()->getSecureLinkTokenParam(),
        );
        self::assertSame(
            $validSecureLinkPathlenParam,
            $resource->getResourceSecureToken()->getSecureLinkPathlenParam(),
        );
        self::assertSame(
            $validSecureLinkSecretParam,
            $resource->getResourceSecureToken()->getSecureLinkSecretParam(),
        );
        self::assertSame(
            $validSecureLinkRewritePlaylist,
            $resource->getResourceSecureToken()->hasSecureLinkRewritePlaylist(),
        );

        self::assertNoMessageInQueue();
    }

    /** @dataProvider providerFailSecureToken */
    public function testFailOnResourceWithoutSecureToken(
        string|null $secureTokenType,
        string|null $secureTokenValue,
        string|null $secureLinkExpiryParam,
        string|null $secureLinkTokenParam,
        string|null $secureLinkPathlenParam,
        string|null $secureLinkSecretParam,
        bool $secureLinkRewritePlaylist,
    ): void {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $response = $this->callApiGetResponse(
            $resource,
            [
                SecureTokenSchema::FIELD_SECURE_TOKEN_TYPE => $secureTokenType,
                SecureTokenSchema::FIELD_SECURE_TOKEN_VALUE => $secureTokenValue,
                SecureTokenSchema::FIELD_SECURE_LINK_EXPIRY_PARAM => $secureLinkExpiryParam,
                SecureTokenSchema::FIELD_SECURE_LINK_TOKEN_PARAM => $secureLinkTokenParam,
                SecureTokenSchema::FIELD_SECURE_LINK_PATHLEN_PARAM => $secureLinkPathlenParam,
                SecureTokenSchema::FIELD_SECURE_LINK_SECRET_PARAM => $secureLinkSecretParam,
                SecureTokenSchema::FIELD_SECURE_LINK_REWRITE_PLAYLIST => $secureLinkRewritePlaylist,
            ],
        );

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $resource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());

        self::assertNull($resource->getResourceSecureToken());

        self::assertNoMessageInQueue();
    }

    public function testIgnoreChange(): void
    {
        $resource = $this->createTemporaryResource();
        $secureToken = $this->enableResourceSecureToken($resource);

        $validType = $secureToken->getType();
        $validValue = $secureToken->getValue();
        $validSecureLinkExpiryParam = $secureToken->getSecureLinkExpiryParam();
        $validSecureLinkTokenParam = $secureToken->getSecureLinkTokenParam();
        $validSecureLinkPathlenParam = $secureToken->getSecureLinkPathlenParam();
        $validSecureLinkSecretParam = $secureToken->getSecureLinkSecretParam();
        $validSecureLinkRewritePlaylist = $secureToken->hasSecureLinkRewritePlaylist();

        FlushAndClear::do($this->getEntityManager());

        $response = $this->callApiGetResponse($resource, null);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());

        self::assertInstanceOf(SecureToken::class, $resource->getResourceSecureToken());

        self::assertSame($validType, $resource->getResourceSecureToken()->getType());
        self::assertSame($validValue, $resource->getResourceSecureToken()->getValue());
        self::assertSame(
            $validSecureLinkExpiryParam,
            $resource->getResourceSecureToken()->getSecureLinkExpiryParam(),
        );
        self::assertSame(
            $validSecureLinkTokenParam,
            $resource->getResourceSecureToken()->getSecureLinkTokenParam(),
        );
        self::assertSame(
            $validSecureLinkPathlenParam,
            $resource->getResourceSecureToken()->getSecureLinkPathlenParam(),
        );
        self::assertSame(
            $validSecureLinkSecretParam,
            $resource->getResourceSecureToken()->getSecureLinkSecretParam(),
        );
        self::assertSame(
            $validSecureLinkRewritePlaylist,
            $resource->getResourceSecureToken()->hasSecureLinkRewritePlaylist(),
        );

        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testDisableSecureToken(): void
    {
        $resource = $this->createTemporaryResource();
        $this->enableResourceSecureToken($resource);
        self::assertInstanceOf(SecureToken::class, $resource->getResourceSecureToken());

        FlushAndClear::do($this->getEntityManager());

        $response = $this->callApiGetResponse(
            $resource,
            [
                SecureTokenSchema::FIELD_SECURE_TOKEN_TYPE => ResourceChanger::DISABLE_SECURE_TOKEN_TYPE_STRING,
                SecureTokenSchema::FIELD_SECURE_TOKEN_VALUE => '',
                SecureTokenSchema::FIELD_SECURE_LINK_EXPIRY_PARAM => '',
                SecureTokenSchema::FIELD_SECURE_LINK_TOKEN_PARAM => '',
                SecureTokenSchema::FIELD_SECURE_LINK_PATHLEN_PARAM => '',
                SecureTokenSchema::FIELD_SECURE_LINK_SECRET_PARAM => '',
                SecureTokenSchema::FIELD_SECURE_LINK_REWRITE_PLAYLIST => '',
            ],
        );

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());

        self::assertNull($resource->getResourceSecureToken());

        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testTryDisableDisabledSecureToken(): void
    {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $response = $this->callApiGetResponse(
            $resource,
            [
                SecureTokenSchema::FIELD_SECURE_TOKEN_TYPE => ResourceChanger::DISABLE_SECURE_TOKEN_TYPE_STRING,
                SecureTokenSchema::FIELD_SECURE_TOKEN_VALUE => '',
                SecureTokenSchema::FIELD_SECURE_LINK_EXPIRY_PARAM => '',
                SecureTokenSchema::FIELD_SECURE_LINK_TOKEN_PARAM => '',
                SecureTokenSchema::FIELD_SECURE_LINK_PATHLEN_PARAM => '',
                SecureTokenSchema::FIELD_SECURE_LINK_SECRET_PARAM => '',
                SecureTokenSchema::FIELD_SECURE_LINK_REWRITE_PLAYLIST => '',
            ],
        );

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());

        self::assertNull($resource->getResourceSecureToken());

        self::assertUpdatingMessageInQueue($resource->getId());
    }

    /** @param array<string, string|null>|null $data */
    private function callApiGetResponse(CdnResource $resource, array|null $data): Response
    {
        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceAddInfo::FIELD_SECURE_TOKEN => $data],
                    ),
                ],
            ),
        );

        return $this->client->getResponse();
    }
}
