<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\SecureToken;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo;
use Cdn77\NxgApi\Resource\Application\Payload\SecureTokenSchema;
use Cdn77\NxgApi\Service\Legacy\Edit\ResourceChanger;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;

class AddTest extends WebTestCase
{
    use NotifyResourceChangeHelper;
    use TemporaryData;

    /** @return Generator<string, array<string, string, string|null, string|null, string|null, string|null, bool>> */
    public static function providerSecureToken(): Generator
    {
        yield 'all set 1' => ['highwinds', 'token', 'expiryParam', 'tokenParam', 'pathlenParam', 'secretParam', true];
        yield 'some set 1' => ['path', 'token', 'expiryParam', '', '', '', true];
        yield 'some set 2' => ['parameter', 'token', '', 'tokenParam', '', '', false];
        yield 'some set 3' => ['highwinds', 'token', '', '', 'pathlenParam', '', false];
        yield 'required set 1' => ['path', 'token', '', '', '', '', false];
    }

    /** @return Generator<string, array<string, string, string|null, string|null, string|null, string|null, bool>> */
    public static function providerFailSecureToken(): Generator
    {
        yield 'bad 1' => ['path', '', '', '', '', '', true];
        yield 'bad 2' => ['wrongType', '', '', '', '', '', true];
        yield 'bad 3' => ['', 'token', '', '', '', '', true];
        yield 'bad 4' => ['', '', '', '', '', '', true];
        yield 'bad 5' => ['path', 'token', null, '', '', '', true];
        yield 'bad 6' => ['path', 'token', '', null, '', '', true];
        yield 'bad 7' => ['path', 'token', '', '', null, '', true];
        yield 'bad 8' => ['path', 'token', '', '', '', null, true];
        yield 'bad 9' => [null, '', '', '', '', '', true];
        yield 'bad 10' => ['', null, '', '', '', '', true];
    }

    /** @dataProvider providerSecureToken */
    public function testOkWithSecureToken(
        string $secureTokenType,
        string $secureTokenValue,
        string|null $secureLinkExpiryParam,
        string|null $secureLinkTokenParam,
        string|null $secureLinkPathlenParam,
        string|null $secureLinkSecretParam,
        bool $secureLinkRewritePlaylist,
    ): void {
        $response = $this->callApiGetResponse([
            SecureTokenSchema::FIELD_SECURE_TOKEN_TYPE => $secureTokenType,
            SecureTokenSchema::FIELD_SECURE_TOKEN_VALUE => $secureTokenValue,
            SecureTokenSchema::FIELD_SECURE_LINK_EXPIRY_PARAM => $secureLinkExpiryParam,
            SecureTokenSchema::FIELD_SECURE_LINK_TOKEN_PARAM => $secureLinkTokenParam,
            SecureTokenSchema::FIELD_SECURE_LINK_PATHLEN_PARAM => $secureLinkPathlenParam,
            SecureTokenSchema::FIELD_SECURE_LINK_SECRET_PARAM => $secureLinkSecretParam,
            SecureTokenSchema::FIELD_SECURE_LINK_REWRITE_PLAYLIST => $secureLinkRewritePlaylist,
        ]);

        $responseSecureToken = $response->decodedContent['cdn_resource']['secure_token'];

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);

        $resource = $this->getEntityManager()->find(CdnResource::class, $resourceId);

        self::assertInstanceOf(CdnResource::class, $resource);
        self::assertSame($secureTokenType, $responseSecureToken['secure_token_type']);
        self::assertSame($secureTokenType, $resource->getResourceSecureToken()->getType());

        self::assertSame($secureTokenValue, $responseSecureToken['secure_token_value']);
        self::assertSame($secureTokenValue, $resource->getResourceSecureToken()->getValue());

        if ($secureLinkExpiryParam !== '') {
            self::assertSame($secureLinkExpiryParam, $responseSecureToken['secure_link_expiry_param']);
        }

        self::assertSame(
            $this->nullingEmptyString($secureLinkExpiryParam),
            $resource->getResourceSecureToken()->getSecureLinkExpiryParam(),
        );

        if ($secureLinkTokenParam !== '') {
            self::assertSame($secureLinkTokenParam, $responseSecureToken['secure_link_token_param']);
        }

        self::assertSame(
            $this->nullingEmptyString($secureLinkTokenParam),
            $resource->getResourceSecureToken()->getSecureLinkTokenParam(),
        );

        if ($secureLinkPathlenParam !== '') {
            self::assertSame($secureLinkPathlenParam, $responseSecureToken['secure_link_pathlen_param']);
        }

        self::assertSame(
            $this->nullingEmptyString($secureLinkPathlenParam),
            $resource->getResourceSecureToken()->getSecureLinkPathlenParam(),
        );

        if ($secureLinkSecretParam !== '') {
            self::assertSame($secureLinkSecretParam, $responseSecureToken['secure_link_secret_param']);
        }

        self::assertSame(
            $this->nullingEmptyString($secureLinkSecretParam),
            $resource->getResourceSecureToken()->getSecureLinkSecretParam(),
        );

        self::assertSame($secureLinkRewritePlaylist, $responseSecureToken['secure_link_rewrite_playlist']);
        self::assertSame(
            $secureLinkRewritePlaylist,
            $resource->getResourceSecureToken()->hasSecureLinkRewritePlaylist(),
        );
    }

    public function testOkWithSecureTokenAsNull(): void
    {
        $response = $this->callApiGetResponse(null);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);

        $resource = $this->getEntityManager()->find(CdnResource::class, $resourceId);

        self::assertInstanceOf(CdnResource::class, $resource);
        self::assertNull($resource->getResourceSecureToken());
    }

    public function testOkSecureTokenIgnoredWhenTypeIsNone(): void
    {
        $response = $this->callApiGetResponse([
            SecureTokenSchema::FIELD_SECURE_TOKEN_TYPE => ResourceChanger::DISABLE_SECURE_TOKEN_TYPE_STRING,
            SecureTokenSchema::FIELD_SECURE_TOKEN_VALUE => '',
            SecureTokenSchema::FIELD_SECURE_LINK_EXPIRY_PARAM => '',
            SecureTokenSchema::FIELD_SECURE_LINK_TOKEN_PARAM => '',
            SecureTokenSchema::FIELD_SECURE_LINK_PATHLEN_PARAM => '',
            SecureTokenSchema::FIELD_SECURE_LINK_SECRET_PARAM => '',
            SecureTokenSchema::FIELD_SECURE_LINK_REWRITE_PLAYLIST => '',
        ]);

        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);

        $resource = $this->getEntityManager()->find(CdnResource::class, $resourceId);

        self::assertInstanceOf(CdnResource::class, $resource);
        self::assertNull($resource->getResourceSecureToken());
    }

    /** @dataProvider providerFailSecureToken */
    public function testFailWithSecureToken(
        string|null $secureTokenType,
        string|null $secureTokenValue,
        string|null $secureLinkExpiryParam,
        string|null $secureLinkTokenParam,
        string|null $secureLinkPathlenParam,
        string|null $secureLinkSecretParam,
        bool $secureLinkRewritePlaylist,
    ): void {
        $response = $this->callApiGetResponse([
            SecureTokenSchema::FIELD_SECURE_TOKEN_TYPE => $secureTokenType,
            SecureTokenSchema::FIELD_SECURE_TOKEN_VALUE => $secureTokenValue,
            SecureTokenSchema::FIELD_SECURE_LINK_EXPIRY_PARAM => $secureLinkExpiryParam,
            SecureTokenSchema::FIELD_SECURE_LINK_TOKEN_PARAM => $secureLinkTokenParam,
            SecureTokenSchema::FIELD_SECURE_LINK_PATHLEN_PARAM => $secureLinkPathlenParam,
            SecureTokenSchema::FIELD_SECURE_LINK_SECRET_PARAM => $secureLinkSecretParam,
            SecureTokenSchema::FIELD_SECURE_LINK_REWRITE_PLAYLIST => $secureLinkRewritePlaylist,
        ]);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertNoMessageInQueue();
    }

    /** @param array<string, string|bool|null>|null $data */
    private function callApiGetResponse(array|null $data): ResponseDecoded
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn[ResourceAddInfo::FIELD_SECURE_TOKEN] = $data;

            $this->client->request(
                Request::METHOD_POST,
                '/cdn_resources.json',
                [],
                [],
                static::getDefaultHeaders(),
                json_encode(['cdn_resource' => $dataForNewCdn]),
            );

        $response = $this->client->getResponse();

        return ResponseDecoded::fromResponse($response);
    }
}
