<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller;

use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Resource\Application\Controller\GetCertificateController;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceCertificateSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceIdsSchema;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\CertificateBucket;
use Cdn77\NxgApi\Tests\Functional\CertificateBucketHelper;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function array_keys;
use function implode;
use function sprintf;

class GetCertificateControllerTest extends WebTestCase
{
    use CertificateBucketHelper;
    use TemporaryData;

    public function testSingleResourceWithoutCertificate(): void
    {
        $resource = $this->createTemporaryResource();

        $response = $this->callApiGetResponse([$resource->getId()]);

        $this->evaluateListResponse($response, $this->prepareValidData());
    }

    public function testSingleResourceWithSingleCertificate(): void
    {
        $resource = $this->createTemporaryResource();

        $sslFile = $this->addResourceSslFile($resource);
        $certificatePair = new CertificatePair('certificateString', 'keyString');

        $bucket = self::$container->get(CertificateBucket::class);
        $bucket->save($sslFile, $certificatePair);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $validData = sprintf(
            '{"1":{"certificate":"%s","key":"%s"},"%d":{"certificate":"%s","key":"%s"}}',
            self::CERT_1_PEM,
            self::CERT_1_KEY,
            $resource->getId(),
            $certificatePair->getCertificate(),
            $certificatePair->getPrivateKey(),
        );

        $response = $this->callApiGetResponse([$resource->getId()]);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        self::assertSame($validData, $response->response->getContent());
    }

    public function testSingleResourceWithMultipleCertificates(): void
    {
        $resource = $this->createTemporaryResource();

        $sslFileA = $this->addResourceSslFile($resource);
        $this->createCertificatePairAndSaveToBucket($sslFileA);

        $sslFileB = $this->addResourceSslFile($resource, $sslFileA->getSsl());
        $certificatePairB = $this->createCertificatePairAndSaveToBucket($sslFileB);

        $validData = $this->prepareValidData();
        $validData[$resource->getId()] = $this->prepareValidCertificateAndKey($certificatePairB);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse([$resource->getId()]);

        $this->evaluateListResponse($response, $validData);
    }

    public function testSingleResourceWithMultipleCertificatesWhenAssignedIsNotTheLast(): void
    {
        $resource = $this->createTemporaryResource();

        $sslFileA = $this->addResourceSslFile($resource);
        $certificatePairA = $this->createCertificatePairAndSaveToBucket($sslFileA);

        $validData = $this->prepareValidData();
        $validData[$resource->getId()] = $this->prepareValidCertificateAndKey($certificatePairA);

        $ssl = $sslFileA->getSsl();

        $sslFileB = $this->addResourceSslFile($resource, $ssl);
        $this->createCertificatePairAndSaveToBucket($sslFileB);

        $ssl->setAssignedIndex(1);

        $this->getEntityManager()->persist($ssl);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse([$resource->getId()]);

        $this->evaluateListResponse($response, $validData);
    }

    public function testSingleResourceWithMultipleCertificatesWhenAssignedIsMissing(): void
    {
        $resource = $this->createTemporaryResource();

        $sslFileA = $this->addResourceSslFile($resource);
        $certificatePairA = $this->createCertificatePairAndSaveToBucket($sslFileA);

        $validData = $this->prepareValidData();
        $validData[$resource->getId()] = $this->prepareValidCertificateAndKey($certificatePairA);

        $ssl = $sslFileA->getSsl();

        $sslFileB = $this->addResourceSslFile($resource, $ssl);
        $this->createCertificatePairAndSaveToBucket($sslFileB);

        $ssl->setAssignedIndex(666);

        $this->getEntityManager()->persist($ssl);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse([$resource->getId()]);

        $validData = ['errors' => ['Unknown error: Cannot find SslFile for assignedIndex specified in Ssl']];
        $this->evaluateErrorResponse($response, Response::HTTP_INTERNAL_SERVER_ERROR, $validData);
    }

    public function testSingleResourceWithMultipleHistoryCertificatesButNoneAssigned(): void
    {
        $resource = $this->createTemporaryResource();

        $sslFileA = $this->addResourceSslFile($resource);
        $this->createCertificatePairAndSaveToBucket($sslFileA);

        $ssl = $sslFileA->getSsl();

        $sslFileB = $this->addResourceSslFile($resource, $ssl);
        $this->createCertificatePairAndSaveToBucket($sslFileB);

        $this->getEntityManager()->remove($ssl);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse([$resource->getId()]);

        $this->evaluateListResponse($response, $this->prepareValidData());
    }

    public function testMultipleResourcesWhenOnlyOneHasCertificate(): void
    {
        $resource = $this->createTemporaryResource();
        $resourceB = $this->createTemporaryResource();

        $sslFile = $this->addResourceSslFile($resource);

        $certificatePair = $this->createCertificatePairAndSaveToBucket($sslFile);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse([$resource->getId(), $resourceB->getId()]);

        $validData = $this->prepareValidData();
        $validData[$resource->getId()] = $this->prepareValidCertificateAndKey($certificatePair);

        $this->evaluateListResponse($response, $validData);
    }

    public function testMultipleResourcesWithSingleCertificate(): void
    {
        $resources = $this->createTemporaryResources();

        $validData = $this->prepareValidData();
        foreach ($resources as $resource) {
            $sslFile = $this->addResourceSslFile($resource);

            $certificatePair = $this->createCertificatePairAndSaveToBucket($sslFile);

            $validData[$resource->getId()] = $this->prepareValidCertificateAndKey($certificatePair);
        }

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse(array_keys($resources));

        $this->evaluateListResponse($response, $validData);
    }

    public function testEmptyResourceIds(): void
    {
        $response = $this->callApiGetResponse([]);

        $errorMsg = "Query parameter 'resourceIds' is not specified.";
        $validResponseData = [
            'errors' => [$errorMsg],
            'fields' => [ResourceIdsSchema::PARAMETER_RESOURCE_IDS => [$errorMsg]],
        ];

        $this->evaluateErrorResponse($response, Response::HTTP_BAD_REQUEST, $validResponseData);
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->cleanBucketAndPrepareCertificate1();
    }

    /** @param array<int, array<string, mixed>> $validData */
    private function evaluateListResponse(ResponseDecoded $response, array $validData = []): void
    {
        self::assertSame(Response::HTTP_OK, $response->statusCode);

        self::assertEquals($validData, $response->decodedContent);
    }

    /** @param array<string, array<int|string, mixed>> $validData */
    private function evaluateErrorResponse(ResponseDecoded $response, int $validResponseCode, array $validData): void
    {
        self::assertSame($validResponseCode, $response->statusCode);

        self::assertEquals($validData, $response->decodedContent);
    }

    /** @param array<int> $resourceIds */
    private function callApiGetResponse(array $resourceIds): ResponseDecoded
    {
        $query = [];
        foreach ($resourceIds as $id) {
            $query[] = sprintf('%s[]=%d', ResourceIdsSchema::PARAMETER_RESOURCE_IDS, $id);
        }

        $this->client->request(
            Request::METHOD_GET,
            sprintf(
                '%s?%s',
                GetCertificateController::ROUTE,
                implode('&', $query),
            ),
            [],
            [],
            static::getDefaultHeaders(),
        );

        $response = $this->client->getResponse();

        return ResponseDecoded::fromResponse($response);
    }

    /** @return array<string, string> */
    private function prepareValidCertificateAndKey(CertificatePair $certificatePair): array
    {
        return [
            ResourceCertificateSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
            ResourceCertificateSchema::FIELD_KEY => $certificatePair->getPrivateKey(),
        ];
    }

    /** @return array<int, array<string, string>> */
    private function prepareValidData(): array
    {
        return [
            1 => [
                ResourceCertificateSchema::FIELD_CERTIFICATE => self::CERT_1_PEM,
                ResourceCertificateSchema::FIELD_KEY => self::CERT_1_KEY,
            ],
        ];
    }
}
