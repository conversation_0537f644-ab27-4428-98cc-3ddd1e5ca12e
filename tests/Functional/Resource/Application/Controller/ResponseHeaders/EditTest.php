<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\ResponseHeaders;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeadersSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Resource\Domain\DTO\ResponseHeaders;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function is_array;
use function Safe\json_encode;
use function sprintf;

class EditTest extends WebTestCase
{
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    public const ERROR_STRICTLY_ARRAY
        = 'This value have to be strictly an array (neither 1, 0, null, string etc. is allowed).';

    public const ERROR_DESERILAIZATION_FAILED = 'Request deserialization failed. Check the endpoint documentation.';
    public const ERROR_NOT_NULL = 'This value should not be null.';

    /**
     * @param array<string, string> $responseHeadersForPreparation
     * @param array<string, string> $responseHeadersForRequest
     *
     * @dataProvider providerOk
     */
    public function testOk(array $responseHeadersForPreparation, array $responseHeadersForRequest): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setResponseHeaders(new ResponseHeaders($responseHeadersForPreparation));

        FlushAndClear::do($this->getEntityManager());

        $requestData = $this->prepareRequestData($responseHeadersForRequest);
        $response = $this->callApiGetResponse($resource, $requestData);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertUpdatingMessageInQueue($resource->getId());

        $this->assertResponseHeaders($resource->getId(), $responseHeadersForRequest);
    }

    /** @return Generator<string, array<array<string, string>>> */
    public static function providerOk(): Generator
    {
        yield 'empty -> empty' => [[], []];
        yield 'empty -> some header' => [[], ['Access-Control-Allow-Origin' => 'some.value']];
        yield 'some header -> empty' => [['Access-Control-Allow-Origin' => 'some.value'], []];
        yield 'some header -> another header' => [
            ['Access-Control-Allow-Origin' => 'some.value'],
            ['Access-Control-Allow-Origin' => 'other.value, *.next.url'],
        ];
    }

    /**
     * @param mixed $responseHeaders
     *
     * @dataProvider providerFailType
     */
    public function testFailType($responseHeaders, string $errorMsg): void
    {
        $validResponseHeaders = ['Access-Control-Allow-Origin' => 'some.value'];
        $resource = $this->createTemporaryResource();
        $resource->setResponseHeaders(new ResponseHeaders($validResponseHeaders));

        FlushAndClear::do($this->getEntityManager());

        $requestData = $this->prepareRequestData($responseHeaders);
        $response = $this->callApiGetResponse($resource, $requestData);

        $this->evaluateFailedResponse($response, [$errorMsg], Response::HTTP_UNPROCESSABLE_ENTITY);

        $this->assertResponseHeaders($resource->getId(), $validResponseHeaders);
    }

    /** @return Generator<string, array<int, mixed>> */
    public static function providerFailType(): Generator
    {
        yield 'bad type 0' => ['', self::ERROR_DESERILAIZATION_FAILED];
        yield 'bad type 1' => ['0', self::ERROR_DESERILAIZATION_FAILED];
        yield 'bad type 2' => ['1', self::ERROR_DESERILAIZATION_FAILED];
        yield 'bad type 3' => [666, self::ERROR_DESERILAIZATION_FAILED];
        yield 'bad type 5' => [true, self::ERROR_DESERILAIZATION_FAILED];
        yield 'bad type 6' => [false, self::ERROR_DESERILAIZATION_FAILED];
    }

    /**
     * @param mixed $responseHeaders
     *
     * @dataProvider providerFail
     */
    public function testFail($responseHeaders, string $errorMsg): void
    {
        $validResponseHeaders = ['Access-Control-Allow-Origin' => 'some.value'];
        $resource = $this->createTemporaryResource();
        $resource->setResponseHeaders(new ResponseHeaders($validResponseHeaders));

        FlushAndClear::do($this->getEntityManager());

        $requestData = $this->prepareRequestData($responseHeaders);
        $response = $this->callApiGetResponse($resource, $requestData);

        $this->evaluateUnprocessableEntityResponse($response, $errorMsg, 'response_headers');

        $this->assertResponseHeaders($resource->getId(), $validResponseHeaders);
    }

    /** @return Generator<string, array<int, mixed>> */
    public static function providerFail(): Generator
    {
        yield 'bad type 4' => [null, self::ERROR_NOT_NULL];

        $forbiddenHeaderNames = [
            'accept-ranges',
            'alt-svc',
            'connection',
            'content-encoding',
            'content-length',
            'content-range',
            'date',
            'trailer',
            'transfer-encoding',
            'upgrade',
            'server',
            'www-authenticate',
            'WWW-Authenticate', // to check case insensitive
        ];

        foreach ($forbiddenHeaderNames as $index => $forbiddenHeaderName) {
            yield sprintf('Forbidden header name %d', $index) => [
                [$forbiddenHeaderName => 'Value'],
                sprintf('Response header name "%s" is forbidden.', $forbiddenHeaderName),
            ];
        }

        yield 'Invalid name with colon' => [
            ['Access-Control-Allow-Origin:' => 'Value'],
            'Response header name "Access-Control-Allow-Origin:" contains invalid characters.',
        ];

        yield 'Invalid value with backslash' => [
            ['Access-Control-Allow-Origin' => 'value_with_backslash\\'],
            'Response header value "value_with_backslash\\" contains invalid characters.',
        ];
    }

    public function testFailWhenResponseHeadersMissing(): void
    {
        $resource = $this->createTemporaryResource();

        $response = $this->callApiGetResponse($resource, [], [ResourceEditInfo::FIELD_RESPONSE_HEADERS]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            'This value should not be null.',
            'response_headers',
        );
    }

    public function testFailWhenCorsIsEnabled(): void
    {
        $resource = $this->createTemporaryResource();

        $requestData = $this->prepareRequestData(['Access-Control-Allow-Origin' => 'some.value']);
        $requestData['cors_origin_header'] = 1;

        $response = $this->callApiGetResponse($resource, $requestData, [ResourceEditInfo::FIELD_RESPONSE_HEADERS]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            'Response header name "Access-Control-Allow-Origin" is not allowed when cors_origin_header is enabled.',
            'response_headers',
        );
    }

    /** @param array<string, string> $responseHeaders */
    private function assertResponseHeaders(int $resourceId, array $responseHeaders): void
    {
        $resource = $this->getResource($resourceId);

        self::assertSame($responseHeaders, $resource->getResponseHeaders()->toArray());
    }

    /**
     * @param mixed $responseHeaders
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData($responseHeaders): array
    {
        if (is_array($responseHeaders)) {
            return [
                ResourceEditInfo::FIELD_RESPONSE_HEADERS => ResponseHeadersSchema::fromArray($responseHeaders)->headers,
            ];
        }

        return [ResourceEditInfo::FIELD_RESPONSE_HEADERS => $responseHeaders];
    }

    /**
     * @param array<string, mixed> $data
     * @param array<string> $propertiesToExclude
     */
    private function callApiGetResponse(
        CdnResource $resource,
        array $data,
        array $propertiesToExclude = [],
    ): ResponseDecoded {
        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        $data,
                        $propertiesToExclude,
                    ),
                ],
            ),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
