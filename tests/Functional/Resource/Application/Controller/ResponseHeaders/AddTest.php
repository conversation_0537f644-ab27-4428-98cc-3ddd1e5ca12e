<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\ResponseHeaders;

use Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeadersSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function is_array;
use function Safe\json_encode;
use function sprintf;

class AddTest extends WebTestCase
{
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    /**
     * @param array<string, string> $responseHeaders
     * @param array<string, string> $expected
     *
     * @dataProvider providerOk
     */
    public function testOk(array $responseHeaders, array $expected): void
    {
        $dataForNewCdn = $this->prepareRequestData($responseHeaders);
        $response = $this->callApiGetResponse($dataForNewCdn);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        self::assertSame($responseHeaders, $resource->getResponseHeaders()->toArray());

        self::assertSame(
            $expected,
            $response->decodedContent['cdn_resource']['response_headers'],
        );
    }

    /** @return Generator<string, array<int, array<int|string, array<string, string>|string>>> */
    public static function providerOk(): Generator
    {
        yield 'empty 1' => [[], []];
        yield 'data 1' => [
            ['Access-Control-Allow-Origin' => 'some.value'],
            [['name' => 'Access-Control-Allow-Origin', 'value' => 'some.value']],
        ];

        yield 'x-77 in middle' => [
            ['some-x-77-header' => 'some.value'],
            [['name' => 'some-x-77-header', 'value' => 'some.value']],
        ];
    }

    /**
     * @param mixed $responseHeaders
     *
     * @dataProvider providerFailType
     */
    public function testFailType($responseHeaders, string $errorMsg): void
    {
        $dataForNewCdn = $this->prepareRequestData($responseHeaders);
        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateFailedResponse($response, [$errorMsg], Response::HTTP_UNPROCESSABLE_ENTITY);
        self::assertNoMessageInQueue();
    }

    /** @return Generator<string, array<int, mixed>> */
    public static function providerFailType(): Generator
    {
        yield 'bad type 0' => ['', EditTest::ERROR_DESERILAIZATION_FAILED];
        yield 'bad type 1' => ['0', EditTest::ERROR_DESERILAIZATION_FAILED];
        yield 'bad type 2' => ['1', EditTest::ERROR_DESERILAIZATION_FAILED];
        yield 'bad type 3' => [666, EditTest::ERROR_DESERILAIZATION_FAILED];
        yield 'bad type 5' => [true, EditTest::ERROR_DESERILAIZATION_FAILED];
        yield 'bad type 6' => [false, EditTest::ERROR_DESERILAIZATION_FAILED];
    }

    /**
     * @param mixed $responseHeaders
     *
     * @dataProvider providerFail
     */
    public function testFail($responseHeaders, string $errorMsg): void
    {
        $dataForNewCdn = $this->prepareRequestData($responseHeaders);
        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse($response, $errorMsg, 'response_headers');
        self::assertNoMessageInQueue();
    }

    /** @return Generator<string, array<int, mixed>> */
    public static function providerFail(): Generator
    {
        yield 'bad type' => [null, EditTest::ERROR_NOT_NULL];

        $forbiddenHeaderNames = [
            'accept-ranges',
            'alt-svc',
            'connection',
            'content-encoding',
            'content-length',
            'content-range',
            'date',
            'trailer',
            'transfer-encoding',
            'upgrade',
            'server',
            'www-authenticate',
        ];

        foreach ($forbiddenHeaderNames as $index => $forbiddenHeaderName) {
            yield sprintf('Forbidden header name %d', $index) => [
                [$forbiddenHeaderName => 'Value'],
                sprintf('Response header name "%s" is forbidden.', $forbiddenHeaderName),
            ];
        }

        yield 'Invalid name with colon' => [
            ['Access-Control-Allow-Origin:' => 'Value'],
            'Response header name "Access-Control-Allow-Origin:" contains invalid characters.',
        ];

        yield 'Forbidden prefix' => [
            ['x-77-' => 'Value'],
            'Response header name containing "x-77-" is forbidden.',
        ];

        yield 'Invalid value with backslash' => [
            ['Access-Control-Allow-Origin' => 'value_with_backslash\\'],
            'Response header value "value_with_backslash\\" contains invalid characters.',
        ];
    }

    public function testFailWhenResponseHeadersMissing(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        unset($dataForNewCdn['response_headers']);

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse($response, EditTest::ERROR_NOT_NULL, 'response_headers');
    }

    public function testFailWhenCorsIsEnabled(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['cors_origin_header'] = 1;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'Response header name "Access-Control-Allow-Origin" is not allowed when cors_origin_header is enabled.',
            'response_headers',
        );
    }

    /**
     * @param mixed $responseHeaders
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData($responseHeaders): array
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();

        if (is_array($responseHeaders)) {
            $dataForNewCdn[ResourceEditInfo::FIELD_RESPONSE_HEADERS]
                = ResponseHeadersSchema::fromArray($responseHeaders)->headers;
        } else {
            $dataForNewCdn[ResourceEditInfo::FIELD_RESPONSE_HEADERS] = $responseHeaders;
        }

        return $dataForNewCdn;
    }

    /** @param array<string, mixed> $dataForNewCdn */
    private function callApiGetResponse(array $dataForNewCdn): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_POST,
            '/resource',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(['cdn_resource' => $dataForNewCdn]),
        );

        $response = $this->client->getResponse();

        return ResponseDecoded::fromResponse($response);
    }
}
