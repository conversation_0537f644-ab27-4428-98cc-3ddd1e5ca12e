<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\HotlinkProtection;

use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtectionAddress;
use Cdn77\NxgApi\HotlinkProtection\Domain\Value\HotlinkProtectionType;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function array_map;
use function range;
use function Safe\json_decode;
use function Safe\json_encode;

final class EditTest extends WebTestCase
{
    use NotifyResourceChangeHelper;
    use TemporaryData;

    public function testInvalidHotlinkProtectionType(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_HOTLINK_PROTECTION => [
                'type' => 'invalid-type',
                'deny_empty' => true,
                'addresses' => [
                    [ 'domain' => 'foo.bar'],
                ],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();
        $responseContent = $response->getContent();

        self::assertIsString($responseContent);
        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ["Type must be either 'whitelist', 'blacklist' or 'disabled'."],
                'fields' => [
                    'hotlink_protection.type' => ["Type must be either 'whitelist', 'blacklist' or 'disabled'."],
                ],
            ],
            json_decode($responseContent, true),
        );

        self::assertNoMessageInQueue();
    }

    public function testHotlinkProtectionWithNoAddresses(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_HOTLINK_PROTECTION => [
                'type' => HotlinkProtectionType::TYPE_WHITELIST,
                'deny_empty' => true,
                'addresses' => [],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $hotlinkProtection = $this->getEntityManager()
            ->getRepository(ResourceRefererProtection::class)
            ->findOneBy(['resource' => $resource]);

        $hotlinkProtectionAddresses = $this->getEntityManager()
            ->getRepository(ResourceRefererProtectionAddress::class)
            ->findBy(['referer' => $hotlinkProtection]);

        self::assertNotNull($hotlinkProtection);
        self::assertSame(HotlinkProtectionType::TYPE_WHITELIST, $hotlinkProtection->getType());
        self::assertCount(0, $hotlinkProtectionAddresses);
        self::assertTrue($hotlinkProtection->isEmptyDenied());

        $this->assertUpdatingMessageInQueue($resource->getId());
    }

    public function testDisableHotlinkProtectionWithEmptyDenied(): void
    {
        $resource = $this->createTemporaryResource();

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_HOTLINK_PROTECTION => [
                'type' => HotlinkProtectionType::TYPE_DISABLED,
                'deny_empty' => true,
                'addresses' => [],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $hotlinkProtection = $this->getEntityManager()
            ->getRepository(ResourceRefererProtection::class)
            ->findOneBy(['resource' => $resource]);

        self::assertInstanceOf(ResourceRefererProtection::class, $hotlinkProtection);
        self::assertSame(HotlinkProtectionType::TYPE_DISABLED, $hotlinkProtection->getType());
        self::assertCount(0, $hotlinkProtection->getAddresses());
        self::assertTrue($hotlinkProtection->isEmptyDenied());
    }

    public function testHotlinkProtectionChangeBlacklistToWhitelist(): void
    {
        $resource = $this->createTemporaryResource();
        $this->createResourceRefererProtection(
            $resource,
            ResourceRefererProtection::TYPE_BLACKLIST,
            true,
            ['*.foo', 'bar.baz'],
        );
        FlushAndClear::do($this->getEntityManager());

        $newAddresses = [['domain' => 'domain.com'], ['domain' => 'foo.com']];
        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_HOTLINK_PROTECTION => [
                'type' => ResourceRefererProtection::TYPE_WHITELIST,
                'deny_empty' => false,
                'addresses' => $newAddresses,
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $refererProtectionActual = $this->getEntityManager()
            ->getRepository(ResourceRefererProtection::class)
            ->findOneBy(['resource' => $resource]);
        self::assertInstanceOf(ResourceRefererProtection::class, $refererProtectionActual);

        $refererProtectionAddresses = $this->getEntityManager()
            ->getRepository(ResourceRefererProtectionAddress::class)
            ->findBy(['referer' => $refererProtectionActual]);

        self::assertSame('whitelist', $refererProtectionActual->getType());
        self::assertCount(2, $refererProtectionActual->getAddresses());
        self::assertFalse($refererProtectionActual->isEmptyDenied());
        self::assertSame($newAddresses, $refererProtectionActual->getAddresses()->map(
            static fn (ResourceRefererProtectionAddress $address): array => ['domain' => $address->getDomain()],
        )->toArray());

        self::assertCount(2, $refererProtectionAddresses);

        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testHotlinkProtectionWithTooManyAddresses(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_HOTLINK_PROTECTION => [
                'type' => 'whitelist',
                'deny_empty' => true,
                'addresses' => array_map(
                    static fn (int $i): array => [ 'domain' => $i . '.foo' ],
                    range(1, 301),
                ),
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();
        $responseContent = $response->getContent();

        self::assertIsString($responseContent);
        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['This collection should contain 300 elements or less.'],
                'fields' => [
                    'hotlink_protection.addresses' => ['This collection should contain 300 elements or less.'],
                ],
            ],
            json_decode($responseContent, true),
        );

        self::assertNoMessageInQueue();
    }

    public function testHotlinkProtectionWithInvalidAddress(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_HOTLINK_PROTECTION => [
                'type' => 'blacklist',
                'deny_empty' => true,
                'addresses' => [
                    [ 'domain' => 'domain.com'],
                    [ 'domain' => 'f oo.*'],
                    [ 'domain' => '***********'],
                ],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();
        $responseContent = $response->getContent();

        self::assertIsString($responseContent);
        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['Address should be either a domain or IP.'],
                'fields' => [
                    'hotlink_protection.addresses[1].domain' => ['Address should be either a domain or IP.'],
                ],
            ],
            json_decode($responseContent, true),
        );

        self::assertNoMessageInQueue();
    }

    public function testDisableHotlinkProtection(): void
    {
        $resource = $this->createTemporaryResource();
        $refererProtection = $this->createResourceRefererProtection(
            $resource,
            ResourceRefererProtection::TYPE_BLACKLIST,
            false,
            ['*.foo', 'bar.baz'],
        );
        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                $resource,
                [ResourceEditInfo::FIELD_INSTANT_SSL => 1],
            ),
            ResourceEditSchema::FIELD_HOTLINK_PROTECTION => [
                'type' => 'disabled',
                'deny_empty' => false,
                'addresses' => [],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $refererProtectionActual = $this->getEntityManager()
            ->getRepository(ResourceRefererProtection::class)
            ->findOneBy(['resource' => $resource]);

        $refererProtectionAddresses = $this->getEntityManager()
            ->getRepository(ResourceRefererProtectionAddress::class)
            ->findBy(['referer' => $refererProtection]);

        self::assertNull($refererProtectionActual);
        self::assertCount(0, $refererProtectionAddresses);

        self::assertUpdatingMessageInQueue($resource->getId());
    }
}
