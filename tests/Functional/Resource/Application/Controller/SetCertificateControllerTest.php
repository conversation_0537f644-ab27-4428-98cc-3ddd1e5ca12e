<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Resource\Application\Payload\SetCertificateSchema;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\CertificateBucket;
use Cdn77\NxgApi\Tests\Functional\Certificate\CertificateStorageTemporaryData;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Generators\CertificateDefinitions;
use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use Generator;
use Ramsey\Uuid\Uuid;
use ReflectionProperty;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function array_keys;
use function array_map;
use function assert;
use function Safe\json_decode;
use function Safe\json_encode;
use function sort;
use function sprintf;

final class SetCertificateControllerTest extends WebTestCase
{
    use EntityGetter;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use CertificateDefinitions;
    use CertificateStorageTemporaryData;
    use ResponseEvaluateHelper;

    protected function setUp(): void
    {
        parent::setUp();

        $this->setUpCertificateStorage();
    }

    public function testWithNoPreviousCertificate(): void
    {
        $now = new DateTimeImmutable();
        $resourceIds = array_map(
            static fn (CdnResource $resource): int => $resource->getId(),
            $this->createTemporaryResources(true),
        );

        $certificatePair = (new CertificatePairGenerator())->generateRandomCertificatePair();

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_POST,
            '/resources/set-certificate',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    SetCertificateSchema::FIELD_RESOURCE_IDS => $resourceIds,
                    SetCertificateSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                    SetCertificateSchema::FIELD_PRIVATE_KEY => $certificatePair->getPrivateKey(),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $sslRepository = $this->getEntityManager()->getRepository(Ssl::class);
        foreach ($resourceIds as $resourceId) {
            $ssl = $sslRepository->find($resourceId);
            self::assertInstanceOf(Ssl::class, $ssl);
            self::assertNotNull($ssl);

            self::assertCount(1, $ssl->getFiles());

            $file = $ssl->getFiles()->first();
            self::assertInstanceOf(SslFile::class, $file);
            self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
            self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
            self::assertSame($ssl->getAssignedIndex(), 1);
            self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());
            self::assertSame(
                $file->getExpiresAt()->getTimestamp(),
                $this->extractCertificateExpiration($certificatePair)->getTimestamp(),
            );
            self::assertSame([], $this->extractCertificateDomains($certificatePair));
        }
    }

    public function testSslFileAlreadyExists(): void
    {
        $now = new DateTimeImmutable();
        $resources = $this->createTemporaryResources(true);
        $resourceIds = array_keys($resources);

        $resource = Stub::create(CdnResource::class, ['id' => $resourceIds[0]]);
        $ssl = Stub::create(Ssl::class, ['resource' => $resource]);
        $sslFile = Stub::create(SslFile::class, ['ssl' => $ssl, 'index' => 1]);
        $bucket = self::$container->get(CertificateBucket::class);
        assert($bucket instanceof CertificateBucket);
        $bucket->save($sslFile, new CertificatePair('CERTIFICATE', 'PRIVATE_KEY'));

        $certificatePair = (new CertificatePairGenerator())->generateRandomCertificatePair();

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_POST,
            '/resources/set-certificate',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    SetCertificateSchema::FIELD_RESOURCE_IDS => $resourceIds,
                    SetCertificateSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                    SetCertificateSchema::FIELD_PRIVATE_KEY => $certificatePair->getPrivateKey(),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $sslRepository = $this->getEntityManager()->getRepository(Ssl::class);
        foreach ($resourceIds as $resourceId) {
            $ssl = $sslRepository->find($resourceId);
            self::assertInstanceOf(Ssl::class, $ssl);
            self::assertNotNull($ssl);

            self::assertCount(1, $ssl->getFiles());

            $file = $ssl->getFiles()->first();
            self::assertInstanceOf(SslFile::class, $file);
            self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
            self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
            self::assertSame($ssl->getAssignedIndex(), 1);
            self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());

            self::assertSame(
                $file->getExpiresAt()->getTimestamp(),
                $this->extractCertificateExpiration($certificatePair)->getTimestamp(),
            );
            self::assertSame([], $this->extractCertificateDomains($certificatePair));
        }

        $replacedSslCertificateFile = $bucket->get($sslFile);
        self::assertEquals($certificatePair->getCertificate(), $replacedSslCertificateFile->getCertificate());
        self::assertEquals($certificatePair->getPrivateKey(), $replacedSslCertificateFile->getPrivateKey());
    }

    public function testWithExistingPreviousCertificateFiles(): void
    {
        $now = new DateTimeImmutable();
        $resourceIds = [];
        $resources = $this->createTemporaryResources(true);
        $certificatePair = (new CertificatePairGenerator())->generateRandomCertificatePair();
        foreach ($resources as $resource) {
            $resourceIds[] = $resource->getId();
            $resource->setInstantSsl(true);

            $ssl = $this->enableResourceSsl($resource);

            for ($i = 1; $i <= 3; $i++) {
                $sslFile = $this->addResourceSslFile(
                    $resource,
                    $ssl,
                    $i,
                    new DateTimeImmutable(),
                    ['foo.bar'],
                    SslFile::TYPE_LETSENCRYPT,
                );

                $rp = new ReflectionProperty(SslFile::class, 'createdAt');
                $rp->setAccessible(true);
                $rp->setValue($sslFile, new DateTimeImmutable('- ' . ((3 - $i) * 10) . ' minutes'));
            }

            $ssl->setAssignedIndex(2);
            $ssl->setAssignedAt(new DateTimeImmutable('- 15 minutes'));
        }

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_POST,
            '/resources/set-certificate',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    SetCertificateSchema::FIELD_RESOURCE_IDS => $resourceIds,
                    SetCertificateSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                    SetCertificateSchema::FIELD_PRIVATE_KEY => $certificatePair->getPrivateKey(),
                ],
            ),
        );

        self::assertSame(Response::HTTP_NO_CONTENT, $this->client->getResponse()->getStatusCode());

        $sslRepository = $this->getEntityManager()->getRepository(Ssl::class);
        foreach ($resources as $resource) {
            $ssl = $sslRepository->find($resource->getId());
            self::assertInstanceOf(Ssl::class, $ssl);
            self::assertNotNull($ssl);

            self::assertCount(4, $ssl->getFiles());

            $file = $ssl->getFiles()->get(4);
            self::assertInstanceOf(SslFile::class, $file);
            self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
            self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
            self::assertSame($ssl->getAssignedIndex(), 4);
            self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());

            $updatedResource = $this->getEntityManager()->getRepository(CdnResource::class)->find($resource->getId());
            self::assertFalse($updatedResource->hasInstantSsl());
        }
    }

    public function testWithInvalidCertificatePair(): void
    {
        $resourceIds = array_map(
            static fn (CdnResource $resource): int => $resource->getId(),
            $this->createTemporaryResources(true),
        );

        $certificatePair = (new CertificatePairGenerator())->generateRandomInvalidCertificatePair();

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_POST,
            '/resources/set-certificate',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    SetCertificateSchema::FIELD_RESOURCE_IDS => $resourceIds,
                    SetCertificateSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                    SetCertificateSchema::FIELD_PRIVATE_KEY => $certificatePair->getPrivateKey(),
                ],
            ),
        );

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $this->client->getResponse()->getStatusCode());
        self::assertNoMessageInQueue();
    }

    public function testWithInvalidCertificateChain(): void
    {
        $resourceIds = array_map(
            static fn (CdnResource $resource): int => $resource->getId(),
            $this->createTemporaryResources(true),
        );

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();

        $certificatePairChain = new CertificatePair(
            $certificatePair->getCertificate()
            . $certificatePairGenerator->generateRandomCertificate()
            . "-----BEGIN CERTIFICATE-----\ninvalidCertificate\n-----END CERTIFICATE-----\n"
            . $certificatePairGenerator->generateRandomCertificate(),
            $certificatePair->getPrivateKey(),
        );

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_POST,
            '/resources/set-certificate',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    SetCertificateSchema::FIELD_RESOURCE_IDS => $resourceIds,
                    SetCertificateSchema::FIELD_CERTIFICATE => $certificatePairChain->getCertificate(),
                    SetCertificateSchema::FIELD_PRIVATE_KEY => $certificatePairChain->getPrivateKey(),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['The certificate chain is malformed, probably 2. intermediate is corrupted.'],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    public function testWithResourcesUnderDifferentAccounts(): void
    {
        $resourceIds = array_map(
            static fn (CdnResource $resource): int => $resource->getId(),
            $this->createTemporaryResources(),
        );
        sort($resourceIds);

        $certificatePair = (new CertificatePairGenerator())->generateRandomInvalidCertificatePair();

        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse(
            $resourceIds,
            $certificatePair->getCertificate(),
            $certificatePair->getPrivateKey(),
        );

        $this->evaluateFailedResponse(
            $response,
            [sprintf('All resources have to be under the same account. Wrong resources: %s', $resourceIds[1])],
            Response::HTTP_BAD_REQUEST,
        );
    }

    public function testWithUuidAsPrivateKey(): void
    {
        $now = new DateTimeImmutable();
        [$resourceA, $resourceB] = $this->createTemporaryResources(true);

        $originalCertificatePair = (new CertificatePairGenerator())->generateRandomCertificatePair();
        $uuid = Uuid::uuid4()->toString();

        $this->storePrivateKey($resourceA->getAccount()->getId(), $uuid, $originalCertificatePair);

        $response = $this->callApiGetResponse(
            [$resourceA->getId(), $resourceB->getId()],
            $originalCertificatePair->getCertificate(),
            $uuid,
        );

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);

        $bucket = self::$container->get(CertificateBucket::class);
        self::assertInstanceOf(CertificateBucket::class, $bucket);

        foreach ([$resourceA, $resourceB] as $resource) {
            $ssl = $this->getSsl($resource);

            self::assertCount(1, $ssl->getFiles());

            $file = $ssl->getFiles()->first();
            self::assertInstanceOf(SslFile::class, $file);
            self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
            self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
            self::assertSame(1, $ssl->getAssignedIndex());
            self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());

            $savedCertificatePair = $bucket->get($file);
            self::assertSame($originalCertificatePair->getCertificate(), $savedCertificatePair->getCertificate());
            self::assertSame($originalCertificatePair->getPrivateKey(), $savedCertificatePair->getPrivateKey());
        }
    }

    public function testWithNotExistingUuidAsPrivateKey(): void
    {
        $resource = $this->createTemporaryResource();

        $originalCertificatePair = (new CertificatePairGenerator())->generateRandomCertificatePair();
        $uuid = Uuid::uuid4()->toString();

        $this->storePrivateKey($resource->getAccount()->getId(), Uuid::uuid4()->toString(), $originalCertificatePair);

        $response = $this->callApiGetResponse(
            [$resource->getId()],
            $originalCertificatePair->getCertificate(),
            $uuid,
        );

        $this->evaluateFailedResponse(
            $response,
            [
                sprintf(
                    'Failed to read "%s/%s.key" when assigning certificate to resource.',
                    $resource->getAccount()->getId(),
                    $uuid,
                ),
            ],
            Response::HTTP_UNPROCESSABLE_ENTITY,
        );
    }

    public function testUuidWithInvalidCertificatePair(): void
    {
        $resource = $this->createTemporaryResource();

        $originalCertificatePair = (new CertificatePairGenerator())->generateRandomInvalidCertificatePair();
        $uuid = Uuid::uuid4()->toString();

        $this->storePrivateKey($resource->getAccount()->getId(), $uuid, $originalCertificatePair);

        $response = $this->callApiGetResponse(
            [$resource->getId()],
            $originalCertificatePair->getCertificate(),
            $uuid,
        );

        $this->evaluateFailedResponse(
            $response,
            ['Certificate and private key must form a valid certificate pair.'],
            Response::HTTP_UNPROCESSABLE_ENTITY,
        );
    }

    /**
     * @param string[] $domains
     *
     * @dataProvider certificatesProvider
     */
    public function testWithMultipleCertificateConfigurations(
        CertificatePair $certificatePair,
        array $domains,
        DateTimeImmutable $expiration,
    ): void {
        $now = new DateTimeImmutable();
        $resourceIds = array_map(
            static fn (CdnResource $resource): int => $resource->getId(),
            $this->createTemporaryResources(true),
        );

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_POST,
            '/resources/set-certificate',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    SetCertificateSchema::FIELD_RESOURCE_IDS => $resourceIds,
                    SetCertificateSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                    SetCertificateSchema::FIELD_PRIVATE_KEY => $certificatePair->getPrivateKey(),
                ],
            ),
        );

        self::assertSame(Response::HTTP_NO_CONTENT, $this->client->getResponse()->getStatusCode());

        foreach ($resourceIds as $resourceId) {
            $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resourceId);
            self::assertInstanceOf(Ssl::class, $ssl);
            self::assertNotNull($ssl);

            self::assertCount(1, $ssl->getFiles());

            $file = $ssl->getFiles()->first();
            self::assertInstanceOf(SslFile::class, $file);
            self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
            self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
            self::assertSame($ssl->getAssignedIndex(), 1);
            self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());

            self::assertSame(
                $expiration->getTimestamp(),
                $this->extractCertificateExpiration($certificatePair)->getTimestamp(),
            );
            self::assertSame($file->getExpiresAt()->getTimestamp(), $expiration->getTimestamp());

            self::assertSame($domains, $this->extractCertificateDomains($certificatePair));
            self::assertSame($domains, $file->getDomains());
        }
    }

    /** @return Generator<list<CertificatePair|array|DateTimeImmutable>> */
    public function certificatesProvider(): iterable
    {
        yield $this->getCertificateWithCommonNameAlternativeNames();
        yield $this->getCertificateWithCommonNameAndOneDifferentAlternativeName();
        yield $this->getCertificateWithCommonNameAndMultipleDifferentAternativeNames();
        yield $this->getCertificateWithCommonNameAndMatchingAlternativeNames();
        yield $this->getCertificateWithCommonNameAndMultipleAndNonDnsAlternativeNames();
    }

    /** @param array<int> $resourceIds */
    private function callApiGetResponse(array $resourceIds, string $certificate, string $privateKey): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_POST,
            '/resources/set-certificate',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    SetCertificateSchema::FIELD_RESOURCE_IDS => $resourceIds,
                    SetCertificateSchema::FIELD_CERTIFICATE => $certificate,
                    SetCertificateSchema::FIELD_PRIVATE_KEY => $privateKey,
                ],
            ),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
