<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\CustomLocation;
use Cdn77\NxgApi\Entity\Legacy\IgnoredQueryParam;
use Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtectionLocation;
use Cdn77\NxgApi\Entity\Legacy\ResourceIpProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceIpProtectionAddress;
use Cdn77\NxgApi\Entity\Legacy\ResourceOriginS3;
use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtectionAddress;
use Cdn77\NxgApi\GeoProtection\Domain\Value\GeoProtectionType;
use Cdn77\NxgApi\HotlinkProtection\Domain\Value\HotlinkProtectionType;
use Cdn77\NxgApi\IpProtection\Domain\Value\IpProtectionType;
use Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeadersSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Generators\CertificateDefinitions;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

use function array_map;
use function assert;
use function count;
use function is_array;
use function ksort;
use function range;
use function Safe\json_decode;
use function Safe\json_encode;
use function sort;
use function sprintf;
use function str_contains;
use function str_repeat;

final class EditControllerTest extends WebTestCase
{
    use CertificateDefinitions;
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    /** @return Generator<
     *     string,
     *     array<array<string, string>|null, array<string, string>|null>>
     */
    public static function providerOriginHeaders(): Generator
    {
        $data = ['abc' => 'def', 'ghi' => '666'];

        yield 'set data 1' => [null, $data];
        yield 'set data 2' => [[], $data];
        yield 'set empty 1' => [null, []];
        yield 'set empty 2' => [[], []];
        yield 'set empty 3' => [$data, []];
        yield 'change' => [$data, ['other' => 'string']];
    }

    /** @return Generator<string, array<array<string, string>|bool|string|int|null>> */
    public static function providerBadOriginHeaders(): Generator
    {
        $data = ['abc' => 'def', 'ghi' => '666'];

        yield 'bad data 1' => [null, 6];
        yield 'bad data 2' => [[], 6];
        yield 'bad data 3' => [$data, 6];
        yield 'bad data 4' => [null, 'string'];
        yield 'bad data 5' => [[], 'string'];
        yield 'bad data 6' => [$data, 'string'];
        yield 'bad data 7' => [null, true];
        yield 'bad data 8' => [[], true];
        yield 'bad data 9' => [$data, true];
        yield 'bad data 10' => [null, false];
        yield 'bad data 11' => [[], false];
        yield 'bad data 12' => [$data, false];
    }

    /** @return Generator<array{string}> */
    public static function providerBadDomain(): Generator
    {
        yield ['baddomain'];
        yield ['bad_domain.com'];
        yield ['-.not.good.com'];
        yield ['not.good-.com'];
        yield ['not.-good.com'];
        yield ['notok-.domain.com'];
    }

    public function testEdit(): void
    {
        $whitelistedCountries = [['country' => 'CZ']];
        $blacklistedDomains = [['domain' => 'some-domain.com']];
        $blacklistedNetworks = [['network' => '*******/32']];

        $resource = $this->createTemporaryResource();
        $group = $this->createLocationGroup();

        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, [
                'origin_url' => 'something.example.com',
                'resource_type' => 'HTTP_PULL',
                'group_id' => [
                    $group->getId(),
                ],
                'mp4_pseudo_streaming' => true,
                'cnames' => [
                    'foo-test.example.com',
                    'bar-test.example.com',
                ],
                'disable_query_string' => true,
                'ignore_set_cookie' => true,
                'cache_expiry' => 555555,
                'cache_expiry_404' => null,
                'cache_content_length_limit' => 1000000,
                'cache_lock_age' => 10,
                'cache_lock_timeout' => 20,
                'https_redirect_code' => 302,
                ResourceEditInfo::FIELD_SSL_VERIFY_DISABLE => false,
                ResourceEditInfo::FIELD_ORIGIN_BASEDIR => '',
                'custom_data' => ['edited' => 'yes'],
                ResourceAddInfo::FIELD_RATE_LIMIT => true,
                ResourceAddInfo::FIELD_CONTENT_DISPOSITION_BY_PARAM => true,
                ResourceAddInfo::FIELD_ORIGIN_HEADERS => ['header1' => '666', 'header2' => 'something'],
                ResourceEditInfo::FIELD_RESPONSE_HEADERS => ResponseHeadersSchema::fromArray(
                    ['Access-Control-Allow-Origin' => 'some.value'],
                )->headers,
            ]),
            ResourceEditSchema::FIELD_GEO_PROTECTION => [
                'type' => GeoProtectionType::TYPE_WHITELIST,
                'locations' => $whitelistedCountries,
            ],
            ResourceEditSchema::FIELD_HOTLINK_PROTECTION => [
                'type' => HotlinkProtectionType::TYPE_BLACKLIST,
                'addresses' => $blacklistedDomains,
                'deny_empty' => true,
            ],
            ResourceEditSchema::FIELD_IP_PROTECTION => [
                'type' => IpProtectionType::TYPE_BLACKLIST,
                'addresses' => $blacklistedNetworks,
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());

        $geoProtection = $this->getEntityManager()
            ->getRepository(ResourceGeoProtection::class)
            ->findOneBy(['resource' => $resource]);
        $hotlinkProtection = $this->getEntityManager()
            ->getRepository(ResourceRefererProtection::class)
            ->findOneBy(['resource' => $resource]);
        $ipProtection = $this->getEntityManager()
            ->getRepository(ResourceIpProtection::class)
            ->findOneBy(['resource' => $resource]);

        Assert::isInstanceOf($geoProtection, ResourceGeoProtection::class);
        Assert::isInstanceOf($hotlinkProtection, ResourceRefererProtection::class);
        Assert::isInstanceOf($ipProtection, ResourceIpProtection::class);

        self::assertNotNull($geoProtection);
        self::assertSame(GeoProtectionType::TYPE_WHITELIST, $geoProtection->getType());
        self::assertSame(count($whitelistedCountries), $geoProtection->getLocations()->count());
        self::assertNotNull($hotlinkProtection);
        self::assertSame(HotlinkProtectionType::TYPE_BLACKLIST, $hotlinkProtection->getType());
        self::assertSame(count($blacklistedDomains), $hotlinkProtection->getAddresses()->count());
        self::assertTrue($hotlinkProtection->isEmptyDenied());
        self::assertNotNull($ipProtection);
        self::assertSame(IpProtectionType::TYPE_BLACKLIST, $ipProtection->getType());
        self::assertSame(count($blacklistedNetworks), $ipProtection->getAddresses()->count());

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['origin_url'],
            $resource->getMainOrigin()->getHost(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['group_id'][0],
            $resource->getGroup()->getId(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['mp4_pseudo_streaming'],
            $resource->isMp4PseudoStreaming(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['cnames'],
            $resource->getCnames(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['disable_query_string'],
            $resource->isDisableQueryString(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['ignore_set_cookie'],
            $resource->isIgnoreSetCookie(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['cache_expiry'],
            $resource->getCaching()->getExpiry(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['cache_expiry_404'],
            $resource->getCaching()->getExpiry404(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['cache_content_length_limit'],
            $resource->getCaching()->getContentLengthLimit(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['cache_lock_age'],
            $resource->getCaching()->getLockAge(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['cache_lock_timeout'],
            $resource->getCaching()->getLockTimeout(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['https_redirect_code'],
            $resource->getHttpsRedirectCode(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['custom_data'],
            $resource->getCustomData(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['rate_limit'],
            $resource->hasRateLimit(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['content_disposition_by_param'],
            $resource->hasContentDispositionByParam(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['origin_headers'],
            $resource->getMainOrigin()->getOriginHeaders(),
        );
        self::assertSame(
            ['Access-Control-Allow-Origin' => 'some.value'],
            $resource->getResponseHeaders()->toArray(),
        );

        self::assertFalse($resource->getMainOrigin()->hasSslVerifyDisable());

        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testResourceForEditNotFound(): void
    {
        $nonExistingResourceId = **********;
        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => [
                'account_id' => 666,
                'origin_url' => 'some_new-origin.url.com',
                'forward_host_header' => true,
                'ssl_verify_disable' => true,
                'origin_basedir' => '',
                'origin_port' => 0,
                'origin_headers' => [],
                'response_headers' => [],
                'follow_redirect' => ['enabled' => true, 'codes' => []],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $nonExistingResourceId,
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NOT_FOUND, $response->getStatusCode());
    }

    public function testPartialEdit(): void
    {
        $resource = $this->createTemporaryResource();
        $geoProtectionType = ResourceGeoProtection::TYPE_BLACKLIST;
        $geoProtectionCountries = ['GB'];
        $this->createResourceGeoProtection($resource, $geoProtectionType, $geoProtectionCountries);

        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                $resource,
                ['origin_url' => 'something-else.ex-ample.com'],
            ),
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        $resource = $this->getResource($resource->getId());
        $geoProtection = $this->getEntityManager()
            ->getRepository(ResourceGeoProtection::class)
            ->findOneBy(['resource' => $resource]);

        Assert::isInstanceOf($geoProtection, ResourceGeoProtection::class);

        self::assertNotNull($geoProtection);
        self::assertSame(GeoProtectionType::TYPE_BLACKLIST, $geoProtection->getType());
        self::assertEquals(
            $geoProtectionCountries,
            array_map(
                static fn (ResourceGeoProtectionLocation $geoLocation) => $geoLocation->getCountry(),
                $geoProtection->getLocations()->toArray(),
            ),
        );
        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['origin_url'],
            $resource->getMainOrigin()->getHost(),
        );

        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testEditProtectionOnly(): void
    {
        $whitelistedCountries = [['country' => 'DE']];
        $blacklistedDomains = [['domain' => 'some.domain.com']];
        $blacklistedNetworks = [['network' => '*******/32'], ['network' => '127.0.0.1/32']];

        $resource = $this->createTemporaryResource();
        $this->createResourceGeoProtection($resource, GeoProtectionType::TYPE_BLACKLIST, ['DE', 'GB']);

        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_GEO_PROTECTION => [
                'type' => GeoProtectionType::TYPE_WHITELIST,
                'locations' => $whitelistedCountries,
            ],
            ResourceEditSchema::FIELD_HOTLINK_PROTECTION => [
                'type' => HotlinkProtectionType::TYPE_BLACKLIST,
                'addresses' => $blacklistedDomains,
                'deny_empty' => false,
            ],
            ResourceEditSchema::FIELD_IP_PROTECTION => [
                'type' => IpProtectionType::TYPE_BLACKLIST,
                'addresses' => $blacklistedNetworks,
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        $geoProtection = $this->getEntityManager()
            ->getRepository(ResourceGeoProtection::class)
            ->findOneBy(['resource' => $resource]);
        $hotlinkProtection = $this->getEntityManager()
            ->getRepository(ResourceRefererProtection::class)
            ->findOneBy(['resource' => $resource]);
        $ipProtection = $this->getEntityManager()
            ->getRepository(ResourceIpProtection::class)
            ->findOneBy(['resource' => $resource]);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        Assert::isInstanceOf($geoProtection, ResourceGeoProtection::class);
        Assert::isInstanceOf($hotlinkProtection, ResourceRefererProtection::class);
        Assert::isInstanceOf($ipProtection, ResourceIpProtection::class);

        self::assertNotNull($geoProtection);
        self::assertSame(GeoProtectionType::TYPE_WHITELIST, $geoProtection->getType());
        self::assertEquals(
            [$whitelistedCountries[0]['country']],
            array_map(
                static fn (ResourceGeoProtectionLocation $protectedLocation) => $protectedLocation->getCountry(),
                $geoProtection->getLocations()->toArray(),
            ),
        );
        self::assertNotNull($hotlinkProtection);
        self::assertSame(HotlinkProtectionType::TYPE_BLACKLIST, $hotlinkProtection->getType());
        self::assertEquals(
            [$blacklistedDomains[0]['domain']],
            array_map(
                static fn (ResourceRefererProtectionAddress $protectedDomain) => $protectedDomain->getDomain(),
                $hotlinkProtection->getAddresses()->toArray(),
            ),
        );
        self::assertFalse($hotlinkProtection->isEmptyDenied());
        self::assertNotNull($ipProtection);
        self::assertSame(IpProtectionType::TYPE_BLACKLIST, $ipProtection->getType());
        self::assertEquals(
            array_map(
                static fn (array $network) => str_contains($network['network'], '/')
                    ? $network['network']
                    : $network['network'] . '/32',
                $blacklistedNetworks,
            ),
            array_map(
                static fn (ResourceIpProtectionAddress $protectedNetwork) => $protectedNetwork->getAddress(),
                $ipProtection->getAddresses()->toArray(),
            ),
        );

        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testUnprocessableModification(): void
    {
        $whitelistedCountries = [['country' => 'DE']];
        $blacklistedDomains = [['domain' => 'some.domain.com']];
        $blacklistedNetworks = [['network' => '*******/32']];

        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                $resource,
                ['cnames' => ['test.cdn77.com']],
            ),
            ResourceEditSchema::FIELD_GEO_PROTECTION => [
                'type' => GeoProtectionType::TYPE_WHITELIST,
                'locations' => $whitelistedCountries,
            ],
            ResourceEditSchema::FIELD_HOTLINK_PROTECTION => [
                'type' => HotlinkProtectionType::TYPE_BLACKLIST,
                'addresses' => $blacklistedDomains,
                'deny_empty' => true,
            ],
            ResourceEditSchema::FIELD_IP_PROTECTION => [
                'type' => IpProtectionType::TYPE_BLACKLIST,
                'addresses' => $blacklistedNetworks,
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        $geoProtection = $this->getEntityManager()
            ->getRepository(ResourceGeoProtection::class)
            ->findOneBy(['resource' => $resource]);
        $hotlinkProtection = $this->getEntityManager()
            ->getRepository(ResourceRefererProtection::class)
            ->findOneBy(['resource' => $resource]);
        $ipProtection = $this->getEntityManager()
            ->getRepository(ResourceIpProtection::class)
            ->findOneBy(['resource' => $resource]);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertNull($geoProtection);
        self::assertNull($hotlinkProtection);
        self::assertNull($ipProtection);
    }

    public function testGroupChangeToLsGroup(): void
    {
        $resource = $this->createTemporaryResource();
        $group = $this->createLocationGroup('LS foo', false, true, 158);
        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                $resource,
                ['group_id' => [$group->getId()]],
            ),
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        $resource = $this->getResource($resource->getId());

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['group_id'][0],
            $resource->getGroup()->getId(),
        );
        self::assertSame(60, $resource->getCaching()->getLockAge());
        self::assertSame(5, $resource->getCaching()->getLockTimeout());
    }

    public function testPartialInput(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['cnames' => ['foo-test.example.com', 'bar-test.example.com']],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $updatedResource = $this->getResource($resource->getId());

        $resourceCustomData = $resource->getCustomData();
        $updatedResourceCustomData = $updatedResource->getCustomData();
        if (is_array($resourceCustomData)) {
            ksort($resourceCustomData);
        }

        if (is_array($updatedResourceCustomData)) {
            ksort($updatedResourceCustomData);
        }

        self::assertSame(['foo-test.example.com', 'bar-test.example.com'], $updatedResource->getCnames());
        self::assertSame($resource->getId(), $updatedResource->getId());
        self::assertSame($resource->getMainOrigin()->getHost(), $updatedResource->getMainOrigin()->getHost());
        self::assertSame($resource->getGroup()->getId(), $updatedResource->getGroup()->getId());
        self::assertSame($resource->isMp4PseudoStreaming(), $updatedResource->isMp4PseudoStreaming());
        self::assertSame($resource->isDisableQueryString(), $updatedResource->isDisableQueryString());
        self::assertSame($resource->isIgnoreSetCookie(), $updatedResource->isIgnoreSetCookie());
        self::assertSame($resource->getCaching()->getExpiry(), $updatedResource->getCaching()->getExpiry());
        self::assertSame($resource->getCdnUrl(), $updatedResource->getCdnUrl());
        self::assertSame($resource->isFlvPseudoStreaming(), $updatedResource->isFlvPseudoStreaming());
        self::assertSame($resource->isMp4PseudoStreaming(), $updatedResource->isMp4PseudoStreaming());
        self::assertEquals($resource->getCreated()->getTimestamp(), $updatedResource->getCreated()->getTimestamp());
        self::assertEquals(
            $resource->getGroupUpdated()->getTimestamp(),
            $updatedResource->getGroupUpdated()->getTimestamp(),
        );
        self::assertGreaterThanOrEqual(
            $resource->getUpdated()->getTimestamp(),
            $updatedResource->getUpdated()->getTimestamp(),
        );
        self::assertSame($resource->getPurgeAllKey(), $updatedResource->getPurgeAllKey());
        self::assertSame($resource->isSuspended(), $updatedResource->isSuspended());
        self::assertSame($resource->getHttpsRedirectCode(), $updatedResource->getHttpsRedirectCode());
        self::assertSame($resourceCustomData, $updatedResourceCustomData);

        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testFailureOnDuplicateCname(): void
    {
        $fooResource = $this->createTemporaryResource();
        $barResource = $this->createTemporaryResource();
        $editedResource = $this->createTemporaryResource();

        $fooResource->setCnames(['1.foo']);
        $barResource->setCnames(['2.bar', '3.baz']);
        $editedResource->setCnames(['4.xyz']);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $editedResource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $editedResource,
                        ['cnames' => ['5.abc', '2.bar']],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['2.bar is already used in another resource.'],
            ],
            json_decode($response->getContent(), true),
        );

        self::assertNoMessageInQueue();
    }

    public function testWorksOnUpdatingSelfCname(): void
    {
        $fooResource = $this->createTemporaryResource();
        $editedResource = $this->createTemporaryResource();

        $fooResource->setCnames(['1.foo']);
        $editedResource->setCnames(['2.bar', '3.baz']);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $editedResource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $editedResource,
                        ['cnames' => ['2.bar', '4.xyz']],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $updatedResource = $this->getResource($editedResource->getId());
        self::assertSame(['2.bar', '4.xyz'], $updatedResource->getCnames());

        self::assertUpdatingMessageInQueue($editedResource->getId());
    }

    public function testUpdatingToEmptyCnames(): void
    {
        $fooResource = $this->createTemporaryResource();
        $barResource = $this->createTemporaryResource();
        $bazResource = $this->createTemporaryResource();
        $editedResource = $this->createTemporaryResource();

        $fooResource->setCnames(['foo.test']);
        $barResource->setCnames(['bar.test', 'bar2.test']);
        $bazResource->setCnames([]);
        $editedResource->setCnames(['edited.test']);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $editedResource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($editedResource, ['cnames' => []]),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $updatedResource = $this->getResource($editedResource->getId());
        self::assertSame([], $updatedResource->getCnames());

        self::assertUpdatingMessageInQueue($editedResource->getId());
    }

    public function testFailsWhenAddingExistingCnameWithDifferentCase(): void
    {
        $foo = $this->createTemporaryResource();
        $foo->setCnames(['test.foo', 'Test.bar']);

        $bar = $this->createTemporaryResource();
        $bar->setCnames(['test2.foo']);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $bar->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $bar,
                        ['cnames' => ['Test.foo', 'test2.foo']],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['test.foo is already used in another resource.'],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    public function testCustomLocationIsDeletedWhenGroupIsChanged(): void
    {
        $groupFoo = $this->createLocationGroup();
        $groupBar = $this->createLocationGroup();
        $location = $this->createLocation('XX', 'Foo', 'XX', 'EU');
        $resource = $this->createResource($this->createAccount(), $groupFoo);
        $customLocation = $this->createCustomLocation($groupFoo, $resource);
        $customLocation->getPops()->add($this->createPop($location, true, true));

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['group_id' => [$groupBar->getId()]],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());
        self::assertSame($groupBar->getId(), $resource->getGroup()->getId());

        self::assertNull($this->getEntityManager()->find(CustomLocation::class, $resource));

        self::assertUpdatingMessageInQueue($resource->getId());
        self::assertUpdatingMessageInQueue($resource->getId());
        self::assertNoMessageInQueue();
    }

    public function testCustomLocationIsPreservedWhenGroupIsUnchanged(): void
    {
        $groupFoo = $this->createLocationGroup();
        $location = $this->createLocation('XX', 'Foo', 'XX', 'EU');
        $resource = $this->createResource($this->createAccount(), $groupFoo);
        $customLocation = $this->createCustomLocation($groupFoo, $resource);
        $customLocation->getPops()->add($this->createPop($location, true, true));

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['group_id' => [$groupFoo->getId()], 'ignore_set_cookie' => true],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());
        self::assertSame($groupFoo->getId(), $resource->getGroup()->getId());

        $customLocation = $this->getEntityManager()->find(CustomLocation::class, $resource);
        assert($customLocation instanceof CustomLocation);
        self::assertInstanceOf(CustomLocation::class, $customLocation);
        self::assertSame($groupFoo->getId(), $customLocation->getGroup()->getId());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testSettingIgnoredQueryParamsWhenNoExisted(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [
                            'ignored_query_params' => [
                                'foo',
                                'bar',
                            ],
                        ],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $updatedResource = $this->getResource($resource->getId());
        self::assertResourceHasIgnoredQueryParams($updatedResource, ['foo', 'bar']);
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testChangingIgnoredQueryParams(): void
    {
        $resource = $this->createTemporaryResource();
        $this->createResourceIgnoredParameter($resource, 'foo');
        $this->createResourceIgnoredParameter($resource, 'bar');
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [
                            'ignored_query_params' => [
                                'foo',
                                'baz',
                            ],
                        ],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
        $updatedResource = $this->getResource($resource->getId());

        self::assertResourceHasIgnoredQueryParams($updatedResource, ['foo', 'baz']);
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testNoChangeIsDoneWhenIgnoredQueryParamsIsNotSet(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getIgnoredQueryParams()->add(new IgnoredQueryParam($resource, 'foo'));
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['cnames' => ['foo.bar']],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $updatedResource = $this->getResource($resource->getId());
        self::assertResourceHasIgnoredQueryParams($updatedResource, ['foo']);
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testRejectsTooLongQueryParams(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['ignored_query_params' => [str_repeat('x', 41)]],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['Maximum length is 40 characters.'],
                'fields' => [
                    'cdn_resource.ignored_query_params[0]' => ['Maximum length is 40 characters.'],
                ],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    public function testSettingIgnoredQueryParamsWithUpperLimit(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getIgnoredQueryParams()->add(new IgnoredQueryParam($resource, 'foo'));
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [
                            'ignored_query_params' => $ignoredParams = array_map(
                                static fn ($i) => 'param' . $i,
                                range(1, 100),
                            ),
                        ],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $updatedResource = $this->getResource($resource->getId());
        self::assertResourceHasIgnoredQueryParams($updatedResource, $ignoredParams);
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testRejectsDuplicateQueryParams(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['ignored_query_params' => ['foo', 'Foo']],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $error = 'Ignored query parameters must be unique (case-insensitive).';
        self::assertSame(
            [
                'errors' => [$error],
                'fields' => [
                    'cdn_resource.ignored_query_params[1]' => [$error],
                ],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    public function testRejectsInvalidQueryParamFormat(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['ignored_query_params' => ['foo', 'bar?baz']],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['Parameter is in invalid format.'],
                'fields' => [
                    'cdn_resource.ignored_query_params[1]' => ['Parameter is in invalid format.'],
                ],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    public function testChangingIgnoredQueryParamsWithTooManyItems(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [
                            'ignored_query_params' => array_map(
                                static fn ($i) => 'param' . $i,
                                range(1, 101),
                            ),
                        ],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['You may only use up to 100 ignored query parameters.'],
                'fields' => [
                    'cdn_resource.ignored_query_params' => ['You may only use up to 100 ignored query parameters.'],
                ],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    public function testHttpsRedirectCodeChange(): void
    {
        $resource = $this->createTemporaryResource(['httpsRedirectCode' => 301]);
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['https_redirect_code' => 302],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $updatedResource = $this->getResource($resource->getId());
        self::assertSame(302, $updatedResource->getHttpsRedirectCode());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testAcceptsZeroToResetHttpsRedirectCode(): void
    {
        $resource = $this->createTemporaryResource(['httpsRedirectCode' => 301]);
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['https_redirect_code' => 0],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $updatedResource = $this->getResource($resource->getId());
        self::assertNull($updatedResource->getHttpsRedirectCode());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testAcceptsNullInHttpsRedirectCodeAndDoesNothing(): void
    {
        $resource = $this->createTemporaryResource(['httpsRedirectCode' => 301]);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['https_redirect_code' => null],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $updatedResource = $this->getResource($resource->getId());
        self::assertSame(301, $updatedResource->getHttpsRedirectCode());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testRejectsInvalidHttpsRedirectCode(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['https_redirect_code' => 404],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['Allowed values: 301, 302 or 0 to disable.'],
                'fields' => [
                    'cdn_resource.https_redirect_code' => ['Allowed values: 301, 302 or 0 to disable.'],
                ],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    public function testWithCnameLong64Bytes(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['cnames' => [str_repeat('x', 59) . '.test']],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testWithCnameLong65Bytes(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['cnames' => [str_repeat('x', 60) . '.test']],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['Maximum allowed CNAME length is 64 characters.'],
                'fields' => [
                    'cdn_resource.cnames[0]' => ['Maximum allowed CNAME length is 64 characters.'],
                ],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    public function testOriginUrlWithLocalhostIp(): void
    {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['origin_url' => '127.0.0.1'],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['127.0.0.1 is not a valid origin url or IP.'],
                'fields' => [
                    'cdn_resource.origin_url' => ['127.0.0.1 is not a valid origin url or IP.'],
                ],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    public function testOriginUrlWithReservedIp(): void
    {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['origin_url' => '*********'],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['********* is not a valid origin url or IP.'],
                'fields' => [
                    'cdn_resource.origin_url' => ['********* is not a valid origin url or IP.'],
                ],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    public function testFailedUnallowedOriginDomain(): void
    {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['origin_url' => 'abc.c.cdn77.org'],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['\'c.cdn77.org\' is not allowed origin.'],
                'fields' => [
                    'cdn_resource.origin_url' => ['\'c.cdn77.org\' is not allowed origin.'],
                ],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    /** @dataProvider providerBadDomain */
    public function testEditFailsWithBadDomain(string $badDomain): void
    {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                    $resource,
                    [ResourceEditInfo::FIELD_ORIGIN_URL => $badDomain],
                ),
            ]),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => [$badDomain . ' is not a valid origin url or IP.'],
                'fields' => [
                    'cdn_resource.origin_url' => [$badDomain . ' is not a valid origin url or IP.'],
                ],
            ],
            json_decode($response->getContent(), true),
        );

        $this->assertNoMessageInQueue();
    }

    public function testFailedUnallowedCname(): void
    {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['cnames' => ['test.cdn77.com']],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['\'cdn77.com\' is not allowed CNAME.'],
                'fields' => [
                    'cdn_resource.cnames[0]' => ['\'cdn77.com\' is not allowed CNAME.'],
                ],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    public function testChangingAccountToSameAccountDoesNothing(): void
    {
        $resource = $this->createTemporaryResource();
        $account = $resource->getAccount();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['account_id' => $account->getId()],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());
        self::assertSame($account->getId(), $resource->getAccount()->getId());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testChangingAccount(): void
    {
        $resource = $this->createTemporaryResource();
        $newAccount = $this->createAccount();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['account_id' => $newAccount->getId()],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());
        self::assertSame($newAccount->getId(), $resource->getAccount()->getId());
    }

    public function testChangingAccountCreatesAccount(): void
    {
        $resource = $this->createTemporaryResource();
        $newAccountId = $resource->getAccount()->getId() + 1;

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['account_id' => $newAccountId],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());
        self::assertSame($newAccountId, $resource->getAccount()->getId());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testFailNotSupplyingOriginPort(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setPort(8080);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['cnames' => ['foo.bar']],
                        ['origin_port'],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::evaluateUnprocessableEntityResponse($response, 'This value should not be null.', 'origin_port');

        $resource = $this->getResource($resource->getId());
        self::assertSame(8080, $resource->getMainOrigin()->getPort());
    }

    public function testInvalidOriginPort(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['origin_port' => 99999],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['Origin port should be either in range 1-65535 or 0 to disable.'],
                'fields' => [
                    'cdn_resource.origin_port' => ['Origin port should be either in range 1-65535 or 0 to disable.'],
                ],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    public function testResettingOriginPortWorks(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setPort(8080);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['origin_port' => 0],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());
        self::assertNull($resource->getMainOrigin()->getPort());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testForwardHostHeaderIsNotMandatoryAndNotReset(): void
    {
        $resource = $this->createTemporaryResource([], ['forwardHostHeader' => true]);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['cnames' => []],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());
        self::assertTrue($resource->getMainOrigin()->hasForwardHostHeader());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testForwardHostHeader(): void
    {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['forward_host_header' => false],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());
        self::assertFalse($resource->getMainOrigin()->hasForwardHostHeader());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testStreamingPlaylistBypassIsNotMandatoryAndNotReset(): void
    {
        $resource = $this->createTemporaryResource(['streamingPlaylistBypass' => true]);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['cnames' => []],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());
        self::assertTrue($resource->hasStreamingPlaylistBypass());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testStreamingPlaylistBypass(): void
    {
        $resource = $this->createTemporaryResource(['streamingPlaylistBypass' => true]);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['streaming_playlist_bypass' => false],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());
        self::assertFalse($resource->hasStreamingPlaylistBypass());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testChangingOriginTimeout(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setTimeout(10);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['origin_timeout' => 20],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());
        self::assertSame(20, $resource->getMainOrigin()->getTimeout());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testDisablingOriginTimeout(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setTimeout(10);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['origin_timeout' => 0],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());
        self::assertNull($resource->getMainOrigin()->getTimeout());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testOriginTimeoutTooLarge(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setTimeout(10);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['origin_timeout' => 150],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $error = 'Origin timeout should be either in range 1-120 or 0 to disable.';
        self::assertSame(
            [
                'errors' => [$error],
                'fields' => [
                    'cdn_resource.origin_timeout' => [$error],
                ],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    public function testOriginTimeoutTooSmall(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setTimeout(10);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['origin_timeout' => -1],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $error = 'Origin timeout should be either in range 1-120 or 0 to disable.';
        self::assertSame(
            [
                'errors' => [$error],
                'fields' => [
                    'cdn_resource.origin_timeout' => [$error],
                ],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    public function testNullingCustomData(): void
    {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['custom_data' => []],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());
        self::assertNull($resource->getCustomData());
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testSettingCustomData(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setCustomData([]);

        FlushAndClear::do($this->getEntityManager());

        $testCustomData = ['boolean' => true, 'float' => 88.8, 'integer' => 666, 'string' => 'text'];

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['custom_data' => $testCustomData],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());
        $actualCustomData = $resource->getCustomData();

        if (is_array($actualCustomData)) {
            ksort($actualCustomData);
        }

        self::assertSame($testCustomData, $actualCustomData);

        self::assertUpdatingMessageInQueue($resource->getId());
    }

    /**
     * @param array<string, string>|null $originHeadersForPreparation
     * @param array<string, string>|null $originHeadersForRequest
     *
     * @dataProvider providerOriginHeaders
     */
    public function testSettingOriginHeaders(array|null $originHeadersForPreparation, $originHeadersForRequest): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setOriginHeaders($originHeadersForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                ResourceEditSchema::FIELD_CDN_RESOURCE =>
                    $this->addRequiredData(
                        $resource,
                        [ResourceAddInfo::FIELD_ORIGIN_HEADERS => $originHeadersForRequest],
                    ),
            ]),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());

        $actualOriginHeaders = $resource->getMainOrigin()->getOriginHeaders();
        if (is_array($actualOriginHeaders)) {
            ksort($actualOriginHeaders);
        }

        if ($originHeadersForRequest === null) {
            $originHeadersForRequest = $originHeadersForPreparation;
        }

        if (is_array($originHeadersForRequest)) {
            ksort($originHeadersForRequest);
        }

        self::assertSame(ValueReplacer::emptyArrayToNull($originHeadersForRequest), $actualOriginHeaders);

        self::assertUpdatingMessageInQueue($resource->getId());
    }

    /**
     * @param array<string, string>|null $originHeadersForPreparation
     * @param mixed $originHeadersForRequest
     *
     * @dataProvider providerBadOriginHeaders
     */
    public function testFailSettingOriginHeaders(
        array|null $originHeadersForPreparation,
        $originHeadersForRequest,
    ): void {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setOriginHeaders($originHeadersForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                ResourceEditSchema::FIELD_CDN_RESOURCE =>
                    $this->addRequiredData(
                        $resource,
                        [ResourceAddInfo::FIELD_ORIGIN_HEADERS => $originHeadersForRequest],
                    ),
            ]),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());

        $actualOriginHeaders = $resource->getMainOrigin()->getOriginHeaders();
        if (is_array($actualOriginHeaders)) {
            ksort($actualOriginHeaders);
        }

        if (is_array($originHeadersForPreparation)) {
            ksort($originHeadersForPreparation);
        }

        self::assertSame(ValueReplacer::emptyArrayToNull($originHeadersForPreparation), $actualOriginHeaders);

        self::assertNoMessageInQueue();
    }

    public function testResourceEditWithBucketOriginEnabled(): void
    {
        $s3BucketName = 'test-bucket';
        $s3Type = 'cdn77-ceph-rgw';
        $host = 'us-1.cdn77-storage.com';
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [
                            ResourceEditInfo::FIELD_INSTANT_SSL => 1,
                            ResourceEditInfo::FIELD_ORIGIN_URL => $host,
                            ResourceEditInfo::FIELD_S3_BUCKET_NAME => $s3BucketName,
                            ResourceEditInfo::FIELD_S3_TYPE => $s3Type,
                        ],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());

        self::assertSame($s3BucketName, $resource->getMainOrigin()->getS3()->getBucketName());
        self::assertSame($s3Type, $resource->getMainOrigin()->getS3()->getType());
        self::assertSame($host, $resource->getMainOrigin()->getHost());
        self::assertTrue($resource->hasInstantSsl());
    }

    public function testResourceEditWithBucketOriginDisabled(): void
    {
        $s3BucketName = 'test-bucket';
        $s3Type = 'cdn77-ceph-rgw';
        $host = '123host.com';
        $resource = $this->createTemporaryResource();

        $resource->getMainOrigin()->setS3(new ResourceOriginS3(null, null, null, $s3BucketName, $s3Type));

        $this->getEntityManager()->persist($resource);
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [
                            ResourceEditInfo::FIELD_INSTANT_SSL => 1,
                            ResourceEditInfo::FIELD_S3_BUCKET_NAME => null,
                            ResourceEditInfo::FIELD_ORIGIN_URL => $host,
                        ],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());

        self::assertNull($resource->getMainOrigin()->getS3()->getBucketName());
        self::assertNull($resource->getMainOrigin()->getS3()->getType());
        self::assertTrue($resource->hasInstantSsl());
        self::assertSame($host, $resource->getMainOrigin()->getHost());
    }

    public function testResourceEditBucketFailsWithInvalidHost(): void
    {
        $s3BucketName = 'test-bucket';
        $s3Type = 'cdn77-ceph-rgw';
        $host = '123host.com';
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [
                            ResourceEditInfo::FIELD_INSTANT_SSL => 1,
                            ResourceEditInfo::FIELD_ORIGIN_URL => $host,
                            ResourceEditInfo::FIELD_S3_BUCKET_NAME => $s3BucketName,
                            ResourceEditInfo::FIELD_S3_TYPE => $s3Type,
                        ],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
    }

    public function testResourceEditBucketFailWithInvalidType(): void
    {
        $s3BucketName = 'test-bucket';
        $s3Type = 'external-s3';
        $host = 'us-1.cdn77-storage.com';
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [
                            ResourceEditInfo::FIELD_INSTANT_SSL => 1,
                            ResourceEditInfo::FIELD_ORIGIN_URL => $host,
                            ResourceEditInfo::FIELD_S3_BUCKET_NAME => $s3BucketName,
                            ResourceEditInfo::FIELD_S3_TYPE => $s3Type,
                        ],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
    }

    public function testSwitchNonStreamOriginToStreamOrigin(): void
    {
        $currentResource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $currentResource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                    $currentResource,
                    [ResourceEditInfo::FIELD_ORIGIN_URL => '1234.s.cdn77.com'],
                ),
            ]),
        );

        $response = $this->client->getResponse();
        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = $response->getContent();
        self::assertIsString($content);
        self::assertSame(
            [
                'errors' => ['Switch between stream and non-stream origins is not allowed.'],
            ],
            json_decode($content, true),
        );
    }

    public function testSwitchStreamOriginToNonStreamOrigin(): void
    {
        $currentResource = $this->createTemporaryResource();
        $currentResource->getMainOrigin()->setHost('1234.s.cdn77.com');

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $currentResource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $currentResource,
                        [ResourceEditInfo::FIELD_ORIGIN_URL => 'test.com'],
                    ),
                ],
            ),
        );

        $response = $this->client->getResponse();
        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = $response->getContent();
        self::assertIsString($content);
        self::assertSame(
            [
                'errors' => ['Switch between stream and non-stream origins is not allowed.'],
            ],
            json_decode($content, true),
        );
    }

    public function testSetCacheParameters(): void
    {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, [
                ResourceEditInfo::FIELD_CACHE_EXPIRY => 555555,
                ResourceEditInfo::FIELD_CACHE_EXPIRY_404 => null,
                ResourceEditInfo::FIELD_CACHE_CONTENT_LENGTH_LIMIT => null,
                ResourceEditInfo::FIELD_CACHE_CONTENT_LOCK_AGE => null,
                ResourceEditInfo::FIELD_CACHE_CONTENT_LOCK_TIMEOUT => null,
            ]),
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $resource = $this->getResource($resource->getId());

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['cache_expiry'],
            $resource->getCaching()->getExpiry(),
        );
        self::assertSame(
            $modifiedData[ResourceEditSchema::FIELD_CDN_RESOURCE]['cache_expiry_404'],
            $resource->getCaching()->getExpiry404(),
        );
        self::assertNull($resource->getCaching()->getContentLengthLimit());
        self::assertNull($resource->getCaching()->getLockAge());
        self::assertNull($resource->getCaching()->getLockTimeout());
    }

    public function testFailMissingParameters(): void
    {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([ResourceEditSchema::FIELD_CDN_RESOURCE => []]),
        );

        $response = $this->client->getResponse();
        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $content = $response->getContent();
        self::assertIsString($content);

        self::assertSame(
            [
                'errors' => [
                    'This value have to be strictly an integer (neither string, null, boolean, array etc. is allowed).',
                    'This value should not be null.',
                    'This value should not be null.',
                    'This value have to be strictly a string (neither 1, 0, null, boolean, array etc. is allowed).',
                    'This value should not be null.',
                    'This value have to be strictly a boolean (neither 1, 0, null, string etc. is allowed).',
                    'This value should not be null.',
                    'This value should not be null.',
                    'This value should not be null.',
                ],
                'fields' => [
                    'cdn_resource.account_id' => ['This value have to be strictly an integer (neither string, null, boolean, array etc. is allowed).'], // phpcs:ignore
                    'cdn_resource.origin_url' => ['This value should not be null.'],
                    'cdn_resource.origin_port' => ['This value should not be null.'],
                    'cdn_resource.origin_basedir' => ['This value have to be strictly a string (neither 1, 0, null, boolean, array etc. is allowed).'], // phpcs:ignore
                    'cdn_resource.forward_host_header' => ['This value should not be null.'], // phpcs:ignore
                    'cdn_resource.ssl_verify_disable' => ['This value have to be strictly a boolean (neither 1, 0, null, string etc. is allowed).'], // phpcs:ignore
                    'cdn_resource.origin_headers' => ['This value should not be null.'],
                    'cdn_resource.response_headers' => ['This value should not be null.'],
                    'cdn_resource.follow_redirect' => ['This value should not be null.'],
                ],
            ],
            json_decode($content, true),
        );
    }

    /** @param string[] $expected */
    private function assertResourceHasIgnoredQueryParams(CdnResource $resource, array $expected): void
    {
        sort($expected);

        $actual = $resource->getIgnoredQueryParams()->getKeys();
        sort($actual);

        self::assertSame($expected, $actual);
    }
}
