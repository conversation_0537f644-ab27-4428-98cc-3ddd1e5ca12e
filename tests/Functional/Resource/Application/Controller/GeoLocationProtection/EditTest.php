<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\GeoLocationProtection;

use Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtectionLocation;
use Cdn77\NxgApi\GeoProtection\Domain\Value\GeoProtectionType;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;

final class EditTest extends WebTestCase
{
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    public function testInvalidGeoProtectionType(): void
    {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_GEO_PROTECTION => [
                'type' => 'invalid-type',
                'locations' => [['country' => 'DE']],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        $this->evaluateUnprocessableEntityResponse(
            $response,
            "The value should be either 'whitelist', 'blacklist' or 'disabled'.",
            'type',
            'geo_protection',
        );
    }

    public function testInvalidCountryCode(): void
    {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_GEO_PROTECTION => [
                'type' => GeoProtectionType::TYPE_WHITELIST,
                'locations' => [['country' => 'UNKNOWN']],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'UNKNOWN is not a valid country.',
            'locations[0].country',
            'geo_protection',
        );
    }

    public function testAllowedNotIsoCountryCode(): void
    {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_GEO_PROTECTION => [
                'type' => GeoProtectionType::TYPE_WHITELIST,
                'locations' => [['country' => 'XK']],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
    }

    public function testNoGeoLocations(): void
    {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_GEO_PROTECTION => [
                'type' => GeoProtectionType::TYPE_BLACKLIST,
                'locations' => [],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
    }

    public function testGeoProtectionChangeBlacklistToWhitelist(): void
    {
        $resource = $this->createTemporaryResource();

        $this->createResourceGeoProtection($resource, GeoProtectionType::TYPE_BLACKLIST, ['DE', 'GB']);

        FlushAndClear::do($this->getEntityManager());

        $newLocations = [['country' => 'CZ'], ['country' => 'US']];
        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_GEO_PROTECTION => [
                'type' => 'whitelist',
                'locations' => $newLocations,
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $geoProtection = $this->getEntityManager()
            ->getRepository(ResourceGeoProtection::class)
            ->findOneBy(['resource' => $resource]);

        $geoProtectionLocations = $this->getEntityManager()
            ->getRepository(ResourceGeoProtectionLocation::class)
            ->findBy(['geoProtection' => $geoProtection]);

        self::assertNotNull($geoProtection);
        self::assertSame('whitelist', $geoProtection->getType());
        self::assertCount(2, $geoProtection->getLocations());
        self::assertSame($newLocations, $geoProtection->getLocations()->map(
            static fn (ResourceGeoProtectionLocation $location): array => ['country' => $location->getCountry()],
        )->toArray());

        self::assertCount(2, $geoProtectionLocations);

        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testDisableGeoLocations(): void
    {
        $resource = $this->createTemporaryResource();
        $geoProtection = $this->createResourceGeoProtection($resource, GeoProtectionType::TYPE_BLACKLIST, ['DE', 'GB']);

        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                $resource,
                [ResourceEditInfo::FIELD_INSTANT_SSL => 1],
            ),
            ResourceEditSchema::FIELD_GEO_PROTECTION => [
                'type' => GeoProtectionType::TYPE_DISABLED,
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $geoProtectionActual = $this->getEntityManager()
            ->getRepository(ResourceGeoProtection::class)
            ->findOneBy(['resource' => $resource]);

        $geoProtectionLocations = $this->getEntityManager()
            ->getRepository(ResourceGeoProtectionLocation::class)
            ->findBy(['geoProtection' => $geoProtection]);

        self::assertNull($geoProtectionActual);
        self::assertCount(0, $geoProtectionLocations);
    }
}
