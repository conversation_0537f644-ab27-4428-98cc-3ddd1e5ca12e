<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller;

use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_decode;
use function Safe\json_encode;

final class EnableDatacentersControllerTest extends WebTestCase
{
    use TemporaryData;

    public function testEnableLocations(): void
    {
        $locationA = $this->createLocation('tmpCZ', 'tmp', 'CZ', 'EU');
        $locationB = $this->createLocation('tmp_2_CZ', 'tmp', 'CZ', 'EU');
        $locationC = $this->createLocation('testUS', 'test', 'US', 'NA');
        $locationD = $this->createLocation('tmpshanghaiCN', 'tmpshanghai', 'CN', 'AS');
        $locationE = $this->createLocation('tmpshanghai_2_CN', 'tmpshanghai', 'CN', 'AS');
        $locationF = $this->createLocation('tmpshanghai_3_CN', 'tmpshanghai', 'CN', 'AS');
        $locationG = $this->createLocation('tmptest', 'tmptest', 'CN', 'AS');

        $popA = $this->createPop($locationA, true, true, 'ABC-Something');
        $popB = $this->createPop($locationB, true, true, 'ABC-Something');
        $popC = $this->createPop($locationC, true, true, 'EFG-Something');
        $popD = $this->createPop($locationD, true, true, 'HIJ-Something');
        $popE = $this->createPop($locationE, true, true, 'HIJ-Something');
        $popF = $this->createPop($locationE, true, true, 'HIJ-Something');
        $popG = $this->createPop($locationF, true, true, 'HIJ-Something');
        $popH = $this->createPop($locationG, true, true, 'KLM-Something');

        $serverA = $this->createServerWithIp(false, true);
        $serverB = $this->createServerWithIp(false, true);
        $serverC = $this->createServerWithIp(false, true);
        $serverD = $this->createServerWithIp(false, true);
        $serverE = $this->createServerWithIp(false, true);
        $serverF = $this->createServerWithIp(false, true);
        $serverG = $this->createServerWithIp(false, true);
        $serverH = $this->createServerWithIp(false, true);

        $this->assignServerPop($serverA, $popA);
        $this->assignServerPop($serverB, $popB);
        $this->assignServerPop($serverC, $popC);
        $this->assignServerPop($serverD, $popD);
        $this->assignServerPop($serverE, $popE);
        $this->assignServerPop($serverF, $popF);
        $this->assignServerPop($serverG, $popG);
        $this->assignServerPop($serverH, $popH);

        $groupA = $this->createLocationGroup();
        $groupA->getPops()->add($popA);
        $groupA->getPops()->add($popB);
        $groupA->getPops()->add($popC);
        $groupA->getPops()->add($popD);
        $groupA->getPops()->add($popE);

        $groupB = $this->createLocationGroup();
        $groupB->getPops()->add($popF);
        $groupB->getPops()->add($popG);
        $groupB->getPops()->add($popH);

        $resource = $this->createResourceWithAccount($groupA);

        $this->client->request(
            Request::METHOD_PUT,
            '/resource/' . $resource->getId() . '/datacenters',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                ['tmp', 'tmpshanghai', 'nonexistent', 'tmptest'],
            ),
        );

        $enabledResponse = $this->client->getResponse();

        self::assertSame(Response::HTTP_ACCEPTED, $enabledResponse->getStatusCode());

        $connection = $this->getEntityManager()->getConnection();
        $invalidAssignedPopCount = $connection->fetchOne(
            <<<'PSQL'
SELECT count(popGroupRelation)
FROM group_pop_relation popGroupRelation
WHERE popGroupRelation.pop_id IN (
    SELECT relation.pop_id
    FROM custom_location_relation relation
    WHERE relation.resource_id = :resourceId
)
AND NOT EXISTS (
    SELECT popGroupRelation
    FROM group_pop_relation popGroupRelation
    WHERE popGroupRelation.group_id = :groupId
)
PSQL
            ,
            [
                'resourceId' => $resource->getId(),
                'groupId' => $groupA->getId(),
            ],
        );

        self::assertSame(0, $invalidAssignedPopCount);

        $this->client->request(
            Request::METHOD_GET,
            '/resource/' . $resource->getId() . '/datacenters',
            [],
            [],
            static::getDefaultHeaders(),
        );

        $listResponse = $this->client->getResponse();

        $expectedResponse = [
            [
                'city_id' => 'tmp',
                'city_code' => 'ABC',
                'is_enabled' => true,
            ],
            [
                'city_id' => 'test',
                'city_code' => 'EFG',
                'is_enabled' => false,
            ],
            [
                'city_id' => 'tmpshanghai',
                'city_code' => 'HIJ',
                'is_enabled' => true,
            ],
        ];

        self::assertSame(Response::HTTP_OK, $listResponse->getStatusCode());
        self::assertSame($expectedResponse, json_decode($listResponse->getContent(), true));
    }
}
