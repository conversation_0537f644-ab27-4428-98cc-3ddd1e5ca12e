<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\SslVerifyDisable;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Generators\CertificateDefinitions;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;
use function sprintf;

final class EditTest extends WebTestCase
{
    use CertificateDefinitions;
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use ResponseEvaluateHelper;
    use TemporaryData;

    /** @return Generator<string, array<bool>> */
    public static function providerOk(): Generator
    {
        yield 'true -> true' => [true, true];
        yield 'true -> false' => [true, false];
        yield 'false -> true' => [false, true];
        yield 'false -> false' => [false, false];
    }

    /** @return Generator<string, array<bool|string|int|array|null>> */
    public static function providerFail(): Generator
    {
        yield 'false -> bad enabled 1' => [false, 'true'];
        yield 'false -> bad enabled 2' => [false, 'false'];
        yield 'false -> bad enabled 3' => [false, 'string'];
        yield 'false -> bad enabled 4' => [false, '1'];
        yield 'false -> bad enabled 5' => [false, '0'];
        yield 'false -> bad enabled 6' => [false, 1];
        yield 'false -> bad enabled 7' => [false, 0];
        yield 'false -> bad enabled 8' => [false, []];
        yield 'false -> bad enabled 9' => [false, null];

        yield 'true -> bad enabled 1' => [true, 'true'];
        yield 'true -> bad enabled 2' => [true, 'false'];
        yield 'true -> bad enabled 3' => [true, 'string'];
        yield 'true -> bad enabled 4' => [true, '1'];
        yield 'true -> bad enabled 5' => [true, '0'];
        yield 'true -> bad enabled 6' => [true, 1];
        yield 'true -> bad enabled 7' => [true, 0];
        yield 'true -> bad enabled 8' => [true, []];
        yield 'true -> bad enabled 9' => [true, null];
    }

    /** @dataProvider providerOk */
    public function testOk(bool $sslVerifyDisableForPreparation, bool $sslVerifyDisableForRequest): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setSslVerifyDisable($sslVerifyDisableForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $requestData = $this->prepareRequestData($sslVerifyDisableForRequest);
        $response = $this->callApiGetResponse($resource, $requestData);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertUpdatingMessageInQueue($resource->getId());

        $this->assertSslVerifyDisable($resource->getId(), $sslVerifyDisableForRequest);
    }

    /**
     * @param mixed $sslVerifyDisableForRequest
     *
     * @dataProvider providerFail
     */
    public function testFail(bool $sslVerifyDisableForPreparation, $sslVerifyDisableForRequest): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setSslVerifyDisable($sslVerifyDisableForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $requestData = $this->prepareRequestData($sslVerifyDisableForRequest);
        $response = $this->callApiGetResponse($resource, $requestData);

        $this->assertUnprocessableEntityResponse($response);

        $this->assertSslVerifyDisable($resource->getId(), $sslVerifyDisableForPreparation);
    }

    private function assertUnprocessableEntityResponse(Response $response): void
    {
        $this->evaluateUnprocessableEntityResponse(
            $response,
            'This value have to be strictly a boolean (neither 1, 0, null, string etc. is allowed).',
            'ssl_verify_disable',
        );
    }

    private function assertSslVerifyDisable(int $resourceId, bool $expecteSslVerifyDisable): void
    {
        $resource = $this->getResource($resourceId);

        self::assertSame($expecteSslVerifyDisable, $resource->getMainOrigin()->hasSslVerifyDisable());
    }

    /**
     * @param mixed $sslVerifyDisable
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData($sslVerifyDisable): array
    {
        return [ResourceEditInfo::FIELD_SSL_VERIFY_DISABLE => $sslVerifyDisable];
    }

    /** @param array<string, string|null> $data */
    private function callApiGetResponse(CdnResource $resource, array $data): Response
    {
        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        $data,
                    ),
                ],
            ),
        );

        return $this->client->getResponse();
    }
}
