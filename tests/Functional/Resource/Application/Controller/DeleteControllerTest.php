<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\LocationGroup;
use Cdn77\NxgApi\Filter\Resource\DeletedResourceFilter;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function sprintf;

class DeleteControllerTest extends WebTestCase
{
    use NotifyResourceChangeHelper;
    use TemporaryData;

    public function testDeletingInvalidResource(): void
    {
        $this->client->request(
            Request::METHOD_DELETE,
            '/cdn_resources/123456789.json',
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        self::assertNoMessageInQueue();
    }

    public function testDeletingExistingResource(): void
    {
        $entityManager = $this->getEntityManager();
        $this->createLocationGroup('Default group', true, true, 4);
        $resource = $this->createTemporaryResource();
        $resourceId = $resource->getId();

        $entityManager->clear();

        $this->client->request(
            Request::METHOD_DELETE,
            sprintf('/cdn_resources/%d.json', $resourceId),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $filters = $entityManager->getFilters();
        $filters->disable(DeletedResourceFilter::NAME);
        $deletedResource = $entityManager->find(CdnResource::class, $resourceId);
        self::assertInstanceOf(CdnResource::class, $deletedResource);
        self::assertTrue($deletedResource->isDeleted());
        self::assertSame(LocationGroup::DEFAULT_ID, $deletedResource->getGroup()->getId());
        self::assertSingleDeletingMessageInQueue($resource->getId());
    }
}
