<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller;

use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_decode;

final class DatacenterListControllerTest extends WebTestCase
{
    use TemporaryData;

    public function testNonCustomDcLocationsListResponseForResource(): void
    {
        $locationA = $this->createLocation('tmpCZ', 'tmp', 'CZ', 'EU');
        $locationB = $this->createLocation('tmp_2_CZ', 'tmp', 'CZ', 'EU');
        $locationC = $this->createLocation('tmp_3_CZ', 'thecity', 'CZ', 'EU');
        $locationD = $this->createLocation('test_IE', 'test', 'IE', 'EU');
        $locationWithoutServer = $this->createLocation('without-server-location', 'testing', 'CZ', 'EU');

        $popA = $this->createPop($locationA, true, true, 'ABC-Something');
        $popB = $this->createPop($locationB, true, true, 'ABC-Anything');
        $popC = $this->createPop($locationC, true, true, 'EFG-Description');
        $popD = $this->createPop($locationD, true, true, 'XYZ-Something');
        $popWithoutServer = $this->createPop($locationWithoutServer, true, true, 'ABC-Without');

        $this->createServerWithIp(false, true, true, $popA);
        $this->createServerWithIp(false, true, true, $popB);
        $this->createServerWithIp(false, true, true, $popC);

        $groupA = $this->createLocationGroup();
        $groupA->getPops()->add($popA);
        $groupA->getPops()->add($popB);
        $groupA->getPops()->add($popC);
        $groupA->getPops()->add($popWithoutServer);

        $groupB = $this->createLocationGroup();
        $groupB->getPops()->add($popD);

        $resourceA = $this->createResourceWithAccount($groupA);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_GET,
            '/resource/' . $resourceA->getId() . '/datacenters',
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        $expectedResponse = [
            [
                'city_id' => 'tmp',
                'city_code' => 'ABC',
                'is_enabled' => true,
            ],
            [
                'city_id' => 'thecity',
                'city_code' => 'EFG',
                'is_enabled' => true,
            ],
        ];

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame($expectedResponse, json_decode($response->getContent(), true));
    }

    public function testCustomDcLocationsListResponseForResource(): void
    {
        $locationA = $this->createLocation('tmpCZ', 'tmp', 'CZ', 'EU');
        $locationB = $this->createLocation('tmp_2_CZ', 'tmp', 'CZ', 'EU');
        $locationWithoutServer = $this->createLocation('without-server-location', 'testing', 'CZ', 'EU');

        $popA = $this->createPop($locationA, true, true, 'ABC-Something');
        $popB = $this->createPop($locationB, true, true, 'ABC-Something');
        $popWithoutServer = $this->createPop($locationWithoutServer, true, true, 'ABC-Without');

        $this->createServerWithIp(false, true, true, $popA);
        $this->createServerWithIp(false, true, true, $popB);

        $groupA = $this->createLocationGroup();
        $groupA->getPops()->add($popA);

        $resource = $this->createResourceWithAccount($groupA);

        $this->createCustomLocationWithPops($groupA, $resource, $popB, $popWithoutServer);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_GET,
            '/resource/' . $resource->getId() . '/datacenters',
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        $expectedResponse = [
            [
                'city_id' => 'tmp',
                'city_code' => 'ABC',
                'is_enabled' => true,
            ],
        ];

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame($expectedResponse, json_decode($response->getContent(), true));
    }
}
