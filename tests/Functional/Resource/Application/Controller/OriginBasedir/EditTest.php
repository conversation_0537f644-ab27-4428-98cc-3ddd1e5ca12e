<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\OriginBasedir;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;
use function sprintf;
use function str_repeat;

final class EditTest extends WebTestCase
{
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    /** @return Generator<string, array<string|null>> */
    public static function providerOriginBasedir(): Generator
    {
        yield 'empty -> empty' => [null, ''];
        yield 'basedir -> empty' => ['basedir', ''];

        yield 'empty -> basedir 1' => [null, 'basedir'];
        yield 'empty -> basedir 2' => [null, 'basedir/another'];
        yield 'empty -> basedir 3' => [null, 'basedir/another/file.type'];
        yield 'empty -> basedir 4' => [null, str_repeat('a', ResourceEditInfo::MAX_ORIGIN_BASEDIR_LENGTH)];

        yield 'basedir -> other basedir 1' => ['basedir', 'other/basedir'];
    }

    /** @return Generator<string, array<int, array<mixed>|string|int|bool|null>> */
    public static function providerBadOriginBasedir(): Generator
    {
        $strictlyStringErrorMsg =
            'This value have to be strictly a string (neither 1, 0, null, boolean, array etc. is allowed).';
        $slashErrorMsg = "Origin base dir can't start or end with '/'.";
        $tooLongErrorMsg = 'This value is too long. It should have 255 characters or less.';

        yield 'empty -> bad type 1' => [null, 0, $strictlyStringErrorMsg];
        yield 'empty -> bad type 2' => [null, 1, $strictlyStringErrorMsg];
        yield 'empty -> bad type 3' => [null, [], $strictlyStringErrorMsg];
        yield 'empty -> bad type 4' => [null, null, $strictlyStringErrorMsg];
        yield 'empty -> bad type 5' => [null, true, $strictlyStringErrorMsg];
        yield 'empty -> bad type 6' => [null, false, $strictlyStringErrorMsg];

        yield 'empty -> bad value 1' => [null, '/basedir', $slashErrorMsg];
        yield 'empty -> bad value 2' => [null, '/basedir/', $slashErrorMsg];
        yield 'empty -> bad value 3' => [null, 'basedir/', $slashErrorMsg];
        yield 'empty -> bad value 4' => [
            null,
            str_repeat('a', ResourceEditInfo::MAX_ORIGIN_BASEDIR_LENGTH + 1),
            $tooLongErrorMsg,
        ];

        yield 'basedir -> bad type 1' => ['basedir', 0, $strictlyStringErrorMsg];
        yield 'basedir -> bad type 2' => ['basedir', 1, $strictlyStringErrorMsg];
        yield 'basedir -> bad type 3' => ['basedir', [], $strictlyStringErrorMsg];
        yield 'basedir -> bad type 4' => ['basedir', null, $strictlyStringErrorMsg];
        yield 'basedir -> bad type 5' => ['basedir', true, $strictlyStringErrorMsg];
        yield 'basedir -> bad type 6' => ['basedir', false, $strictlyStringErrorMsg];

        yield 'basedir -> bad value 1' => ['basedir', '/basedir', $slashErrorMsg];
        yield 'basedir -> bad value 2' => ['basedir', '/basedir/', $slashErrorMsg];
        yield 'basedir -> bad value 3' => ['basedir', 'basedir/', $slashErrorMsg];
        yield 'basedir -> bad value 4' => ['basedir', '/', $slashErrorMsg];
        yield 'basedir -> bad value 5' => [
            'basedir',
            str_repeat('a', ResourceEditInfo::MAX_ORIGIN_BASEDIR_LENGTH + 1),
            $tooLongErrorMsg,
        ];
    }

    /** @dataProvider providerOriginBasedir */
    public function testOk(string|null $originBasedirForPreparation, string $originBasedirForRequest): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setBasedir($originBasedirForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $requestData = $this->prepareRequestData($originBasedirForRequest);
        $response = $this->callApiGetResponse($resource, $requestData);

        $updatedResource = $this->getResource($resource->getId());

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertUpdatingMessageInQueue($resource->getId());

        self::assertSame(
            ValueReplacer::emptyStringToNull($originBasedirForRequest),
            $updatedResource->getMainOrigin()->getBasedir(),
        );
    }

    /**
     * @param mixed $originBasedirForRequest
     *
     * @dataProvider providerBadOriginBasedir
     */
    public function testFail(string|null $originBasedirForPreparation, $originBasedirForRequest, string $errorMsg): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setBasedir($originBasedirForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $requestData = $this->prepareRequestData($originBasedirForRequest);
        $response = $this->callApiGetResponse($resource, $requestData);

        self::evaluateUnprocessableEntityResponse($response, $errorMsg, 'origin_basedir');

        $updatedResource = $this->getResource($resource->getId());

        self::assertSame($originBasedirForPreparation, $updatedResource->getMainOrigin()->getBasedir());
    }

    /**
     * @param mixed $originBasedir
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData($originBasedir): array
    {
        return [ResourceEditInfo::FIELD_ORIGIN_BASEDIR => $originBasedir];
    }

    /** @param array<string, string|null> $data */
    private function callApiGetResponse(CdnResource $resource, array $data): Response
    {
        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        $data,
                    ),
                ],
            ),
        );

        return $this->client->getResponse();
    }
}
