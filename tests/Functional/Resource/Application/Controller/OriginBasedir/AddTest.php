<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\OriginBasedir;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

use function Safe\json_decode;
use function Safe\json_encode;
use function str_repeat;

class AddTest extends WebTestCase
{
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    /** @return Generator<string, array<string>> */
    public static function providerOriginBasedir(): Generator
    {
        yield 'empty 1' => [''];
        yield 'data 1' => ['basedir'];
        yield 'data 2' => ['basedir/another'];
        yield 'data 3' => ['basedir/another/file.type'];
        yield 'data 4' => [str_repeat('a', ResourceEditInfo::MAX_ORIGIN_BASEDIR_LENGTH)];
    }

    /** @return Generator<string, array<int, array<mixed>|int|bool|string|null>> */
    public static function providerFailOriginBasedir(): Generator
    {
        $strictlyStringErrorMsg =
            'This value have to be strictly a string (neither 1, 0, null, boolean, array etc. is allowed).';
        $slashErrorMsg = "Origin base dir can't start or end with '/'.";
        $tooLongErrorMsg = 'This value is too long. It should have 255 characters or less.';

        yield 'bad type 1' => [0, $strictlyStringErrorMsg];
        yield 'bad type 2' => [1, $strictlyStringErrorMsg];
        yield 'bad type 3' => [[], $strictlyStringErrorMsg];
        yield 'bad type 4' => [null, $strictlyStringErrorMsg];
        yield 'bad type 5' => [true, $strictlyStringErrorMsg];
        yield 'bad type 6' => [false, $strictlyStringErrorMsg];

        yield 'bad value 1' => ['/string', $slashErrorMsg];
        yield 'bad value 2' => ['/string/', $slashErrorMsg];
        yield 'bad value 3' => ['string/', $slashErrorMsg];
        yield 'bad value 4' => ['/', $slashErrorMsg];
        yield 'bad value 5' => [str_repeat('a', ResourceEditInfo::MAX_ORIGIN_BASEDIR_LENGTH + 1), $tooLongErrorMsg];
    }

    /** @dataProvider providerOriginBasedir */
    public function testOk(string $originBasedir): void
    {
        $dataForNewCdn = $this->prepareRequestData($originBasedir);
        $response = $this->callApiGetResponse($dataForNewCdn);

        $content = $response->getContent();
        Assert::string($content);
        $responseData = json_decode($content, true);

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        $resourceId = $responseData['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        self::assertSame($originBasedir, $responseData['cdn_resource'][ResourceAddInfo::FIELD_ORIGIN_BASEDIR]);
        self::assertSame(ValueReplacer::emptyStringToNull($originBasedir), $resource->getMainOrigin()->getBasedir());
    }

    /**
     * @param mixed $originBasedir
     *
     * @dataProvider providerFailOriginBasedir
     */
    public function testFail($originBasedir, string $errorMsg): void
    {
        $dataForNewCdn = $this->prepareRequestData($originBasedir);
        $response = $this->callApiGetResponse($dataForNewCdn);

        self::evaluateUnprocessableEntityResponse($response, $errorMsg, 'origin_basedir');
    }

    /**
     * @param mixed $originBasedir
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData($originBasedir): array
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();

        $dataForNewCdn[ResourceAddInfo::FIELD_ORIGIN_BASEDIR] = $originBasedir;

        return $dataForNewCdn;
    }

    /** @param array<string, mixed> $dataForNewCdn */
    private function callApiGetResponse(array $dataForNewCdn): Response
    {
        $this->client->request(
            Request::METHOD_POST,
            '/cdn_resources.json',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(['cdn_resource' => $dataForNewCdn]),
        );

        return $this->client->getResponse();
    }
}
