<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\RequestState;
use Cdn77\NxgApi\Entity\LetsEncrypt\ResultStatus;
use Cdn77\NxgApi\LetsEncrypt\Domain\Value\RequestStateReason;
use Cdn77\NxgApi\Resource\Domain\Enum\CertificateStatus;
use Cdn77\NxgApi\Resource\Domain\Enum\CertificateStatusDescription;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use DateTime;
use DateTimeImmutable;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

use function Safe\json_decode;
use function sprintf;

final class StatusControllerTest extends WebTestCase
{
    use TemporaryData;

    /** @return Generator<string, array<string>> */
    public static function providerCertificateType(): Generator
    {
        yield 'custom' => [SslFile::TYPE_CUSTOM];
        yield 'instant' => [SslFile::TYPE_LETSENCRYPT];
    }

    /** @return Generator<string, array<int, ResultStatus|string|null>> */
    public static function providerFailedResult(): Generator
    {
        $errorDesc = 'error desc';

        yield 'generating error then validation error'
            => [ResultStatus::getError(), ResultStatus::getValidationError(), $errorDesc, null, $errorDesc];

        yield 'validation error then generating error'
            => [ResultStatus::getValidationError(), ResultStatus::getError(), $errorDesc, $errorDesc, null];

        yield 'single validation error' => [ResultStatus::getValidationError(), null, $errorDesc, null, $errorDesc];
        yield 'single generating error' => [ResultStatus::getError(), null, $errorDesc, $errorDesc, null];
    }

    /** @return Generator<string, array<int, array<int, string>|int|string>>> */
    public static function providerMultipleAssignedFiles(): Generator
    {
        yield 'only letsencrypt with last assigned'
            => [[SslFile::TYPE_LETSENCRYPT, SslFile::TYPE_LETSENCRYPT], SslFile::TYPE_LETSENCRYPT, 2];

        yield 'only custom with last assigned'
            => [[SslFile::TYPE_CUSTOM, SslFile::TYPE_CUSTOM], SslFile::TYPE_CUSTOM, 2];

        yield 'custom and letsencrypt with previous assigned'
            => [[SslFile::TYPE_CUSTOM, SslFile::TYPE_LETSENCRYPT], SslFile::TYPE_CUSTOM, 1];

        yield 'letsencrypt and custom with previous assigned'
            => [[SslFile::TYPE_LETSENCRYPT, SslFile::TYPE_CUSTOM], SslFile::TYPE_LETSENCRYPT, 1];
    }

    /** @return Generator<string, array<int, array<int, string>>> */
    public static function providerAssignedCertificateAndInstantSslWithPendingRequestAndNoExistingResult(): Generator
    {
        yield 'only letsencrypt' => [[SslFile::TYPE_LETSENCRYPT, SslFile::TYPE_LETSENCRYPT]];
        yield 'only custom' => [[SslFile::TYPE_CUSTOM, SslFile::TYPE_CUSTOM]];
        yield 'mixed certificates' => [[SslFile::TYPE_LETSENCRYPT, SslFile::TYPE_CUSTOM]];
    }

    public function testWithNoSsl(): void
    {
        $resource = $this->createTemporaryResource();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource);

        self::assertOkResponseWithMissingStatus($response);
    }

    public function testWithoutSslFile(): void
    {
        $resource = $this->createTemporaryResource();
        $ssl = new Ssl();
        $ssl->setResource($resource);
        $this->getEntityManager()->persist($ssl);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource);

        self::assertOkResponseWithMissingStatus($response);
    }

    /** @dataProvider providerCertificateType */
    public function testWithAssignedFile(string $fileType): void
    {
        $resource = $this->createTemporaryResource();
        $assignedAt = $this->createDate('2016-01-01T01:10:00+0000');

        $ssl = new Ssl();
        $ssl->setResource($resource);
        $ssl->setAssignedAt($assignedAt);
        $ssl->setAssignedIndex(1);
        $this->getEntityManager()->persist($ssl);

        $this->addResourceSslFile(
            $resource,
            $ssl,
            1,
            $assignedAt,
            ['foo.test'],
            $fileType,
        );

        if ($fileType === SslFile::TYPE_LETSENCRYPT) {
            $this->createLetsEncryptRequestForResource($resource, RequestState::getCompleted());
        }

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource);

        $expected = $this->prepareExpected(
            'active',
            CertificateStatusDescription::ACTIVE,
            $assignedAt,
            $this->prepareActual($fileType, $assignedAt, ['foo.test']),
        );

        self::assertOkResponse($expected, $response);
    }

    /**
     * @param array<string> $fileTypes
     *
     * @dataProvider providerMultipleAssignedFiles
     */
    public function testWithMultipleAssignedFiles(array $fileTypes, string $expectedType, int $assignedIndex): void
    {
        $resource = $this->createTemporaryResource();
        $assignedAt = $this->createDate('2016-01-01T01:10:00+0000');

        $ssl = $this->enableResourceSsl($resource);

        foreach ($fileTypes as $i => $fileType) {
            $this->addResourceSslFile(
                $resource,
                $ssl,
                $i + 1,
                $assignedAt,
                ['foo.bar'],
                $fileType,
            );
        }

        if ($expectedType === SslFile::TYPE_LETSENCRYPT) {
            $this->createLetsEncryptRequestForResource($resource, RequestState::getCompleted());

            $resource->setInstantSsl(true);
        } else {
            $resource->setInstantSsl(false);
        }

        $ssl->setAssignedAt($assignedAt);
        $ssl->setAssignedIndex($assignedIndex);

        $this->getEntityManager()->persist($resource);
        $this->getEntityManager()->persist($ssl);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource);

        $expected = $this->prepareExpected(
            'active',
            CertificateStatusDescription::ACTIVE,
            $assignedAt,
            $this->prepareActual($expectedType, $assignedAt, ['foo.bar']),
        );

        self::assertOkResponse($expected, $response);
    }

    public function testWithInstantSslWithNoRequest(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(true);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource);

        $expected = $this->prepareExpected(
            'missing',
            CertificateStatusDescription::MISSING,
            null,
            null,
            $this->prepareRequested(
                SslFile::TYPE_LETSENCRYPT,
                CertificateStatus::INSTANT_SSL_REQUEST_MISSING,
                CertificateStatusDescription::INSTANT_SSL_REQUEST_MISSING,
            ),
        );

        self::assertOkResponse($expected, $response);
    }

    public function testWithInstantSslWithCancelledRequest(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(true);

        $instantSslRequest = $this->createLetsEncryptRequestForResource($resource, RequestState::getCanceled());
        $instantSslRequest->cancel(new DateTimeImmutable(), RequestStateReason::CancelledInstantSslDisabled);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource);

        $expected = $this->prepareExpected(
            'missing',
            sprintf(
                '%s %s',
                CertificateStatusDescription::MISSING,
                CertificateStatusDescription::INSTANT_SSL_REQUEST_CANCELED,
            ),
            requested: $this->prepareRequested(
                SslFile::TYPE_LETSENCRYPT,
                CertificateStatus::INSTANT_SSL_REQUEST_CANCELED,
                CertificateStatusDescription::INSTANT_SSL_REQUEST_CANCELED,
            ),
        );
        self::assertOkResponse($expected, $response);
    }

    public function testWithInstantSslWithCancelledRequestWhenActiveCertificate(): void
    {
        $resource = $this->createTemporaryResource();
        $assignedAt = $this->createDate('2016-01-01T01:10:00+0000');

        $ssl = new Ssl();
        $ssl->setResource($resource);
        $ssl->setAssignedAt($assignedAt);
        $ssl->setAssignedIndex(1);
        $this->getEntityManager()->persist($ssl);

        $this->addResourceSslFile(
            $resource,
            $ssl,
            1,
            $assignedAt,
            ['foo.test'],
            SslFile::TYPE_CUSTOM,
        );

        $resource->setInstantSsl(true);

        $instantSslRequest = $this->createLetsEncryptRequestForResource($resource, RequestState::getCanceled());
        $instantSslRequest->cancel(new DateTimeImmutable(), RequestStateReason::CancelledInstantSslDisabled);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource);

        $expected = $this->prepareExpected(
            'active',
            sprintf(
                '%s %s',
                CertificateStatusDescription::ACTIVE,
                CertificateStatusDescription::INSTANT_SSL_REQUEST_CANCELED,
            ),
            $assignedAt,
            $this->prepareActual(SslFile::TYPE_CUSTOM, $assignedAt, ['foo.test']),
            $this->prepareRequested(
                SslFile::TYPE_LETSENCRYPT,
                CertificateStatus::INSTANT_SSL_REQUEST_CANCELED,
                CertificateStatusDescription::INSTANT_SSL_REQUEST_CANCELED,
            ),
        );

        self::assertOkResponse($expected, $response);
    }

    public function testWithInstantSslWithPendingRequestAndNoExistingResult(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(true);

        $instantSslRequest = $this->createLetsEncryptRequestForResource($resource, RequestState::getPending());

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource);

        $expected = $this->prepareExpected(
            'missing',
            CertificateStatusDescription::MISSING,
            null,
            null,
            $this->prepareRequested(
                SslFile::TYPE_LETSENCRYPT,
                CertificateStatus::INSTANT_SSL_GENERATING,
                CertificateStatusDescription::INSTANT_SSL_GENERATING,
                $instantSslRequest->getUpdatedAt(),
                $instantSslRequest->getUpdatedAt(),
                $instantSslRequest->getDomains(),
            ),
        );

        self::assertOkResponse($expected, $response);
    }

    /**
     * @param array<string> $fileTypes
     *
     * @dataProvider providerAssignedCertificateAndInstantSslWithPendingRequestAndNoExistingResult
     */
    public function testWithAssignedCertificateAndInstantSslWithPendingRequestAndNoExistingResult(
        array $fileTypes,
    ): void {
        $resource = $this->createTemporaryResource();
        $assignedAt = $this->createDate('2016-01-01T01:10:00+0000');

        $ssl = $this->enableResourceSsl($resource);

        foreach ($fileTypes as $i => $fileType) {
            $this->addResourceSslFile(
                $resource,
                $ssl,
                $i + 1,
                $assignedAt,
                ['foo.bar'],
                $fileType,
            );
        }

        $instantSslRequest = $this->createLetsEncryptRequestForResource($resource, RequestState::getPending());

        $resource->setInstantSsl(true);

        $ssl->setAssignedAt($assignedAt);
        $ssl->setAssignedIndex(2);

        $this->getEntityManager()->persist($resource);
        $this->getEntityManager()->persist($ssl);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource);
        $lastSslFile = $ssl->getFiles()->last();
        Assert::isInstanceOf($lastSslFile, SslFile::class);

        $expected = $this->prepareExpected(
            'active',
            CertificateStatusDescription::ACTIVE,
            $assignedAt,
            $this->prepareActual($lastSslFile->getType(), $assignedAt, ['foo.bar']),
            $this->prepareRequested(
                SslFile::TYPE_LETSENCRYPT,
                CertificateStatus::INSTANT_SSL_GENERATING,
                CertificateStatusDescription::INSTANT_SSL_GENERATING,
                $instantSslRequest->getUpdatedAt(),
                $instantSslRequest->getUpdatedAt(),
                $instantSslRequest->getDomains(),
            ),
        );

        self::assertOkResponse($expected, $response);
    }

    /** @dataProvider providerFailedResult */
    public function testWithInstantSslWithPendingRequestAndFailedResult(
        ResultStatus $firstResultStatus,
        ResultStatus|null $secondResultStatus,
        string $resultDescription,
        string|null $expectedGeneratingError,
        string|null $expectedValidationError,
    ): void {
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(true);

        $instantSslRequest = $this->createLetsEncryptRequestForResource($resource, RequestState::getPending());

        self::createLetsEncryptResultForRequest(
            $instantSslRequest,
            $this->createDate('2016-01-01T01:15:00+0000'),
            $this->createDate('2016-01-01T01:16:00+0000'),
            $firstResultStatus,
            $resultDescription,
        );

        if ($secondResultStatus !== null) {
            $this->createLetsEncryptResultForRequest(
                $instantSslRequest,
                $this->createDate('2016-01-01T01:17:00+0000'),
                $this->createDate('2016-01-01T01:18:00+0000'),
                $secondResultStatus,
                $resultDescription,
            );
        }

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource);

        $expected = $this->prepareExpected(
            'missing',
            CertificateStatusDescription::MISSING,
            null,
            null,
            $this->prepareRequested(
                SslFile::TYPE_LETSENCRYPT,
                CertificateStatus::INSTANT_SSL_RETRYING,
                CertificateStatusDescription::INSTANT_SSL_RETRYING,
                $instantSslRequest->getCreatedAt(),
                $instantSslRequest->getUpdatedAt(),
                $instantSslRequest->getDomains(),
                $expectedValidationError,
                $expectedGeneratingError,
            ),
        );

        self::assertOkResponse($expected, $response);
    }

    private function makeRequest(CdnResource $resource): Response
    {
        $this->client->request(
            Request::METHOD_GET,
            sprintf('/cdn_resources/%d/certificates/status.json', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
        );

        return $this->client->getResponse();
    }

    /**
     * @param array<string, array<string>|string|null> $actual
     * @param array<string, array<string>|string|null> $requested
     *
     * @return array<string, array<string, array<string>|string|null>|string|null>
     */
    private function prepareExpected(
        string $status,
        string $statusDescription,
        DateTimeImmutable|null $lastChangedAt = null,
        array|null $actual = null,
        array|null $requested = null,
    ): array {
        return [
            'status' => $status,
            'status_description' => $statusDescription,
            'last_changed_at' => $this->formatToZuluDate($lastChangedAt),
            'actual' => $actual,
            'requested' => $requested,
        ];
    }

    /**
     * @param array<string> $domains
     *
     * @return array<string, array<string>|string|null>
     */
    private function prepareActual(string $type, DateTimeImmutable $expirationAt, array $domains): array
    {
        return [
            'type' => $type,
            'expires_at' => $this->formatToZuluDate($expirationAt),
            'domains' => $domains,
        ];
    }

    /**
     * @param array<string> $domains
     *
     * @return array<string, array<string>|string|null>
     */
    private function prepareRequested(
        string $type,
        string $status,
        string $statusDescription,
        DateTimeImmutable|null $requestedAt = null,
        DateTimeImmutable|null $lastChangedAt = null,
        array|null $domains = null,
        string|null $validationError = null,
        string|null $generatingError = null,
    ): array {
        return [
            'type' => $type,
            'status' => $status,
            'status_description' => $statusDescription,
            'requested_at' => $this->formatToZuluDate($requestedAt),
            'last_change_at' => $this->formatToZuluDate($lastChangedAt),
            'domains' => $domains,
            'validation_error' => $validationError,
            'generating_error' => $generatingError,
        ];
    }

    private function assertOkResponseWithMissingStatus(Response $response): void
    {
        $expected = $this->prepareExpected('missing', CertificateStatusDescription::MISSING);
        self::assertOkResponse($expected, $response);
    }

    /** @param mixed[] $expected */
    private function assertOkResponse(array $expected, Response $response): void
    {
        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame($expected, json_decode($response->getContent(), true));
    }

    private function createDate(string $date): DateTimeImmutable
    {
        return DateTimeImmutable::createFromFormat(DateTime::ISO8601, $date);
    }
}
