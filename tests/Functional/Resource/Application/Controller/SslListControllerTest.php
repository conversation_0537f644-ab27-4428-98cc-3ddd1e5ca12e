<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\Resources\Ssl\SslHelper;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Utils\DateFormatter;
use DateTimeImmutable;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function array_map;
use function Safe\json_decode;
use function usort;

class SslListControllerTest extends WebTestCase
{
    use TemporaryData;
    use SslHelper;

    public function testWithNoParameters(): void
    {
        $customSet = $this->createResourceAndSslSet();
        $instantSslSet = $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT);
        $expiredSet = $this->createResourceAndSslSet(SslFile::TYPE_CUSTOM, '2000-01-01T00:00:00+0000');

        $response = $this->makeRequest();

        self::assertSame($response::HTTP_OK, $response->getStatusCode());
        self::assertSameCertificates([$customSet, $instantSslSet, $expiredSet], $response);
    }

    public function testWithCustomTypeOnly(): void
    {
        $customSet = $this->createResourceAndSslSet(SslFile::TYPE_CUSTOM);
        $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT);

        $response = $this->makeRequest(['type' => 'custom']);

        self::assertSame($response::HTTP_OK, $response->getStatusCode());
        self::assertSameCertificates([$customSet], $response);
    }

    public function testWithInstantSslTypeOnly(): void
    {
        $this->createResourceAndSslSet(SslFile::TYPE_CUSTOM);
        $instantSslSet = $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT);

        $response = $this->makeRequest(['type' => 'instant_ssl']);

        self::assertSame($response::HTTP_OK, $response->getStatusCode());
        self::assertSameCertificates([$instantSslSet], $response);
    }

    public function testWithInvalidType(): void
    {
        $response = $this->makeRequest(['type' => 'invalid']);

        self::assertSame($response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['Invalid certificate type.'],
                'fields' => [
                    'type' => ['Invalid certificate type.'],
                ],
            ],
            json_decode($response->getContent(), true),
        );
    }

    public function testWithExpiration(): void
    {
        $this->createResourceAndSslSet(SslFile::TYPE_CUSTOM);
        $expiredSet = $this->createResourceAndSslSet(SslFile::TYPE_CUSTOM, '2010-01-01T00:00:00+0000');

        $response = $this->makeRequest(['expirationBefore' => '2015-01-01T00:00:00Z']);

        self::assertSame($response::HTTP_OK, $response->getStatusCode());
        self::assertSameCertificates([$expiredSet], $response);
    }

    public function testWithTypeAndExpirationSet(): void
    {
        $this->createResourceAndSslSet(SslFile::TYPE_CUSTOM);
        $instantSslSet = $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT, '2000-01-01T00:00:00+0000');
        $this->createResourceAndSslSet(SslFile::TYPE_CUSTOM, '2000-01-01T00:00:00+0000');

        $response = $this->makeRequest(
            [
                'type' => 'instant_ssl',
                'expirationBefore' => '2015-01-01T00:00:00Z',
            ],
        );

        self::assertSame($response::HTTP_OK, $response->getStatusCode());
        self::assertSameCertificates([$instantSslSet], $response);
    }

    public function testWithMalformedExpiration(): void
    {
        $response = $this->makeRequest(['expirationBefore' => 'blah']);

        self::assertSame($response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['Request deserialization failed. Check the endpoint documentation.'],
            ],
            json_decode($response->getContent(), true),
        );
    }

    public function testSuspendedAreNotIncludedByDefault(): void
    {
        $first = $this->createResourceAndSslSet();
        $second = $this->createResourceAndSslSet();
        $third = $this->createResourceAndSslSet();
        $this->markResourceSuspended($second[0]);

        $response = $this->makeRequest();

        self::assertSame($response::HTTP_OK, $response->getStatusCode());
        self::assertSameCertificates([$first, $third], $response);
    }

    public function testSuspendedIsPresentWhenIncludeSuspendedIsSet(): void
    {
        $first = $this->createResourceAndSslSet();
        $second = $this->createResourceAndSslSet();
        $third = $this->createResourceAndSslSet();
        $this->markResourceSuspended($second[0]);

        $response = $this->makeRequest(
            ['includeSuspended' => '1'],
        );

        self::assertSame($response::HTTP_OK, $response->getStatusCode());
        self::assertSameCertificates([$first, $second, $third], $response);
    }

    /** @param string[] $parameters */
    private function makeRequest(array $parameters = []): Response
    {
        $this->client->request(
            Request::METHOD_GET,
            '/certificates.json',
            $parameters,
            [],
            static::getDefaultHeaders(),
        );

        return $this->client->getResponse();
    }

    private function markResourceSuspended(CdnResource $resource): void
    {
        $resource->setSuspended(new DateTimeImmutable());
        $this->getEntityManager()->flush();
    }

    /** @param list<array{CdnResource, Ssl, SslFile}> $expected */
    private function assertSameCertificates(array $expected, Response $response): void
    {
        $this->sortInput($expected);

        $result = json_decode($response->getContent(), true);
        $this->sortResult($result['certificates']);

        self::assertSame(
            [
                'certificates' => array_map(
                    static fn (array $input) => [
                        'resource' => [
                            'id' => $input[0]->getId(),
                        ],
                        'type' => [
                            SslFile::TYPE_CUSTOM => 'custom',
                            SslFile::TYPE_LETSENCRYPT => 'instant_ssl',
                        ][$input[2]->getType()],
                        'expires_at' => DateFormatter::formatDate($input[2]->getExpiresAt()),
                        'domains' => $input[2]->getDomains(),
                    ],
                    $expected,
                ),
            ],
            $result,
        );
    }

    /** @param list<array{CdnResource}> $input */
    private function sortInput(array &$input): void
    {
        usort(
            $input,
            static fn (array $a, array $b): int => $a[0]->getId() <=> $b[0]->getId(),
        );
    }

    /** @param mixed[][] $result */
    private function sortResult(array &$result): void
    {
        usort(
            $result,
            static fn (array $a, array $b): int => $a['resource']['id'] <=> $b['resource']['id'],
        );
    }
}
