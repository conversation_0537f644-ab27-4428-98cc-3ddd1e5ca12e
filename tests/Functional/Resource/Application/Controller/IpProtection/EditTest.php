<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\IpProtection;

use Cdn77\NxgApi\Entity\Legacy\ResourceIpProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceIpProtectionAddress;
use Cdn77\NxgApi\IpProtection\Domain\Value\IpProtectionType;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_decode;
use function Safe\json_encode;

final class EditTest extends WebTestCase
{
    use NotifyResourceChangeHelper;
    use TemporaryData;

    public function testInvalidIpProtectionType(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_IP_PROTECTION => [
                'type' => 'invalid-type',
                'addresses' => [
                    [ 'network' => '*******/32'],
                ],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();
        $responseContent = $response->getContent();

        self::assertIsString($responseContent);
        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ["The value should be either 'whitelist', 'blacklist' or 'disabled'"],
                'fields' => [
                    'ip_protection.type' => ["The value should be either 'whitelist', 'blacklist' or 'disabled'"],
                ],
            ],
            json_decode($responseContent, true),
        );

        self::assertNoMessageInQueue();
    }

    public function testIpProtectionChangeBlacklistToWhitelist(): void
    {
        $resource = $this->createTemporaryResource();

        $this->createResourceIpProtection(
            $resource,
            IpProtectionType::TYPE_BLACKLIST,
            ['***********/32', '***********/32'],
        );

        FlushAndClear::do($this->getEntityManager());

        $newIps = [['network' => '*******/32'], ['network' => '*******/32']];
        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_IP_PROTECTION => [
                'type' => 'whitelist',
                'addresses' => $newIps,
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $ipProtection = $this->getEntityManager()
            ->getRepository(ResourceIpProtection::class)
            ->findOneBy(['resource' => $resource]);

        $ipProtectionAddresses = $this->getEntityManager()
            ->getRepository(ResourceIpProtectionAddress::class)
            ->findBy(['ipProtection' => $ipProtection]);

        self::assertNotNull($ipProtection);
        self::assertSame('whitelist', $ipProtection->getType());
        self::assertCount(2, $ipProtection->getAddresses());
        self::assertSame($newIps, $ipProtection->getAddresses()->map(
            static fn (ResourceIpProtectionAddress $address): array => ['network' => $address->getAddress()],
        )->toArray());

        self::assertCount(2, $ipProtectionAddresses);

        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testIpProtectionWithNoAddresses(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_IP_PROTECTION => [
                'type' => 'blacklist',
                'addresses' => [],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();
        $responseContent = $response->getContent();

        self::assertIsString($responseContent);
        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testInvalidIpProtectionNetwork(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_IP_PROTECTION => [
                'type' => 'blacklist',
                'addresses' => [
                    ['network' => 'crap'],
                    ['network' => '***********/10'],
                ],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();
        $responseContent = $response->getContent();

        self::assertIsString($responseContent);
        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => [
                    'Value is not a valid IPv4/IPv6 CIDR notation.',
                    'Value is not a valid IPv4/IPv6 CIDR notation.',
                ],
                'fields' => [
                    'ip_protection.addresses[0].network' => ['Value is not a valid IPv4/IPv6 CIDR notation.'],
                    'ip_protection.addresses[1].network' => ['Value is not a valid IPv4/IPv6 CIDR notation.'],
                ],
            ],
            json_decode($responseContent, true),
        );

        self::assertNoMessageInQueue();
    }

    public function testIpProtectionWholeNetwork(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
            ResourceEditSchema::FIELD_IP_PROTECTION => [
                'type' => 'blacklist',
                'addresses' => [
                    ['network' => '0.0.0.0/0'],
                ],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();
        $responseContent = $response->getContent();

        self::assertIsString($responseContent);
        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['This value should not be identical to string "0.0.0.0/0".'],
                'fields' => [
                    'ip_protection.addresses[0].network' =>
                        ['This value should not be identical to string "0.0.0.0/0".'],
                ],
            ],
            json_decode($responseContent, true),
        );

        self::assertNoMessageInQueue();
    }

    public function testDisableIpProtection(): void
    {
        $resource = $this->createTemporaryResource();
        $ipProtection = $this->createResourceIpProtection(
            $resource,
            ResourceIpProtection::TYPE_WHITELIST,
            ['*******/32', '*******/24'],
        );

        FlushAndClear::do($this->getEntityManager());

        $modifiedData = [
            ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                $resource,
                [ResourceEditInfo::FIELD_INSTANT_SSL => 1],
            ),
            ResourceEditSchema::FIELD_IP_PROTECTION => [
                'type' => IpProtectionType::TYPE_DISABLED,
                'addresses' => [],
            ],
        ];

        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($modifiedData),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $ipProtectionActual = $this->getEntityManager()
            ->getRepository(ResourceIpProtection::class)
            ->findOneBy(['resource' => $resource]);

        $ipProtectionAddresses = $this->getEntityManager()
            ->getRepository(ResourceIpProtectionAddress::class)
            ->findBy(['ipProtection' => $ipProtection]);

        self::assertNull($ipProtectionActual);
        self::assertCount(0, $ipProtectionAddresses);

        self::assertUpdatingMessageInQueue($resource->getId());
    }
}
