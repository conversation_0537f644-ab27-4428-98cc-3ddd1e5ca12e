<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller;

use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function array_column;
use function array_filter;
use function array_key_exists;
use function array_map;
use function Safe\json_decode;
use function sprintf;
use function usort;

class ServersStatusControllerTest extends WebTestCase
{
    use TemporaryData;

    public function testWithInvalidResource(): void
    {
        $response = $this->makeRequest(123456789);

        self::assertSame(Response::HTTP_NOT_FOUND, $response->getStatusCode());
    }

    public function testWithNoServersInGroup(): void
    {
        $location = $this->createLocation('tmpCZ', 'tmp', 'CZ', 'EU');

        $popA = $this->createPop($location);
        $popB = $this->createPop($location);

        $groupA = $this->createLocationGroup();
        $groupA->getPops()->add($popA);
        $groupB = $this->createLocationGroup();
        $groupB->getPops()->add($popB);

        $server = $this->createServer(false);
        $server->setPop($popB);
        $ip = $this->createIp($server, true);

        $resource = $this->createResource($this->createAccount(), $groupA);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource->getId());

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertEquals(
            [
                'resource' => [
                    'id' => $resource->getId(),
                ],
                'servers' => [],
            ],
            json_decode($response->getContent(), true),
        );
    }

    public function testWithExistingServers(): void
    {
        $location = $this->createLocation('tmpCZ', 'tmp', 'CZ', 'EU');

        $popA = $this->createPop($location);
        $popB = $this->createPop($location);
        $popC = $this->createPop($location);

        $groupA = $this->createLocationGroup();
        $groupA->getPops()->add($popA);
        $groupA->getPops()->add($popB);
        $groupB = $this->createLocationGroup();
        $groupB->getPops()->add($popC);

        $servers = [];
        $serverDefinitions = [
            // [fixture => [paused, ip up, ip primary, pop], expected => [paused, auto_up, up, forced] (NULL to ignore)]
            [[false, true, true, $popA], [false, true, true]],
            [[false, true, false, $popA], null],
            [[false, false, false, $popA], null],
            [[true, true, true, $popA], [true, true, false]],
            [[false, true, true, $popB], [false, true, true]],
            [[false, false, true, $popB], [false, false, false]],
            [[false, false, false, $popB], null],
            [[false, true, true, $popC], null],
            [[false, true, false, $popC], null],
        ];
        foreach (array_column($serverDefinitions, 0) as $serverDefinition) {
            $servers[] = $server = $this->createServer($serverDefinition[0]);
            $server->setPop($serverDefinition[3]);
            $this->createIp($server, $serverDefinition[1], $serverDefinition[2]);
        }

        $resourceA = $this->createResourceWithAccount($groupA);
        $resourceB = $this->createResourceWithAccount($groupB);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resourceA->getId());

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $responseData['servers'] = $this->sortServers($responseData['servers']);
        self::assertEquals(
            [
                'resource' => [
                    'id' => $resourceA->getId(),
                ],
                'servers' => $this->sortServers(
                    array_filter(
                        array_map(
                            static function (Server $server, $definition) {
                                if ($definition === null) {
                                    return null;
                                }

                                $server = [
                                    'uid' => $server->getUid(),
                                    'auto_up' => $definition[1],
                                    'up' => $definition[2],
                                    'paused' => $definition[0],
                                ];

                                if (array_key_exists(3, $definition)) {
                                    $server['forced'] = $definition[3];
                                }

                                return $server;
                            },
                            $servers,
                            array_column($serverDefinitions, 1),
                        ),
                    ),
                ),
            ],
            $responseData,
        );
    }

    public function testWithCustomLocation(): void
    {
        $location = $this->createLocation('tmpCZ', 'tmp', 'CZ', 'EU');

        $popA = $this->createPop($location);
        $popB = $this->createPop($location);
        $popC = $this->createPop($location);

        $groupA = $this->createLocationGroup();
        $groupA->getPops()->add($popA);
        $groupA->getPops()->add($popB);
        $groupB = $this->createLocationGroup();
        $groupB->getPops()->add($popC);

        $servers = [];
        foreach ([$popA, $popB, $popC] as $serverPop) {
            $servers[] = $server = $this->createServer(false);
            $server->setPop($serverPop);
            $this->createIp($server, true);
        }

        $resource = $this->createResourceWithAccount($groupA);

        $customLocation = $this->createCustomLocation($groupA, $resource);
        $customLocation->getPops()->add($popA);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resource->getId());

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertEquals(
            [
                'resource' => [
                    'id' => $resource->getId(),
                ],
                'servers' => [
                    [
                        'uid' => $servers[0]->getUid(),
                        'auto_up' => true,
                        'up' => true,
                        'paused' => false,
                    ],
                ],
            ],
            json_decode($response->getContent(), true),
        );
    }

    public function testWithCustomLocationWithNotMatchingGroups(): void
    {
        $location = $this->createLocation('tmpCZ', 'tmp', 'CZ', 'EU');

        $popA = $this->createPop($location);
        $popB = $this->createPop($location);

        $groupA = $this->createLocationGroup();
        $groupA->getPops()->add($popA);
        $groupB = $this->createLocationGroup();
        $groupB->getPops()->add($popB);

        $serverA = $this->createServer(false);
        $serverA->setPop($popA);
        $this->createIp($serverA, true);

        $serverB = $this->createServer(false);
        $serverB->setPop($popB);
        $this->createIp($serverB, true);

        $resourceA = $this->createResourceWithAccount($groupA);
        $customLocationA = $this->createCustomLocation($groupB, $resourceA);
        $customLocationA->getPops()->add($popA);

        $resourceB = $this->createResourceWithAccount($groupB);
        $customLocationB = $this->createCustomLocation($groupB, $resourceB);
        $customLocationB->getPops()->add($popB);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest($resourceA->getId());

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertEquals(
            [
                'resource' => [
                    'id' => $resourceA->getId(),
                ],
                'servers' => [
                    [
                        'uid' => $serverA->getUid(),
                        'auto_up' => true,
                        'up' => true,
                        'paused' => false,
                    ],
                ],
            ],
            json_decode($response->getContent(), true),
        );
    }

    /**
     * @param mixed[] $servers
     *
     * @return mixed[]
     */
    private function sortServers(array $servers): array
    {
        usort($servers, static fn (array $a, array $b): int => $a['uid'] <=> $b['uid']);

        return $servers;
    }

    private function makeRequest(int $id): Response
    {
        $this->client->request(
            Request::METHOD_GET,
            sprintf('/internal/resource/%d/servers', $id),
            [],
            [],
            static::getDefaultHeaders(),
        );

        return $this->client->getResponse();
    }
}
