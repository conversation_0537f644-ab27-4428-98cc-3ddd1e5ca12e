<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceIpProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtection;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\LetsEncrypt\Request as LetsEncryptRequest;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeaderSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use Cdn77\NxgApi\Validator\Constraints\Resource\AllowedDomainValidator;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function array_combine;
use function array_key_exists;
use function array_keys;
use function array_map;
use function array_search;
use function assert;
use function count;
use function in_array;
use function range;
use function Safe\json_encode;
use function str_repeat;

class AddControllerTest extends WebTestCase
{
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use AllowedDomainValidator;
    use ResponseEvaluateHelper;

    /** @return Generator<string, array<bool|null, bool|null, array<string, string>|null>> */
    public static function providerRateLimitContentDispositionOriginHeaders(): Generator
    {
        $data = ['abc' => 'def', 'ghi' => '666'];

        yield 'all set 1' => [true, false, $data];
        yield 'all set 2' => [false, false, $data];
        yield 'all set 3' => [false, true, $data];
        yield 'some set 1' => [null, true, $data];
        yield 'some set 2' => [true, null, null];
        yield 'none set 1' => [null, null, null];
        yield 'none set 2' => [null, null, []];
        yield 'set with number 1' => [0, 1, $data];
        yield 'set with number 2' => [1, 0, $data];
        yield 'set with number 3' => ['1', '0', $data];
        yield 'set with number 4' => ['0', '1', $data];
    }

    /** @return Generator<array{string}> */
    public static function providerBadDomain(): Generator
    {
        yield ['baddomain'];
        yield ['bad_domain.com'];
        yield ['-.not.good.com'];
        yield ['not.good-.com'];
        yield ['not.-good.com'];
        yield ['notok-.domain.com'];
    }

    public function testOk(): void
    {
        $okDataForNewCdn = $this->prepareDataForNewCdn();

        $response = $this->callApiGetResponse($okDataForNewCdn);

        $this->evaluateOkResponse($response, $okDataForNewCdn);
    }

    public function testOkWithIp(): void
    {
        $okDataForNewCdn = $this->prepareDataForNewCdn();
        $okDataForNewCdn['origin_url'] = '***********';

        $response = $this->callApiGetResponse($okDataForNewCdn);

        $this->evaluateOkResponse($response, $okDataForNewCdn);
    }

    public function testOkWithIdAndCdnUrl(): void
    {
        $okDataForNewCdn = $this->prepareDataForNewCdn();
        $id = **********;
        $okDataForNewCdn['id'] = $id;
        $cdnUrl = 'whatever.org';
        $okDataForNewCdn['cdn_url'] = $cdnUrl;

        $response = $this->callApiGetResponse($okDataForNewCdn);

        self::assertSame($id, $response->decodedContent['cdn_resource']['cdn_reference']);
        self::assertSame($id, $response->decodedContent['cdn_resource']['id']);
        self::assertSame($cdnUrl, $response->decodedContent['cdn_resource']['cdn_url']);

        $resource = $this->getResource($id);
        self::assertSame($cdnUrl, $resource->getCdnUrl());

        $this->evaluateOkResponse($response, $okDataForNewCdn);
    }

    public function testOkWithUrl(): void
    {
        $okDataForNewCdn = $this->prepareDataForNewCdn();
        $okDataForNewCdn['origin_url'] = '_static.domain.foo';

        $response = $this->callApiGetResponse($okDataForNewCdn);

        $this->evaluateOkResponse($response, $okDataForNewCdn);
    }

    public function testFailedMp4(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['disable_query_string'] = 0;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            "Enabled 'MP4 streaming' requires enabled 'Ignore query string'.",
            'mp4_pseudo_streaming',
        );
    }

    public function testFailedGroup(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['group_id'] = [666666];

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            '666666 is not valid group ID.',
            'group_id',
        );
    }

    /** @dataProvider providerBadDomain */
    public function testFailedOriginUrlWithBadDomain(string $badDomain): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['origin_url'] = $badDomain;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            $badDomain . ' is not a valid origin url or IP.',
            'origin_url',
        );
    }

    public function testFailedUnallowedCnameDomain(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['cnames'] = ['test.cdn77.com'];

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            '\'cdn77.com\' is not allowed CNAME.',
            'cnames[0]',
        );
    }

    public function testFailedUnallowedOriginDomain(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['origin_url'] = 'abc.c.cdn77.org';

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            '\'c.cdn77.org\' is not allowed origin.',
            'origin_url',
        );
    }

    public function testAccountExceptionForUnallowedCnameDomain(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['account_id'] = $this->getAllowedAccounts()[0];
        $dataForNewCdn['cnames'] = ['test.cdn77.com'];

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateOkResponse($response, $dataForNewCdn);
    }

    public function testFailedOriginUrlWithLocalhostIp(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['origin_url'] = '127.0.0.1';

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            '127.0.0.1 is not a valid origin url or IP.',
            'origin_url',
        );
    }

    public function testFailedOriginUrlWithPrivateRangeIp(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['origin_url'] = '*********';

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            '********* is not a valid origin url or IP.',
            'origin_url',
        );
    }

    public function testCertificateRequestIsCreatedWithInstantSsl(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['instant_ssl'] = 1;
        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateOkResponse($response, $dataForNewCdn);

        $letsEncryptRequests = $this->findCertificateRequestsForResourceId(
            $response->decodedContent['cdn_resource']['cdn_reference'],
        );
        self::assertSame(1, count($letsEncryptRequests));

        $request = $letsEncryptRequests[0];
        $tasks = $this->getEntityManager()->getRepository(Task::class)->findBy(['request' => $request]);
        self::assertCount(1, $tasks);
    }

    public function testCertificateRequestIsNotCreatedWithNoCnames(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['cnames'] = [];
        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateOkResponse($response, $dataForNewCdn);

        $letsEncryptRequests = $this->findCertificateRequestsForResourceId(
            $response->decodedContent['cdn_resource']['cdn_reference'],
        );
        self::assertSame(0, count($letsEncryptRequests));
    }

    public function testCertificateRequestIsNotCreatedWithNoCustomCnames(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['cnames'] = ['foo.c.cdn77.org'];
        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateOkResponse($response, $dataForNewCdn);

        $letsEncryptRequests = $this->findCertificateRequestsForResourceId(
            $response->decodedContent['cdn_resource']['cdn_reference'],
        );
        self::assertSame(0, count($letsEncryptRequests));
    }

    public function testInstantSslEnabled(): void
    {
        $okDataForNewCdn = $this->prepareDataForNewCdn();
        $okDataForNewCdn['instant_ssl'] = 1;

        $response = $this->callApiGetResponse($okDataForNewCdn);

        $this->evaluateOkResponse($response, $okDataForNewCdn);
    }

    public function testFailsWhenAddingExistingCnameWithDifferentCase(): void
    {
        $foo = $this->createTemporaryResource();
        $foo->setCnames(['test.foo', 'Test.bar']);
        $this->getEntityManager()->flush();

        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['cnames'] = ['xyz.test', 'TEST.bar'];

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'test.bar is already used in another resource.',
            'cnames[1]',
        );
    }

    public function testResourceCnamesAreSavedAsLowercase(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['cnames'] = ['Test.Foo', 'TEST.bar'];

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForCdn = $dataForNewCdn;
        $validDataForCdn['cnames'] = array_map('mb_strtolower', $validDataForCdn['cnames']);
        $this->evaluateOkResponse($response, $validDataForCdn);
    }

    public function testWithIgnoredQueryParamsNotSet(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        unset($dataForNewCdn['ignored_query_params']);

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForCdn = $dataForNewCdn;
        $validDataForCdn['ignored_query_params'] = [];
        $this->evaluateOkResponse($response, $validDataForCdn);
    }

    public function testWithIgnoredQueryParamsSet(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['ignored_query_params'] = ['foo', 'bar'];

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForCdn = $dataForNewCdn;
        $this->evaluateOkResponse($response, $validDataForCdn);
    }

    public function testWithIgnoredQueryParamsContainingDuplicates(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['ignored_query_params'] = ['foo', 'bar', 'Foo'];

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'Ignored query parameters must be unique (case-insensitive).',
            'ignored_query_params[2]',
        );
    }

    public function testWithIgnoredQueryParamsContainingMalformedName(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['ignored_query_params'] = ['foo', 'bar?baz'];

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'Parameter is in invalid format.',
            'ignored_query_params[1]',
        );
    }

    public function testWithIgnoredQueryParamsTooLong(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['ignored_query_params'] = [str_repeat('x', 41)];

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'Maximum length is 40 characters.',
            'ignored_query_params[0]',
        );
    }

    public function testWithIgnoredQueryParamsUpperLimit(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['ignored_query_params'] = array_map(
            static fn ($i) => 'param' . $i,
            range(1, 100),
        );

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateOkResponse($response, $dataForNewCdn);
    }

    public function testWithIgnoredQueryParamsHavingTooMuchItems(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['ignored_query_params'] = array_map(
            static fn ($i) => 'param' . $i,
            range(1, 101),
        );

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'You may only use up to 100 ignored query parameters.',
            'ignored_query_params',
        );
    }

    public function testWithHttpsRedirectCodeNotSetTreatedAsDisabled(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        unset($dataForNewCdn['https_redirect_code']);

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForCdn = $dataForNewCdn;
        $validDataForCdn['https_redirect_code'] = 0;

        $this->evaluateOkResponse($response, $validDataForCdn);
    }

    public function testWithHttpsRedirectCodeSet(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['https_redirect_code'] = 302;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForCdn = $dataForNewCdn;

        $this->evaluateOkResponse($response, $validDataForCdn);
    }

    public function testWithHttpsRedirectCodeZeroTreatsAsNull(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['https_redirect_code'] = 0;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForCdn = $dataForNewCdn;

        $this->evaluateOkResponse($response, $validDataForCdn);

        $resource = $this->getEntityManager()->find(
            CdnResource::class,
            $response->decodedContent['cdn_resource']['cdn_reference'],
        );
        self::assertNull($resource->getHttpsRedirectCode());
    }

    public function testWithInvalidHttpsRedirectCode(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['https_redirect_code'] = 404;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'Allowed values: 301, 302 or 0 to disable.',
            'https_redirect_code',
        );
    }

    public function testWithCnameLong64Bytes(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['cnames'] = [str_repeat('x', 59) . '.test'];

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForCdn = $dataForNewCdn;

        $this->evaluateOkResponse($response, $validDataForCdn);
    }

    public function testWithCnameLong65Bytes(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['cnames'] = [str_repeat('x', 60) . '.test'];

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'Maximum allowed CNAME length is 64 characters.',
            'cnames[0]',
        );
    }

    public function testAccountIsCreated(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['account_id'] = 12345;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForNewCdn = $dataForNewCdn;
        $this->evaluateOkResponse($response, $validDataForNewCdn);

        $this->getEntityManager()->clear();
        $resource = $this->getEntityManager()->find(
            CdnResource::class,
            $response->decodedContent['cdn_resource']['cdn_reference'],
        );
        self::assertNotNull($resource);
        self::assertSame(12345, $resource->getAccount()->getId());
    }

    public function testExistingAccountIsUsed(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['account_id'] = 12345;

        $this->createAccount(12345);

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForNewCdn = $dataForNewCdn;
        $this->evaluateOkResponse($response, $validDataForNewCdn);

        $this->getEntityManager()->clear();
        $resource = $this->getEntityManager()->find(
            CdnResource::class,
            $response->decodedContent['cdn_resource']['cdn_reference'],
        );
        self::assertNotNull($resource);
        self::assertSame(12345, $resource->getAccount()->getId());
    }

    public function testForwardHostHeader(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['forward_host_header'] = 0;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForNewCdn = $dataForNewCdn;
        $this->evaluateOkResponse($response, $validDataForNewCdn);
    }

    public function testStreamingPlaylistBypassIsNotMandatory(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        unset($dataForNewCdn['streaming_playlist_bypass']);

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForNewCdn = $dataForNewCdn;
        $validDataForNewCdn['streaming_playlist_bypass'] = 0;
        $this->evaluateOkResponse($response, $validDataForNewCdn);
    }

    public function testStreamingPlaylistBypassShouldBeEnabledForStreamOrigin(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['origin_url'] = 'test-1.s.cdn77.com';
        $dataForNewCdn['streaming_playlist_bypass'] = 0;

        $response = $this->callApiGetResponse($dataForNewCdn);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertSame(
            [
                'errors' => ['Streaming playlist bypass should be enabled for new streaming resource.'],
            ],
            $response->decodedContent,
        );
    }

    public function testStreamingPlaylistBypassEnabledForStreamOrigin(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['origin_url'] = 'test-1.s.cdn77.com';
        $dataForNewCdn['streaming_playlist_bypass'] = 1;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForNewCdn = $dataForNewCdn;
        $this->evaluateOkResponse($response, $validDataForNewCdn);
    }

    public function testStreamingPlaylistBypass(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['streaming_playlist_bypass'] = 1;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForNewCdn = $dataForNewCdn;
        $this->evaluateOkResponse($response, $validDataForNewCdn);
    }

    public function testOriginTimeout(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['origin_timeout'] = 10;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForNewCdn = $dataForNewCdn;
        $this->evaluateOkResponse($response, $validDataForNewCdn);
    }

    public function testOriginTimeoutWithZero(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['origin_timeout'] = 0;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForNewCdn = $dataForNewCdn;
        unset($validDataForNewCdn['origin_timeout']);

        $this->evaluateOkResponse($response, $validDataForNewCdn);
    }

    public function testOriginPortWithZero(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['origin_port'] = 0;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $validDataForNewCdn = $dataForNewCdn;
        unset($validDataForNewCdn['origin_port']);

        $this->evaluateOkResponse($response, $validDataForNewCdn);
    }

    public function testOriginTimeoutTooSmall(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['origin_timeout'] = -1;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'This value should be between 0 and 120.',
            'origin_timeout',
        );
    }

    public function testOriginTimeoutTooLarge(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['origin_timeout'] = 150;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'This value should be between 0 and 120.',
            'origin_timeout',
        );
    }

    public function testOkWithBucketName(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $s3BucketName = 'new-bucket';
        $s3Type = 'cdn77-ceph-rgw';
        $host = 'eu-1.cdn77-storage.com';
        $dataForNewCdn[ResourceAddInfo::FIELD_ORIGIN_URL] = $host;
        $dataForNewCdn[ResourceAddInfo::FIELD_S3_BUCKET_NAME] = $s3BucketName;
        $dataForNewCdn[ResourceAddInfo::FIELD_S3_TYPE] = $s3Type;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateOkResponse($response, $dataForNewCdn);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);

        $resource = $this->getEntityManager()->find(
            CdnResource::class,
            $resourceId,
        );

        self::assertInstanceOf(CdnResource::class, $resource);
        self::assertSame($s3BucketName, $resource->getMainOrigin()->getS3()->getBucketName());
        self::assertSame($s3Type, $resource->getMainOrigin()->getS3()->getType());
        self::assertSame($host, $resource->getMainOrigin()->getHost());
    }

    /**
     * @param string|int|bool|null $rateLimit
     * @param string|int|bool|null $contentDispositionByParam
     * @param array<string, mixed> $originHeaders
     *
     * @dataProvider providerRateLimitContentDispositionOriginHeaders
     */
    public function testOkWithRateLimitContentDispositionOriginHeaders(
        $rateLimit,
        $contentDispositionByParam,
        array|null $originHeaders,
    ): void {
        $dataForNewCdn = $this->prepareDataForNewCdn();

        $dataForNewCdn[ResourceAddInfo::FIELD_RATE_LIMIT] = $rateLimit;
        $dataForNewCdn[ResourceAddInfo::FIELD_CONTENT_DISPOSITION_BY_PARAM] = $contentDispositionByParam;
        $dataForNewCdn[ResourceAddInfo::FIELD_ORIGIN_HEADERS] = $originHeaders;

        $response = $this->callApiGetResponse($dataForNewCdn);

        if (
            $dataForNewCdn[ResourceAddInfo::FIELD_ORIGIN_HEADERS] === null
            || count($dataForNewCdn[ResourceAddInfo::FIELD_ORIGIN_HEADERS]) === 0
        ) {
            unset($dataForNewCdn[ResourceAddInfo::FIELD_ORIGIN_HEADERS]);
        }

        $trueValues = ['1', 1, true];
        $dataForNewCdn[ResourceAddInfo::FIELD_RATE_LIMIT]
            = in_array($dataForNewCdn[ResourceAddInfo::FIELD_RATE_LIMIT], $trueValues);
        $dataForNewCdn[ResourceAddInfo::FIELD_CONTENT_DISPOSITION_BY_PARAM]
            = in_array($dataForNewCdn[ResourceAddInfo::FIELD_CONTENT_DISPOSITION_BY_PARAM], $trueValues);

        $this->evaluateOkResponse($response, $dataForNewCdn);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);

        $resource = $this->getResource($resourceId);

        self::assertSame(in_array($rateLimit, $trueValues), $resource->hasRateLimit());
        self::assertSame(in_array($contentDispositionByParam, $trueValues), $resource->hasContentDispositionByParam());
        self::assertSame(
            ValueReplacer::emptyArrayToNull($originHeaders),
            $resource->getMainOrigin()->getOriginHeaders(),
        );
    }

    public function testFailWhenOriginIsNotOkForBucket(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $s3BucketName = 'new-bucket';
        $s3Type = 'external-s3';
        $host = 'this.is.invalid.host.com';

        $dataForNewCdn[ResourceAddInfo::FIELD_ORIGIN_URL] = $host;
        $dataForNewCdn[ResourceAddInfo::FIELD_S3_BUCKET_NAME] = $s3BucketName;
        $dataForNewCdn[ResourceAddInfo::FIELD_S3_TYPE] = $s3Type;

        $response = $this->callApiGetResponse($dataForNewCdn);
        $failedResponse = $this->prepareFailedResponse(
            ['this.is.invalid.host.com is not a valid host for this configuration.'],
        );

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertEquals($failedResponse, $response->decodedContent);
    }

    public function testFailWhenBucketIsNotOkForOrigin(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $s3BucketName = null;
        $host = 'eu-1.cdn77-storage.com';

        $dataForNewCdn[ResourceAddInfo::FIELD_S3_BUCKET_NAME] = $s3BucketName;
        $dataForNewCdn[ResourceAddInfo::FIELD_ORIGIN_URL] = $host;

        $response = $this->callApiGetResponse($dataForNewCdn);
        $failedResponse = $this->prepareFailedResponse(
            ['eu-1.cdn77-storage.com is not a valid host for this configuration.'],
        );

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertEquals($failedResponse, $response->decodedContent);
    }

    public function testSetupWithHttpProtection(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $geoProtectionData = [
            'type' => 'whitelist',
            'locations' => [
                ['country' => 'CZ'],
                ['country' => 'SK'],
            ],
        ];
        $hotlinkProtectionData = [
            'type' => 'whitelist',
            'deny_empty' => false,
            'addresses' => [
                ['domain' => 'example.com'],
            ],
        ];
        $ipProtectionData = [
            'type' => 'whitelist',
            'addresses' => [
                ['network' => '***********/32'],
            ],
        ];

        $response = $this->callApiGetResponse(
            $dataForNewCdn,
            $geoProtectionData,
            $hotlinkProtectionData,
            $ipProtectionData,
        );

        $validDataForNewCdn = $dataForNewCdn;
        $this->evaluateOkResponse($response, $validDataForNewCdn);

        $geoProtection = $this->getEntityManager()->getRepository(ResourceGeoProtection::class)
            ->findOneBy(['resource' => $response->decodedContent['cdn_resource']['cdn_reference']]);
        self::assertNotNull($geoProtection);

        $hotlinkProtection = $this->getEntityManager()->getRepository(ResourceRefererProtection::class)
            ->findOneBy(['resource' => $response->decodedContent['cdn_resource']['cdn_reference']]);
        self::assertNotNull($hotlinkProtection);

        $ipProtection = $this->getEntityManager()->getRepository(ResourceIpProtection::class)
            ->findOneBy(['resource' => $response->decodedContent['cdn_resource']['cdn_reference']]);
        self::assertNotNull($ipProtection);
    }

    /**
     * @param array<string, mixed>|null $geoProtectionData
     * @param array<string, mixed>|null $hotlinkProtectionData
     * @param array<string, mixed>|null $ipProtectionData
     * @param array<string, mixed>|null $sslData
     *
     * @dataProvider providerInvalidData
     */
    public function testInvalidHotlinkProtection(
        array|null $geoProtectionData,
        array|null $hotlinkProtectionData,
        array|null $ipProtectionData,
        array|null $sslData,
    ): void {
        $dataForNewCdn = $this->prepareDataForNewCdn();

        $response = $this->callApiGetResponse(
            $dataForNewCdn,
            $geoProtectionData,
            $hotlinkProtectionData,
            $ipProtectionData,
            $sslData,
        );

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
    }

    /**
     * @return Generator<string, array{
     *     array<string, mixed>|null,
     *     array<string, mixed>|null,
     *     array<string, mixed>|null,
     *     array<string, mixed>|null
     * }>
     */
    public static function providerInvalidData(): Generator
    {
        yield 'invalid geo protection type' => [
            ['type' => 'invalid-type', 'locations' => [['country' => 'CZ']]],
            null,
            null,
            null,
        ];

        yield 'invalid hotlink protection' => [
            null,
            ['type' => 'invalid-type', 'addresses' => ['domain' => 'example.com']],
            null,
            null,
        ];

        yield 'invalid IP protection' => [
            null,
            null,
            ['type' => 'type', 'addresses' => []],
            null,
        ];

        yield 'invalid SSL data' => [
            null,
            null,
            null,
            ['certificate' => 'invalid', 'key' => 'invalid'],
        ];
    }

    public function testSetupWithSsl(): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();

        $certificatePair = (new CertificatePairGenerator())->generateRandomCertificatePair();

        $sslData = [
            'certificate' => $certificatePair->getCertificate(),
            'key' => $certificatePair->getPrivateKey(),
        ];

        $response = $this->callApiGetResponse(
            $dataForNewCdn,
            sslData: $sslData,
        );

        $validDataForNewCdn = $dataForNewCdn;
        $this->evaluateOkResponse($response, $validDataForNewCdn);

        $ssl = $this->getEntityManager()->getRepository(Ssl::class)
            ->findOneBy(['resource' => $response->decodedContent['cdn_resource']['cdn_reference']]);
        self::assertNotNull($ssl);
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->setUpNotifyResourceChangeHelper();
    }

    /**
     * @param mixed[] $data
     * @param array<string, mixed> $geoProtectionData
     * @param array<string, mixed> $hotlinkProtectionData
     * @param array<string, mixed> $ipProtectionData
     * @param array<string, mixed> $sslData
     */
    private function callApiGetResponse(
        array $data,
        array|null $geoProtectionData = null,
        array|null $hotlinkProtectionData = null,
        array|null $ipProtectionData = null,
        array|null $sslData = null,
    ): ResponseDecoded {
        $this->client->request(
            Request::METHOD_POST,
            '/resource',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                'cdn_resource' => $data,
                'geo_protection' => $geoProtectionData,
                'hotlink_protection' => $hotlinkProtectionData,
                'ip_protection' => $ipProtectionData,
                'ssl' => $sslData,
            ]),
        );

        $response = $this->client->getResponse();

        return ResponseDecoded::fromResponse($response);
    }

    /** @param mixed[] $validData */
    private function evaluateOkResponse(ResponseDecoded $response, array $validData): void
    {
        self::assertSame(Response::HTTP_OK, $response->statusCode);

        $validResponse = $this->addDynamicParametersFromResponse(
            $response->decodedContent,
            ['cdn_resource' => $validData],
        );
        $validResponse = $this->fixInputOutputInconsistencies($validResponse);
        self::assertEquals($validResponse, $response->decodedContent);

        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertSingleCreatingMessageInQueue($resourceId);
    }

    /**
     * @param mixed[][] $responseData
     * @param mixed[][] $originalData
     *
     * @return mixed[][]
     */
    private function addDynamicParametersFromResponse(array $responseData, array $originalData): array
    {
        $dynamicParameters = ['cdn_url', 'id', 'cdn_reference', 'created_at', 'updated_at', 'suspended_at'];

        foreach ($dynamicParameters as $parameter) {
            if (! array_key_exists($parameter, $responseData['cdn_resource'])) {
                continue;
            }

            $originalData['cdn_resource'][$parameter] = $responseData['cdn_resource'][$parameter];
        }

        return $originalData;
    }

    /**
     * @param mixed[][] $responseData
     *
     * @return mixed[][]
     */
    private function fixInputOutputInconsistencies(array $responseData): array
    {
        // rename mp4_pseudo_streaming (input) to mp4_pseudo_on (output)
        $this->renameKey($responseData['cdn_resource'], 'mp4_pseudo_streaming', 'mp4_pseudo_on');

        // https_redirect_code is 0 when not set in database
        $responseData['cdn_resource']['https_redirect_code'] =
            (int) $responseData['cdn_resource']['https_redirect_code'];

        $responseData['cdn_resource']['response_headers'] = array_map(
            static fn (ResponseHeaderSchema $header) => ['name' => $header->name, 'value' => $header->value],
            $responseData['cdn_resource']['response_headers'],
        );

        return $responseData;
    }

    /** @param mixed[] $data */
    private function renameKey(array &$data, string $oldKey, string $newKey): void
    {
        $keys = array_keys($data);

        $position = array_search($oldKey, $keys);
        assert($position !== false);

        $keys[$position] = $newKey;

        $data = array_combine($keys, $data);
    }

    /** @return LetsEncryptRequest[] */
    private function findCertificateRequestsForResourceId(int $resource): array
    {
        return $this->getEntityManager()->createQueryBuilder()
            ->from(LetsEncryptRequest::class, 'r')
            ->select('r')
            ->where('r.resource = :resourceId')
            ->setParameter('resourceId', $resource)
            ->getQuery()
            ->getResult();
    }
}
