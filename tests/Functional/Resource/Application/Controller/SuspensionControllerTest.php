<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use DateTimeImmutable;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function sprintf;

class SuspensionControllerTest extends WebTestCase
{
    use NotifyResourceChangeHelper;
    use TemporaryData;

    public function testSuspendAction(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setSuspended(null);
        $this->getEntityManager()->flush();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/cdn_resources/%d/suspend.json', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        $resource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertTrue($resource->isSuspended());
        self::assertSingleUpdatingMessageInQueue($resource->getId());
    }

    public function testSuspendActionWithSuspendedResource(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setSuspended(new DateTimeImmutable());
        $this->getEntityManager()->flush();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/cdn_resources/%d/suspend.json', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        $resource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertTrue($resource->isSuspended()); // still suspended
        self::assertNoMessageInQueue();
    }

    public function testUnsuspendAction(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setSuspended(new DateTimeImmutable());
        $this->getEntityManager()->flush();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/cdn_resources/%d/unsuspend.json', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        $resource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertFalse($resource->isSuspended());
        self::assertSingleRestoringMessageInQueue($resource->getId());
    }

    public function testUnsuspendActionWithUnsuspendedResource(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setSuspended(null);
        $this->getEntityManager()->flush();

        $this->client->request(
            Request::METHOD_PUT,
            sprintf('/cdn_resources/%d/unsuspend.json', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        $resource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertFalse($resource->isSuspended()); // still not suspended
        self::assertNoMessageInQueue();
    }
}
