<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\FollowRedirect;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;
use Cdn77\NxgApi\Resource\Application\Payload\FollowRedirectSchema;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Generators\CertificateDefinitions;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function is_array;
use function ksort;
use function Safe\json_encode;

final class EditTest extends WebTestCase
{
    use CertificateDefinitions;
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    /** @return Generator<string, array<int, array<int, int>|bool>> */
    public static function providerFollowRedirectPreparationOnly(): Generator
    {
        yield 'empty 1' => [false, []];
        yield 'empty 2' => [true, []];
        yield 'data 1' => [true, [301]];
        yield 'data 2' => [true, [301, 302]];
    }

    /** @return Generator<string, array<int, array<int, int>|bool>> */
    public static function providerFollowRedirect(): Generator
    {
        $single = [301];
        $multi = [301, 302];

        yield 'not used -> turn on enabled' => [false, true, [], []];
        yield 'not used -> turn on enabled with codes 1' => [false, true, [], $single];
        yield 'not used -> turn on enabled with codes 2' => [false, true, [], $multi];
        yield 'not used -> off' => [false, false, [], []];
        yield 'only enabled -> enabled with codes 1' => [true, true, [], $single];
        yield 'only enabled -> enabled with codes 2' => [true, true, [], $multi];
        yield 'only enabled -> off' => [true, false, [], []];
        yield 'only enabled -> only enabled' => [true, true, [], []];
        yield 'enabled with codes -> only enabled' => [true, true, $single, []];
        yield 'enabled with codes -> off' => [true, false, $multi, []];
        yield 'enabled with codes -> enabled with changed codes' => [true, true, $multi, $single];
    }

    /** @return Generator<string, array<int, array<int, int>|bool>> */
    public static function providerFollowRedirectMultiple(): Generator
    {
        $single = [301];
        $multi = [301, 302];
        $multi2 = [301, 302, 303];

        yield 'enabled with codes -> first off, second changed' => [true, false, true, $multi, [], $single];
        yield 'enabled with codes -> enabled with changed codes' => [true, true, true, $multi, $single, $multi2];
    }

    /** @return Generator<string, array<int, array<int, int|string|bool>|bool|string|int|null>> */
    public static function providerBadFollowRedirect(): Generator
    {
        $single = [301];
        $multi = [301, 302];

        yield 'not used -> bad enabled 1' => [false, 'true', [], []];
        yield 'not used -> bad enabled 2' => [false, 'false', [], []];
        yield 'not used -> bad enabled 3' => [false, 'string', [], []];
        yield 'not used -> bad enabled 4' => [false, '1', [], []];
        yield 'not used -> bad enabled 5' => [false, '0', [], []];
        yield 'not used -> bad enabled 6' => [false, 1, [], []];
        yield 'not used -> bad enabled 7' => [false, 0, [], []];
        yield 'not used -> bad enabled 8' => [false, [], [], []];
        yield 'not used -> bad enabled 9' => [false, null, [], []];

        yield 'not used -> bad codes 1' => [false, true, [], 'string'];
        yield 'not used -> bad codes 2' => [false, true, [], 1];
        yield 'not used -> bad codes 3' => [false, true, [], 0];
        yield 'not used -> bad codes 4' => [false, true, [], true];
        yield 'not used -> bad codes 5' => [false, true, [], false];
        yield 'not used -> bad codes 6' => [false, true, [], ['301']];
        yield 'not used -> bad codes 7' => [false, true, [], [true]];
        yield 'not used -> bad codes 9' => [false, true, [], [201]];
        yield 'not used -> bad codes 10' => [false, true, [], [400]];
        yield 'not used -> bad codes 11' => [false, true, [], [301, '302']];
        yield 'not used -> bad codes 12' => [false, true, [], null];

        yield 'not used -> bad combination 1' => [false, false, [], $single];

        yield 'only enabled -> bad enabled 1' => [true, 'true', [], []];
        yield 'only enabled -> bad enabled 2' => [true, 'false', [], []];
        yield 'only enabled -> bad enabled 3' => [true, 'string', [], []];
        yield 'only enabled -> bad enabled 4' => [true, '1', [], []];
        yield 'only enabled -> bad enabled 5' => [true, '0', [], []];
        yield 'only enabled -> bad enabled 6' => [true, 1, [], []];
        yield 'only enabled -> bad enabled 7' => [true, 0, [], []];
        yield 'only enabled -> bad enabled 8' => [true, [], [], []];
        yield 'only enabled -> bad enabled 9' => [true, null, [], []];

        yield 'only enabled -> bad codes 1' => [true, true, [], 'string'];
        yield 'only enabled -> bad codes 2' => [true, true, [], 1];
        yield 'only enabled -> bad codes 3' => [true, true, [], 0];
        yield 'only enabled -> bad codes 4' => [true, true, [], true];
        yield 'only enabled -> bad codes 5' => [true, true, [], false];
        yield 'only enabled -> bad codes 6' => [true, true, [], ['301']];
        yield 'only enabled -> bad codes 7' => [true, true, [], [true]];
        yield 'only enabled -> bad codes 9' => [true, true, [], [201]];
        yield 'only enabled -> bad codes 10' => [true, true, [], [400]];
        yield 'only enabled -> bad codes 11' => [true, true, [], [301, '302']];
        yield 'only enabled -> bad codes 12' => [true, true, [], null];

        yield 'only enabled -> bad combination 1' => [true, false, [], $single];

        yield 'enabled with codes -> bad enabled 1' => [true, 'true', $multi, []];
        yield 'enabled with codes -> bad enabled 2' => [true, 'false', $multi, []];
        yield 'enabled with codes -> bad enabled 3' => [true, 'string', $multi, []];
        yield 'enabled with codes -> bad enabled 4' => [true, '1', $multi, []];
        yield 'enabled with codes -> bad enabled 5' => [true, '0', $multi, []];
        yield 'enabled with codes -> bad enabled 6' => [true, 1, $multi, []];
        yield 'enabled with codes -> bad enabled 7' => [true, 0, $multi, []];
        yield 'enabled with codes -> bad enabled 8' => [true, [], $multi, []];
        yield 'enabled with codes -> bad enabled 9' => [true, null, $multi, []];

        yield 'enabled with codes -> bad codes 1' => [true, true, $multi, 'string'];
        yield 'enabled with codes -> bad codes 2' => [true, true, $multi, 1];
        yield 'enabled with codes -> bad codes 3' => [true, true, $multi, 0];
        yield 'enabled with codes -> bad codes 4' => [true, true, $multi, true];
        yield 'enabled with codes -> bad codes 5' => [true, true, $multi, false];
        yield 'enabled with codes -> bad codes 6' => [true, true, $multi, ['301']];
        yield 'enabled with codes -> bad codes 7' => [true, true, $multi, [true]];
        yield 'enabled with codes -> bad codes 9' => [true, true, $multi, [201]];
        yield 'enabled with codes -> bad codes 10' => [true, true, $multi, [400]];
        yield 'enabled with codes -> bad codes 11' => [true, true, $multi, [301, '302']];
        yield 'enabled with codes -> bad codes 12' => [true, true, $multi, null];

        yield 'origin with codes -> bad combination 1' => [true, false, $multi, $single];
    }

    public static function providerBadFollowRedirectMultiple(): Generator
    {
        $single = [301];
        $multi = [301, 302];

        yield 'not used -> first ok, second bad' => [false, true, 'true', [], [], []];
        yield 'not used -> first bad, second ok' => [false, 'true', true, [], [], []];

        yield 'not used -> first ok, second bad codes' => [false, true, true, [], [], 'notArray'];
        yield 'not used -> first bad codes, second ok' => [false, true, true, [], 'notArray', []];
    }

    /**
     * @param list<int> $codesForPreparation
     * @param list<int> $codesForRequest
     *
     * @dataProvider providerFollowRedirect
     */
    public function testOk(
        bool $enabledForPreparation,
        bool $enabledForRequest,
        array $codesForPreparation,
        array $codesForRequest,
    ): void {
        $resource = $this->createTemporaryResource();

        $resource->getMainOrigin()->setupFollowRedirect($enabledForPreparation, $codesForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $requestData = $this->prepareRequestData(
            $resource->getMainOrigin(),
            $enabledForRequest,
            $codesForRequest,
        );
        $response = $this->callApiGetResponse($resource, [$requestData]);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertUpdatingMessageInQueue($resource->getId());

        $this->assertOriginAndCodes($resource->getId(), 1, $enabledForRequest, $codesForRequest);
    }

    /**
     * @param list<int> $codesForPreparation
     * @param list<int> $codesForRequestA
     * @param list<int> $codesForRequestB
     *
     * @dataProvider providerFollowRedirectMultiple
     */
    public function testOkMultipleOrigins(
        bool $enabledForPreparation,
        bool $enabledForRequestA,
        bool $enabledForRequestB,
        array $codesForPreparation,
        array $codesForRequestA,
        array $codesForRequestB,
    ): void {
        $resource = $this->createTemporaryResource();
        $originB = $this->createResourceOrigin($resource, 2);
        $resource->getOrigins()->add($originB);
        $this->getEntityManager()->refresh($resource);

        $resource->getMainOrigin()->setupFollowRedirect($enabledForPreparation, $codesForPreparation);
        $resource->getSecondOrigin()->setupFollowRedirect($enabledForPreparation, $codesForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $originDataA = $this->prepareRequestData(
            $resource->getMainOrigin(),
            $enabledForRequestA,
            $codesForRequestA,
        );
        $originDataB = $this->prepareRequestData(
            $resource->getSecondOrigin(),
            $enabledForRequestB,
            $codesForRequestB,
        );
        $response = $this->callApiGetResponse($resource, [$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertUpdatingMessageInQueue($resource->getId());

        $this->assertOriginAndCodes($resource->getId(), 1, $enabledForRequestA, $codesForRequestA);
        $this->assertOriginAndCodes($resource->getId(), 2, $enabledForRequestB, $codesForRequestB);
    }

    /**
     * @param mixed $enabledForRequest
     * @param list<int> $codesForPreparation
     * @param mixed $codesForRequest
     *
     * @dataProvider providerBadFollowRedirect
     */
    public function testFail(
        bool $enabledForPreparation,
        $enabledForRequest,
        array $codesForPreparation,
        $codesForRequest,
    ): void {
        $resource = $this->createTemporaryResource();

        $resource->getMainOrigin()->setupFollowRedirect($enabledForPreparation, $codesForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $requestData = $this->prepareRequestData(
            $resource->getMainOrigin(),
            $enabledForRequest,
            $codesForRequest,
        );
        $response = $this->callApiGetResponse($resource, [$requestData]);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertNoMessageInQueue();

        $this->assertOriginAndCodes($resource->getId(), 1, $enabledForPreparation, $codesForPreparation);
    }

    /**
     * @param mixed $enabledForRequestA
     * @param mixed $enabledForRequestB
     * @param list<int> $codesForPreparation
     * @param mixed $codesForRequestA
     * @param mixed $codesForRequestB
     *
     * @dataProvider providerBadFollowRedirectMultiple
     */
    public function testFailMultiple(
        bool $enabledForPreparation,
        $enabledForRequestA,
        $enabledForRequestB,
        array $codesForPreparation,
        $codesForRequestA,
        $codesForRequestB,
    ): void {
        $resource = $this->createTemporaryResource();
        $originB = $this->createResourceOrigin($resource, 2);
        $resource->getOrigins()->add($originB);
        $this->getEntityManager()->refresh($resource);

        $resource->getMainOrigin()->setupFollowRedirect($enabledForPreparation, $codesForPreparation);
        $resource->getSecondOrigin()->setupFollowRedirect($enabledForPreparation, $codesForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $originDataA = $this->prepareRequestData(
            $resource->getMainOrigin(),
            $enabledForRequestA,
            $codesForRequestA,
        );
        $originDataB = $this->prepareRequestData(
            $resource->getSecondOrigin(),
            $enabledForRequestB,
            $codesForRequestB,
        );
        $response = $this->callApiGetResponse($resource, [$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertNoMessageInQueue();

        $this->assertOriginAndCodes($resource->getId(), 1, $enabledForPreparation, $codesForPreparation);
        $this->assertOriginAndCodes($resource->getId(), 2, $enabledForPreparation, $codesForPreparation);
    }

    /**
     * @param list<int> $codesForPreparation
     *
     * @dataProvider providerFollowRedirectPreparationOnly
     */
    public function testFailWhenFollowRedirectMissing(bool $enabledForPreparation, array $codesForPreparation): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setupFollowRedirect($enabledForPreparation, $codesForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $originData = $this->addRequiredOriginData($resource->getMainOrigin(), []);
        unset($originData[OriginSchema::FIELD_FOLLOW_REDIRECT]);

        $response = $this->callApiGetResponse($resource, [$originData]);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'This value should not be null.',
            'origins[0].follow_redirect',
            'origins',
        );

        $this->assertOriginAndCodes($resource->getId(), 1, $enabledForPreparation, $codesForPreparation);
    }

    /**
     * @param list<int> $codesForPreparation
     *
     * @dataProvider providerFollowRedirectPreparationOnly
     */
    public function testFailWhenFollowRedirectMissingMultipleOrigins(
        bool $enabledForPreparation,
        array $codesForPreparation,
    ): void {
        $resource = $this->createTemporaryResource();
        $originB = $this->createResourceOrigin($resource, 2);
        $resource->getOrigins()->add($originB);
        $this->getEntityManager()->refresh($resource);

        $resource->getMainOrigin()->setupFollowRedirect($enabledForPreparation, $codesForPreparation);
        $resource->getSecondOrigin()->setupFollowRedirect($enabledForPreparation, $codesForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $originDataA = $this->addRequiredOriginData($resource->getMainOrigin(), []);
        $originDataB = $this->addRequiredOriginData($resource->getMainOrigin(), []);
        unset($originDataB[OriginSchema::FIELD_FOLLOW_REDIRECT]);

        $response = $this->callApiGetResponse($resource, [$originDataA, $originDataB]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            'This value should not be null.',
            'origins[1].follow_redirect',
            'origins',
        );

        $this->assertOriginAndCodes($resource->getId(), 1, $enabledForPreparation, $codesForPreparation);
        $this->assertOriginAndCodes($resource->getId(), 2, $enabledForPreparation, $codesForPreparation);
    }

    /** @param array<int, int> $expectedCodes */
    private function assertOriginAndCodes(
        int $resourceId,
        int $priority,
        bool $expectedFollowRedirectOrigin,
        array $expectedCodes,
    ): void {
        $resource = $this->getResource($resourceId);

        $origin = $resource->getOrigins()->get($priority);
        self::assertInstanceOf(ResourceOrigin::class, $origin);

        $actualFollowRedirectCodes = $origin->getFollowRedirectCodes();
        if (is_array($actualFollowRedirectCodes)) {
            ksort($actualFollowRedirectCodes);
        }

        ksort($expectedCodes);

        self::assertSame($expectedFollowRedirectOrigin, $origin->hasFollowRedirectOrigin());

        /** @var array<int, int> $expectedCodes */
        $expectedCodes = $expectedCodes; // to make cs happy
        self::assertSame(ValueReplacer::emptyArrayToNull($expectedCodes), $actualFollowRedirectCodes);
    }

    /**
     * @param mixed $followRedirectOrigin
     * @param mixed $followRedirectCodes
     *
     * @return array<string, array<string, mixed>>
     */
    private function prepareRequestData(ResourceOrigin $origin, $followRedirectOrigin, $followRedirectCodes): array
    {
        $data = [
            ResourceAddInfo::FIELD_FOLLOW_REDIRECT => [
                FollowRedirectSchema::FIELD_ENABLED => $followRedirectOrigin,
                FollowRedirectSchema::FIELD_CODES => $followRedirectCodes,
            ],
        ];

        return $this->addRequiredOriginData($origin, $data);
    }

    /** @param array<int, array<string, mixed>> $origins */
    private function callApiGetResponse(CdnResource $resource, array|null $origins = null): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                'cdn_resource' => $this->addRequiredData($resource, []),
                ResourceAddSchema::FIELD_ORIGINS => $origins,
            ]),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
