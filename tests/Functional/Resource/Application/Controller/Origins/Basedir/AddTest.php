<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\Basedir;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginSchema;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginUrlSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;
use function str_repeat;

class AddTest extends WebTestCase
{
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    private const ERROR_STRICTLY_STRING
        = 'This value have to be strictly a string (neither 1, 0, null, boolean, array etc. is allowed).';

    /** @return Generator<string, array<string>> */
    public static function providerOriginBasedir(): Generator
    {
        yield 'empty 1' => [''];
        yield 'data 1' => ['basedir'];
        yield 'data 2' => ['basedir/another'];
        yield 'data 3' => ['basedir/another/file.type'];
        yield 'data 4' => [str_repeat('a', ResourceEditInfo::MAX_ORIGIN_BASEDIR_LENGTH)];
    }

    /** @return Generator<string, array<string>> */
    public static function providerOriginBasedirMultipleOrigins(): Generator
    {
        yield 'empty 1' => ['', ''];
        yield 'empty 1, value 2' => ['', 'basedir'];
        yield 'data 1' => ['basedir', 'basedir/another'];
        yield 'data 2' => ['basedir', str_repeat('a', ResourceEditInfo::MAX_ORIGIN_BASEDIR_LENGTH)];
    }

    /** @return Generator<string, array<int, array<mixed>|int|bool|string|null>> */
    public static function providerFailOriginBasedir(): Generator
    {
        $slashErrorMsg = "Origin base dir can't start or end with '/'.";
        $tooLongErrorMsg = 'This value is too long. It should have 255 characters or less.';

        yield 'bad type 1' => [0, self::ERROR_STRICTLY_STRING];
        yield 'bad type 2' => [1, self::ERROR_STRICTLY_STRING];
        yield 'bad type 3' => [[], self::ERROR_STRICTLY_STRING];
        yield 'bad type 4' => [null, self::ERROR_STRICTLY_STRING];
        yield 'bad type 5' => [true, self::ERROR_STRICTLY_STRING];
        yield 'bad type 6' => [false, self::ERROR_STRICTLY_STRING];

        yield 'bad value 1' => ['/string', $slashErrorMsg];
        yield 'bad value 2' => ['/string/', $slashErrorMsg];
        yield 'bad value 3' => ['string/', $slashErrorMsg];
        yield 'bad value 4' => ['/', $slashErrorMsg];
        yield 'bad value 5' => [str_repeat('a', ResourceEditInfo::MAX_ORIGIN_BASEDIR_LENGTH + 1), $tooLongErrorMsg];
    }

    /** @return Generator<string, array<int, array<mixed>|int|bool|string|null>> */
    public static function providerFailOriginBasedirMultipleOrigins(): Generator
    {
        yield 'bad type 1 - first ok, second bad' => [
            '',
            0,
            [self::ERROR_STRICTLY_STRING],
            ['origins.origins[1].url.basedir' => [self::ERROR_STRICTLY_STRING]],
        ];

        yield 'bad type 1 - first bad, second ok' => [
            0,
            '',
            [self::ERROR_STRICTLY_STRING],
            ['origins.origins[0].url.basedir' => [self::ERROR_STRICTLY_STRING]],
        ];

        yield 'bad type 1 - both bad' => [
            0,
            0,
            [self::ERROR_STRICTLY_STRING, self::ERROR_STRICTLY_STRING],
            [
                'origins.origins[0].url.basedir' => [self::ERROR_STRICTLY_STRING],
                'origins.origins[1].url.basedir' => [self::ERROR_STRICTLY_STRING],
            ],
        ];
    }

    /** @dataProvider providerOriginBasedir */
    public function testOk(string $originBasedir): void
    {
        $originData = $this->prepareRequestData($originBasedir);
        $response = $this->callApiGetResponse([$originData]);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        self::assertSame(
            $originBasedir,
            $response->decodedContent['cdn_resource'][ResourceAddInfo::FIELD_ORIGIN_BASEDIR],
        );
        self::assertSame(ValueReplacer::emptyStringToNull($originBasedir), $resource->getMainOrigin()->getBasedir());
    }

    /** @dataProvider providerOriginBasedirMultipleOrigins */
    public function testOkMultipleOrigins(string $originBasedirA, string $originBasedirB): void
    {
        $originDataA = $this->prepareRequestData($originBasedirA);
        $originDataB = $this->prepareRequestData($originBasedirB, 2);
        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        self::assertSame(
            $originBasedirA,
            $response->decodedContent['cdn_resource'][ResourceAddInfo::FIELD_ORIGIN_BASEDIR],
        );
        self::assertSame(
            ValueReplacer::emptyStringToNull($originBasedirA),
            $resource->getMainOrigin()->getBasedir(),
        );
        self::assertSame(
            ValueReplacer::emptyStringToNull($originBasedirB),
            $resource->getSecondOrigin()->getBasedir(),
        );
    }

    /**
     * @param mixed $originBasedir
     *
     * @dataProvider providerFailOriginBasedir
     */
    public function testFail($originBasedir, string $errorMsg): void
    {
        $originData = $this->prepareRequestData($originBasedir);
        $response = $this->callApiGetResponse([$originData]);

        $this->evaluateUnprocessableEntityResponse($response, $errorMsg, 'origins[0].url.basedir', 'origins');
    }

    /**
     * @param mixed $originBasedirA
     * @param mixed $originBasedirB
     * @param array<string> $errors
     * @param array<string, array<string>> $fields
     *
     * @dataProvider providerFailOriginBasedirMultipleOrigins
     */
    public function testFailMultipleOrigins($originBasedirA, $originBasedirB, array $errors, array $fields): void
    {
        $originDataA = $this->prepareRequestData($originBasedirA);
        $originDataB = $this->prepareRequestData($originBasedirB, 2);
        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertSame($errors, $response->decodedContent['errors']);
        self::assertSame($fields, $response->decodedContent['fields']);
    }

    public function testFailWhenBasedirMissing(): void
    {
        $originData = $this->prepareDataForOrigin();
        unset($originData['url']['basedir']);

        $response = $this->callApiGetResponse([$originData]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            'This value have to be strictly a string (neither 1, 0, null, boolean, array etc. is allowed).',
            'origins[0].url.basedir',
            'origins',
        );
    }

    public function testFailWhenBasedirMissingMultipleOrigins(): void
    {
        $originDataA = $this->prepareDataForOrigin();
        $originDataB = $this->prepareDataForOrigin(2);
        unset($originDataB['url']['basedir']);

        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            'This value have to be strictly a string (neither 1, 0, null, boolean, array etc. is allowed).',
            'origins[1].url.basedir',
            'origins',
        );
    }

    /**
     * @param mixed $originBasedir
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData($originBasedir, int $priority = 1): array
    {
        $dataForOrigin = $this->prepareDataForOrigin($priority);

        self::assertIsArray($dataForOrigin[OriginSchema::FIELD_URL]);
        $dataForOrigin[OriginSchema::FIELD_URL][OriginUrlSchema::FIELD_BASEDIR] = $originBasedir;

        return $dataForOrigin;
    }

    /** @param array<int, array<string, mixed>> $origins */
    private function callApiGetResponse(array $origins): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_POST,
            '/cdn_resources.json',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                'cdn_resource' => $this->prepareDataForNewCdn(),
                ResourceAddSchema::FIELD_ORIGINS => $origins,
            ]),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
