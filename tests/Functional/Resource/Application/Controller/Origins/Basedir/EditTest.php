<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\Basedir;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginSchema;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginUrlSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;
use function str_repeat;

final class EditTest extends WebTestCase
{
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    /** @return Generator<string, array<string|null>> */
    public static function providerOriginBasedir(): Generator
    {
        yield 'empty -> empty' => [null, ''];
        yield 'basedir -> empty' => ['basedir', ''];

        yield 'empty -> basedir 1' => [null, 'basedir'];
        yield 'empty -> basedir 2' => [null, 'basedir/another'];
        yield 'empty -> basedir 3' => [null, 'basedir/another/file.type'];
        yield 'empty -> basedir 4' => [null, str_repeat('a', ResourceEditInfo::MAX_ORIGIN_BASEDIR_LENGTH)];

        yield 'basedir -> other basedir 1' => ['basedir', 'other/basedir'];
    }

    /** @return Generator<string, array<string|null>> */
    public static function providerOriginBasedirMultipleOrigins(): Generator
    {
        yield 'basedir -> empty' => ['basedir', '', ''];
        yield 'empty -> basedir' => ['', 'basedirA', 'basedirB'];
        yield 'basedir -> another basedir' => ['basedir', 'basedirA', 'basedirB'];
    }

    /** @return Generator<string, array<int, array<mixed>|string|int|bool|null>> */
    public static function providerBadOriginBasedir(): Generator
    {
        $strictlyStringErrorMsg =
            'This value have to be strictly a string (neither 1, 0, null, boolean, array etc. is allowed).';
        $slashErrorMsg = "Origin base dir can't start or end with '/'.";
        $tooLongErrorMsg = 'This value is too long. It should have 255 characters or less.';

        yield 'empty -> bad type 1' => [null, 0, $strictlyStringErrorMsg];
        yield 'empty -> bad type 2' => [null, 1, $strictlyStringErrorMsg];
        yield 'empty -> bad type 3' => [null, [], $strictlyStringErrorMsg];
        yield 'empty -> bad type 4' => [null, null, $strictlyStringErrorMsg];
        yield 'empty -> bad type 5' => [null, true, $strictlyStringErrorMsg];
        yield 'empty -> bad type 6' => [null, false, $strictlyStringErrorMsg];

        yield 'empty -> bad value 1' => [null, '/basedir', $slashErrorMsg];
        yield 'empty -> bad value 2' => [null, '/basedir/', $slashErrorMsg];
        yield 'empty -> bad value 3' => [null, 'basedir/', $slashErrorMsg];
        yield 'empty -> bad value 4' => [
            null,
            str_repeat('a', ResourceEditInfo::MAX_ORIGIN_BASEDIR_LENGTH + 1),
            $tooLongErrorMsg,
        ];

        yield 'basedir -> bad type 1' => ['basedir', 0, $strictlyStringErrorMsg];
        yield 'basedir -> bad type 2' => ['basedir', 1, $strictlyStringErrorMsg];
        yield 'basedir -> bad type 3' => ['basedir', [], $strictlyStringErrorMsg];
        yield 'basedir -> bad type 4' => ['basedir', null, $strictlyStringErrorMsg];
        yield 'basedir -> bad type 5' => ['basedir', true, $strictlyStringErrorMsg];
        yield 'basedir -> bad type 6' => ['basedir', false, $strictlyStringErrorMsg];

        yield 'basedir -> bad value 1' => ['basedir', '/basedir', $slashErrorMsg];
        yield 'basedir -> bad value 2' => ['basedir', '/basedir/', $slashErrorMsg];
        yield 'basedir -> bad value 3' => ['basedir', 'basedir/', $slashErrorMsg];
        yield 'basedir -> bad value 4' => ['basedir', '/', $slashErrorMsg];
        yield 'basedir -> bad value 5' => [
            'basedir',
            str_repeat('a', ResourceEditInfo::MAX_ORIGIN_BASEDIR_LENGTH + 1),
            $tooLongErrorMsg,
        ];
    }

    /** @return Generator<string, array<int, array<mixed>|string|int|bool|null>> */
    public static function providerBadOriginBasedirMultipleOrigins(): Generator
    {
        yield 'empty -> bad type - first, second ok' => [null, 0, 'basedirB'];
        yield 'empty -> bad type - first ok, second bad' => [null, 'basedirA', 0];

        yield 'empty -> bad value - first ok, second bad ' => [null, 'basedirA', '/basedirB'];
        yield 'empty -> bad value - first bad, second ok ' => [null, 'basedirA/', 'basedirB'];
    }

    /** @dataProvider providerOriginBasedir */
    public function testOk(string|null $originBasedirForPreparation, string $originBasedirForRequest): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setBasedir($originBasedirForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $originData = $this->prepareRequestData($resource->getMainOrigin(), $originBasedirForRequest);
        $response = $this->callApiGetResponse($resource, [$originData]);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);

        $this->assertBasedir($resource->getId(), 1, $originBasedirForRequest);
    }

    /** @dataProvider providerOriginBasedirMultipleOrigins */
    public function testOkMultipleOrigins(
        string|null $originBasedirForPreparation,
        string $originBasedirForRequestA,
        string $originBasedirForRequestB,
    ): void {
        $resource = $this->createTemporaryResource();
        $originB = $this->createResourceOrigin($resource, 2);
        $resource->getOrigins()->add($originB);
        $this->getEntityManager()->refresh($resource);

        $resource->getMainOrigin()->setBasedir($originBasedirForPreparation);
        $resource->getSecondOrigin()->setBasedir($originBasedirForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $originDataA = $this->prepareRequestData($resource->getMainOrigin(), $originBasedirForRequestA);
        $originDataB = $this->prepareRequestData($resource->getSecondOrigin(), $originBasedirForRequestB);
        $response = $this->callApiGetResponse($resource, [$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);

        $this->assertBasedir($resource->getId(), 1, $originBasedirForRequestA);
        $this->assertBasedir($resource->getId(), 2, $originBasedirForRequestB);
    }

    /**
     * @param mixed $originBasedirForRequest
     *
     * @dataProvider providerBadOriginBasedir
     */
    public function testFail(string|null $originBasedirForPreparation, $originBasedirForRequest, string $errorMsg): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setBasedir($originBasedirForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $originData = $this->prepareRequestData($resource->getMainOrigin(), $originBasedirForRequest);
        $response = $this->callApiGetResponse($resource, [$originData]);

        $this->evaluateUnprocessableEntityResponse($response, $errorMsg, 'origins[0].url.basedir', 'origins');

        $updatedResource = $this->getResource($resource->getId());

        self::assertSame($originBasedirForPreparation, $updatedResource->getMainOrigin()->getBasedir());
    }

    /**
     * @param mixed $originBasedirForRequestA
     * @param mixed $originBasedirForRequestB
     *
     * @dataProvider providerBadOriginBasedirMultipleOrigins
     */
    public function testFailMultipleOrigins(
        string|null $originBasedirForPreparation,
        $originBasedirForRequestA,
        $originBasedirForRequestB,
    ): void {
        $resource = $this->createTemporaryResource();
        $originB = $this->createResourceOrigin($resource, 2);
        $resource->getOrigins()->add($originB);
        $this->getEntityManager()->refresh($resource);

        $resource->getMainOrigin()->setBasedir($originBasedirForPreparation);
        $resource->getSecondOrigin()->setBasedir($originBasedirForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $originDataA = $this->prepareRequestData($resource->getMainOrigin(), $originBasedirForRequestA);
        $originDataB = $this->prepareRequestData($resource->getMainOrigin(), $originBasedirForRequestB);
        $response = $this->callApiGetResponse($resource, [$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertNoMessageInQueue();

        $updatedResource = $this->getResource($resource->getId());

        self::assertSame($originBasedirForPreparation, $updatedResource->getMainOrigin()->getBasedir());
        self::assertSame($originBasedirForPreparation, $updatedResource->getSecondOrigin()->getBasedir());
    }

    public function testFailWhenFollowRedirectMissing(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setBasedir('base');

        FlushAndClear::do($this->getEntityManager());

        $originData = $this->addRequiredOriginData($resource->getMainOrigin(), []);
        unset($originData[OriginSchema::FIELD_URL][OriginUrlSchema::FIELD_BASEDIR]);

        $response = $this->callApiGetResponse($resource, [$originData]);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'This value have to be strictly a string (neither 1, 0, null, boolean, array etc. is allowed).',
            'origins[0].url.basedir',
            'origins',
        );

        $updatedResource = $this->getResource($resource->getId());

        self::assertSame('base', $updatedResource->getMainOrigin()->getBasedir());
    }

    public function testFailWhenFollowRedirectMissingMultipleOrigins(): void
    {
        $resource = $this->createResourceWithTwoOrigins();

        $resource->getMainOrigin()->setBasedir('baseA');
        $resource->getSecondOrigin()->setBasedir('baseB');

        FlushAndClear::do($this->getEntityManager());

        $originDataA = $this->addRequiredOriginData($resource->getMainOrigin(), []);
        $originDataB = $this->addRequiredOriginData($resource->getSecondOrigin(), []);
        unset($originDataB[OriginSchema::FIELD_URL][OriginUrlSchema::FIELD_BASEDIR]);

        $response = $this->callApiGetResponse($resource, [$originDataA, $originDataB]);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'This value have to be strictly a string (neither 1, 0, null, boolean, array etc. is allowed).',
            'origins[1].url.basedir',
            'origins',
        );

        $updatedResource = $this->getResource($resource->getId());

        self::assertSame('baseA', $updatedResource->getMainOrigin()->getBasedir());
        self::assertSame('baseB', $updatedResource->getSecondOrigin()->getBasedir());
    }

    private function assertBasedir(int $resourceId, int $priority, string $expectedBasedir): void
    {
        $resource = $this->getResource($resourceId);

        $origin = $resource->getOrigins()->get($priority);
        self::assertInstanceOf(ResourceOrigin::class, $origin);

        self::assertSame(ValueReplacer::emptyStringToNull($expectedBasedir), $origin->getBasedir());
    }

    /**
     * @param mixed $originBasedir
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData(ResourceOrigin $origin, $originBasedir): array
    {
        $data = $this->addRequiredOriginData($origin, []);

        $data[OriginSchema::FIELD_URL][OriginUrlSchema::FIELD_BASEDIR] = $originBasedir;

        return $data;
    }

    /** @param array<int, array<string, mixed>> $origins */
    private function callApiGetResponse(CdnResource $resource, array|null $origins = null): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                'cdn_resource' => $this->addRequiredData($resource, []),
                ResourceAddSchema::FIELD_ORIGINS => $origins,
            ]),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
