<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\Priority;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;

class AddTest extends WebTestCase
{
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    public const ERROR_STRICTLY_INT
        = 'This value have to be strictly an integer (neither string, null, boolean, array etc. is allowed).';
    public const ERROR_RANGE = 'This value should be between 1 and 100.';

    /** @return Generator<string, array<int>> */
    public static function providerOk(): Generator
    {
        yield 'priority min' => [1];
    }

    /** @return Generator<string, array<int>> */
    public static function providerOkMultipleOrigins(): Generator
    {
        yield 'min 1, some 2' => [1, 2];
    }

    /** @return Generator<string, array<int, array<mixed>|int|bool|string|null>> */
    public static function providerFail(): Generator
    {
        yield 'bad type 0' => ['', self::ERROR_STRICTLY_INT];
        yield 'bad type 1' => ['0', self::ERROR_STRICTLY_INT];
        yield 'bad type 2' => ['1', self::ERROR_STRICTLY_INT];
        yield 'bad type 3' => [[], self::ERROR_STRICTLY_INT];
        yield 'bad type 4' => [null, self::ERROR_STRICTLY_INT];
        yield 'bad type 5' => [true, self::ERROR_STRICTLY_INT];
        yield 'bad type 6' => [false, self::ERROR_STRICTLY_INT];

        yield 'bad value 1' => [0, self::ERROR_RANGE];
        yield 'bad value 2' => [101, self::ERROR_RANGE];
    }

    /** @return Generator<string, array<int, array<mixed>|int|bool|string|null>> */
    public static function providerFailMultipleOrigins(): Generator
    {
        yield 'bad type 1 - first ok, second bad' => [
            1,
            '0',
            [self::ERROR_STRICTLY_INT],
            ['origins.origins[1].priority' => [self::ERROR_STRICTLY_INT]],
        ];

        yield 'bad type 1 - first bad, second ok' => [
            true,
            1,
            [self::ERROR_STRICTLY_INT],
            ['origins.origins[0].priority' => [self::ERROR_STRICTLY_INT]],
        ];

        yield 'bad type 1 - both bad' => [
            '0',
            '0',
            [self::ERROR_STRICTLY_INT, self::ERROR_STRICTLY_INT],
            [
                'origins.origins[0].priority' => [self::ERROR_STRICTLY_INT],
                'origins.origins[1].priority' => [self::ERROR_STRICTLY_INT],
            ],
        ];
    }

    /** @dataProvider providerOk */
    public function testOk(int $priority): void
    {
        $originData = $this->prepareRequestData($priority);
        $response = $this->callApiGetResponse([$originData]);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        self::assertSame(ValueReplacer::zeroToNull($priority), $resource->getMainOrigin()->getPriority());
    }

    /** @dataProvider providerOkMultipleOrigins */
    public function testOkMultipleOrigins(int $priorityA, int $priorityB): void
    {
        $originDataA = $this->prepareRequestData($priorityA);
        $originDataB = $this->prepareRequestData($priorityB);
        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        self::assertSame($priorityA, $resource->getMainOrigin()->getPriority());
        self::assertSame($priorityB, $resource->getSecondOrigin()->getPriority());
    }

    /**
     * @param mixed $priority
     *
     * @dataProvider providerFail
     */
    public function testFail($priority, string $errorMsg): void
    {
        $originData = $this->prepareRequestData($priority);
        $response = $this->callApiGetResponse([$originData]);

        $this->evaluateUnprocessableEntityResponse($response, $errorMsg, 'origins[0].priority', 'origins');
    }

    /**
     * @param mixed $priorityA
     * @param mixed $priorityB
     * @param array<string> $errors
     * @param array<string, array<string>> $fields
     *
     * @dataProvider providerFailMultipleOrigins
     */
    public function testFailMultipleOrigins($priorityA, $priorityB, array $errors, array $fields): void
    {
        $originDataA = $this->prepareRequestData($priorityA);
        $originDataB = $this->prepareRequestData($priorityB);
        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertSame($errors, $response->decodedContent['errors']);
        self::assertSame($fields, $response->decodedContent['fields']);
    }

    public function testFailWhenPriorityMissing(): void
    {
        $originData = $this->prepareDataForOrigin();
        unset($originData['priority']);

        $response = $this->callApiGetResponse([$originData]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            'This value have to be strictly an integer (neither string, null, boolean, array etc. is allowed).',
            'origins[0].priority',
            'origins',
        );
    }

    public function testFailWhenPriorityMissingMultipleOrigins(): void
    {
        $originDataA = $this->prepareDataForOrigin();
        $originDataB = $this->prepareDataForOrigin(2);
        unset($originDataB['priority']);

        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            self::ERROR_STRICTLY_INT,
            'origins[1].priority',
            'origins',
        );
    }

    public function testFailWithMultipleOriginsWithSamePriority(): void
    {
        $dataForNewOriginA = $this->prepareDataForOrigin();
        $dataForNewOriginB = $this->prepareDataForOrigin(2);
        $dataForNewOriginC = $this->prepareDataForOrigin(2);

        $response = $this->callApiGetResponse(
            [$dataForNewOriginA, $dataForNewOriginB, $dataForNewOriginC],
        );

        $this->evaluateFailedResponse(
            $response,
            ['All origins must have unique priority.'],
            Response::HTTP_UNPROCESSABLE_ENTITY,
        );
    }

    public function testFailWithMissingMainPriority(): void
    {
        $dataForNewOriginA = $this->prepareDataForOrigin(2);
        $dataForNewOriginB = $this->prepareDataForOrigin(3);

        $response = $this->callApiGetResponse([$dataForNewOriginA, $dataForNewOriginB]);

        $this->evaluateFailedResponse(
            $response,
            ['Main priority origin must exist.'],
            Response::HTTP_UNPROCESSABLE_ENTITY,
        );
    }

    public function testFailWithMultipleMainPriority(): void
    {
        $dataForNewOriginA = $this->prepareDataForOrigin();
        $dataForNewOriginB = $this->prepareDataForOrigin();

        $response = $this->callApiGetResponse([$dataForNewOriginA, $dataForNewOriginB]);

        $this->evaluateFailedResponse(
            $response,
            ['Only one origin can have main priority.'],
            Response::HTTP_UNPROCESSABLE_ENTITY,
        );
    }

    /**
     * @param mixed $priority
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData($priority): array
    {
        $dataForOrigin = $this->prepareDataForOrigin();

        $dataForOrigin[OriginSchema::FIELD_PRIORITY] = $priority;

        return $dataForOrigin;
    }

    /** @param array<int, array<string, mixed>> $origins */
    private function callApiGetResponse(array $origins): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_POST,
            '/cdn_resources.json',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                'cdn_resource' => $this->prepareDataForNewCdn(),
                ResourceAddSchema::FIELD_ORIGINS => $origins,
            ]),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
