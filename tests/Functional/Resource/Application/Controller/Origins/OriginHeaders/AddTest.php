<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\OriginHeaders;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;

class AddTest extends WebTestCase
{
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    public const ERROR_STRICTLY_ARRAY
        = 'This value have to be strictly an array (neither 1, 0, null, string etc. is allowed).';

    /** @return Generator<string, array<array<string, string>>> */
    public static function providerOk(): Generator
    {
        yield 'empty' => [[]];
        yield 'single item' => [['one' => 'two']];
        yield 'double item' => [['one' => 'two', 'three' => 'four']];
    }

    /** @return Generator<string, array<array<string, string>>> */
    public static function providerOkMultipleOrigins(): Generator
    {
        $array = ['one' => 'two', 'three' => 'four'];

        yield 'default both' => [[], []];
        yield 'default 1, array 2' => [[], $array];
        yield 'array 1, default 2' => [$array, []];
        yield 'array 1, array 2' => [$array, $array];
    }

    /** @return Generator<string, array<int, mixed>> */
    public static function providerFail(): Generator
    {
        yield 'bad type 0' => ['', self::ERROR_STRICTLY_ARRAY];
        yield 'bad type 1' => ['0', self::ERROR_STRICTLY_ARRAY];
        yield 'bad type 2' => ['1', self::ERROR_STRICTLY_ARRAY];
        yield 'bad type 3' => [666, self::ERROR_STRICTLY_ARRAY];
        yield 'bad type 4' => [null, self::ERROR_STRICTLY_ARRAY];
        yield 'bad type 5' => [true, self::ERROR_STRICTLY_ARRAY];
        yield 'bad type 6' => [false, self::ERROR_STRICTLY_ARRAY];

        yield 'Forbidden header name Accept' => [
            ['Accept' => 'Value'],
            'Origin header name "Accept" is forbidden.',
        ];

        yield 'Invalid name with colon' => [
            ['Name:' => 'Value'],
            'Origin header name "Name:" contains invalid characters.',
        ];

        yield 'Invalid value with backslash' => [
            ['Key' => 'value_with_backslash\\'],
            'Origin header value "value_with_backslash\\" contains invalid characters.',
        ];
    }

    /** @return Generator<string, array<int, array<mixed>|int|bool|string|null>> */
    public static function providerFailMultipleOrigins(): Generator
    {
        yield 'bad type 1 - first ok, second bad' => [
            [],
            '0',
            [self::ERROR_STRICTLY_ARRAY],
            ['origins.origins[1].origin_headers' => [self::ERROR_STRICTLY_ARRAY]],
        ];

        yield 'bad type 1 - first bad, second ok' => [
            true,
            [],
            [self::ERROR_STRICTLY_ARRAY],
            ['origins.origins[0].origin_headers' => [self::ERROR_STRICTLY_ARRAY]],
        ];

        yield 'bad type 1 - both bad' => [
            '0',
            1,
            [self::ERROR_STRICTLY_ARRAY, self::ERROR_STRICTLY_ARRAY],
            [
                'origins.origins[0].origin_headers' => [self::ERROR_STRICTLY_ARRAY],
                'origins.origins[1].origin_headers' => [self::ERROR_STRICTLY_ARRAY],
            ],
        ];

        yield 'Forbidden header names Accept' => [
            ['acCept' => 'Value'],
            ['ACCEPT' => 'Value'],
            ['Origin header name "acCept" is forbidden.', 'Origin header name "ACCEPT" is forbidden.'],
            [
                'origins.origins[0].origin_headers' => ['Origin header name "acCept" is forbidden.'],
                'origins.origins[1].origin_headers' => ['Origin header name "ACCEPT" is forbidden.'],
            ],
        ];

        yield 'Forbidden header names via and range' => [
            ['via' => 'Value'],
            ['range' => 'Value'],
            ['Origin header name "via" is forbidden.', 'Origin header name "range" is forbidden.'],
            [
                'origins.origins[0].origin_headers' => ['Origin header name "via" is forbidden.'],
                'origins.origins[1].origin_headers' => ['Origin header name "range" is forbidden.'],
            ],
        ];

        yield 'Invalid header names "\\" and "{"' => [
            ['\\' => 'Value'],
            ['{' => 'Value'],
            [
                'Origin header name "\\" contains invalid characters.',
                'Origin header name "{" contains invalid characters.',
            ],
            [
                'origins.origins[0].origin_headers' => ['Origin header name "\\" contains invalid characters.'],
                'origins.origins[1].origin_headers' => ['Origin header name "{" contains invalid characters.'],
            ],
        ];
    }

    /**
     * @param array<string, string> $originHeaders
     *
     * @dataProvider providerOk
     */
    public function testOk(array $originHeaders): void
    {
        $originData = $this->prepareRequestData($originHeaders);
        $response = $this->callApiGetResponse([$originData]);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        if ($originHeaders !== []) {
            self::assertSame(
                $originHeaders,
                $response->decodedContent['cdn_resource']['origin_headers'],
            );
        }

        self::assertSame(
            ValueReplacer::emptyArrayToNull($originHeaders),
            $resource->getMainOrigin()->getOriginHeaders(),
        );
    }

    /**
     * @param array<string, string> $originHeadersA
     * @param array<string, string> $originHeadersB
     *
     * @dataProvider providerOkMultipleOrigins
     */
    public function testOkMultipleOrigins(array $originHeadersA, array $originHeadersB): void
    {
        $originDataA = $this->prepareRequestData($originHeadersA);
        $originDataB = $this->prepareRequestData($originHeadersB, 2);
        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        if ($originHeadersA !== []) {
            self::assertSame(
                $originHeadersA,
                $response->decodedContent['cdn_resource']['origin_headers'],
            );
        }

        self::assertSame(
            ValueReplacer::emptyArrayToNull($originHeadersA),
            $resource->getMainOrigin()->getOriginHeaders(),
        );
        self::assertSame(
            ValueReplacer::emptyArrayToNull($originHeadersB),
            $resource->getSecondOrigin()->getOriginHeaders(),
        );
    }

    /**
     * @param mixed $originHeaders
     *
     * @dataProvider providerFail
     */
    public function testFail($originHeaders, string $errorMsg): void
    {
        $originData = $this->prepareRequestData($originHeaders);
        $response = $this->callApiGetResponse([$originData]);

        $this->evaluateUnprocessableEntityResponse($response, $errorMsg, 'origins[0].origin_headers', 'origins');
    }

    /**
     * @param mixed $originHeadersA
     * @param mixed $originHeadersB
     * @param array<string> $errors
     * @param array<string, array<string>> $fields
     *
     * @dataProvider providerFailMultipleOrigins
     */
    public function testFailMultipleOrigins($originHeadersA, $originHeadersB, array $errors, array $fields): void
    {
        $originDataA = $this->prepareRequestData($originHeadersA);
        $originDataB = $this->prepareRequestData($originHeadersB, 2);
        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertSame($errors, $response->decodedContent['errors']);
        self::assertSame($fields, $response->decodedContent['fields']);
    }

    public function testFailWhenOriginHeadersMissing(): void
    {
        $originData = $this->prepareDataForOrigin();
        unset($originData['origin_headers']);

        $response = $this->callApiGetResponse([$originData]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            'This value have to be strictly an array (neither 1, 0, null, string etc. is allowed).',
            'origins[0].origin_headers',
            'origins',
        );
    }

    public function testFailWhenOriginHeadersMissingMultipleOrigins(): void
    {
        $originDataA = $this->prepareDataForOrigin();
        $originDataB = $this->prepareDataForOrigin(2);
        unset($originDataB['origin_headers']);

        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            self::ERROR_STRICTLY_ARRAY,
            'origins[1].origin_headers',
            'origins',
        );
    }

    /**
     * @param mixed $originHeaders
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData($originHeaders, int $priority = 1): array
    {
        $dataForOrigin = $this->prepareDataForOrigin($priority);

        $dataForOrigin[OriginSchema::FIELD_ORIGIN_HEADERS] = $originHeaders;

        return $dataForOrigin;
    }

    /** @param array<int, array<string, mixed>> $origins */
    private function callApiGetResponse(array $origins): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_POST,
            '/cdn_resources.json',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                'cdn_resource' => $this->prepareDataForNewCdn(),
                ResourceAddSchema::FIELD_ORIGINS => $origins,
            ]),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
