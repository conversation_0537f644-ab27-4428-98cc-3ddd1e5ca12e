<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\Timeout;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;

class AddTest extends WebTestCase
{
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    public const ERROR_STRICTLY_INT
        = 'This value have to be strictly an integer (neither string, null, boolean, array etc. is allowed).';
    public const ERROR_RANGE = 'Origin timeout should be either in range 1-120 or 0 to disable.';

    /** @return Generator<string, array<int>> */
    public static function providerOk(): Generator
    {
        yield 'default' => [0];
        yield 'timeout min' => [1];
        yield 'timeout max' => [120];
        yield 'some timeout' => [66];
    }

    /** @return Generator<string, array<int>> */
    public static function providerOkMultipleOrigins(): Generator
    {
        yield 'default both' => [0, 0];
        yield 'default 1, value 2' => [0, 66];
        yield 'value 1, default 2' => [66, 0];
        yield 'value 1, value 2' => [66, 88];
    }

    /** @return Generator<string, array<int, array<mixed>|int|bool|string|null>> */
    public static function providerFail(): Generator
    {
        yield 'bad type 0' => ['', self::ERROR_STRICTLY_INT];
        yield 'bad type 1' => ['0', self::ERROR_STRICTLY_INT];
        yield 'bad type 2' => ['1', self::ERROR_STRICTLY_INT];
        yield 'bad type 3' => [[], self::ERROR_STRICTLY_INT];
        yield 'bad type 4' => [null, self::ERROR_STRICTLY_INT];
        yield 'bad type 5' => [true, self::ERROR_STRICTLY_INT];
        yield 'bad type 6' => [false, self::ERROR_STRICTLY_INT];

        yield 'bad value 1' => [-1, self::ERROR_RANGE];
        yield 'bad value 2' => [65536, self::ERROR_RANGE];
    }

    /** @return Generator<string, array<int, array<mixed>|int|bool|string|null>> */
    public static function providerFailMultipleOrigins(): Generator
    {
        yield 'bad type 1 - first ok, second bad' => [
            66,
            '0',
            [self::ERROR_STRICTLY_INT],
            ['origins.origins[1].timeout' => [self::ERROR_STRICTLY_INT]],
        ];

        yield 'bad type 1 - first bad, second ok' => [
            true,
            66,
            [self::ERROR_STRICTLY_INT],
            ['origins.origins[0].timeout' => [self::ERROR_STRICTLY_INT]],
        ];

        yield 'bad type 1 - both bad' => [
            '0',
            '0',
            [self::ERROR_STRICTLY_INT, self::ERROR_STRICTLY_INT],
            [
                'origins.origins[0].timeout' => [self::ERROR_STRICTLY_INT],
                'origins.origins[1].timeout' => [self::ERROR_STRICTLY_INT],
            ],
        ];
    }

    /** @dataProvider providerOk */
    public function testOk(int $timeout): void
    {
        $originData = $this->prepareRequestData($timeout);
        $response = $this->callApiGetResponse([$originData]);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        if ($timeout !== 0) {
            self::assertSame(
                $timeout,
                $response->decodedContent['cdn_resource']['origin_timeout'],
            );
        }

        self::assertSame(ValueReplacer::zeroToNull($timeout), $resource->getMainOrigin()->getTimeout());
    }

    /** @dataProvider providerOkMultipleOrigins */
    public function testOkMultipleOrigins(int $timeoutA, int $timeoutB): void
    {
        $originDataA = $this->prepareRequestData($timeoutA);
        $originDataB = $this->prepareRequestData($timeoutB, 2);
        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        if ($timeoutA !== 0) {
            self::assertSame(
                $timeoutA,
                $response->decodedContent['cdn_resource']['origin_timeout'],
            );
        }

        self::assertSame(ValueReplacer::zeroToNull($timeoutA), $resource->getMainOrigin()->getTimeout());
        self::assertSame(ValueReplacer::zeroToNull($timeoutB), $resource->getSecondOrigin()->getTimeout());
    }

    /**
     * @param mixed $timeout
     *
     * @dataProvider providerFail
     */
    public function testFail($timeout, string $errorMsg): void
    {
        $originData = $this->prepareRequestData($timeout);
        $response = $this->callApiGetResponse([$originData]);

        $this->evaluateUnprocessableEntityResponse($response, $errorMsg, 'origins[0].timeout', 'origins');
    }

    /**
     * @param mixed $timeoutA
     * @param mixed $timeoutB
     * @param array<string> $errors
     * @param array<string, array<string>> $fields
     *
     * @dataProvider providerFailMultipleOrigins
     */
    public function testFailMultipleOrigins($timeoutA, $timeoutB, array $errors, array $fields): void
    {
        $originDataA = $this->prepareRequestData($timeoutA);
        $originDataB = $this->prepareRequestData($timeoutB, 2);
        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertSame($errors, $response->decodedContent['errors']);
        self::assertSame($fields, $response->decodedContent['fields']);
    }

    public function testFailWhenTimeoutMissing(): void
    {
        $originData = $this->prepareDataForOrigin();
        unset($originData['timeout']);

        $response = $this->callApiGetResponse([$originData]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            'This value have to be strictly an integer (neither string, null, boolean, array etc. is allowed).',
            'origins[0].timeout',
            'origins',
        );
    }

    public function testFailWhenBasedirMissingMultipleOrigins(): void
    {
        $originDataA = $this->prepareDataForOrigin();
        $originDataB = $this->prepareDataForOrigin(2);
        unset($originDataB['timeout']);

        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            self::ERROR_STRICTLY_INT,
            'origins[1].timeout',
            'origins',
        );
    }

    /**
     * @param mixed $timeout
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData($timeout, int $priority = 1): array
    {
        $dataForOrigin = $this->prepareDataForOrigin($priority);

        $dataForOrigin[OriginSchema::FIELD_TIMEOUT] = $timeout;

        return $dataForOrigin;
    }

    /** @param array<int, array<string, mixed>> $origins */
    private function callApiGetResponse(array $origins): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_POST,
            '/cdn_resources.json',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                'cdn_resource' => $this->prepareDataForNewCdn(),
                ResourceAddSchema::FIELD_ORIGINS => $origins,
            ]),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
