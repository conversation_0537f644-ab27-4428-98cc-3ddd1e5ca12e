<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\Port;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginSchema;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginUrlSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;

class AddTest extends WebTestCase
{
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    public const ERROR_STRICTLY_INT
        = 'This value have to be strictly an integer (neither string, null, boolean, array etc. is allowed).';
    public const ERROR_RANGE = 'Origin port should be either in range 1-65535 or 0 to disable.';

    /** @return Generator<string, array<int>> */
    public static function providerOk(): Generator
    {
        yield 'default' => [0];
        yield 'port min' => [1];
        yield 'port max' => [65535];
        yield 'some port' => [666];
    }

    /** @return Generator<string, array<int>> */
    public static function providerOkMultipleOrigins(): Generator
    {
        yield 'default both' => [0, 0];
        yield 'default 1, value 2' => [0, 666];
        yield 'value 1, default 2' => [666, 0];
        yield 'value 1, value 2' => [666, 888];
    }

    /** @return Generator<string, array<int, array<mixed>|int|bool|string|null>> */
    public static function providerFail(): Generator
    {
        yield 'bad type 0' => ['', self::ERROR_STRICTLY_INT];
        yield 'bad type 1' => ['0', self::ERROR_STRICTLY_INT];
        yield 'bad type 2' => ['1', self::ERROR_STRICTLY_INT];
        yield 'bad type 3' => [[], self::ERROR_STRICTLY_INT];
        yield 'bad type 4' => [null, self::ERROR_STRICTLY_INT];
        yield 'bad type 5' => [true, self::ERROR_STRICTLY_INT];
        yield 'bad type 6' => [false, self::ERROR_STRICTLY_INT];

        yield 'bad value 1' => [-1, self::ERROR_RANGE];
        yield 'bad value 2' => [65536, self::ERROR_RANGE];
    }

    /** @return Generator<string, array<int, array<mixed>|int|bool|string|null>> */
    public static function providerFailMultipleOrigins(): Generator
    {
        yield 'bad type 1 - first ok, second bad' => [
            666,
            '0',
            [self::ERROR_STRICTLY_INT],
            ['origins.origins[1].url.port' => [self::ERROR_STRICTLY_INT]],
        ];

        yield 'bad type 1 - first bad, second ok' => [
            true,
            666,
            [self::ERROR_STRICTLY_INT],
            ['origins.origins[0].url.port' => [self::ERROR_STRICTLY_INT]],
        ];

        yield 'bad type 1 - both bad' => [
            '0',
            '0',
            [self::ERROR_STRICTLY_INT, self::ERROR_STRICTLY_INT],
            [
                'origins.origins[0].url.port' => [self::ERROR_STRICTLY_INT],
                'origins.origins[1].url.port' => [self::ERROR_STRICTLY_INT],
            ],
        ];
    }

    /** @dataProvider providerOk */
    public function testOk(int $port): void
    {
        $originData = $this->prepareRequestData($port);
        $response = $this->callApiGetResponse([$originData]);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        if ($port !== 0) {
            self::assertSame(
                $port,
                $response->decodedContent['cdn_resource']['origin_port'],
            );
        }

        self::assertSame(ValueReplacer::zeroToNull($port), $resource->getMainOrigin()->getPort());
    }

    /** @dataProvider providerOkMultipleOrigins */
    public function testOkMultipleOrigins(int $portA, int $portB): void
    {
        $originDataA = $this->prepareRequestData($portA);
        $originDataB = $this->prepareRequestData($portB, 2);
        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        if ($portA !== 0) {
            self::assertSame(
                $portA,
                $response->decodedContent['cdn_resource']['origin_port'],
            );
        }

        self::assertSame(ValueReplacer::zeroToNull($portA), $resource->getMainOrigin()->getPort());
        self::assertSame(ValueReplacer::zeroToNull($portB), $resource->getSecondOrigin()->getPort());
    }

    /**
     * @param mixed $port
     *
     * @dataProvider providerFail
     */
    public function testFail($port, string $errorMsg): void
    {
        $originData = $this->prepareRequestData($port);
        $response = $this->callApiGetResponse([$originData]);

        $this->evaluateUnprocessableEntityResponse($response, $errorMsg, 'origins[0].url.port', 'origins');
    }

    /**
     * @param mixed $portA
     * @param mixed $portB
     * @param array<string> $errors
     * @param array<string, array<string>> $fields
     *
     * @dataProvider providerFailMultipleOrigins
     */
    public function testFailMultipleOrigins($portA, $portB, array $errors, array $fields): void
    {
        $originDataA = $this->prepareRequestData($portA);
        $originDataB = $this->prepareRequestData($portB, 2);
        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertSame($errors, $response->decodedContent['errors']);
        self::assertSame($fields, $response->decodedContent['fields']);
    }

    public function testFailWhenPortMissing(): void
    {
        $originData = $this->prepareDataForOrigin();
        unset($originData['url']['port']);

        $response = $this->callApiGetResponse([$originData]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            'This value have to be strictly an integer (neither string, null, boolean, array etc. is allowed).',
            'origins[0].url.port',
            'origins',
        );
    }

    public function testFailWhenPortMissingMultipleOrigins(): void
    {
        $originDataA = $this->prepareDataForOrigin();
        $originDataB = $this->prepareDataForOrigin(2);
        unset($originDataB['url']['port']);

        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            self::ERROR_STRICTLY_INT,
            'origins[1].url.port',
            'origins',
        );
    }

    /**
     * @param mixed $port
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData($port, int $priority = 1): array
    {
        $dataForOrigin = $this->prepareDataForOrigin($priority);

        self::assertIsArray($dataForOrigin[OriginSchema::FIELD_URL]);
        $dataForOrigin[OriginSchema::FIELD_URL][OriginUrlSchema::FIELD_PORT] = $port;

        return $dataForOrigin;
    }

    /** @param array<int, array<string, mixed>> $origins */
    private function callApiGetResponse(array $origins): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_POST,
            '/cdn_resources.json',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                'cdn_resource' => $this->prepareDataForNewCdn(),
                ResourceAddSchema::FIELD_ORIGINS => $origins,
            ]),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
