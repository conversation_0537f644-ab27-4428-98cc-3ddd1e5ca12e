<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Generators\CertificateDefinitions;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;

//TODO add test for invalid data

final class EditTest extends WebTestCase
{
    use CertificateDefinitions;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use EvaluateHelper;

    public function testLegacy(): void
    {
        $resource = $this->createTemporaryResource();

        $modifiedResourceData = [
            ResourceEditInfo::FIELD_ORIGIN_URL => 'eu-1.cdn77-storage.com',
            'account_id' => $resource->getAccount()->getId(),
            'origin_scheme' => 'https',
            'origin_port' => 12345,
            ResourceEditInfo::FIELD_ORIGIN_BASEDIR => 'newBaseDir',
            'origin_timeout' => 45,
            'aws_access_key_id' => 'new_aws_access_key',
            'aws_secret' => 'new_aws_secret',
            'aws_region' => 'new_aws_region',
            's3_bucket_name' => 'new_s3_bucket_name',
            's3_type' => 'cdn77-ceph-rgw',
            'forward_host_header' => true,
            ResourceEditInfo::FIELD_SSL_VERIFY_DISABLE => false,
            ResourceAddInfo::FIELD_ORIGIN_HEADERS => ['header1' => '666', 'header2' => 'something'],
            'follow_redirect' => ['enabled' => true, 'codes' => [301, 302]],
        ];

        $validOrigins = [
            [
                'clap_origin_id' => null,
                'priority' => 1,
                'url' => [
                    'host' => $modifiedResourceData['origin_url'],
                    'scheme' => $modifiedResourceData['origin_scheme'],
                    'basedir' => $modifiedResourceData['origin_basedir'],
                    'port' => $modifiedResourceData['origin_port'],
                ],
                'timeout' => $modifiedResourceData['origin_timeout'],
                's3' => [
                    'access_key_id' => $modifiedResourceData['aws_access_key_id'],
                    'secret' => $modifiedResourceData['aws_secret'],
                    'region' => $modifiedResourceData['aws_region'],
                    'bucket_name' => $modifiedResourceData['s3_bucket_name'],
                    'type' => $modifiedResourceData['s3_type'],
                ],
                'forward_host_header' => $modifiedResourceData['forward_host_header'],
                'ssl_verify_disable' => $modifiedResourceData['ssl_verify_disable'],
                'origin_headers' => $modifiedResourceData['origin_headers'],
                'follow_redirect' => [
                    'enabled' => $modifiedResourceData['follow_redirect']['enabled'],
                    'codes' => $modifiedResourceData['follow_redirect']['codes'],
                ],
            ],
        ];

        $response = $this->callApiGetResponse($resource, $modifiedResourceData);

        $this->evaluateOkResponse($response, $resource, $validOrigins);
    }

    public function testSingleOrigin(): void
    {
        $resource = $this->createTemporaryResource();

        $origins = [$this->prepareDataForOrigin()];

        $response = $this->callApiGetResponse($resource, [], $origins);

        $this->evaluateOkResponse($response, $resource, $origins);
    }

    public function testMultipleOrigins(): void
    {
        $resource = $this->createTemporaryResource();

        $origins = [$this->prepareDataForOrigin(), $this->prepareDataForOrigin(2)];

        $response = $this->callApiGetResponse($resource, [], $origins);

        $this->evaluateOkResponse($response, $resource, $origins);
    }

    public function testOriginsHavePriorityOverLegacy(): void
    {
        $resource = $this->createTemporaryResource();

        $origins = [$this->prepareDataForOrigin()];

        $response = $this->callApiGetResponse($resource, ['origin_url' => 'legacy.com'], $origins);

        $this->evaluateOkResponse($response, $resource, $origins);
    }

    public function testDefaultValuesForOrigin(): void
    {
        $resource = $this->createTemporaryResource();

        $origins = [$this->prepareDataForOrigin()];

        self::assertIsArray($origins[0]['url']);
        self::assertArrayHasKey('port', $origins[0]['url']);

        $origins[0]['timeout'] = 0;
        $origins[0]['url']['port'] = 0;

        $response = $this->callApiGetResponse($resource, [], $origins);

        $origins[0]['timeout'] = null;
        $origins[0]['url']['port'] = null;

        $this->evaluateOkResponse($response, $resource, $origins);
    }

    public function testPartialEdit(): void
    {
        //TODO
        self::markTestSkipped('we will probably not allow partial edit as clap is sending all data anyway.');
    }

    /** @param array<int<0,100>, array<string, mixed>> $validData */
    private function evaluateOkResponse(ResponseDecoded $response, CdnResource $resource, array $validData): void
    {
        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);

        $this->evaluateOrigins($resource, $validData);
    }

    /**
     * @param array<string, mixed> $resourceData
     * @param array<int, array<string, mixed>> $origins
     */
    private function callApiGetResponse(
        CdnResource $resource,
        array $resourceData,
        array|null $origins = null,
    ): ResponseDecoded {
        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                'cdn_resource' => $this->addRequiredData($resource, $resourceData),
                ResourceAddSchema::FIELD_ORIGINS => $origins,
            ]),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
