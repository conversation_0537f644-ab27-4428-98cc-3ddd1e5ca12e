<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginS3Schema;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginSchema;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginUrlSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Validator\Constraints\Resource\AllowedDomainValidator;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

use function array_combine;
use function array_key_exists;
use function array_keys;
use function array_search;
use function assert;
use function Safe\json_encode;

//TODO: test for deleting/changing origin with priority=1
//TODO: test for deleting last origin
//TODO: test fail for origins that are completely same
//TODO: test for max/min/ok/bad/missing values for originHeaders etc

class AddTest extends WebTestCase
{
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use AllowedDomainValidator;
    use ResponseEvaluateHelper;
    use EvaluateHelper;

    /** @return Generator<string, array<int|string|bool|array<string, string>|null>> */
    public static function providerRateLimitContentDispositionOriginHeaders(): Generator
    {
        $data = ['abc' => 'def', 'ghi' => '666'];

        yield 'all set 1' => [true, false, $data];
        yield 'all set 2' => [false, false, $data];
        yield 'all set 3' => [false, true, $data];
        yield 'some set 1' => [null, true, $data];
        yield 'some set 2' => [true, null, null];
        yield 'none set 1' => [null, null, null];
        yield 'none set 2' => [null, null, []];
        yield 'set with number 1' => [0, 1, $data];
        yield 'set with number 2' => [1, 0, $data];
        yield 'set with number 3' => ['1', '0', $data];
        yield 'set with number 4' => ['0', '1', $data];
    }

    /** @return Generator<array{string}> */
    public static function providerBadOriginUrl(): Generator
    {
        yield ['baddomain'];
        yield ['bad_domain.com'];
        yield ['-.not.good.com'];
        yield ['not.good-.com'];
        yield ['not.-good.com'];
        yield ['notok-.domain.com'];
        yield ['127.0.0.1'];
        yield ['127.1.2.3'];
    }

    public function testLegacy(): void
    {
        $okDataForNewCdn = $this->prepareDataForNewCdn();
        $okDataForNewCdn['origin_url'] = 'eu-1.cdn77-storage.com';
        $okDataForNewCdn[ResourceAddInfo::FIELD_S3_BUCKET_NAME] = 'buckName';
        $okDataForNewCdn[ResourceAddInfo::FIELD_S3_TYPE] = 'cdn77-ceph-rgw';
        $okDataForNewCdn['aws_access_key_id'] = 'key';
        $okDataForNewCdn['aws_secret'] = 'secret';
        $okDataForNewCdn['aws_region'] = 'region';

        $response = $this->callApiGetResponse($okDataForNewCdn);

        $this->evaluateOkResponse($response, $okDataForNewCdn);

        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        $validOrigins = [
            [
                'clap_origin_id' => null,
                'priority' => 1,
                'url' => [
                    'host' => $okDataForNewCdn['origin_url'],
                    'scheme' => $okDataForNewCdn['origin_scheme'],
                    'basedir' => ValueReplacer::emptyStringToNull($okDataForNewCdn['origin_basedir']),
                    'port' => $okDataForNewCdn['origin_port'],
                ],
                'timeout' => $okDataForNewCdn['origin_timeout'],
                's3' => [
                    'access_key_id' => $okDataForNewCdn['aws_access_key_id'],
                    'secret' => $okDataForNewCdn['aws_secret'],
                    'region' => $okDataForNewCdn['aws_region'],
                    'bucket_name' => $okDataForNewCdn['s3_bucket_name'],
                    'type' => $okDataForNewCdn['s3_type'],
                ],
                'forward_host_header' => $okDataForNewCdn['forward_host_header'] === 1,
                'ssl_verify_disable' => $okDataForNewCdn['ssl_verify_disable'],
                'origin_headers' => $okDataForNewCdn['origin_headers'],
                'follow_redirect' => [
                    'enabled' => $okDataForNewCdn['follow_redirect']['enabled'],
                    'codes' => $okDataForNewCdn['follow_redirect']['codes'],
                ],
            ],
        ];

        $this->evaluateOriginsByResourceId($resourceId, $validOrigins);
    }

    public function testOkWithSingleOrigin(): void
    {
        $okDataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewOrigin = $this->prepareDataForOrigin();

        $response = $this->callApiGetResponse($okDataForNewCdn, [$dataForNewOrigin]);

        $this->evaluateOkResponse(
            $response,
            $this->replaceLegacyOriginValuesWithNewOriginValues($okDataForNewCdn, $dataForNewOrigin),
        );

        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        $this->evaluateOriginsByResourceId($resourceId, [$dataForNewOrigin]);
    }

    public function testBadWithSingleOrigin(): void
    {
        $okDataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewOrigin = $this->prepareDataForOrigin();
        Assert::isArray($dataForNewOrigin['url']);
        $dataForNewOrigin['url']['scheme'] = 'bla';

        $response = $this->callApiGetResponse($okDataForNewCdn, [$dataForNewOrigin]);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'The value you selected is not a valid choice.',
            'url.scheme',
            'origins.origins[0]',
        );
    }

    public function testOkWithMultipleOrigin(): void
    {
        $okDataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewOriginA = $this->prepareDataForOrigin();
        $dataForNewOriginB = $this->prepareDataForOrigin(2);

        $response = $this->callApiGetResponse($okDataForNewCdn, [$dataForNewOriginA, $dataForNewOriginB]);

        $this->evaluateOkResponse(
            $response,
            $this->replaceLegacyOriginValuesWithNewOriginValues($okDataForNewCdn, $dataForNewOriginA),
        );

        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        $this->evaluateOriginsByResourceId($resourceId, [$dataForNewOriginA, $dataForNewOriginB]);
    }

    public function testWithMultipleOriginWhenFirstIsBad(): void
    {
        $okDataForNewCdn = $this->prepareDataForNewCdn();

        $dataForNewOriginA = $this->prepareDataForOrigin();
        Assert::isArray($dataForNewOriginA['url']);
        $dataForNewOriginA['url']['scheme'] = 'bla';

        $dataForNewOriginB = $this->prepareDataForOrigin(2);

        $response = $this->callApiGetResponse($okDataForNewCdn, [$dataForNewOriginA, $dataForNewOriginB]);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'The value you selected is not a valid choice.',
            'url.scheme',
            'origins.origins[0]',
        );
    }

    public function testWithMultipleOriginWhenSecondIsBad(): void
    {
        $okDataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewOriginA = $this->prepareDataForOrigin();
        $dataForNewOriginB = $this->prepareDataForOrigin(2);

        Assert::isArray($dataForNewOriginB['url']);
        $dataForNewOriginB['url']['scheme'] = 'bla';

        $response = $this->callApiGetResponse($okDataForNewCdn, [$dataForNewOriginA, $dataForNewOriginB]);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'The value you selected is not a valid choice.',
            'url.scheme',
            'origins.origins[1]',
        );
    }

    /** @dataProvider providerBadOriginUrl */
    public function testFailedOriginUrlWithBadDomain(string $badDomain): void
    {
        $dataForNewCdn = $this->prepareDataForNewCdn();
        $dataForNewCdn['origin_url'] = $badDomain;

        $response = $this->callApiGetResponse($dataForNewCdn);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            $badDomain . ' is not a valid origin url or IP.',
            'origin_url',
        );
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->setUpNotifyResourceChangeHelper();
    }

    /**
     * @param array<string, mixed> $data
     * @param array<int, array<string, mixed>> $origins
     */
    private function callApiGetResponse(array $data, array|null $origins = null): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_POST,
            '/cdn_resources.json',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                'cdn_resource' => $data,
                ResourceAddSchema::FIELD_ORIGINS => $origins,
            ]),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }

    /**
     * @param array<string, mixed> $okDataForNewCdn
     * @param array<string, mixed> $originData
     *
     * @return array<string, mixed>
     */
    private function replaceLegacyOriginValuesWithNewOriginValues(array $okDataForNewCdn, array $originData): array
    {
        $okDataForNewCdn['origin_scheme'] = $originData[OriginSchema::FIELD_URL][OriginUrlSchema::FIELD_SCHEME];
        $okDataForNewCdn['origin_url'] = $originData[OriginSchema::FIELD_URL][OriginUrlSchema::FIELD_HOST];
        $okDataForNewCdn['origin_basedir'] = $originData[OriginSchema::FIELD_URL][OriginUrlSchema::FIELD_BASEDIR];
        $okDataForNewCdn['origin_port'] = $originData[OriginSchema::FIELD_URL][OriginUrlSchema::FIELD_PORT];
        $okDataForNewCdn['origin_timeout'] = $originData[OriginSchema::FIELD_TIMEOUT];
        $okDataForNewCdn['forward_host_header'] = $originData[OriginSchema::FIELD_FORWARD_HOST_HEADER];
        $okDataForNewCdn['ssl_verify_disable'] = $originData[OriginSchema::FIELD_SSL_VERIFY_DISABLE];
        $okDataForNewCdn['follow_redirect'] = $originData[OriginSchema::FIELD_FOLLOW_REDIRECT];
        $okDataForNewCdn['aws_access_key_id']
            = $originData[OriginSchema::FIELD_S3][OriginS3Schema::FIELD_ACCESS_KEY_ID];
        $okDataForNewCdn['aws_secret'] = $originData[OriginSchema::FIELD_S3][OriginS3Schema::FIELD_SECRET];
        $okDataForNewCdn['aws_region'] = $originData[OriginSchema::FIELD_S3][OriginS3Schema::FIELD_REGION];
        $okDataForNewCdn['s3_bucket_name'] = $originData[OriginSchema::FIELD_S3][OriginS3Schema::FIELD_BUCKET_NAME];
        $okDataForNewCdn['s3_type'] = $originData[OriginSchema::FIELD_S3][OriginS3Schema::FIELD_TYPE];

        return $okDataForNewCdn;
    }

    /** @param array<string, mixed> $validData */
    private function evaluateOkResponse(ResponseDecoded $response, array $validData): void
    {
        self::assertSame(Response::HTTP_OK, $response->statusCode);

        $validResponse = $this->addDynamicParametersFromResponse(
            $response->decodedContent,
            ['cdn_resource' => $validData],
        );
        $validResponse = $this->fixInputOutputInconsistencies($validResponse);
        self::assertEquals($validResponse, $response->decodedContent);

        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertSingleCreatingMessageInQueue($resourceId);
    }

    /**
     * @param mixed[][] $responseData
     * @param mixed[][] $originalData
     *
     * @return mixed[][]
     */
    private function addDynamicParametersFromResponse(array $responseData, array $originalData): array
    {
        $dynamicParameters = ['cdn_url', 'id', 'cdn_reference', 'created_at', 'updated_at', 'suspended_at'];

        foreach ($dynamicParameters as $parameter) {
            if (! array_key_exists($parameter, $responseData['cdn_resource'])) {
                continue;
            }

            $originalData['cdn_resource'][$parameter] = $responseData['cdn_resource'][$parameter];
        }

        return $originalData;
    }

    /**
     * @param mixed[][] $responseData
     *
     * @return mixed[][]
     */
    private function fixInputOutputInconsistencies(array $responseData): array
    {
        // rename mp4_pseudo_streaming (input) to mp4_pseudo_on (output)
        $this->renameKey($responseData['cdn_resource'], 'mp4_pseudo_streaming', 'mp4_pseudo_on');

        // https_redirect_code is 0 when not set in database
        $responseData['cdn_resource']['https_redirect_code'] =
            (int) $responseData['cdn_resource']['https_redirect_code'];

        $responseData['cdn_resource']['response_headers'] = $this->convertResponseHeadersToArray(
            $responseData['cdn_resource']['response_headers'],
        );

        return $responseData;
    }

    /** @param mixed[] $data */
    private function renameKey(array &$data, string $oldKey, string $newKey): void
    {
        $keys = array_keys($data);

        $position = array_search($oldKey, $keys, true);
        assert($position !== false);

        $keys[$position] = $newKey;

        $data = array_combine($keys, $data);
    }
}
