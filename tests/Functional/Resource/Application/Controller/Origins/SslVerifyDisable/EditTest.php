<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\SslVerifyDisable;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Generators\CertificateDefinitions;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;

final class EditTest extends WebTestCase
{
    use CertificateDefinitions;
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use ResponseEvaluateHelper;
    use TemporaryData;

    /** @return Generator<string, array<bool>> */
    public static function providerOk(): Generator
    {
        yield 'true -> true' => [true, true];
        yield 'true -> false' => [true, false];
        yield 'false -> true' => [false, true];
        yield 'false -> false' => [false, false];
    }

    /** @return Generator<string, array<bool>> */
    public static function providerOkMultipleOrigins(): Generator
    {
        yield 'true -> first true, second false' => [true, true, false];
        yield 'true -> first false, second true' => [true, false, true];
        yield 'false -> both true' => [false, true, true];
    }

    /** @return Generator<string, array<int, string|bool|int|array<mixed>|null>> */
    public static function providerFail(): Generator
    {
        yield 'false -> bad enabled 1' => [false, 'true'];
        yield 'false -> bad enabled 2' => [false, 'false'];
        yield 'false -> bad enabled 3' => [false, 'string'];
        yield 'false -> bad enabled 4' => [false, '1'];
        yield 'false -> bad enabled 5' => [false, '0'];
        yield 'false -> bad enabled 6' => [false, 1];
        yield 'false -> bad enabled 7' => [false, 0];
        yield 'false -> bad enabled 8' => [false, []];
        yield 'false -> bad enabled 9' => [false, null];

        yield 'true -> bad enabled 1' => [true, 'true'];
        yield 'true -> bad enabled 2' => [true, 'false'];
        yield 'true -> bad enabled 3' => [true, 'string'];
        yield 'true -> bad enabled 4' => [true, '1'];
        yield 'true -> bad enabled 5' => [true, '0'];
        yield 'true -> bad enabled 6' => [true, 1];
        yield 'true -> bad enabled 7' => [true, 0];
        yield 'true -> bad enabled 8' => [true, []];
        yield 'true -> bad enabled 9' => [true, null];
    }

    /** @return Generator<string, array<bool|string>> */
    public static function providerFailMultipleOrigins(): Generator
    {
        yield 'false -> bad first, ok second' => [false, 'true', true];
        yield 'false -> ok first, bad second' => [false, true, 'false'];
        yield 'true -> bad first, ok second' => [true, 'true', false];
        yield 'true -> ok first, bad second' => [true, false, 'false'];
    }

    /** @dataProvider providerOk */
    public function testOk(bool $sslVerifyDisableForPreparation, bool $sslVerifyDisableForRequest): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setSslVerifyDisable($sslVerifyDisableForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $originData = $this->prepareRequestData($resource->getMainOrigin(), $sslVerifyDisableForRequest);
        $response = $this->callApiGetResponse($resource, [$originData]);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertUpdatingMessageInQueue($resource->getId());

        $this->assertSslVerifyDisable($resource->getId(), 1, $sslVerifyDisableForRequest);
    }

    /** @dataProvider providerOkMultipleOrigins */
    public function testOkMultipleOrigins(
        bool $sslVerifyDisableForPreparation,
        bool $sslVerifyDisableForRequestA,
        bool $sslVerifyDisableForRequestB,
    ): void {
        $resource = $this->createResourceWithTwoOrigins();
        $resource->getMainOrigin()->setSslVerifyDisable($sslVerifyDisableForPreparation);
        $resource->getSecondOrigin()->setSslVerifyDisable($sslVerifyDisableForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $originDataA = $this->prepareRequestData($resource->getMainOrigin(), $sslVerifyDisableForRequestA);
        $originDataB = $this->prepareRequestData($resource->getSecondOrigin(), $sslVerifyDisableForRequestB);
        $response = $this->callApiGetResponse($resource, [$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertUpdatingMessageInQueue($resource->getId());

        $this->assertSslVerifyDisable($resource->getId(), 1, $sslVerifyDisableForRequestA);
        $this->assertSslVerifyDisable($resource->getId(), 2, $sslVerifyDisableForRequestB);
    }

    /**
     * @param mixed $sslVerifyDisableForRequest
     *
     * @dataProvider providerFail
     */
    public function testFail(bool $sslVerifyDisableForPreparation, $sslVerifyDisableForRequest): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setSslVerifyDisable($sslVerifyDisableForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $originData = $this->prepareRequestData($resource->getMainOrigin(), $sslVerifyDisableForRequest);
        $response = $this->callApiGetResponse($resource, [$originData]);

        $this->assertUnprocessableEntityResponse($response);

        $this->assertSslVerifyDisable($resource->getId(), 1, $sslVerifyDisableForPreparation);
    }

    /**
     * @param mixed $sslVerifyDisableForRequestA
     * @param mixed $sslVerifyDisableForRequestB
     *
     * @dataProvider providerFailMultipleOrigins
     */
    public function testFailMultipleOrigins(
        bool $sslVerifyDisableForPreparation,
        $sslVerifyDisableForRequestA,
        $sslVerifyDisableForRequestB,
    ): void {
        $resource = $this->createResourceWithTwoOrigins();
        $resource->getMainOrigin()->setSslVerifyDisable($sslVerifyDisableForPreparation);
        $resource->getSecondOrigin()->setSslVerifyDisable($sslVerifyDisableForPreparation);

        FlushAndClear::do($this->getEntityManager());

        $originDataA = $this->prepareRequestData($resource->getMainOrigin(), $sslVerifyDisableForRequestA);
        $originDataB = $this->prepareRequestData($resource->getMainOrigin(), $sslVerifyDisableForRequestB);
        $response = $this->callApiGetResponse($resource, [$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);

        $this->assertSslVerifyDisable($resource->getId(), 1, $sslVerifyDisableForPreparation);
        $this->assertSslVerifyDisable($resource->getId(), 2, $sslVerifyDisableForPreparation);
    }

    public function testFailWhenSslVerifyDisableMissing(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->getMainOrigin()->setSslVerifyDisable(true);

        FlushAndClear::do($this->getEntityManager());

        $originData = $this->addRequiredOriginData($resource->getMainOrigin(), []);
        unset($originData[OriginSchema::FIELD_SSL_VERIFY_DISABLE]);

        $response = $this->callApiGetResponse($resource, [$originData]);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'This value have to be strictly a boolean (neither 1, 0, null, string etc. is allowed).',
            'origins[0].ssl_verify_disable',
            'origins',
        );

        $updatedResource = $this->getResource($resource->getId());
        self::assertTrue($updatedResource->getMainOrigin()->hasSslVerifyDisable());
    }

    public function testFailWhenSslVerifyDisableMissingMultipleOrigins(): void
    {
        $resource = $this->createResourceWithTwoOrigins();
        $resource->getMainOrigin()->setSslVerifyDisable(false);
        $resource->getSecondOrigin()->setSslVerifyDisable(true);

        FlushAndClear::do($this->getEntityManager());

        $originDataA = $this->addRequiredOriginData($resource->getMainOrigin(), []);
        $originDataB = $this->addRequiredOriginData($resource->getSecondOrigin(), []);
        unset($originDataB[OriginSchema::FIELD_SSL_VERIFY_DISABLE]);

        $response = $this->callApiGetResponse($resource, [$originDataA, $originDataB]);

        $this->evaluateUnprocessableEntityResponse(
            $response,
            'This value have to be strictly a boolean (neither 1, 0, null, string etc. is allowed).',
            'origins[1].ssl_verify_disable',
            'origins',
        );

        $updatedResource = $this->getResource($resource->getId());

        self::assertFalse($updatedResource->getMainOrigin()->hasSslVerifyDisable());
        self::assertTrue($updatedResource->getSecondOrigin()->hasSslVerifyDisable());
    }

    private function assertUnprocessableEntityResponse(ResponseDecoded $response): void
    {
        $this->evaluateUnprocessableEntityResponse(
            $response,
            'This value have to be strictly a boolean (neither 1, 0, null, string etc. is allowed).',
            'origins[0].ssl_verify_disable',
            'origins',
        );
    }

    private function assertSslVerifyDisable(int $resourceId, int $priority, bool $expecteSslVerifyDisable): void
    {
        $resource = $this->getResource($resourceId);

        $origin = $resource->getOrigins()->get($priority);
        self::assertInstanceOf(ResourceOrigin::class, $origin);

        self::assertSame($expecteSslVerifyDisable, $origin->hasSslVerifyDisable());
    }

    /**
     * @param mixed $sslVerifyDisable
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData(ResourceOrigin $origin, $sslVerifyDisable): array
    {
        $data = $this->addRequiredOriginData($origin, []);

        $data[OriginSchema::FIELD_SSL_VERIFY_DISABLE] = $sslVerifyDisable;

        return $data;
    }

    /** @param array<int, array<string, mixed>> $origins */
    private function callApiGetResponse(CdnResource $resource, array|null $origins = null): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_PATCH,
            '/resource/' . $resource->getId(),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                'cdn_resource' => $this->addRequiredData($resource, []),
                ResourceAddSchema::FIELD_ORIGINS => $origins,
            ]),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
