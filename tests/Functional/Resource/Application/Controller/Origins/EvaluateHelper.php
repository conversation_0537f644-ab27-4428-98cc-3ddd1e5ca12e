<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;

use function count;

trait EvaluateHelper
{
    use EntityGetter;

    /** @param array<int<0, 100>, array<string, mixed>> $validData */
    private function evaluateOrigins(CdnResource $resource, array $validData): void
    {
        self::assertCount(count($validData), $resource->getOrigins());
        self::assertSame($validData, $this->prepareOriginsFromEntity($resource->getOrigins()));
    }

    /** @param array<int<0, 100>, array<string, mixed>> $validData */
    private function evaluateOriginsByResourceId(int $resourceId, array $validData): void
    {
        $resource = $this->getResource($resourceId);

        $this->evaluateOrigins($resource, $validData);
    }
}
