<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Entity\Legacy\Id;

use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\KernelTestCase;
use Mo<PERSON>y\Adapter\Phpunit\MockeryPHPUnitIntegration;

class ResourceIdGeneratorTest extends KernelTestCase
{
    use MockeryPHPUnitIntegration;
    use TemporaryData;

    private const MIN_ID = 1000000000;
    private const MAX_ID = 1999999999;

    public function testIdIsGeneratedForNewEntity(): void
    {
        $entityManager = $this->getEntityManager();

        $resource = $this->createTemporaryResource();

        $entityManager->persist($resource);
        $resource->setDefaultCdnUrl();

        $id = $resource->getId();

        self::assertIsInt($id);
        self::assertTrue($id >= self::MIN_ID && $id <= self::MAX_ID);

        $entityManager->flush();

        self::assertSame($id, $resource->getId());
    }
}
