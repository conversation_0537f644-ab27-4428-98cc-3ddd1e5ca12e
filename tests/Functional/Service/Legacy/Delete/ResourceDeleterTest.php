<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Service\Legacy\Delete;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Resource\Domain\Finder\ResourcesForPermanentRemoveFinder;
use Cdn77\NxgApi\Service\Legacy\Delete\ResourceDeleter;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use DateTimeImmutable;

final class ResourceDeleterTest extends WebTestCase
{
    use TemporaryData;

    public function testOk(): void
    {
        $this->createTemporaryResource();
        $this->createResourceWithSecureTokenForRemove();
        $this->createResourceWithSecureTokenForRemove();

        FlushAndClear::do($this->getEntityManager());

        $finder = self::getContainer()->get(ResourcesForPermanentRemoveFinder::class);
        $deleter = self::getContainer()->get(ResourceDeleter::class);

        self::assertInstanceOf(ResourcesForPermanentRemoveFinder::class, $finder);
        self::assertInstanceOf(ResourceDeleter::class, $deleter);

        $ids = $finder->findIds(10);
        self::assertCount(2, $ids);

        foreach ($ids as $id) {
            $deleter->remove($id);
        }

        self::assertCount(0, $finder->findIds(10));
        self::assertCount(1, $this->getEntityManager()->getRepository(CdnResource::class)->findAll());
    }

    private function createResourceWithSecureTokenForRemove(): void
    {
        $resource = $this->createTemporaryResource([
            'deleted' => new DateTimeImmutable('- 31 days'),
        ]);
        $this->enableResourceSecureToken($resource);
    }
}
