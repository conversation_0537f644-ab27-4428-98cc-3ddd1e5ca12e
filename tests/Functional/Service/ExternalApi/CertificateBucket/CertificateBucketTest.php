<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Service\ExternalApi\CertificateBucket;

use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\CertificateBucket;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\DefaultNamingStrategy;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\Exception\FileCorrupted;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\Exception\FileWriteFailed;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\KernelTestCase;
use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use DateTimeImmutable;
use League\Flysystem\Filesystem;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\UnableToWriteFile;
use Mockery;

class CertificateBucketTest extends KernelTestCase
{
    use TemporaryData;

    public function testSave(): void
    {
        [$certificatePair, $namingStrategy, $filesystemMock, $sslFile] = $this->prepareData();

        $filesystem = $this->getContainerService(FilesystemOperator::class);

        $certificateBucket = new CertificateBucket($namingStrategy, $filesystem);

        self::assertTrue($certificateBucket->save($sslFile, $certificatePair));
    }

    public function testSaveWrongContent(): void
    {
        $this->expectException(FileCorrupted::class);

        [$certificatePair, $namingStrategy, $filesystemMock, $sslFile] = $this->prepareData();

        $filesystemMock->shouldReceive('write');
        $filesystemMock->shouldReceive('read')->andReturn('wrong_content');

        $certificateBucket = new CertificateBucket($namingStrategy, $filesystemMock);

        $certificateBucket->save($sslFile, $certificatePair);
    }

    public function testSaveWriteProblem(): void
    {
        $this->expectException(FileWriteFailed::class);

        [$certificatePair, $namingStrategy, $filesystemMock, $sslFile] = $this->prepareData();

        $filesystemMock->shouldReceive('write')->andThrow(UnableToWriteFile::class);

        $certificateBucket = new CertificateBucket($namingStrategy, $filesystemMock);
        $certificateBucket->save($sslFile, $certificatePair);
    }

    protected function setUp(): void
    {
        parent::setUp();
    }

    /** @return mixed[] */
    private function prepareData(): array
    {
        $certificatePair = $this->getCertificatePair();

        $resource = $this->createTemporaryResource();
        $ssl = $this->enableResourceSsl($resource);

        $sslFile = $this->addResourceSslFile(
            $resource,
            $ssl,
            1,
            new DateTimeImmutable(),
            ['foo.bar'],
        );

        $namingStrategy = new DefaultNamingStrategy();

        $filesystemMock = Mockery::mock(Filesystem::class);

        return [$certificatePair, $namingStrategy, $filesystemMock, $sslFile];
    }

    private function getCertificatePair(): CertificatePair
    {
        $certificatePairGenerator = new CertificatePairGenerator();
        $pair = $certificatePairGenerator->generateRandomCertificatePair();

        return new CertificatePair($pair->getCertificate(), $pair->getPrivateKey());
    }
}
