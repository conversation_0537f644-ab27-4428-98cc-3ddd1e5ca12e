<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Service\LetsEncrypt;

use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\RequestState;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\LetsEncrypt\Domain\Value\RequestStateReason;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\Resources\Ssl\SslHelper;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\Controller\LetsEncrypt\LetsEncryptHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use DateTimeImmutable;

class RenewalManagerTest extends WebTestCase
{
    use TemporaryData;
    use LetsEncryptHelper;
    use LetsEncryptManagersData;
    use SslHelper;

    public function testEnqueueOldCertificatesForRenewal(): void
    {
        $dateTimeProvider = new DateTimeImmutable();
        $expirationA = $dateTimeProvider->modify('+1 days')->format('Y-m-d\TH:i:sO');
        $expirationB = $dateTimeProvider->modify('+19 days')->format('Y-m-d\TH:i:sO');
        $expirationTooFar = $dateTimeProvider->modify('+100 days')->format('Y-m-d\TH:i:sO');

        $this->createResourceAndSslSet(SslFile::TYPE_CUSTOM, $expirationA);
        $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT, $expirationTooFar);

        [$resourceA] = $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT, $expirationA);
        [$resourceB] = $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT, $expirationB);
        [$resourceC] = $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT, $expirationA);
        [$resourceD] = $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT, $expirationA);
        [$resourceE] = $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT, $expirationA);

        $resourceC->setDeleted(new DateTimeImmutable());
        $resourceD->setCnames([]);
        $this->prepareRequest(
            $this->getEntityManager(),
            $resourceE,
            RequestState::getCanceled(),
            RequestStateReason::CancelledTooOld,
        );

        $this->getEntityManager()->flush();

        $renewalManager = $this->createRenewalManager();

        $pendingRequests = $this->findAllPendingCertificateRequests($this->getEntityManager());
        self::assertCount(0, $pendingRequests);

        $requestAddedToQueue = $renewalManager->enqueueOldCertificates();
        foreach ($requestAddedToQueue as $request) {
            self::assertTrue(RequestState::getPending()->equals($request->getState()));
        }

        $pendingRequests = $this->findAllPendingCertificateRequests($this->getEntityManager());

        self::assertCount(2, $pendingRequests);
        self::assertSame($resourceA, $pendingRequests[0]->getResource());
        self::assertSame($resourceB, $pendingRequests[1]->getResource());

        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find(['resource' => $resourceD]);
        self::assertNull($ssl, 'Certificate should not exist');
    }

    public function testEnqueueOldCertificatesForRenewalWhenTooOldCancelledIsNotTheLastRequest(): void
    {
        $dateTimeProvider = new DateTimeImmutable();
        $expirationA = $dateTimeProvider->modify('+1 days')->format('Y-m-d\TH:i:sO');

        [$resourceA] = $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT, $expirationA);

        $this->prepareRequest(
            $this->getEntityManager(),
            $resourceA,
            RequestState::getCanceled(),
            RequestStateReason::CancelledTooOld,
            new DateTimeImmutable('- 1 year'),
        );
        $this->prepareRequest(
            $this->getEntityManager(),
            $resourceA,
            RequestState::getCompleted(),
            createdAt: new DateTimeImmutable('- 1 month'),
        );

        $this->getEntityManager()->flush();

        $renewalManager = $this->createRenewalManager();

        $pendingRequests = $this->findAllPendingCertificateRequests($this->getEntityManager());
        self::assertCount(0, $pendingRequests);

        $requestAddedToQueue = $renewalManager->enqueueOldCertificates();
        foreach ($requestAddedToQueue as $request) {
            self::assertTrue(RequestState::getPending()->equals($request->getState()));
        }

        $pendingRequests = $this->findAllPendingCertificateRequests($this->getEntityManager());

        self::assertCount(1, $pendingRequests);
        self::assertSame($resourceA, $pendingRequests[0]->getResource());
    }

    public function testEnqueueCertificatesRenewalForSpecificResources(): void
    {
        $dateTimeProvider = new DateTimeImmutable();
        $expirationA = $dateTimeProvider->modify('+1 days')->format('Y-m-d\TH:i:sO');
        $expirationB = $dateTimeProvider->modify('+19 days')->format('Y-m-d\TH:i:sO');
        $expirationTooFar = $dateTimeProvider->modify('+3 months')->format('Y-m-d\TH:i:sO');

        $this->createResourceAndSslSet(SslFile::TYPE_CUSTOM, $expirationA);

        [$resourceA] = $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT, $expirationA);
        [$resourceB] = $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT, $expirationB);
        [$resourceE] = $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT, $expirationTooFar);

        $renewalManager = $this->createRenewalManager();

        $pendingRequests = $this->findAllPendingCertificateRequests($this->getEntityManager());
        self::assertCount(0, $pendingRequests);

        $requestAddedToQueue = $renewalManager->enqueueByResourceIds([$resourceA->getId(), $resourceE->getId()], false);
        foreach ($requestAddedToQueue as $request) {
            self::assertTrue(RequestState::getPending()->equals($request->getState()));
        }

        $pendingRequests = $this->findAllPendingCertificateRequests($this->getEntityManager());

        self::assertCount(2, $pendingRequests);
        $firstRequest = $pendingRequests[0];
        if ($firstRequest->getResource() === $resourceA) {
            self::assertSame($resourceE, $pendingRequests[1]->getResource());
        } else {
            self::assertSame($resourceE, $firstRequest->getResource());
            self::assertSame($resourceA, $pendingRequests[1]->getResource());
        }
    }

    public function testEnqueueCertificatesRenewalForSpecificResourcesAsap(): void
    {
        $dateTimeProvider = new DateTimeImmutable();
        $expirationA = $dateTimeProvider->modify('+1 days')->format('Y-m-d\TH:i:sO');
        $expirationTooFar = $dateTimeProvider->modify('+3 months')->format('Y-m-d\TH:i:sO');

        $this->createResourceAndSslSet(SslFile::TYPE_CUSTOM, $expirationA);

        [$resourceA] = $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT, $expirationA);
        [$resourceB] = $this->createResourceAndSslSet(SslFile::TYPE_LETSENCRYPT, $expirationTooFar);

        $renewalManager = $this->createRenewalManager();

        $pendingRequests = $this->findAllPendingCertificateRequests($this->getEntityManager());
        self::assertCount(0, $pendingRequests);

        $requestAddedToQueue = $renewalManager->enqueueByResourceIds([$resourceB->getId()], true);
        foreach ($requestAddedToQueue as $request) {
            self::assertTrue(RequestState::getPending()->equals($request->getState()));
        }

        $pendingRequests = $this->findAllPendingCertificateRequests($this->getEntityManager());

        self::assertCount(1, $pendingRequests);
        self::assertSame($resourceB, $pendingRequests[0]->getResource());

        $task = $this->getEntityManager()->getRepository(Task::class)->findOneBy(
            ['request' => $pendingRequests[0]->getId()],
        );
        self::assertLessThan(
            $dateTimeProvider->modify('-4 years')->getTimestamp(),
            $task->getRunAt()->getTimestamp(),
        );
    }
}
