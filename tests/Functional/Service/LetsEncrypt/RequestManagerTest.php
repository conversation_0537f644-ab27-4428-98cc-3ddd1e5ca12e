<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Service\LetsEncrypt;

use Cdn77\NxgApi\Entity\LetsEncrypt\Request;
use Cdn77\NxgApi\Entity\LetsEncrypt\RequestState;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\LetsEncrypt\Domain\Value\RequestStateReason;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\Resources\Ssl\SslHelper;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\Controller\LetsEncrypt\LetsEncryptHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use DateTimeImmutable;

class RequestManagerTest extends WebTestCase
{
    use TemporaryData;
    use LetsEncryptHelper;
    use LetsEncryptManagersData;
    use SslHelper;

    public function testCancelAllPendingRequests(): void
    {
        $resourceA = $this->createTemporaryResource();
        $resourceB = $this->createTemporaryResource();

        $requestA = new Request(
            $resourceA,
            $resourceA->getCnames(),
            new DateTimeImmutable(),
            RequestState::getPending(),
        );
        $requestB = new Request(
            $resourceB,
            $resourceB->getCnames(),
            new DateTimeImmutable(),
            RequestState::getPending(),
        );

        $taskA = new Task($requestA, new DateTimeImmutable(), new DateTimeImmutable('- 10 minutes'));
        $taskB = new Task($requestB, new DateTimeImmutable(), new DateTimeImmutable());

        $this->getEntityManager()->persist($requestA);
        $this->getEntityManager()->persist($requestB);
        $this->getEntityManager()->persist($taskA);
        $this->getEntityManager()->persist($taskB);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $taskRepository = $this->getEntityManager()->getRepository(Task::class);
        self::assertCount(2, $taskRepository->findAll());

        $requestManager = $this->createRequestManager();
        $requestManager->cancelAllPendingRequests($resourceA, RequestStateReason::CancelledInstantSslDisabled);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $pendingRequests = $this->findAllPendingCertificateRequests($this->getEntityManager());
        self::assertCount(1, $pendingRequests);

        $requestRepository = $this->getEntityManager()->getRepository(Request::class);
        $requestA = $requestRepository->find($requestA->getId());
        $requestB = $requestRepository->find($requestB->getId());
        self::assertSame(RequestState::getCanceled()->getValue(), $requestA->getState()->getValue());
        self::assertSame(RequestState::getPending()->getValue(), $requestB->getState()->getValue());
        self::assertSame($requestB, $pendingRequests[0]);

        self::assertCount(1, $taskRepository->findAll());
    }

    public function testEnqueue(): void
    {
        $request = $this->createRequest();

        $runAt = new DateTimeImmutable();

        $taskRepository = $this->getEntityManager()->getRepository(Task::class);
        $taskRepository->add(new Task($request, new DateTimeImmutable(), $runAt));

        $this->getEntityManager()->flush();

        $task = $taskRepository->findAll();

        self::assertCount(1, $task);
        self::assertSame($runAt, $task[0]->getRunAt());
    }

    public function testClearQueue(): void
    {
        $request = $this->createRequest();

        $taskRepository = $this->getEntityManager()->getRepository(Task::class);
        $taskRepository->add(new Task($request, new DateTimeImmutable(), new DateTimeImmutable()));

        $this->getEntityManager()->flush();

        $taskRepository->removeRequestTask($request);

        self::assertCount(0, $taskRepository->findAll(['request' => $request]));
    }

    public function testCreateAndScheduleRequest(): void
    {
        $resource = $this->createTemporaryResource(
            [
                'cdnUrl' => '123.rsc.cdn77.org',
                'cnames' => ['1.cz', 'abc.org', '111.rsc.cdn77.org'],
            ],
        );

        $requestManager = $this->createRequestManager();
        $runAt = new DateTimeImmutable();

        $request = $requestManager->createAndScheduleRequest($resource, $runAt);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $expectedSortedDomains = [$resource->getCdnUrl(), '1.cz', '111.rsc.cdn77.org', 'abc.org'];

        self::assertSame($resource, $request->getResource());
        self::assertSame(RequestState::getPending()->getValue(), $request->getState()->getValue());
        self::assertSame($expectedSortedDomains, $request->getDomains());

        $tasks = $this->getEntityManager()->getRepository(Task::class)->findAll();
        self::assertCount(1, $tasks);

        $task = $tasks[0];
        self::assertSame($runAt->format('c'), $task->getRunAt()->format('c'));
        self::assertSame($request->getId()->toString(), $task->getRequest()->getId()->toString());
    }
}
