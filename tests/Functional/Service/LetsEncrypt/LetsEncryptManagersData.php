<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Service\LetsEncrypt;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\LetsEncrypt\Request;
use Cdn77\NxgApi\Entity\LetsEncrypt\RequestState;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\LetsEncrypt\Infrastructure\Finder\DoctrineRequestFinder;
use Cdn77\NxgApi\Resource\Domain\Finder\SslFileIndexFinder;
use Cdn77\NxgApi\Resource\Domain\SslFileFactory;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\CertificateBucket;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateManager;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateMetadataParser;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificatePairValidator;
use Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser\DefaultDomainChooser;
use Cdn77\NxgApi\Service\LetsEncrypt\RenewalManager;
use Cdn77\NxgApi\Service\LetsEncrypt\RequestManager;
use DateTimeImmutable;
use Mockery;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Webmozart\Assert\Assert;

trait LetsEncryptManagersData
{
    private function createCertificateManager(): CertificateManager
    {
        $eventDispatcherMock = Mockery::mock(EventDispatcherInterface::class);
        $eventDispatcherMock->shouldReceive('dispatch');

        return new CertificateManager(
            $this->getEntityManager(),
            $eventDispatcherMock,
            $this->getEntityManager()->getRepository(Ssl::class),
            Mockery::mock(CertificateBucket::class),
            Mockery::mock(CertificatePairValidator::class),
            Mockery::mock(CertificateMetadataParser::class),
            new SslFileFactory(Mockery::mock(SslFileIndexFinder::class)),
            Mockery::mock(RequestManager::class),
        );
    }

    private function createRenewalManager(): RenewalManager
    {
        $certificateManager = $this->createCertificateManager();
        $requestManager = $this->createRequestManager();

        $requestFinder = self::$container->get(DoctrineRequestFinder::class);
        Assert::isInstanceOf($requestFinder, DoctrineRequestFinder::class);

        return new RenewalManager(
            $requestFinder,
            $certificateManager,
            $requestManager,
            $this->getEntityManager()->getRepository(CdnResource::class),
            $this->getEntityManager(),
        );
    }

    private function createRequestManager(): RequestManager
    {
        return new RequestManager(
            new DefaultDomainChooser(),
            $this->getEntityManager(),
            $this->getEntityManager()->getRepository(Request::class),
            $this->getEntityManager()->getRepository(Task::class),
        );
    }

    private function createRequest(): Request
    {
        $request = new Request(
            $this->createTemporaryResource(),
            ['foo'],
            new DateTimeImmutable(),
            RequestState::getPending(),
        );

        $this->getEntityManager()->persist($request);

        return $request;
    }
}
