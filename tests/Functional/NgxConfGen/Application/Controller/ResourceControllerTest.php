<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\NgxConfGen\Application\Controller;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceOriginS3;
use Cdn77\NxgApi\GeoProtection\Domain\Value\GeoProtectionType;
use Cdn77\NxgApi\HotlinkProtection\Domain\Value\HotlinkProtectionType;
use Cdn77\NxgApi\IpProtection\Domain\Value\IpProtectionType;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use DateTimeImmutable;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function ksort;
use function sprintf;

class ResourceControllerTest extends WebTestCase
{
    use SortResourceData;
    use TemporaryData;

    /** @return Generator<string, array<string|bool|null>> */
    public static function providerSecureToken(): Generator
    {
        yield 'all set 1' => ['path', 'token', 'expiryParam', 'tokenParam', 'pathlenParam', 'secretParam', true];
        yield 'some set 1' => ['path', 'token', null, null, null, null, false];
        yield 'disabled' => ['none', 'token', null, null, null, null, false];
    }

    /** @return Generator<string, array<bool|array<int>|null>> */
    public static function providerFollowRedirect(): Generator
    {
        yield 'all set 1' => [true, [301]];
        yield 'all set 2' => [true, [301, 302]];
        yield 'only enabled' => [true, []];
        yield 'disabled' => [false, []];
    }

    public function testWithInvalidResource(): void
    {
        $this->client->request(
            Request::METHOD_GET,
            '/ngx-conf-gen/resources/**********',
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NOT_FOUND, $response->getStatusCode());
    }

    public function test(): void
    {
        $resource = $this->createTemporaryResource();
        $geoProtection = $this->createResourceGeoProtection(
            $resource,
            GeoProtectionType::TYPE_BLACKLIST,
            ['CZ', 'GB', 'US'],
        );
        $ipProtection = $this->createResourceIpProtection(
            $resource,
            IpProtectionType::TYPE_WHITELIST,
            ['127.0.0.1/32', '***********/32'],
        );
        $refererProtection = $this->createResourceRefererProtection(
            $resource,
            HotlinkProtectionType::TYPE_BLACKLIST,
            false,
            ['domain.com', 'domain2.com'],
        );

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse($resource);

        self::assertSame(Response::HTTP_OK, $response->statusCode);

        ksort($response->decodedContent['custom_data']);

        $expected = $this->getValidDataFromResourceInNgxFormat(
            $resource,
            $geoProtection,
            $ipProtection,
            $refererProtection,
        );

        $this->sortResourceData($expected);
        $this->sortResourceData($response->decodedContent);

        self::assertEquals($expected, $response->decodedContent);

        $this->validateFirstOriginHasSameValuesAsResource($response);
        $this->validateOriginsOrderedByPriority($response);
    }

    public function testMultipleOrigins(): void
    {
        $resource = $this->createTemporaryResource();
        $originB = $this->createResourceOrigin($resource, 2, 'host.com', [
            'basedir' => 'basedir',
            'port' => 66,
            'timeout' => 66,
            's3' => new ResourceOriginS3('s3AccessKeyId', 's3Secret', 's3Region', 's3BucketName', 's3Type'),
            'forwardHostHeader' => true,
            'sslVerifyDisable' => true,
            'originHeaders' => ['first' => 'A', 'second' => 'B'],
            'followRedirectOrigin' => true,
            'followRedirectCodes' => [302, 303],
        ]);

        $this->getEntityManager()->refresh($resource);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse($resource);

        self::assertSame(Response::HTTP_OK, $response->statusCode);

        ksort($response->decodedContent['custom_data']);

        $expected = $this->getValidDataFromResourceInNgxFormat(
            $resource,
        );

        $this->sortResourceData($expected);
        $this->sortResourceData($response->decodedContent);

        self::assertCount(2, $response->decodedContent['origins']);
        self::assertEquals($expected, $response->decodedContent);

        $this->validateFirstOriginHasSameValuesAsResource($response);
        $this->validateOriginsOrderedByPriority($response);
    }

    public function testMultipleOriginsWithFailoverOriginPropertiesSameAsResource(): void
    {
        $originData = [
            'basedir' => 'basedir',
            'port' => 66,
            'timeout' => 66,
            's3' => new ResourceOriginS3('s3AccessKeyId', 's3Secret', 's3Region', 's3BucketName', 's3Type'),
            'forwardHostHeader' => true,
            'sslVerifyDisable' => true,
            'originHeaders' => ['first' => 'A', 'second' => 'B'],
            'followRedirectOrigin' => true,
            'followRedirectCodes' => [302, 303],
        ];

        $resource = $this->createTemporaryResource([], $originData);

        $originB = $this->createResourceOrigin($resource, 2, 'secondOriginHost.com', $originData);

        $this->getEntityManager()->refresh($resource);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse($resource);

        self::assertSame(Response::HTTP_OK, $response->statusCode);

        ksort($response->decodedContent['custom_data']);

        $expected = $this->getValidDataFromResourceInNgxFormat(
            $resource,
        );

        $this->sortResourceData($expected);
        $this->sortResourceData($response->decodedContent);

        self::assertCount(2, $response->decodedContent['origins']);
        self::assertEquals($expected, $response->decodedContent);

        $this->validateFirstOriginHasSameValuesAsResource($response);
        $this->validateOriginsOrderedByPriority($response);
    }

    public function testIgnoreQueryParams(): void
    {
        $resource = $this->createTemporaryResource();
        $this->createResourceIgnoredParameter($resource, 'foo');
        $this->createResourceIgnoredParameter($resource, 'Bar');

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse($resource);

        $this->validateOkResponse($response, $resource);
    }

    /** @dataProvider providerSecureToken */
    public function testSecureToken(
        string $secureTokenType,
        string $secureTokenValue,
        string|null $secureLinkExpiryParam,
        string|null $secureLinkTokenParam,
        string|null $secureLinkPathlenParam,
        string|null $secureLinkSecretParam,
        bool $secureLinkRewritePlaylist,
    ): void {
        $resource = $this->createTemporaryResource();

        if ($secureTokenType !== 'none') {
            $this->enableResourceSecureToken(
                $resource,
                $secureTokenType,
                $secureTokenValue,
                $secureLinkExpiryParam,
                $secureLinkTokenParam,
                $secureLinkPathlenParam,
                $secureLinkSecretParam,
                $secureLinkRewritePlaylist,
            );
        }

        $this->getEntityManager()->refresh($resource);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse($resource);

        $this->validateOkResponse($response, $resource);
    }

    /**
     * @param list<int> $codes
     *
     * @dataProvider providerFollowRedirect
     */
    public function testFollowRedirect(bool $enabled, array|null $codes): void
    {
        $resource = $this->createTemporaryResource(
            [],
            ['followRedirectOrigin' => $enabled, 'followRedirectCodes' => $codes],
        );

        $this->getEntityManager()->refresh($resource);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse($resource);

        $this->validateOkResponse($response, $resource);
    }

    public function testWithSuspendedTooLongResource(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setSuspended(new DateTimeImmutable('- 60 days'));

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse($resource);

        self::assertSame(Response::HTTP_NOT_FOUND, $response->statusCode);
        self::assertSame(
            [
                'errors' => [sprintf('CDN Resource with ID "%d" not found.', $resource->getId())],
            ],
            $response->decodedContent,
        );
    }

    private function validateOkResponse(ResponseDecoded $response, CdnResource $resource): void
    {
        self::assertSame(Response::HTTP_OK, $response->statusCode);

        ksort($response->decodedContent['custom_data']);

        $expected = $this->getValidDataFromResourceInNgxFormat($resource);

        $this->sortResourceData($expected);
        $this->sortResourceData($response->decodedContent);

        self::assertEquals($expected, $response->decodedContent);
    }

    private function validateFirstOriginHasSameValuesAsResource(ResponseDecoded $response): void
    {
        self::assertSame(
            $response->decodedContent['origin_url'],
            $response->decodedContent['origins'][0]['host'],
        );
        self::assertSame(
            $response->decodedContent['origin_scheme'],
            $response->decodedContent['origins'][0]['scheme'],
        );
        self::assertSame(
            $response->decodedContent['origin_basedir'],
            $response->decodedContent['origins'][0]['basedir'],
        );
        self::assertSame(
            $response->decodedContent['origin_port'],
            $response->decodedContent['origins'][0]['port'],
        );
        self::assertSame(
            $response->decodedContent['origin_timeout'],
            $response->decodedContent['origins'][0]['timeout'],
        );
        self::assertSame(
            $response->decodedContent['s3_access_key_id'],
            $response->decodedContent['origins'][0]['s3_access_key_id'],
        );
        self::assertSame(
            $response->decodedContent['s3_secret'],
            $response->decodedContent['origins'][0]['s3_secret'],
        );
        self::assertSame(
            $response->decodedContent['s3_region'],
            $response->decodedContent['origins'][0]['s3_region'],
        );
        self::assertSame(
            $response->decodedContent['s3_bucket_name'],
            $response->decodedContent['origins'][0]['s3_bucket_name'],
        );
        self::assertSame(
            $response->decodedContent['s3_type'],
            $response->decodedContent['origins'][0]['s3_type'],
        );
        self::assertSame(
            $response->decodedContent['ssl_verify_disable'],
            $response->decodedContent['origins'][0]['ssl_verify_disable'],
        );
        self::assertSame(
            $response->decodedContent['forward_host_header'],
            $response->decodedContent['origins'][0]['forward_host_header'],
        );
        self::assertSame(
            $response->decodedContent['origin_headers'],
            $response->decodedContent['origins'][0]['origin_headers'],
        );
        self::assertSame(
            $response->decodedContent['follow_redirect_origin'],
            $response->decodedContent['origins'][0]['follow_redirect_origin'],
        );
        self::assertSame(
            $response->decodedContent['follow_redirect_codes'],
            $response->decodedContent['origins'][0]['follow_redirect_codes'],
        );
    }

    private function validateOriginsOrderedByPriority(ResponseDecoded $response): void
    {
        $index = 1;

        foreach ($response->decodedContent['origins'] as $origin) {
            self::assertEquals($index++, $origin['priority']);
        }
    }

    private function callApiGetResponse(CdnResource $resource): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_GET,
            sprintf('/ngx-conf-gen/resources/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
        );

        $response = $this->client->getResponse();

        return ResponseDecoded::fromResponse($response);
    }
}
