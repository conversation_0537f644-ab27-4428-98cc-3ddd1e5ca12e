<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\NgxConfGen\Application\Controller;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\IgnoredQueryParam;
use Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceIpProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;
use Cdn77\NxgApi\Entity\Legacy\ResourceOriginS3;
use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtection;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Utils\DateFormatter;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use DateTimeImmutable;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function implode;
use function sort;
use function sprintf;

class ResourcesControllerTest extends WebTestCase
{
    use NotifyResourceChangeHelper;
    use ResponseEvaluateHelper;
    use SortResourceData;
    use TemporaryData;

    private const FILTERED_IDS_PARAMETER = 'ids';

    public function testWithNoData(): void
    {
        $response = $this->callApiGetResponse();

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        self::assertSame([], $response->decodedContent);
    }

    public function testWithFilter(): void
    {
        $this->createTemporaryResource();
        $resourceA = $this->createTemporaryResource();
        $resourceB = $this->createTemporaryResource();
        $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $validData = [
            $this->getValidDataFromResourceInNgxFormat($resourceA),
            $this->getValidDataFromResourceInNgxFormat($resourceB),
        ];

        $response = $this->callApiGetResponse(
            [self::FILTERED_IDS_PARAMETER => $resourceA->getId() . ',' . $resourceB->getId()],
        );

        $this->sortResourcesById($validData, $response->decodedContent);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        self::assertEquals($validData, $response->decodedContent);
    }

    public function testSuspended(): void
    {
        $resourceA = $this->createTemporaryResource();
        $resourceB = $this->createTemporaryResource();

        $resourceC = $this->createTemporaryResource();
        $resourceC->setSuspended(new DateTimeImmutable());

        $resourceD = $this->createTemporaryResource();
        $resourceD->setSuspended(new DateTimeImmutable('- 31 days'));

        FlushAndClear::do($this->getEntityManager());

        $validData = [
            $this->getValidDataFromResourceInNgxFormat($resourceA),
            $this->getValidDataFromResourceInNgxFormat($resourceB),
            $this->getValidDataFromResourceInNgxFormat($resourceC),
        ];

        $response = $this->callApiGetResponse();

        $this->sortResourcesById($validData, $response->decodedContent);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        self::assertEquals($validData, $response->decodedContent);
    }

    public function testDeleted(): void
    {
        $resourceA = $this->createTemporaryResource();
        $resourceB = $this->createTemporaryResource();

        $resourceC = $this->createTemporaryResource();
        $resourceC->setDeleted(new DateTimeImmutable());

        FlushAndClear::do($this->getEntityManager());

        $validData = [
            $this->getValidDataFromResourceInNgxFormat($resourceA),
            $this->getValidDataFromResourceInNgxFormat($resourceB),
        ];

        $response = $this->callApiGetResponse();

        $this->sortResourcesById($validData, $response->decodedContent);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        self::assertEquals($validData, $response->decodedContent);
    }

    public function testMissingOrigin(): void
    {
        $this->createTemporaryResource();
        $this->createTemporaryResource();

        $resourceC = $this->createTemporaryResource();
        $this->deleteResourceOrigins($resourceC);

        $resourceD = $this->createTemporaryResource();
        $this->deleteResourceOrigins($resourceD);

        FlushAndClear::do($this->getEntityManager());

        $response = $this->callApiGetResponse();

        $missingIds = [$resourceC->getId(), $resourceD->getId()];
        sort($missingIds);

        $this->evaluateFailedResponse(
            $response,
            [sprintf('Origin for these CDN Resources not found: %s', implode(',', $missingIds))],
            Response::HTTP_NOT_FOUND,
        );
    }

    public function testMissingMainOrigin(): void
    {
        $this->createTemporaryResource();
        $this->createTemporaryResource();

        $resourceC = $this->createTemporaryResource();
        $resourceC->getMainOrigin()->setPriority(66);

        $resourceD = $this->createTemporaryResource();
        $resourceD->getMainOrigin()->setPriority(66);

        FlushAndClear::do($this->getEntityManager());

        $response = $this->callApiGetResponse();

        $missingIds = [$resourceC->getId(), $resourceD->getId()];
        sort($missingIds);

        $this->evaluateFailedResponse(
            $response,
            [sprintf('Origin for these CDN Resources not found: %s', implode(',', $missingIds))],
            Response::HTTP_NOT_FOUND,
        );
    }

    public function testWithWrongFilterParameter(): void
    {
        $response = $this->callApiGetResponse([self::FILTERED_IDS_PARAMETER => '1,2,wrongId']);

        self::assertSame(Response::HTTP_BAD_REQUEST, $response->statusCode);
        self::assertSame(
            ['errors' => ['Invalid ids parameter. It should be integers separated with comma.']],
            $response->decodedContent,
        );
    }

    public function test(): void
    {
        $resourceFoo = $this->createTemporaryResource([], ['host' => 'resource.foo', 'originHeaders' => []]);

        $originHeaders = ['abc' => 'def', 'ghi' => 'jkl'];
        $followRedirectCodes = [301, 302];
        $resourceBar = $this->createTemporaryResource(
            [
                'corsOriginHeader' => true,
                'httpsRedirectCode' => 302,
                'quic' => true,
                'streamingPlaylistBypass' => true,
                'waf' => true,
                'rateLimit' => true,
                'contentDispositionByParam' => true,
                'responseHeaders' => ['Access-Control-Allow-Origin' => 'example.com'],
            ],
            [
                'timeout' => 10,
                'forwardHostHeader' => true,
                'sslVerifyDisable' => true,
                'originHeaders' => $originHeaders,
                'followRedirectOrigin' => true,
                'followRedirectCodes' => $followRedirectCodes,
                's3' => new ResourceOriginS3('ABCDEFGH12345', 's3cr3ts3cr3t', 'eu-west-1', 'bucket', 'external-s3'),
            ],
        );
        $resourceBar->setCnames([]);
        $resourceBar->setCaching($resourceBar->getCaching()->withExpiry(123)->withBypass(true)->withMinUses(5));
        $resourceBar->setSuspended(new DateTimeImmutable());
        $resourceBar->setPurgeAllKey(111);
        $resourceBar->getIgnoredQueryParams()['foo'] = new IgnoredQueryParam($resourceBar, 'foo');
        $resourceBar->getIgnoredQueryParams()['bar'] = new IgnoredQueryParam($resourceBar, 'bar');
        $resourceBar->setCustomData(['test' => true]);
        $this->enableResourceSecureToken($resourceBar);

        $resourceRefererFoo = $this->createTemporaryResource();
        $this->createResourceRefererProtection($resourceRefererFoo, ResourceRefererProtection::TYPE_WHITELIST, true);
        $resourceRefererBar = $this->createTemporaryResource();
        $this->createResourceRefererProtection(
            $resourceRefererBar,
            ResourceRefererProtection::TYPE_BLACKLIST,
            false,
            ['*.foo', 'bar.baz'],
        );

        $this->enableResourceFullLog($resourceBar);
        $resourceBarSsl = $this->enableResourceSsl($resourceBar);

        $resourceIpProtection = $this->createTemporaryResource();
        $this->createResourceIpProtection(
            $resourceIpProtection,
            ResourceIpProtection::TYPE_WHITELIST,
            ['*******/32', '*******/24'],
        );

        $resourceGeoProtection = $this->createTemporaryResource();
        $this->createResourceGeoProtection(
            $resourceGeoProtection,
            ResourceGeoProtection::TYPE_BLACKLIST,
            ['US', 'CN'],
        );

        $resourceCachingOptions = $this->createTemporaryResource();
        $resourceCachingOptions->setCaching(
            $resourceCachingOptions->getCaching()
                ->withLockAge(12)
                ->withLockTimeout(34)
                ->withContentLengthLimit(56)
                ->withMissingContentLengthLimit(78),
        );

        $resourceUpstreamOptions = $this->createTemporaryResource();
        $resourceUpstreamOptions->setUpstreamFailTimeout(10);
        $resourceUpstreamOptions->setUpstreamNextAttempts(2);

        $this->getEntityManager()->refresh($resourceFoo);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->callApiGetResponse();

        self::assertSame(Response::HTTP_OK, $response->statusCode);

        $getResourceStatMapKey = static fn (int $id): int => $id;

        $actual = $this->sortCustomDataForMultipleResources($response->decodedContent);

        $expected = [
            $resourceFoo->getId() => [
                'id' => $resourceFoo->getId(),
                'cdn_url' => $resourceFoo->getCdnUrl(),
                'origin_url' => $resourceFoo->getMainOrigin()->getHost(),
                'origin_scheme' => $resourceFoo->getMainOrigin()->getScheme(),
                'cnames' => $resourceFoo->getCnames(),
                'suspended' => null,
                'disable_query_string' => $resourceFoo->isDisableQueryString(),
                'ignore_set_cookie' => $resourceFoo->isIgnoreSetCookie(),
                'mp4_pseudo_streaming' => $resourceFoo->isMp4PseudoStreaming(),
                'cache_expiry' => $resourceFoo->getCaching()->getExpiry(),
                'cache_expiry_404' => $resourceFoo->getCaching()->getExpiry404(),
                'cache_bypass' => false,
                'secure_token' => '',
                'secure_token_path' => '',
                'purge_all_key' => $resourceFoo->getPurgeAllKey(),
                'cache_stat_key' => $getResourceStatMapKey($resourceFoo->getId()),
                'ssl_file_index' => null,
                'full_logs' => false,
                'ignored_query_params' => null,
                'https_redirect_code' => $resourceFoo->getHttpsRedirectCode(),
                'origin_basedir' => $resourceFoo->getMainOrigin()->getBasedir(),
                'referer_deny_empty' => null,
                'referer_type' => null,
                'referer_domains' => null,
                'origin_port' => null,
                'origin_proxy_ip' => null,
                'origin_proxy_cached' => null,
                'ip_protection_type' => null,
                'ip_protection_addresses' => null,
                'geo_protection_type' => null,
                'geo_protection_countries' => null,
                'secure_token_ua_bypass' => null,
                'origin_timeout' => null,
                'cache_min_uses' => null,
                'forward_host_header' => false,
                'streaming_playlist_bypass' => false,
                'cache_lock_age' => null,
                'cache_lock_timeout' => null,
                'cache_content_length_limit' => null,
                'cache_no_content_length_limit' => null,
                'upstream_fail_timeout' => null,
                'upstream_next_attempts' => null,
                'custom_data' => ['boolean' => true, 'float' => 88.8, 'integer' => 666, 'string' => 'text'],
                'quic' => false,
                'waf' => false,
                'cors_origin_header' => false,
                'cors_timing_enabled' => false,
                'cors_wildcard_enabled' => false,
                'aws_access_key_id' => null,
                's3_access_key_id' => null,
                'aws_secret' => null,
                's3_secret' => null,
                'aws_region' => null,
                's3_region' => null,
                's3_bucket_name' => null,
                's3_type' => null,
                'group_id' => $resourceFoo->getGroup()->getId(),
                'account_id' => $resourceFoo->getAccount()->getId(),
                'rate_limit' => null,
                'content_disposition_by_param' => null,
                'origin_headers' => null,
                'secure_token_type' => null,
                'secure_token_value' => null,
                'secure_link_expiry_param' => null,
                'secure_link_token_param' => null,
                'secure_link_pathlen_param' => null,
                'secure_link_secret_param' => null,
                'secure_link_rewrite_playlist' => false,
                'follow_redirect_origin' => null,
                'follow_redirect_codes' => null,
                'ssl_verify_disable' => false,
                'origins' => $this->prepareOriginsInNxgFormat($resourceFoo),
                'response_headers' => null,
            ],
            $resourceBar->getId() => [
                'id' => $resourceBar->getId(),
                'cdn_url' => $resourceBar->getCdnUrl(),
                'origin_url' => $resourceBar->getMainOrigin()->getHost(),
                'origin_scheme' => $resourceBar->getMainOrigin()->getScheme(),
                'cnames' => $resourceBar->getCnames(),
                'suspended' => $resourceBar->getSuspended() === null
                    ? null
                    : DateFormatter::formatDate($resourceBar->getSuspended()),
                'disable_query_string' => $resourceBar->isDisableQueryString(),
                'ignore_set_cookie' => $resourceBar->isIgnoreSetCookie(),
                'mp4_pseudo_streaming' => $resourceBar->isMp4PseudoStreaming(),
                'cache_expiry' => $resourceBar->getCaching()->getExpiry(),
                'cache_expiry_404' => $resourceBar->getCaching()->getExpiry404(),
                'cache_bypass' => true,
                'secure_token' => 'asdfghjkl',
                'secure_token_path' => '',
                'purge_all_key' => $resourceBar->getPurgeAllKey(),
                'cache_stat_key' => $getResourceStatMapKey($resourceBar->getId()),
                'ssl_file_index' => $resourceBarSsl->getAssignedIndex(),
                'full_logs' => true,
                'ignored_query_params' => [
                    'foo',
                    'bar',
                ],
                'https_redirect_code' => $resourceBar->getHttpsRedirectCode(),
                'origin_basedir' => $resourceBar->getMainOrigin()->getBasedir(),
                'referer_deny_empty' => null,
                'referer_type' => null,
                'referer_domains' => null,
                'origin_port' => null,
                'origin_proxy_ip' => null,
                'origin_proxy_cached' => null,
                'ip_protection_type' => null,
                'ip_protection_addresses' => null,
                'geo_protection_type' => null,
                'geo_protection_countries' => null,
                'secure_token_ua_bypass' => null,
                'origin_timeout' => 10,
                'cache_min_uses' => 5,
                'forward_host_header' => true,
                'streaming_playlist_bypass' => true,
                'cache_lock_age' => null,
                'cache_lock_timeout' => null,
                'cache_content_length_limit' => null,
                'cache_no_content_length_limit' => null,
                'upstream_fail_timeout' => null,
                'upstream_next_attempts' => null,
                'custom_data' => ['test' => true],
                'quic' => true,
                'waf' => true,
                'cors_origin_header' => true,
                'cors_timing_enabled' => false,
                'cors_wildcard_enabled' => false,
                'aws_access_key_id' => 'ABCDEFGH12345',
                's3_access_key_id' => 'ABCDEFGH12345',
                'aws_secret' => 's3cr3ts3cr3t',
                's3_secret' => 's3cr3ts3cr3t',
                'aws_region' => 'eu-west-1',
                's3_region' => 'eu-west-1',
                's3_bucket_name' => 'bucket',
                's3_type' => 'external-s3',
                'group_id' => $resourceBar->getGroup()->getId(),
                'account_id' => $resourceBar->getAccount()->getId(),
                'rate_limit' => true,
                'content_disposition_by_param' => true,
                'origin_headers' => $originHeaders,
                'secure_token_type' => 'highwinds',
                'secure_token_value' => 'asdfghjkl',
                'secure_link_expiry_param' => 'secureLinkExpiryParam',
                'secure_link_token_param' => 'secureLinkTokenParam',
                'secure_link_pathlen_param' => 'secureLinkPathlenParam',
                'secure_link_secret_param' => 'secureLinkSecretParam',
                'secure_link_rewrite_playlist' => false,
                'follow_redirect_origin' => true,
                'follow_redirect_codes' => $followRedirectCodes,
                'ssl_verify_disable' => true,
                'origins' => $this->prepareOriginsInNxgFormat($resourceBar),
                'response_headers' => ['Access-Control-Allow-Origin' => 'example.com'],
            ],
            $resourceRefererFoo->getId() => [
                'id' => $resourceRefererFoo->getId(),
                'cdn_url' => $resourceRefererFoo->getCdnUrl(),
                'origin_url' => $resourceRefererFoo->getMainOrigin()->getHost(),
                'origin_scheme' => $resourceRefererFoo->getMainOrigin()->getScheme(),
                'cnames' => $resourceRefererFoo->getCnames(),
                'suspended' => null,
                'disable_query_string' => $resourceRefererFoo->isDisableQueryString(),
                'ignore_set_cookie' => $resourceRefererFoo->isIgnoreSetCookie(),
                'mp4_pseudo_streaming' => $resourceRefererFoo->isMp4PseudoStreaming(),
                'cache_expiry' => $resourceRefererFoo->getCaching()->getExpiry(),
                'cache_expiry_404' => $resourceRefererFoo->getCaching()->getExpiry404(),
                'cache_bypass' => false,
                'secure_token' => '',
                'secure_token_path' => '',
                'purge_all_key' => $resourceRefererFoo->getPurgeAllKey(),
                'cache_stat_key' => $getResourceStatMapKey($resourceRefererFoo->getId()),
                'ssl_file_index' => null,
                'full_logs' => false,
                'ignored_query_params' => null,
                'https_redirect_code' => $resourceRefererFoo->getHttpsRedirectCode(),
                'origin_basedir' => $resourceRefererFoo->getMainOrigin()->getBasedir(),
                'referer_deny_empty' => true,
                'referer_type' => 'whitelist',
                'referer_domains' => null,
                'origin_port' => null,
                'origin_proxy_ip' => null,
                'origin_proxy_cached' => null,
                'ip_protection_type' => null,
                'ip_protection_addresses' => null,
                'geo_protection_type' => null,
                'geo_protection_countries' => null,
                'secure_token_ua_bypass' => null,
                'origin_timeout' => null,
                'cache_min_uses' => null,
                'forward_host_header' => false,
                'streaming_playlist_bypass' => false,
                'cache_lock_age' => null,
                'cache_lock_timeout' => null,
                'cache_content_length_limit' => null,
                'cache_no_content_length_limit' => null,
                'upstream_fail_timeout' => null,
                'upstream_next_attempts' => null,
                'custom_data' => ['boolean' => true, 'float' => 88.8, 'integer' => 666, 'string' => 'text'],
                'quic' => false,
                'waf' => false,
                'cors_origin_header' => false,
                'cors_timing_enabled' => false,
                'cors_wildcard_enabled' => false,
                'aws_access_key_id' => null,
                's3_access_key_id' => null,
                'aws_secret' => null,
                's3_secret' => null,
                'aws_region' => null,
                's3_region' => null,
                's3_bucket_name' => null,
                's3_type' => null,
                'group_id' => $resourceRefererFoo->getGroup()->getId(),
                'account_id' => $resourceRefererFoo->getAccount()->getId(),
                'rate_limit' => null,
                'content_disposition_by_param' => null,
                'origin_headers' => null,
                'secure_token_type' => null,
                'secure_token_value' => null,
                'secure_link_expiry_param' => null,
                'secure_link_token_param' => null,
                'secure_link_pathlen_param' => null,
                'secure_link_secret_param' => null,
                'secure_link_rewrite_playlist' => false,
                'follow_redirect_origin' => null,
                'follow_redirect_codes' => null,
                'ssl_verify_disable' => false,
                'origins' => $this->prepareOriginsInNxgFormat($resourceRefererFoo),
                'response_headers' => null,
            ],
            $resourceRefererBar->getId() => [
                'id' => $resourceRefererBar->getId(),
                'cdn_url' => $resourceRefererBar->getCdnUrl(),
                'origin_url' => $resourceRefererBar->getMainOrigin()->getHost(),
                'origin_scheme' => $resourceRefererBar->getMainOrigin()->getScheme(),
                'cnames' => $resourceRefererBar->getCnames(),
                'suspended' => null,
                'disable_query_string' => $resourceRefererBar->isDisableQueryString(),
                'ignore_set_cookie' => $resourceRefererBar->isIgnoreSetCookie(),
                'mp4_pseudo_streaming' => $resourceRefererBar->isMp4PseudoStreaming(),
                'cache_expiry' => $resourceRefererBar->getCaching()->getExpiry(),
                'cache_expiry_404' => $resourceRefererBar->getCaching()->getExpiry404(),
                'cache_bypass' => false,
                'secure_token' => '',
                'secure_token_path' => '',
                'purge_all_key' => $resourceRefererBar->getPurgeAllKey(),
                'cache_stat_key' => $getResourceStatMapKey($resourceRefererBar->getId()),
                'ssl_file_index' => null,
                'full_logs' => false,
                'ignored_query_params' => null,
                'https_redirect_code' => $resourceRefererBar->getHttpsRedirectCode(),
                'origin_basedir' => $resourceRefererBar->getMainOrigin()->getBasedir(),
                'referer_deny_empty' => false,
                'referer_type' => 'blacklist',
                'referer_domains' => ['*.foo', 'bar.baz'],
                'origin_port' => null,
                'origin_proxy_ip' => null,
                'origin_proxy_cached' => null,
                'ip_protection_type' => null,
                'ip_protection_addresses' => null,
                'geo_protection_type' => null,
                'geo_protection_countries' => null,
                'secure_token_ua_bypass' => null,
                'origin_timeout' => null,
                'cache_min_uses' => null,
                'forward_host_header' => false,
                'streaming_playlist_bypass' => false,
                'cache_lock_age' => null,
                'cache_lock_timeout' => null,
                'cache_content_length_limit' => null,
                'cache_no_content_length_limit' => null,
                'upstream_fail_timeout' => null,
                'upstream_next_attempts' => null,
                'custom_data' => ['boolean' => true, 'float' => 88.8, 'integer' => 666, 'string' => 'text'],
                'quic' => false,
                'waf' => false,
                'cors_origin_header' => false,
                'cors_timing_enabled' => false,
                'cors_wildcard_enabled' => false,
                'aws_access_key_id' => null,
                's3_access_key_id' => null,
                'aws_secret' => null,
                's3_secret' => null,
                'aws_region' => null,
                's3_region' => null,
                's3_bucket_name' => null,
                's3_type' => null,
                'group_id' => $resourceRefererBar->getGroup()->getId(),
                'account_id' => $resourceRefererBar->getAccount()->getId(),
                'rate_limit' => null,
                'content_disposition_by_param' => null,
                'origin_headers' => null,
                'secure_token_type' => null,
                'secure_token_value' => null,
                'secure_link_expiry_param' => null,
                'secure_link_token_param' => null,
                'secure_link_pathlen_param' => null,
                'secure_link_secret_param' => null,
                'secure_link_rewrite_playlist' => false,
                'follow_redirect_origin' => null,
                'follow_redirect_codes' => null,
                'ssl_verify_disable' => false,
                'origins' => $this->prepareOriginsInNxgFormat($resourceRefererBar),
                'response_headers' => null,
            ],
            $resourceIpProtection->getId() => [
                'id' => $resourceIpProtection->getId(),
                'cdn_url' => $resourceIpProtection->getCdnUrl(),
                'origin_url' => $resourceIpProtection->getMainOrigin()->getHost(),
                'origin_scheme' => $resourceIpProtection->getMainOrigin()->getScheme(),
                'cnames' => $resourceIpProtection->getCnames(),
                'suspended' => null,
                'disable_query_string' => $resourceIpProtection->isDisableQueryString(),
                'ignore_set_cookie' => $resourceIpProtection->isIgnoreSetCookie(),
                'mp4_pseudo_streaming' => $resourceIpProtection->isMp4PseudoStreaming(),
                'cache_expiry' => $resourceIpProtection->getCaching()->getExpiry(),
                'cache_expiry_404' => $resourceIpProtection->getCaching()->getExpiry404(),
                'cache_bypass' => false,
                'secure_token' => '',
                'secure_token_path' => '',
                'purge_all_key' => $resourceIpProtection->getPurgeAllKey(),
                'cache_stat_key' => $getResourceStatMapKey($resourceIpProtection->getId()),
                'ssl_file_index' => null,
                'full_logs' => false,
                'ignored_query_params' => null,
                'https_redirect_code' => $resourceIpProtection->getHttpsRedirectCode(),
                'origin_basedir' => $resourceIpProtection->getMainOrigin()->getBasedir(),
                'referer_deny_empty' => null,
                'referer_type' => null,
                'referer_domains' => null,
                'origin_port' => null,
                'origin_proxy_ip' => null,
                'origin_proxy_cached' => null,
                'ip_protection_type' => 'whitelist',
                'ip_protection_addresses' => ['*******/32', '*******/24'],
                'geo_protection_type' => null,
                'geo_protection_countries' => null,
                'secure_token_ua_bypass' => null,
                'origin_timeout' => null,
                'cache_min_uses' => null,
                'forward_host_header' => false,
                'streaming_playlist_bypass' => false,
                'cache_lock_age' => null,
                'cache_lock_timeout' => null,
                'cache_content_length_limit' => null,
                'cache_no_content_length_limit' => null,
                'upstream_fail_timeout' => null,
                'upstream_next_attempts' => null,
                'custom_data' => ['boolean' => true, 'float' => 88.8, 'integer' => 666, 'string' => 'text'],
                'quic' => false,
                'waf' => false,
                'cors_origin_header' => false,
                'cors_timing_enabled' => false,
                'cors_wildcard_enabled' => false,
                'aws_access_key_id' => null,
                's3_access_key_id' => null,
                'aws_secret' => null,
                's3_secret' => null,
                'aws_region' => null,
                's3_region' => null,
                's3_bucket_name' => null,
                's3_type' => null,
                'group_id' => $resourceIpProtection->getGroup()->getId(),
                'account_id' => $resourceIpProtection->getAccount()->getId(),
                'rate_limit' => null,
                'content_disposition_by_param' => null,
                'origin_headers' => null,
                'secure_token_type' => null,
                'secure_token_value' => null,
                'secure_link_expiry_param' => null,
                'secure_link_token_param' => null,
                'secure_link_pathlen_param' => null,
                'secure_link_secret_param' => null,
                'secure_link_rewrite_playlist' => false,
                'follow_redirect_origin' => null,
                'follow_redirect_codes' => null,
                'ssl_verify_disable' => false,
                'origins' => $this->prepareOriginsInNxgFormat($resourceIpProtection),
                'response_headers' => null,
            ],
            $resourceGeoProtection->getId() => [
                'id' => $resourceGeoProtection->getId(),
                'cdn_url' => $resourceGeoProtection->getCdnUrl(),
                'origin_url' => $resourceGeoProtection->getMainOrigin()->getHost(),
                'origin_scheme' => $resourceGeoProtection->getMainOrigin()->getScheme(),
                'cnames' => $resourceGeoProtection->getCnames(),
                'suspended' => null,
                'disable_query_string' => $resourceGeoProtection->isDisableQueryString(),
                'ignore_set_cookie' => $resourceGeoProtection->isIgnoreSetCookie(),
                'mp4_pseudo_streaming' => $resourceGeoProtection->isMp4PseudoStreaming(),
                'cache_expiry' => $resourceGeoProtection->getCaching()->getExpiry(),
                'cache_expiry_404' => $resourceGeoProtection->getCaching()->getExpiry404(),
                'cache_bypass' => false,
                'secure_token' => '',
                'secure_token_path' => '',
                'purge_all_key' => $resourceGeoProtection->getPurgeAllKey(),
                'cache_stat_key' => $getResourceStatMapKey($resourceGeoProtection->getId()),
                'ssl_file_index' => null,
                'full_logs' => false,
                'ignored_query_params' => null,
                'https_redirect_code' => $resourceGeoProtection->getHttpsRedirectCode(),
                'origin_basedir' => $resourceGeoProtection->getMainOrigin()->getBasedir(),
                'referer_deny_empty' => null,
                'referer_type' => null,
                'referer_domains' => null,
                'origin_port' => null,
                'origin_proxy_ip' => null,
                'origin_proxy_cached' => null,
                'ip_protection_type' => null,
                'ip_protection_addresses' => null,
                'geo_protection_type' => 'blacklist',
                'geo_protection_countries' => ['US', 'CN'],
                'secure_token_ua_bypass' => null,
                'origin_timeout' => null,
                'cache_min_uses' => null,
                'forward_host_header' => false,
                'streaming_playlist_bypass' => false,
                'cache_lock_age' => null,
                'cache_lock_timeout' => null,
                'cache_content_length_limit' => null,
                'cache_no_content_length_limit' => null,
                'upstream_fail_timeout' => null,
                'upstream_next_attempts' => null,
                'custom_data' => ['boolean' => true, 'float' => 88.8, 'integer' => 666, 'string' => 'text'],
                'quic' => false,
                'waf' => false,
                'cors_origin_header' => false,
                'cors_timing_enabled' => false,
                'cors_wildcard_enabled' => false,
                'aws_access_key_id' => null,
                's3_access_key_id' => null,
                'aws_secret' => null,
                's3_secret' => null,
                'aws_region' => null,
                's3_region' => null,
                's3_bucket_name' => null,
                's3_type' => null,
                'group_id' => $resourceGeoProtection->getGroup()->getId(),
                'account_id' => $resourceGeoProtection->getAccount()->getId(),
                'rate_limit' => null,
                'content_disposition_by_param' => null,
                'origin_headers' => null,
                'secure_token_type' => null,
                'secure_token_value' => null,
                'secure_link_expiry_param' => null,
                'secure_link_token_param' => null,
                'secure_link_pathlen_param' => null,
                'secure_link_secret_param' => null,
                'secure_link_rewrite_playlist' => false,
                'follow_redirect_origin' => null,
                'follow_redirect_codes' => null,
                'ssl_verify_disable' => false,
                'origins' => $this->prepareOriginsInNxgFormat($resourceGeoProtection),
                'response_headers' => null,
            ],
            $resourceCachingOptions->getId() => [
                'id' => $resourceCachingOptions->getId(),
                'cdn_url' => $resourceCachingOptions->getCdnUrl(),
                'origin_url' => $resourceCachingOptions->getMainOrigin()->getHost(),
                'origin_scheme' => $resourceCachingOptions->getMainOrigin()->getScheme(),
                'cnames' => $resourceCachingOptions->getCnames(),
                'suspended' => null,
                'disable_query_string' => $resourceCachingOptions->isDisableQueryString(),
                'ignore_set_cookie' => $resourceCachingOptions->isIgnoreSetCookie(),
                'mp4_pseudo_streaming' => $resourceCachingOptions->isMp4PseudoStreaming(),
                'cache_expiry' => $resourceCachingOptions->getCaching()->getExpiry(),
                'cache_expiry_404' => $resourceCachingOptions->getCaching()->getExpiry404(),
                'cache_bypass' => false,
                'secure_token' => '',
                'secure_token_path' => '',
                'purge_all_key' => $resourceCachingOptions->getPurgeAllKey(),
                'cache_stat_key' => $getResourceStatMapKey($resourceCachingOptions->getId()),
                'ssl_file_index' => null,
                'full_logs' => false,
                'ignored_query_params' => null,
                'https_redirect_code' => $resourceCachingOptions->getHttpsRedirectCode(),
                'origin_basedir' => $resourceCachingOptions->getMainOrigin()->getBasedir(),
                'referer_deny_empty' => null,
                'referer_type' => null,
                'referer_domains' => null,
                'origin_port' => null,
                'origin_proxy_ip' => null,
                'origin_proxy_cached' => null,
                'ip_protection_type' => null,
                'ip_protection_addresses' => null,
                'geo_protection_type' => null,
                'geo_protection_countries' => null,
                'secure_token_ua_bypass' => null,
                'origin_timeout' => null,
                'cache_min_uses' => null,
                'forward_host_header' => false,
                'streaming_playlist_bypass' => false,
                'cache_lock_age' => 12,
                'cache_lock_timeout' => 34,
                'cache_content_length_limit' => 56,
                'cache_no_content_length_limit' => 78,
                'upstream_fail_timeout' => null,
                'upstream_next_attempts' => null,
                'custom_data' => ['boolean' => true, 'float' => 88.8, 'integer' => 666, 'string' => 'text'],
                'quic' => false,
                'waf' => false,
                'cors_origin_header' => false,
                'cors_timing_enabled' => false,
                'cors_wildcard_enabled' => false,
                'aws_access_key_id' => null,
                's3_access_key_id' => null,
                'aws_secret' => null,
                's3_secret' => null,
                'aws_region' => null,
                's3_region' => null,
                's3_bucket_name' => null,
                's3_type' => null,
                'group_id' => $resourceCachingOptions->getGroup()->getId(),
                'account_id' => $resourceCachingOptions->getAccount()->getId(),
                'rate_limit' => null,
                'content_disposition_by_param' => null,
                'origin_headers' => null,
                'secure_token_type' => null,
                'secure_token_value' => null,
                'secure_link_expiry_param' => null,
                'secure_link_token_param' => null,
                'secure_link_pathlen_param' => null,
                'secure_link_secret_param' => null,
                'secure_link_rewrite_playlist' => false,
                'follow_redirect_origin' => null,
                'follow_redirect_codes' => null,
                'ssl_verify_disable' => false,
                'origins' => $this->prepareOriginsInNxgFormat($resourceCachingOptions),
                'response_headers' => null,
            ],
            $resourceUpstreamOptions->getId() => [
                'id' => $resourceUpstreamOptions->getId(),
                'cdn_url' => $resourceUpstreamOptions->getCdnUrl(),
                'origin_url' => $resourceUpstreamOptions->getMainOrigin()->getHost(),
                'origin_scheme' => $resourceUpstreamOptions->getMainOrigin()->getScheme(),
                'cnames' => $resourceUpstreamOptions->getCnames(),
                'suspended' => null,
                'disable_query_string' => $resourceUpstreamOptions->isDisableQueryString(),
                'ignore_set_cookie' => $resourceUpstreamOptions->isIgnoreSetCookie(),
                'mp4_pseudo_streaming' => $resourceUpstreamOptions->isMp4PseudoStreaming(),
                'cache_expiry' => $resourceUpstreamOptions->getCaching()->getExpiry(),
                'cache_expiry_404' => $resourceUpstreamOptions->getCaching()->getExpiry404(),
                'cache_bypass' => false,
                'secure_token' => '',
                'secure_token_path' => '',
                'purge_all_key' => $resourceUpstreamOptions->getPurgeAllKey(),
                'cache_stat_key' => $getResourceStatMapKey($resourceUpstreamOptions->getId()),
                'ssl_file_index' => null,
                'full_logs' => false,
                'ignored_query_params' => null,
                'https_redirect_code' => $resourceUpstreamOptions->getHttpsRedirectCode(),
                'origin_basedir' => $resourceUpstreamOptions->getMainOrigin()->getBasedir(),
                'referer_deny_empty' => null,
                'referer_type' => null,
                'referer_domains' => null,
                'origin_port' => null,
                'origin_proxy_ip' => null,
                'origin_proxy_cached' => null,
                'ip_protection_type' => null,
                'ip_protection_addresses' => null,
                'geo_protection_type' => null,
                'geo_protection_countries' => null,
                'secure_token_ua_bypass' => null,
                'origin_timeout' => null,
                'cache_min_uses' => null,
                'forward_host_header' => false,
                'streaming_playlist_bypass' => false,
                'cache_lock_age' => null,
                'cache_lock_timeout' => null,
                'cache_content_length_limit' => null,
                'cache_no_content_length_limit' => null,
                'upstream_fail_timeout' => 10,
                'upstream_next_attempts' => 2,
                'custom_data' => ['boolean' => true, 'float' => 88.8, 'integer' => 666, 'string' => 'text'],
                'quic' => false,
                'waf' => false,
                'cors_origin_header' => false,
                'cors_timing_enabled' => false,
                'cors_wildcard_enabled' => false,
                'aws_access_key_id' => null,
                's3_access_key_id' => null,
                'aws_secret' => null,
                's3_secret' => null,
                'aws_region' => null,
                's3_region' => null,
                's3_bucket_name' => null,
                's3_type' => null,
                'group_id' => $resourceUpstreamOptions->getGroup()->getId(),
                'account_id' => $resourceUpstreamOptions->getAccount()->getId(),
                'rate_limit' => null,
                'content_disposition_by_param' => null,
                'origin_headers' => null,
                'secure_token_type' => null,
                'secure_token_value' => null,
                'secure_link_expiry_param' => null,
                'secure_link_token_param' => null,
                'secure_link_pathlen_param' => null,
                'secure_link_secret_param' => null,
                'secure_link_rewrite_playlist' => false,
                'follow_redirect_origin' => null,
                'follow_redirect_codes' => null,
                'ssl_verify_disable' => false,
                'origins' => $this->prepareOriginsInNxgFormat($resourceUpstreamOptions),
                'response_headers' => null,
            ],
        ];

        $this->sortResourcesById($expected, $actual);

        self::assertEquals($expected, $actual);
    }

    private function deleteResourceOrigins(CdnResource $resource): void
    {
        $this->getEntityManager()->createQueryBuilder()
            ->delete(ResourceOrigin::class, 'ro')
            ->where('ro.resource = :resource')
            ->setParameter('resource', $resource)
            ->getQuery()
            ->execute();

        $resource->getOrigins()->clear();
    }

    /** @param string[] $parameters */
    private function callApiGetResponse(array $parameters = []): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_GET,
            '/ngx-conf-gen/resources',
            $parameters,
            [],
            static::getDefaultHeaders(),
        );

        $response = $this->client->getResponse();

        return ResponseDecoded::fromResponse($response);
    }
}
