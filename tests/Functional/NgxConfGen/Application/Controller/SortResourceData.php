<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\NgxConfGen\Application\Controller;

use function array_key_exists;
use function ksort;
use function usort;

trait SortResourceData
{
    /**
     * @param mixed[] $expected
     * @param mixed[] $actual
     */
    private function sortResourcesById(array &$expected, array &$actual, string $sortBy = 'id'): void
    {
        foreach ([&$expected, &$actual] as &$items) {
            usort($items, static fn (array $a, array $b): int => $a[$sortBy] <=> $b[$sortBy]);

            foreach ($items as &$item) {
                $this->sortResourceData($item);
            }
        }
    }

    /** @param mixed[] $resource */
    private function sortResourceData(array &$resource): void
    {
        static $arrayParameters = [
            'ignored_query_params',
            'referer_domains',
            'ip_protection_addresses',
            'geo_protection_countries',
            'custom_data',
        ];

        // sort all array parameters
        foreach ($arrayParameters as $arrayParameter) {
            if (! array_key_exists($arrayParameter, $resource) || $resource[$arrayParameter] === null) {
                continue;
            }

            ksort($resource[$arrayParameter]);
        }
    }
}
