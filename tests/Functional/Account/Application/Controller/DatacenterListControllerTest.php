<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Account\Application\Controller;

use Cdn77\NxgApi\Account\Application\Payload\AccountDatacenterLocationSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_decode;

final class DatacenterListControllerTest extends WebTestCase
{
    use TemporaryData;

    public function testFindDatacenterLocationsForAccount(): void
    {
        $locationA = $this->createLocation('locA', 'cityA', 'CZ', 'EU');
        $locationB = $this->createLocation('locB', 'cityB', 'CZ', 'EU');
        $locationC = $this->createLocation('locC', 'cityC', 'CZ', 'EU');
        $locationD = $this->createLocation('locD', 'cityD', 'CZ', 'EU');

        $popA = $this->createPop($locationA, true, true, 'ABC-Something');
        $popB = $this->createPop($locationB, true, true, 'EFG-Something');
        $popC = $this->createPop($locationC, true, true, 'HIJ-Something');
        $popD = $this->createPop($locationD, true, true, 'KLM-Something');

        $groupA = $this->createLocationGroup();
        $groupA->getPops()->add($popA);

        $groupB = $this->createLocationGroup();
        $groupB->getPops()->add($popB);

        $groupC = $this->createLocationGroup();
        $groupC->getPops()->add($popC);
        $groupC->getPops()->add($popD);

        $accountA = $this->createAccount();
        $accountB = $this->createAccount();

        $this->createResource($accountA, $groupA);
        $this->createResource($accountB, $groupB);
        $this->createResource($accountB, $groupC);

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_GET,
            '/account/' . $accountB->getId() . '/datacenters',
            [],
            [],
            static::getDefaultHeaders(),
        );
        $response = $this->client->getResponse();

        $expectedResponse = [
            [
                AccountDatacenterLocationSchema::FIELD_CITY => 'cityB',
                AccountDatacenterLocationSchema::FIELD_CITY_CODE => 'EFG',
            ],
            [
                AccountDatacenterLocationSchema::FIELD_CITY => 'cityC',
                AccountDatacenterLocationSchema::FIELD_CITY_CODE => 'HIJ',
            ],
            [
                AccountDatacenterLocationSchema::FIELD_CITY => 'cityD',
                AccountDatacenterLocationSchema::FIELD_CITY_CODE => 'KLM',
            ],
        ];

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame($expectedResponse, json_decode($response->getContent(), true));
    }

    public function testNoLocationsForAccount(): void
    {
        $this->client->request(
            Request::METHOD_GET,
            '/account/*********/datacenters',
            [],
            [],
            static::getDefaultHeaders(),
        );

        $response = $this->client->getResponse();

        $expectedResponse = [];

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame($expectedResponse, json_decode($response->getContent(), true));
    }
}
