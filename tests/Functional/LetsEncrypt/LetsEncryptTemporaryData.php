<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\LetsEncrypt;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\LetsEncrypt\Request as LetsEncryptRequest;
use Cdn77\NxgApi\Entity\LetsEncrypt\RequestState;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use DateTimeImmutable;

trait LetsEncryptTemporaryData
{
    /** @return array<mixed> */
    public function prepareResourcesAndRequests(): array
    {
        $resourceA = $this->createTemporaryResource();
        $resourceB = $this->createTemporaryResource();
        $resourceC = $this->createTemporaryResource();
        $resourceD = $this->createTemporaryResource();

        $requestA = new LetsEncryptRequest(
            $resourceA,
            $resourceA->getCnames(),
            new DateTimeImmutable(),
            RequestState::getPending(),
        );
        $requestB = new LetsEncryptRequest(
            $resourceB,
            $resourceB->getCnames(),
            new DateTimeImmutable(),
            RequestState::getPending(),
        );
        $requestC = new LetsEncryptRequest(
            $resourceC,
            $resourceB->getCnames(),
            new DateTimeImmutable(),
            RequestState::getPending(),
        );
        $requestD = new LetsEncryptRequest(
            $resourceD,
            $resourceB->getCnames(),
            new DateTimeImmutable(),
            RequestState::getCanceled(),
        );
        $taskA = new Task($requestA, new DateTimeImmutable(), new DateTimeImmutable('- 10 minutes'));
        $taskB = new Task($requestB, new DateTimeImmutable(), new DateTimeImmutable());
        $taskC = new Task($requestC, new DateTimeImmutable(), new DateTimeImmutable('+ 20 minutes'));
        $taskD = new Task($requestD, new DateTimeImmutable(), new DateTimeImmutable('- 20 minutes'));

        $this->getEntityManager()->persist($requestA);
        $this->getEntityManager()->persist($requestB);
        $this->getEntityManager()->persist($requestC);
        $this->getEntityManager()->persist($requestD);
        $this->getEntityManager()->persist($taskA);
        $this->getEntityManager()->persist($taskB);
        $this->getEntityManager()->persist($taskC);
        $this->getEntityManager()->persist($taskD);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        return [
            $resourceA,
            $resourceB,
            $resourceC,
            $resourceD,
            $requestA,
            $requestB,
            $requestC,
            $requestD,
        ];
    }

    /**
     * @param array<string, mixed> $resourceExtend
     *
     * @return array{CdnResource, LetsEncryptRequest, Task}
     */
    private function prepareResourceWithRequestAndTask(
        array $resourceExtend = [],
        DateTimeImmutable $runAt = new DateTimeImmutable('- 10 minutes'),
        RequestState|null $requestState = null,
    ): array {
        $resource = $this->createTemporaryResource($resourceExtend);

        $request = new LetsEncryptRequest(
            $resource,
            $resource->getCnames(),
            new DateTimeImmutable(),
            $requestState ?? RequestState::getPending(),
        );

        $task = new Task($request, new DateTimeImmutable(), $runAt);

        $this->getEntityManager()->persist($request);
        $this->getEntityManager()->persist($task);
        FlushAndClear::do($this->getEntityManager());

        return [$resource, $request, $task];
    }
}
