<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\LetsEncrypt\Application\Console;

use Cdn77\NxgApi\Entity\LetsEncrypt\Request as LetsEncryptRequest;
use Cdn77\NxgApi\Entity\LetsEncrypt\RequestState;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\LetsEncrypt\Domain\Value\RequestStateReason;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\KernelTestCase;
use Cdn77\NxgApi\Tests\Functional\LetsEncrypt\LetsEncryptTemporaryData;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use DateTimeImmutable;
use Ramsey\Uuid\UuidInterface;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Tester\CommandTester;

use function sprintf;

class InactiveResourceCancellationCommandTest extends KernelTestCase
{
    use EntityGetter;
    use LetsEncryptTemporaryData;
    use TemporaryData;

    public function testWithInactiveResources(): void
    {
        [$resourceA, , , , $requestA, $requestB, $requestC] = $this->prepareResourcesAndRequests();

        $resourceA = $this->getResource($resourceA->getId());
        $resourceA->setDeleted(new DateTimeImmutable('-5 days'));
        $this->getEntityManager()->persist($resourceA);

        [$resourceE, $requestE, $taskE] = $this->prepareResourceWithRequestAndTask(
            ['suspended' => new DateTimeImmutable('-31 days')],
        );

        [, $requestF] = $this->prepareResourceWithRequestAndTask(
            ['suspended' => new DateTimeImmutable('-32 days')],
            requestState: RequestState::getCompleted(),
        );

        FlushAndClear::do($this->getEntityManager());

        $taskAId = $this->getEntityManager()->getRepository(Task::class)->getRequestTask($requestA)->getId();

        $application = new Application(self::$kernel);
        $command = $application->find('letsencrypt:request:cancel-inactive');
        $commandTester = new CommandTester($command);
        $commandTester->execute([]);
        $commandTester->assertCommandIsSuccessful();

        $output = $commandTester->getDisplay();
        self::assertStringContainsString(
            sprintf('Finished. Cancelled requests for 2 resources: %d,%d', $resourceA->getId(), $resourceE->getId()),
            $output,
        );

        $this->validateRequestCancelled($requestA, $taskAId);
        $this->validateRequestCancelled($this->getRequest($requestE->getId()->toString()), $taskE->getId());

        $this->validateRequestPending($requestB);
        $this->validateRequestPending($requestC);

        self::assertSame(RequestState::getCompleted()->getValue(), $requestF->getState()->getValue());
    }

    public function testLimit(): void
    {
        [$resourceA, $requestA, $taskA] = $this->prepareResourceWithRequestAndTask(
            ['deleted' => new DateTimeImmutable('-5 days')],
            new DateTimeImmutable('- 20 minutes'),
        );

        [$resourceB, $requestB, $taskB] = $this->prepareResourceWithRequestAndTask(
            ['deleted' => new DateTimeImmutable('-4 days')],
            new DateTimeImmutable('- 15 minutes'),
        );

        [$resourceC, $requestC] = $this->prepareResourceWithRequestAndTask(
            ['deleted' => new DateTimeImmutable('-3 days')],
        );

        FlushAndClear::do($this->getEntityManager());

        $application = new Application(self::$kernel);
        $command = $application->find('letsencrypt:request:cancel-inactive');
        $commandTester = new CommandTester($command);
        $commandTester->execute(['--limit' => 2]);
        $commandTester->assertCommandIsSuccessful();

        $output = $commandTester->getDisplay();
        self::assertStringContainsString(
            sprintf('Finished. Cancelled requests for 2 resources: %d,%d', $resourceA->getId(), $resourceB->getId()),
            $output,
        );

        $this->validateRequestCancelled($this->getRequest($requestA->getId()->toString()), $taskA->getId());
        $this->validateRequestCancelled($this->getRequest($requestB->getId()->toString()), $taskB->getId());

        $this->validateRequestPending($requestC);
    }

    private function validateRequestPending(LetsEncryptRequest $request): void
    {
        $request = $this->getRequest($request->getId()->toString());
        self::assertSame(RequestState::getPending()->getValue(), $request->getState()->getValue());
        self::assertNull($request->getStateReason());
        $this->getEntityManager()->getRepository(Task::class)->getRequestTask($request);
    }

    private function validateRequestCancelled(LetsEncryptRequest $request, UuidInterface $taskId): void
    {
        $request = $this->getRequest($request->getId()->toString());
        self::assertSame(RequestState::getCanceled()->getValue(), $request->getState()->getValue());
        self::assertSame(RequestStateReason::CancelledInactiveResource, $request->getStateReason());
        self::assertNull($this->getEntityManager()->getRepository(Task::class)->find($taskId));
    }

    protected function setUp(): void
    {
        parent::setUp();

        self::bootKernel();
    }
}
