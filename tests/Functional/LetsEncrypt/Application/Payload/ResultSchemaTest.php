<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\LetsEncrypt\Application\Payload;

use Cdn77\NxgApi\LetsEncrypt\Application\Payload\ResultSchema;
use Cdn77\NxgApi\Tests\Functional\KernelTestCase;
use J<PERSON>\Serializer\SerializerInterface;

use function assert;
use function base64_encode;

class ResultSchemaTest extends KernelTestCase
{
    public function testDeserialize(): void
    {
        $serializer = self::$container->get(SerializerInterface::class);
        assert($serializer instanceof SerializerInterface);

        $requestId = '00000000-0000-0000-0000-000000000000';
        $certificate = base64_encode(
            <<<'CERTIFICATE'
            -----BEGIN CERTIFICATE-----
            MIIDYDCCAkigAwIBAgIUIf3Z
            -----END CERTIFICATE-----
            CERTIFICATE,
        );
        $privateKey = base64_encode(
            <<<'PRIVATE_KEY'
            -----BEGIN PRIVATE KEY-----
            MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQD
            -----END PRIVATE KEY-----
            PRIVATE_KEY,
        );
        $validationError = 'validationError';
        $error = 'error';

        $schema = $serializer->deserialize(
            <<<JSON
{
  "request_id": "$requestId",
  "certificate": "$certificate",
  "private_key": "$privateKey",
  "validation_error": "$validationError",
  "error": "$error"
}
JSON,
            ResultSchema::class,
            'json',
        );

        self::assertNotNull($schema->requestId);
        self::assertSame($requestId, $schema->requestId->toString());
        self::assertSame($certificate, $schema->certificate);
        self::assertSame($privateKey, $schema->privateKey);
        self::assertSame($validationError, $schema->getDescription());
    }
}
