<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\LetsEncrypt\Application\Controller;

use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\Request;
use Cdn77\NxgApi\Entity\LetsEncrypt\Result;
use Cdn77\NxgApi\Entity\LetsEncrypt\ResultStatus;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\LetsEncrypt\Domain\Value\RequestStateReason;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\Controller\LetsEncrypt\LetsEncryptHelper;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use DateTimeImmutable;
use Symfony\Component\HttpFoundation\Request as HttpRequest;
use Symfony\Component\HttpFoundation\Response;

use function assert;
use function Safe\json_decode;
use function Safe\json_encode;
use function sprintf;

class CreateControllerTest extends WebTestCase
{
    use EntityGetter;
    use LetsEncryptHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;

    private const VALIDATION_ERROR_TEXT = 'Random validation error text';

    public function testSuccessfulResult(): void
    {
        [$resource, $request, $certificatePair] = $this->prepareData($this->getEntityManager());
        self::assertFalse($resource->hasInstantSsl());

        $response = $this->makeRequest(
            $request->getId()->toString(),
            $certificatePair->getCertificate(),
            $certificatePair->getPrivateKey(),
        );

        FlushAndClear::do($this->getEntityManager());

        $resource = $this->getResource($resource->getId());

        self::assertTrue($resource->hasInstantSsl());

        $result = $this->getEntityManager()->getRepository(Result::class)->findOneBy(['request' => $request]);
        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertTrue($result->getRequest()->getState()->isCompleted());

        $tasks = $this->getEntityManager()->getRepository(Task::class)->findBy(['request' => $request]);
        self::assertCount(0, $tasks);

        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resource->getId());
        assert($ssl instanceof Ssl);
        self::assertNotNull($ssl);
        self::assertSame(1, $ssl->getFiles()->count());

        $file = $ssl->getFiles()->first();
        assert($file instanceof SslFile);
        self::assertSame(SslFile::TYPE_LETSENCRYPT, $file->getType());
        self::assertSame($ssl->getAssignedIndex(), 1);
        self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
        self::assertSame(
            $file->getExpiresAt()->getTimestamp(),
            $this->extractCertificateExpiration($certificatePair)->getTimestamp(),
        );
        self::assertSame([], $this->extractCertificateDomains($certificatePair));

        self::assertSingleUpdatingMessageInQueue($resource->getId());
    }

    public function testEnqueuedResultWithValidationError(): void
    {
        [$_, $request] = $this->prepareData($this->getEntityManager());

        $response = $this->makeRequest(
            $request->getId()->toString(),
            null,
            null,
            self::VALIDATION_ERROR_TEXT,
        );

        $result = $this->getEntityManager()->getRepository(Result::class)->findOneBy(['request' => $request]);
        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertTrue($result->getRequest()->getState()->isPending());
        self::assertTrue($result->getStatus()->isValidationError());
        self::assertSame(self::VALIDATION_ERROR_TEXT, $result->getDescription());

        $tasks = $this->getEntityManager()->getRepository(Task::class)->findBy(['request' => $request]);
        $dateTime = new DateTimeImmutable();
        self::assertCount(1, $tasks);

        //+10 minutes is normal time for next 'runAt' so +1 minute for the test to run
        self::assertLessThan($dateTime->modify('+11 minutes')->getTimestamp(), $tasks[0]->getRunAt()->getTimestamp());
    }

    public function testEnqueuedResultWithValidationErrorAndDuplicateTask(): void
    {
        [$_, $request] = $this->prepareData($this->getEntityManager());

        $task = new Task($request, new DateTimeImmutable('- 20 minutes'), new DateTimeImmutable('- 20 minutes'));
        $this->getEntityManager()->persist($task);
        $this->getEntityManager()->flush();

        $tasks = $this->getEntityManager()->getRepository(Task::class)->findBy(['request' => $request]);
        self::assertCount(2, $tasks);

        $response = $this->makeRequest(
            $request->getId()->toString(),
            null,
            null,
            self::VALIDATION_ERROR_TEXT,
        );

        $result = $this->getEntityManager()->getRepository(Result::class)->findOneBy(['request' => $request]);
        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertTrue($result->getRequest()->getState()->isPending());
        self::assertTrue($result->getStatus()->isValidationError());
        self::assertSame(self::VALIDATION_ERROR_TEXT, $result->getDescription());

        $tasks = $this->getEntityManager()->getRepository(Task::class)->findBy(['request' => $request]);
        $dateTime = new DateTimeImmutable();
        self::assertCount(1, $tasks);

        //+10 minutes is normal time for next 'runAt' so +1 minute for the test to run
        self::assertLessThan($dateTime->modify('+11 minutes')->getTimestamp(), $tasks[0]->getRunAt()->getTimestamp());
    }

    public function testEmptyValues(): void
    {
        [$_, $request] = $this->prepareData($this->getEntityManager());

        $this->client->request(
            HttpRequest::METHOD_POST,
            '/letsencrypt/result',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    'request_id' => $request->getId(),
                ],
            ),
        );

        $response = $this->client->getResponse();

        $result = $this->getEntityManager()->getRepository(Result::class)->findOneBy(['request' => $request]);
        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertTrue($result->getRequest()->getState()->isPending());
        self::assertEmpty($result->getDescription());

        $tasks = $this->getEntityManager()->getRepository(Task::class)->findBy(['request' => $request]);
        self::assertCount(1, $tasks);

        $dateTime = new DateTimeImmutable();
        //+10 minutes is normal time for next 'runAt' so +1 minute for the test to run
        self::assertLessThan($dateTime->modify('+11 minutes')->getTimestamp(), $tasks[0]->getRunAt()->getTimestamp());
    }

    public function testRejectsResultForUnknownRequest(): void
    {
        $invalidRequestId = '9094a0be-4c5f-4ac0-a778-fbc57e6244e4';
        $response = $this->makeRequest($invalidRequestId);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => [sprintf('Request "%s" doesn\'t exist', $invalidRequestId)],
            ],
            json_decode($response->getContent(), true),
        );
    }

    public function testRejectsResultForFinishedRequest(): void
    {
        [$_, $request] = $this->prepareData($this->getEntityManager());

        $request->cancel(new DateTimeImmutable(), RequestStateReason::CancelledInstantSslDisabled);
        $this->getEntityManager()->persist($request);

        $response = $this->makeRequest($request->getId()->toString());

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            ['errors' => ['The request is finished and could not be changed.']],
            json_decode($response->getContent(), true),
        );
    }

    public function testEnqueuedResultWithReachedLetsEncryptLimit(): void
    {
        [$_, $request] = $this->prepareData($this->getEntityManager());

        $resultStatus = ResultStatus::getSuccess();

        $this->prepareResult($this->getEntityManager(), $request, new DateTimeImmutable('-6 days'), $resultStatus);
        $this->prepareResult($this->getEntityManager(), $request, new DateTimeImmutable('-3 days'), $resultStatus);
        $this->prepareResult($this->getEntityManager(), $request, new DateTimeImmutable('-2 minutes'), $resultStatus);

        $response = $this->makeRequest(
            $request->getId()->toString(),
            null,
            null,
            self::VALIDATION_ERROR_TEXT,
        );

        $result = $this->getEntityManager()->getRepository(Result::class)->findBy(
            ['request' => $request],
            ['completedAt' => 'DESC'],
        )[0];
        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertTrue($result->getRequest()->getState()->isPending());
        self::assertTrue($result->getStatus()->isValidationError());
        self::assertSame(self::VALIDATION_ERROR_TEXT, $result->getDescription());

        $tasks = $this->getEntityManager()->getRepository(Task::class)->findBy(['request' => $request]);
        $dateTime = new DateTimeImmutable();
        self::assertCount(1, $tasks);

        $actualRunAt = $tasks[0]->getRunAt()->getTimestamp();
        //+1 day is normal time for next 'runAt' with reached LE limit
        self::assertLessThan($dateTime->modify('+1 day +1 minute')->getTimestamp(), $actualRunAt);
        self::assertGreaterThan($dateTime->modify('+23 hours +59 minute')->getTimestamp(), $actualRunAt);
    }

    public function testTooOldRequestCancelled(): void
    {
        [$_, $request] = $this->prepareData($this->getEntityManager(), new DateTimeImmutable('-7 months'));

        $response = $this->makeRequest(
            $request->getId()->toString(),
            null,
            null,
            self::VALIDATION_ERROR_TEXT,
        );
        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $result = $this->getEntityManager()->getRepository(Result::class)->findBy(
            ['request' => $request],
            ['completedAt' => 'DESC'],
        )[0];
        self::assertTrue($result->getRequest()->getState()->isCancelled());
        self::assertSame($result->getRequest()->getStateReason(), RequestStateReason::CancelledTooOld);
        self::assertTrue($result->getStatus()->isValidationError());
        self::assertSame(self::VALIDATION_ERROR_TEXT, $result->getDescription());

        $tasks = $this->getEntityManager()->getRepository(Task::class)->findBy(['request' => $request]);
        self::assertCount(0, $tasks);
    }

    public function testLongTimeSuspendedResourceRequestCancelled(): void
    {
        [$resourceSuspendedForLongTime, $request] = $this->prepareData($this->getEntityManager());
        $resourceSuspendedForLongTime->setSuspended(new DateTimeImmutable('-31 days'));

        $this->runInactiveResourceRequest($request);
    }

    public function testDeletedResourceRequestCancelled(): void
    {
        [$resourceSuspendedForLongTime, $request] = $this->prepareData($this->getEntityManager());
        $resourceSuspendedForLongTime->setDeleted(new DateTimeImmutable('-1 days'));

        $this->runInactiveResourceRequest($request);
    }

    private function runInactiveResourceRequest(Request $request): void
    {
        $response = $this->makeRequest(
            $request->getId()->toString(),
            null,
            null,
            self::VALIDATION_ERROR_TEXT,
        );
        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $result = $this->getEntityManager()->getRepository(Result::class)->findBy(
            ['request' => $request],
            ['completedAt' => 'DESC'],
        )[0];
        self::assertTrue($result->getRequest()->getState()->isCancelled());
        self::assertSame($result->getRequest()->getStateReason(), RequestStateReason::CancelledInactiveResource);

        $tasks = $this->getEntityManager()->getRepository(Task::class)->findBy(['request' => $request]);
        self::assertCount(0, $tasks);
    }
}
