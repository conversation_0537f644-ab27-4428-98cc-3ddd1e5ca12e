<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\LetsEncrypt\Application\Controller;

use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\Controller\LetsEncrypt\LetsEncryptHelper;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\LetsEncrypt\LetsEncryptTemporaryData;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use DateTimeImmutable;
use Symfony\Component\HttpFoundation\Request as HttpRequest;
use Symfony\Component\HttpFoundation\Response;

class ListControllerTest extends WebTestCase
{
    use EntityGetter;
    use TemporaryData;
    use LetsEncryptHelper;
    use LetsEncryptTemporaryData;

    public function testEmptyQueue(): void
    {
        $response = $this->makeRequest();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertSame('', $response->response->getContent());
    }

    public function testWithMultipleTasks(): void
    {
        [
            $resourceA,
            $resourceB,
            $resourceC,
            $resourceD,
            $requestA,
            $requestB,
        ] = $this->prepareResourcesAndRequests();

        $resourceC->setSuspended(new DateTimeImmutable());
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $response = $this->makeRequest();

        $this->validateOkResponse($response, [
            [
                'request_id' => $requestA->getId()->toString(),
                'domains' => $requestA->getDomains(),
            ],
            [
                'request_id' => $requestB->getId()->toString(),
                'domains' => $requestB->getDomains(),
            ],
        ]);
    }

    public function testWithDebugResources(): void
    {
        [
            $resourceA,
            $resourceB,
            $resourceC,
            $resourceD,
            $requestA,
            $requestB,
            $requestC,
        ] = $this->prepareResourcesAndRequests();

        $_ENV['LE_DEBUG_RESOURCES_LIST'] = '[' . $resourceC->getId() . ']';

        $response = $this->makeRequest();

        $this->returnBackEnvSoOtherTestsAreNotAffected();

        $this->validateOkResponse($response, [
            [
                'request_id' => $requestC->getId()->toString(),
                'domains' => $requestC->getDomains(),
            ],
        ]);
    }

    public function testWithDebugResourcesWithWrongResourceId(): void
    {
        $this->prepareResourcesAndRequests();

        $_ENV['LE_DEBUG_RESOURCES_LIST'] = '[123]';

        $response = $this->makeRequest();

        $this->returnBackEnvSoOtherTestsAreNotAffected();

        self::assertSame(Response::HTTP_CONFLICT, $response->statusCode);
        self::assertEquals(
            ['errors' => ['Some debug resources are not in tasks. Try add them with letsencrypt:renew command']],
            $response->decodedContent,
        );

        self::assertCount(3, $this->findAllPendingCertificateRequests($this->getEntityManager()));
    }

    private function makeRequest(): ResponseDecoded
    {
        $this->client->request(
            HttpRequest::METHOD_GET,
            '/letsencrypt/queue',
            [],
            [],
            static::getDefaultHeaders(),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }

    /** @param array<array{request_id: string, domains: array<string>}> $expected */
    private function validateOkResponse(ResponseDecoded $response, array $expected): void
    {
        self::assertSame(Response::HTTP_OK, $response->statusCode);
        self::assertEquals($expected, $response->decodedContent);
    }

    private function returnBackEnvSoOtherTestsAreNotAffected(): void
    {
        $_ENV['LE_DEBUG_RESOURCES_LIST'] = '[]';
    }
}
