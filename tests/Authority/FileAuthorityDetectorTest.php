<?php

declare(strict_types=1);

namespace Cdn77\MonMasterDetector\Tests\Authority;

use Cdn77\MonMasterDetector\Authority\FileAuthorityDetector;
use Cdn77\MonMasterDetector\Exception\MasterDetectionFailedException;
use PHPUnit\Framework\TestCase;
use Vfs\FileSystem;
use Vfs\FileSystemInterface;
use Vfs\Node\File;

use function sleep;
use function time;

class FileAuthorityDetectorTest extends TestCase
{
    private const SCHEME = 'test';

    private FileSystemInterface $fileSystemMock;

    private FileAuthorityDetector $detector;

    public function testMissingIsMasterFile(): void
    {
        $this->expectException(MasterDetectionFailedException::class);

        $this->detector->getState();
    }

    public function testMissingIsMasterEpochFile(): void
    {
        $this->expectException(MasterDetectionFailedException::class);

        $this->fileSystemMock->get('/')->add('is_master', new File('1'));

        $this->detector->getState();
    }

    public function testMasterTooOld(): void
    {
        // equals to 2017-06-09T14:34:00Z
        $epoch = 1497018840;

        $this->createIsMasterFile('1');
        $this->createIsMasterEpochFile((string) $epoch);

        $state = $this->detector->getState();

        $this->assertFalse($state->isMaster());
        $this->assertFalse($state->isForced());
    }

    public function testMasterForced(): void
    {
        $epoch = time();

        $this->createIsMasterFile('0');
        $this->createIsMasterForcedFile('1');
        $this->createIsMasterEpochFile((string) $epoch);

        $state = $this->detector->getState();

        $this->assertTrue($state->isMaster());
        $this->assertTrue($state->isForced());
    }

    public function testMasterForcedWithoutIsMasterFile(): void
    {
        // equals to 2017-06-09T14:34:00Z
        $epoch = time();

        $this->createIsMasterForcedFile('1');

        $state = $this->detector->getState();

        $this->assertTrue($state->isMaster());
        $this->assertTrue($state->isForced());
    }

    public function testNotMaster(): void
    {
        $epoch = time();

        $this->createIsMasterFile('0');
        $this->createIsMasterEpochFile((string) $epoch);

        $state = $this->detector->getState();

        $this->assertFalse($state->isMaster());
        $this->assertFalse($state->isForced());
    }

    /**
     * I am a slow test, boooooooooo.
     */
    public function testMasterThreshold(): void
    {
        $now = time();
        $minuteOffset = $now % 60;
        $epoch = $minuteOffset < 2 ? $now - $minuteOffset - 60 : $now - $minuteOffset;

        $this->createIsMasterFile('1');
        $this->createIsMasterEpochFile((string) $epoch);

        if ($minuteOffset >= 2) {
            sleep(60 - $minuteOffset + 1);
        }

        $state = $this->detector->getState();

        $this->assertTrue($state->isMaster());
        $this->assertFalse($state->isForced());
    }

    protected function setUp(): void
    {
        $this->fileSystemMock = FileSystem::factory(self::SCHEME);
        $this->fileSystemMock->mount();

        $this->detector = new FileAuthorityDetector(self::SCHEME . '://');
    }

    protected function tearDown(): void
    {
        $this->fileSystemMock->unmount();
    }

    private function createIsMasterFile(string $content): void
    {
        $this->fileSystemMock->get('/')->add('is_master', new File($content));
    }

    private function createIsMasterForcedFile(string $content): void
    {
        $this->fileSystemMock->get('/')->add('is_master_forced', new File($content));
    }

    private function createIsMasterEpochFile(string $content): void
    {
        $this->fileSystemMock->get('/')->add('is_master.epoch', new File($content));
    }
}
