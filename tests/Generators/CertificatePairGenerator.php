<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Generators;

use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;

use function Safe\openssl_csr_new;
use function Safe\openssl_csr_sign;
use function Safe\openssl_pkey_export;
use function Safe\openssl_pkey_get_private;
use function Safe\openssl_pkey_new;
use function Safe\openssl_x509_export;

final class CertificatePairGenerator
{
    public function generateRandomPrivateKey(): string
    {
        $privateKeyResource = openssl_pkey_new();
        openssl_pkey_export($privateKeyResource, $privateKey);

        return $privateKey;
    }

    public function generateRandomCertificatePair(int $daysToExpireCertificate = 365): CertificatePair
    {
        $privateKey = $this->generateRandomPrivateKey();

        $privateKeyResource = openssl_pkey_get_private($privateKey);
        $csrResource = openssl_csr_new([], $privateKeyResource);
        $certificateResource = openssl_csr_sign($csrResource, null, $privateKeyResource, $daysToExpireCertificate);
        // @phpstan-ignore argument.type
        openssl_x509_export($certificateResource, $certificate);

        return new CertificatePair($certificate, $privateKey);
    }

    public function generateRandomCertificate(): string
    {
        $certificatePair = $this->generateRandomCertificatePair();

        return $certificatePair->getCertificate();
    }

    public function generateRandomInvalidCertificatePair(): CertificatePair
    {
        $certificate = $this->generateRandomCertificate();
        $privateKey = $this->generateRandomPrivateKey();

        return new CertificatePair($certificate, $privateKey);
    }
}
