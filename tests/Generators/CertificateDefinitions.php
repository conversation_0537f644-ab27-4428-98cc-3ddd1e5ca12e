<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Generators;

use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use DateTime;
use DateTimeImmutable;

trait CertificateDefinitions
{
    /**
     * commonName foo.test, no SAN
     *
     * @return mixed[]
     */
    private function getCertificateWithCommonNameAlternativeNames(): array
    {
        return [
            new CertificatePair(
                '-----BEGIN CERTIFICATE-----
MIIC7jCCAdagAwIBAgIBATANBgkqhkiG9w0BAQsFADATMREwDwYDVQQDEwhmb28u
dGVzdDAeFw0xNjAxMDEwMDAwMDBaFw0yNTEyMzEwMDAwMDBaMBMxETAPBgNVBAMT
CGZvby50ZXN0MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA03GhVOdo
U6J9RT46jesm8HufHtp94502Mn6Ny7QVGmAZBmFIH08DeDsBGt1dYPbVczo8O3xF
/tm09J7L9fR6Ap7/s+gfUo3BqDlIAij92IGL5K6IBmDxq2B24wNtq1tuO/E3aaF9
apDkP9HBLKt2osGEslZs+89hYA4Uh2Gc+wft0Ps/ub+0dWvNguRfGitmWONhcrDr
ca0uWDpd2pNmJ6h4bMJGs2mpZArfGXiLjdVgZhug+Pr1rNwTQsoAT1ITXeH+qL34
vEI6rWPnm2WYW+s+z9NgtiUXciD+sSeDsQluohAyU16TLNa1+AeEaNukbGOQh6uG
2rdaLMhkPVCRPwIDAQABo00wSzAJBgNVHRMEAjAAMAsGA1UdDwQEAwIF4DARBglg
hkgBhvhCAQEEBAMCBkAwHgYJYIZIAYb4QgENBBEWD3hjYSBjZXJ0aWZpY2F0ZTAN
BgkqhkiG9w0BAQsFAAOCAQEAKfaB/S2KinKWGs9GlGbZXuUDcn9rHPW0ffj8bDGO
Ruk915ePKzi1jA4HKY8Gbbcxrs73gWmATPcFMLsucVzGdPFmhdci7HW/8Z8mxGX1
kogvnuiIw7azncNroTo66R3HfgWBu85bejunwXDBFfcGR9v4X2Wgg0gAnY/BCDcW
2YGhVCEqq4yOAaxQAHJRIf6mfsrnNJpWJvyA3nPVHHjO/aD8i8cldwXtbX0ocJDO
+5ccYoA7Q588Joezrp58oSmrUJaeDUSpSTBJoWhjGuWuY9P1dr7cN7oooO0tRo8f
6s5uetemFSVcHomxpw2epO0vhb68Gu0aVI3lvELTVcFAIw==
-----END CERTIFICATE-----',
                '******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
            ),
            ['foo.test'],
            DateTimeImmutable::createFromFormat(DateTime::ISO8601, '2025-12-31T00:00:00+0000'),
        ];
    }

    /**
     * commonName=foo.test, SAN=DNS:bar.test
     *
     * @return mixed[]
     */
    private function getCertificateWithCommonNameAndOneDifferentAlternativeName(): array
    {
        return [
            new CertificatePair(
                '-----BEGIN CERTIFICATE-----
MIICwzCCAaugAwIBAgIBATANBgkqhkiG9w0BAQsFADATMREwDwYDVQQDEwhmb28u
dGVzdDAeFw0xNjAxMDEwMDAwMDBaFw0yNTEyMzEwMDAwMDBaMBMxETAPBgNVBAMT
CGZvby50ZXN0MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA6VAmIDmw
2BMB6Z4Yip9NSs35T1MKiSxb03ck9e322FY5lyvCzQCHgGMjg8xndBG2TLrdAHNw
82MJwZk3e0ndL0BmFhQLUj7ubY9mjL3gTnNz1vDSoGro+PrmSOX2gFmz5K6d6kGZ
f+RZCTo72BRMyJWZX7RrZS+gendfp7SpX8oQJxWYmQIIMwrtBVMKPO7LdRjdz09d
TmPTNbIEMmH7kBUDX/dnHUj1u+YA/owxGU4zH5BAZvkwloR+8KMvYWXjTchop4tg
vBLgx2b1Lke+bzsQMA46Vo/cS2R05Rndrqu3tdQ5/voWjqA2Ws3pBgQmYP5VpO0r
rCIy9xUgP7zDBQIDAQABoyIwIDAJBgNVHRMEAjAAMBMGA1UdEQQMMAqCCGJhci50
ZXN0MA0GCSqGSIb3DQEBCwUAA4IBAQDUHY/CB503w/Wd1HCsCGAIWzl8N7uPcj0l
dzGqrqS13S18uq/V51y2FTwJGZsNUk47rz3K+rjXzUsho1Urf/p9ciO/M//SQqzr
EnnoTdihxitZ/F+CoNRKRiNyfuJOF0weRss6KVVDM9iR9tN4GYraThsu4oqw1jHQ
qdRV3jMj9Ia4+HvZJtnPWB8LsQ6/gC62dpMG/a7fYS/IlThRXnAL0jslAITbeGpx
zcl9YXcJPt0w5kF4HKkxua1PdNo8eB4DzKKkqPl3JJG4Z1OW4zPlkwiXGNxo9R6p
i2BeVevl0eQf6RafeKhw0gdjpRAFcwwaWuCV8wwzoe9eKeIFw7JL
-----END CERTIFICATE-----',
                '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
            ),
            ['bar.test'],
            DateTimeImmutable::createFromFormat(DateTime::ISO8601, '2025-12-31T00:00:00+0000'),
        ];
    }

    /**
     * commonName=foo.test, SAN=DNS:bar.test,email:<EMAIL>,DNS:baz.test
     *
     * @return mixed[]
     */
    private function getCertificateWithCommonNameAndMultipleDifferentAternativeNames(): array
    {
        return [
            new CertificatePair(
                '-----BEGIN CERTIFICATE-----
MIIC2jCCAcKgAwIBAgIBATANBgkqhkiG9w0BAQsFADATMREwDwYDVQQDEwhmb28u
dGVzdDAeFw0xNjAxMDEwMDAwMDBaFw0yNTEyMzEwMDAwMDBaMBMxETAPBgNVBAMT
CGZvby50ZXN0MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2WPZrKfk
XTdLxsxnQ9fXKPwNd/8oBVR1PuxbtFFyQdrsxtIukETCgfSrAi4P7+SJbVm8qZLi
gGHv/tIetwjY8I+JydZVx5wxWNA7x/DxefqeeGHKqggopqHZWWdqFhM81Hzt5JAn
9Uar0Tx6dqNXxvxuT1ldqWwUeYCH6V1nWt6IQaRrkVyRXeRMHj/QpR+IrLnT8Y23
Dzk1I82XY5NftrCNkypSqRtL8wW9UBEgY7nAgVuH+OUvmd8jjWyFBs6+Yc4ewfZl
XkVQDetQC2ZBwcvwRgYS/1C6aG4PLE9p4Pfl7N6f25i5JQMbilPy1X1843Mq9thZ
uALrbfXTShP6UQIDAQABozkwNzAJBgNVHRMEAjAAMCoGA1UdEQQjMCGCCGJhci50
ZXN0gQtmb29AYmFyLmJheoIIYmF6LnRlc3QwDQYJKoZIhvcNAQELBQADggEBACG7
zLopRGguGD6lZnivo3VSHa+Z6H4bNPiwtghkutQG2/7+Zw1qGF0GIY3NDSvpiKgR
Ppr++wKs+qMksvsLxlD0dSJs77OtbufNLFGeidEB/zKhsQujy0MVi2d1rI94Sfsx
Us6pLTRYvtu7UcUxiwFBjeBssNlY5QuRi29uOgi2/qWIEars+Dfk/opDDx6oqWOG
dw1wcTsxgOK44fyFR+/OjPkSTZJOYeJTt24oxUEEFMSprP/MN2L+5wjkTsEvip5Y
GC7QhxsVwZ7buIeLar0g6bIxjPFu21RM+OVM8pYkY2QJ+SFSolAwt/DwVWHg/qEO
kXZ/7nPQfF80S0tTMwg=
-----END CERTIFICATE-----',
                '******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
            ),
            ['bar.test', 'baz.test'],
            DateTimeImmutable::createFromFormat(DateTime::ISO8601, '2025-12-31T00:00:00+0000'),
        ];
    }

    /**
     * commonName=foo.test, SAN=DNS:foo.test,DNS:bar.test
     *
     * @return mixed[]
     */
    private function getCertificateWithCommonNameAndMatchingAlternativeNames(): array
    {
        return [
            new CertificatePair(
                '-----BEGIN CERTIFICATE-----
MIICzTCCAbWgAwIBAgIBATANBgkqhkiG9w0BAQsFADATMREwDwYDVQQDEwhmb28u
dGVzdDAeFw0xNjAxMDEwMDAwMDBaFw0yNTEyMzEwMDAwMDBaMBMxETAPBgNVBAMT
CGZvby50ZXN0MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuM1ES64e
SY/esF5/qnbwZfdaosbg1fgz6f+i77qGLa5jqa/EpjMCN0Y5lQu0XPuSv7Uk9zu7
oJnjgx63ht1itDtGzk7jlpIcHyTENBjOt1/n6wJclH1c1Hif/X2OKoiQZFB2gQWI
T46rKLOwECPj/dnH8pPVuTNS6EhjvaqeP4o6Vktw71cIbGJZVpmt551+ctpHvwg6
vIwcfb9Cr0qUqBcYTWKiEEUyrXPJcYAXQAd1bfnK23Xo5thU6SKUpb8lqXH3Aas+
l24XTUBJJc8PN5we2RXNqF5ZgBoGPOidxDWM1Z2EVbnkYfJ9O6RA+0tGUx8GoTcP
b5w3PPwI2CcOPwIDAQABoywwKjAJBgNVHRMEAjAAMB0GA1UdEQQWMBSCCGZvby50
ZXN0gghiYXIudGVzdDANBgkqhkiG9w0BAQsFAAOCAQEASXwjJXLdMsAQrxA9yHmf
H6/oM8V8xApcKN8IjAoy3Pu2ur7yWlGkFemKTLT8uSGTJMjia7oWOA8UMMqUJkr1
3nmHLZdXvnsQ7PvFVl1fl+EkvYOBmtPnea0gtaQJHzkQPf2xAMrfOt0xnWoc9uE+
VGQQn7OhV7atWs4tRrH3aTrSAooOl5yrS/zqz4l+Uqbzd7gxcMBVy/OF5RGdXX4I
2n5ebEMbR4hHlfJcS30hsoW9JX8IDXFJrqH4L39S8HMx0NVZ0zwPo9mbJFlIaQpp
icPetqI2RYxG+kRkVmK/wfWVO7xzNX3fElbgxk7zOzQs46BvT8pFjITK2vePYcXW
xw==
-----END CERTIFICATE-----',
                '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
            ),
            ['foo.test', 'bar.test'],
            DateTimeImmutable::createFromFormat(DateTime::ISO8601, '2025-12-31T00:00:00+0000'),
        ];
    }

    /**
     * commonName=foo.test, SAN=DNS:foo.test,DNS:bar.test,DNS:baz.test,email:<EMAIL>
     *
     * @return mixed[]
     */
    private function getCertificateWithCommonNameAndMultipleAndNonDnsAlternativeNames(): array
    {
        return [
            new CertificatePair(
                '-----BEGIN CERTIFICATE-----
MIIC5jCCAc6gAwIBAgIBATANBgkqhkiG9w0BAQsFADATMREwDwYDVQQDEwhmb28u
dGVzdDAeFw0xNjAxMDEwMDAwMDBaFw0yNTEyMzEwMDAwMDBaMBMxETAPBgNVBAMT
CGZvby50ZXN0MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAx0VWWjpb
MVqMcjIbHAi8ZNpImYRr9KuwITyECZdhtzsWfPys1Z9BeMEc/DEfhdwa9ex/UH05
xAy34/qM/ef6drZF60vqk2QIpOSLBYiM/U+Mzu8TQ/f//KI2WzXvHLKc5XrZNx2+
rmKwebRvq4s6ocAmoCdSYQnlKOMwQd15x4meE2Z0DDgK6b6IcxOP0yghXHs9EA8I
E/RvGVCIrpvjc+ajxkqYn+JZAqFrdQKK9zS9wqlP65gm4QSD+Sl02RTnHa4tf3c1
JzBvqHFAlNiY/2qEd+EUsGTiezYWIyxfy3emh0goe44fFdSik7yfCKkNLvVHJ/IN
CZnz4saHIUwBlQIDAQABo0UwQzAJBgNVHRMEAjAAMDYGA1UdEQQvMC2CCGZvby50
ZXN0gghiYXIudGVzdIIIYmF6LnRlc3SBDXRlc3RAZm9vLnRlc3QwDQYJKoZIhvcN
AQELBQADggEBAMYzu8aBlDhoIBAoNZIzM0kKRLgAzQ6Quzto/4U5o/4v/TyPFR3/
oABOCMmD/+FB1v/QEC0Y5mxJqqqn2qf8ud78TC96I8admD5Sor0oeLJ8mY5azD9n
gDDK8gOg8NYQEE7PK7qrLFWuQQKMGKio2qlmOlMKjX6YwDNQHFbmSIdBA0gFNuLE
mTg94BSvuapOpW//LZl5+UZj3hkkacGIf5zg5Rs+zIjbo+hTQeJuix6dR8jgEoLV
wOl3lyJ+9l7dvboQ1UhKjHMs7eJi2X79DkTS9XjtTwWPxCWTj0hFX19N8tKhRAOv
wEClusXR0iWCFq+Z24CIFJ/Idn/FS+RDZg0=
-----END CERTIFICATE-----',
                '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
            ),
            ['foo.test', 'bar.test', 'baz.test'],
            DateTimeImmutable::createFromFormat(DateTime::ISO8601, '2025-12-31T00:00:00+0000'),
        ];
    }
}
