<?php

namespace PHPSTORM_META {

    use Cdn77\Flop\Core\Domain\Serializer;
    use Doctrine\ORM\EntityManagerInterface;
    use <PERSON><PERSON>\Serializer\SerializerInterface;
    use Mockery;
    use Symfony\Bundle\FrameworkBundle\Test\TestContainer;
    use Symfony\Component\DependencyInjection\ContainerInterface;

    override(ContainerInterface::get(0), type(0));
    override(EntityManagerInterface::find(0), type(0));
    override(EntityManagerInterface::getReference(0), type(0));
    override(Mockery::mock(0), type(0));
    override(Serializer::deserialize(0), type(1));
    override(SerializerInterface::deserialize(0), type(1));
    override(TestContainer::get(0), type(0));
}
