SET statement_timeout = 0;
SET lock_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET client_min_messages = warning;
CREATE SCHEMA access_protection;
CREATE SCHEMA hotlink_protection;
CREATE SCHEMA letsencrypt;
CREATE SCHEMA pop_redirection;
CREATE SCHEMA status;
SET search_path = public, pg_catalog;
CREATE FUNCTION general_update_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
BEGIN
  NEW.created := OLD.created;
  NEW.updated := now();
  return NEW;
END;
$$;
CREATE FUNCTION generate_resource_id() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
  current_id integer;
  count_live integer;
  count_dead integer;
  current_id_used boolean;
  
  value_min_raw integer;
  value_max_raw integer;
  value_min integer;
  value_max integer;
  divisor integer;
  
  counter integer;
BEGIN

  value_min_raw := 1000000000;
  value_max_raw := 1999999999;
  divisor := 7;
  
  value_min := value_min_raw + divisor - (value_min_raw % divisor);
  value_max := value_max_raw - divisor - (value_max_raw % divisor);
  
  counter := 0;
  
  LOOP
  	counter := counter + 1;
    current_id := NULL;
    exit when counter > 1000;
    
  	current_id := floor(random() * (value_max - value_min + 1)::double precision)::integer + value_min;
    current_id := current_id - (current_id % divisor) + get_master_suffix();
    
    SELECT true INTO current_id_used FROM resource WHERE id = current_id LIMIT 1;
    CONTINUE WHEN NOT current_id_used IS NULL AND current_id_used;
    SELECT true INTO current_id_used FROM used_resource_id WHERE id = current_id LIMIT 1;
    CONTINUE WHEN NOT current_id_used IS NULL AND current_id_used;
    CONTINUE WHEN (current_id < value_min_raw) OR (current_id > value_max_raw);
    EXIT;
  END LOOP;
  RETURN current_id;
END;
$$;
CREATE FUNCTION get_master_suffix() RETURNS integer
    LANGUAGE plpgsql IMMUTABLE
    AS $$
BEGIN
  RETURN 1;
END;
$$;
CREATE FUNCTION get_mastered_id(seq_name character varying) RETURNS bigint
    LANGUAGE plpgsql COST 1
    AS $$
BEGIN
  RETURN nextval(seq_name::regclass) * 10 + get_master_suffix();
END;
$$;
CREATE FUNCTION group_pop_relation_tr() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
BEGIN
    IF (NEW.prefered_order IS NULL) THEN 
        NEW.prefered_order := (SELECT coalesce(max(prefered_order), 0::integer) + 1 as new_order FROM group_pop_relation WHERE group_id = NEW.group_id);
    END IF;
	return NEW;
END;
$$;
CREATE FUNCTION group_pop_relation_tr2() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
BEGIN
	UPDATE group_pop_relation SET prefered_order = prefered_order - 1 WHERE group_id = OLD.group_id AND prefered_order > OLD.prefered_order;
    RETURN NULL;
END;
$$;
CREATE FUNCTION remember_deleted_resource_id() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  locate_id integer;
BEGIN
  locate_id := null;
  select id INTO locate_id FROM used_resource_id WHERE id = OLD.id;
  if locate_id is null then
  	INSERT INTO used_resource_id (id) values (OLD.id);
  end if;
  RETURN NULL;
END;
$$;
CREATE FUNCTION resource_group_id_updated() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
BEGIN
  if (OLD.group_id != NEW.group_id) THEN 
  	NEW.group_id_updated := now();
  END IF;
  return NEW;
END;
$$;
CREATE FUNCTION ts_to_ms(ts timestamp with time zone) RETURNS bigint
    LANGUAGE plpgsql IMMUTABLE STRICT
    AS $$
DECLARE
BEGIN
	RETURN date_part('epoch'::text, date_trunc('minutes'::text, ts))::bigint * 1000::bigint + date_part('milliseconds'::text, ts)::bigint;
END;
$$;
CREATE FUNCTION trg_resource_cnames_unique() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    newCnames text[];
    existingCnames text[];
    cname text;
BEGIN
    newCnames := array_remove(
        regexp_split_to_array(trim(NEW.cnames), '\s+'),
        ''
    );
    IF newCnames IS NULL THEN
        newCnames := '{}';
    END IF;

SELECT
    COALESCE(array_agg(lower(elem)), '{}')
INTO existingCnames
FROM (
         SELECT
             unnest(regexp_split_to_array(r.cnames, '\s+')) AS elem
         FROM public.resource r
         WHERE r.deleted IS NULL
           AND r.id <> COALESCE(NEW.id, -1)
     ) AS sub;

existingCnames := array_remove(existingCnames, '');

FOREACH cname IN ARRAY newCnames LOOP
    IF lower(cname) = ANY(existingCnames) THEN
      RAISE EXCEPTION
        'CNAME "%" have to be unique in all undeleted resources', cname;
    END IF;
END LOOP;

RETURN NEW;
END
$$;
SET search_path = access_protection, pg_catalog;
SET default_tablespace = '';
SET default_with_oids = false;
CREATE TABLE geo_protection (
    id integer NOT NULL,
    resource_id integer NOT NULL,
    type character varying NOT NULL
);
CREATE SEQUENCE geo_protection_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE geo_protection_id_seq OWNED BY geo_protection.id;
CREATE TABLE geo_protection_location (
    id integer NOT NULL,
    geo_protection_id integer NOT NULL,
    country character varying NOT NULL
);
CREATE SEQUENCE geo_protection_location_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE geo_protection_location_id_seq OWNED BY geo_protection_location.id;
CREATE TABLE ip_protection (
    id integer NOT NULL,
    resource_id integer NOT NULL,
    type character varying NOT NULL
);
CREATE TABLE ip_protection_address (
    id integer NOT NULL,
    ip_protection_id integer NOT NULL,
    address cidr NOT NULL
);
CREATE SEQUENCE ip_protection_address_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE ip_protection_address_id_seq OWNED BY ip_protection_address.id;
CREATE SEQUENCE ip_protection_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE ip_protection_id_seq OWNED BY ip_protection.id;
SET search_path = hotlink_protection, pg_catalog;
CREATE TABLE referer (
     id integer NOT NULL,
     resource_id integer NOT NULL,
     type character varying NOT NULL,
     deny_empty boolean NOT NULL
);
CREATE TABLE referer_address (
    id integer NOT NULL,
    referer_id integer NOT NULL,
    domain character varying NOT NULL
);
CREATE SEQUENCE referer_address_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE referer_address_id_seq OWNED BY referer_address.id;
CREATE SEQUENCE referer_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE referer_id_seq OWNED BY referer.id;
SET search_path = letsencrypt, pg_catalog;
CREATE TABLE queue_task (
    id uuid NOT NULL,
    request_id uuid NOT NULL,
    created_at timestamp(0) with time zone NOT NULL,
    run_at timestamp(0) with time zone NOT NULL
);
CREATE TABLE request (
    id uuid NOT NULL,
    created_at timestamp(0) with time zone NOT NULL,
    resource_id integer NOT NULL,
    domains json NOT NULL,
    state character varying(10) DEFAULT 'pending'::character varying NOT NULL,
    state_reason character varying DEFAULT NULL,
    updated_at timestamp(0) with time zone NOT NULL
);
CREATE TABLE result (
    id uuid NOT NULL,
    request_id uuid NOT NULL,
    requested_at timestamp(0) with time zone NOT NULL,
    run_at timestamp(0) with time zone NOT NULL,
    completed_at timestamp(0) with time zone NOT NULL,
    status character varying(20) NOT NULL,
    description character varying
);
SET search_path = pop_redirection, pg_catalog;
CREATE TABLE redirection (
    id integer NOT NULL,
    pop_id integer NOT NULL,
    active boolean NOT NULL,
    use_next_best_pop boolean,
    probability real NOT NULL,
    created_at timestamp(0) with time zone NOT NULL,
    updated_at timestamp(0) with time zone NOT NULL
);
CREATE SEQUENCE redirection_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE redirection_id_seq OWNED BY redirection.id;
CREATE TABLE target (
    id integer NOT NULL,
    redirection_id integer NOT NULL,
    pop_id integer NOT NULL,
    priority integer NOT NULL,
    created_at timestamp(0) with time zone NOT NULL
);
CREATE SEQUENCE target_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE target_id_seq OWNED BY target.id;
SET search_path = public, pg_catalog;
CREATE TABLE account (
    id integer NOT NULL
);
CREATE TABLE custom_location (
    resource_id integer NOT NULL,
    group_id integer NOT NULL
);
CREATE TABLE custom_location_relation (
    resource_id integer NOT NULL,
    pop_id integer NOT NULL
);
CREATE TABLE group_pop_relation(
    group_id integer NOT NULL,
    pop_id integer NOT NULL,
    prefered_order integer
);
CREATE TABLE ip (
    id integer NOT NULL,
    server_id integer NOT NULL,
    ip character varying(15) NOT NULL,
    "primary" boolean DEFAULT false NOT NULL,
    up boolean DEFAULT true NOT NULL,
    disabled boolean DEFAULT false NOT NULL
);
COMMENT ON COLUMN ip."primary" IS 'primary ip of server';
CREATE SEQUENCE ip_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE ip_id_seq OWNED BY ip.id;
CREATE TABLE ipv6 (
    id integer NOT NULL,
    server_id integer NOT NULL,
    ip character varying(45) NOT NULL,
    "primary" boolean DEFAULT false NOT NULL,
    up boolean DEFAULT true NOT NULL,
    disabled boolean DEFAULT false NOT NULL
);
COMMENT ON COLUMN ipv6."primary" IS 'primary ip of server';
CREATE SEQUENCE ipv6_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE ipv6_id_seq OWNED BY ipv6.id;
CREATE TABLE location (
    id character varying(100) NOT NULL,
    city character varying(100) NOT NULL,
    country_code character varying(2) NOT NULL,
    region_code character varying(2),
    longitude real NOT NULL,
    latitude real NOT NULL,
    continent character(2) NOT NULL
);
CREATE TABLE location_group (
    id integer NOT NULL,
    name character varying(100),
    "primary" boolean DEFAULT false NOT NULL,
    allow_backup_pops boolean DEFAULT true NOT NULL
);
CREATE SEQUENCE location_group_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE location_group_id_seq OWNED BY location_group.id;
CREATE TABLE pop (
    id integer NOT NULL,
    location_id character varying(100) NOT NULL,
    use_ip boolean DEFAULT false NOT NULL,
    description text,
    backup boolean DEFAULT false NOT NULL,
    tag character varying(100),
    qat boolean DEFAULT false NOT NULL,
    nodedup boolean DEFAULT false NOT NULL
);
CREATE SEQUENCE pops_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE pops_id_seq OWNED BY pop.id;
CREATE TABLE resource (
    id integer DEFAULT generate_resource_id() NOT NULL,
    cdn_url character varying NOT NULL,
    cnames character varying DEFAULT ''::character varying NOT NULL,
    group_id integer NOT NULL,
    group_id_updated timestamp(0) with time zone DEFAULT now(),
    purge_all_key integer DEFAULT 0 NOT NULL,
    cache_expiry integer,
    cache_expiry_404 integer,
    disable_query_string boolean DEFAULT false NOT NULL,
    mp4_pseudo_streaming boolean DEFAULT false NOT NULL,
    flv_pseudo_streaming boolean DEFAULT false NOT NULL,
    ignore_set_cookie boolean DEFAULT false NOT NULL,
    created timestamp(0) with time zone DEFAULT now() NOT NULL,
    updated timestamp(0) with time zone DEFAULT now() NOT NULL,
    suspended timestamp(0) with time zone,
    instant_ssl boolean DEFAULT false NOT NULL,
    https_redirect_code integer,
    deleted timestamp(0) with time zone DEFAULT NULL::timestamp with time zone,
    account_id integer,
    cache_bypass boolean DEFAULT false,
    cache_min_uses integer,
    streaming_playlist_bypass boolean DEFAULT false NOT NULL,
    cache_lock_age integer,
    cache_lock_timeout integer,
    cache_content_length_limit bigint,
    cache_missing_content_length_limit bigint,
    upstream_fail_timeout integer,
    upstream_next_attempts integer,
    custom_data jsonb,
    quic boolean DEFAULT false NOT NULL,
    waf boolean DEFAULT false NOT NULL,
    cors_origin_header boolean DEFAULT false NOT NULL,
    cors_timing_enabled boolean DEFAULT false NOT NULL,
    cors_wildcard_enabled boolean DEFAULT false NOT NULL,
    rate_limit boolean DEFAULT false NOT NULL,
    content_disposition_by_param boolean DEFAULT false NOT NULL,
    response_headers jsonb,
    CONSTRAINT positive_cache_content_length_limit CHECK ((cache_content_length_limit > 0)),
    CONSTRAINT positive_cache_lock_age CHECK ((cache_lock_age > 0)),
    CONSTRAINT positive_cache_lock_timeout CHECK ((cache_lock_timeout > 0)),
    CONSTRAINT positive_cache_missing_content_length_limit CHECK ((cache_missing_content_length_limit > 0)),
    CONSTRAINT positive_upstream_fail_timeout CHECK ((upstream_fail_timeout > 0)),
    CONSTRAINT positive_upstream_next_attempts CHECK ((upstream_next_attempts > 0))
);
COMMENT ON COLUMN resource.cnames IS 'space separated cnames';
COMMENT ON COLUMN resource.cache_expiry IS 'Cache expiry in minutes';
COMMENT ON COLUMN resource.disable_query_string IS 'True => ignore query part of URL for caching';
CREATE TABLE public.resource_origin (
    id uuid NOT NULL,
    resource_id integer NOT NULL,
    clap_origin_id uuid,
    priority integer NOT NULL,
    scheme character varying NOT NULL,
    host character varying NOT NULL,
    basedir character varying,
    port integer,
    timeout integer,
    s3_access_key_id character varying(64),
    s3_secret character varying(64),
    s3_region character varying(16),
    s3_bucket_name character varying(64),
    s3_type character varying(32),
    forward_host_header boolean DEFAULT false NOT NULL,
    ssl_verify_disable boolean DEFAULT false NOT NULL,
    follow_redirect_origin boolean DEFAULT false NOT NULL,
    follow_redirect_codes jsonb,
    origin_headers jsonb,
    CONSTRAINT resource_origin_resource_id_priority UNIQUE (resource_id, priority),
    CONSTRAINT range_priority CHECK ((priority BETWEEN 1 AND 100))
);
CREATE TABLE resource_full_log (
    resource_id integer NOT NULL
);
CREATE TABLE resource_ignored_query_param (
    id integer NOT NULL,
    resource_id integer NOT NULL,
    name character varying NOT NULL
);
CREATE SEQUENCE resource_ignored_query_param_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE resource_ignored_query_param_id_seq OWNED BY resource_ignored_query_param.id;
CREATE TABLE resource_secure_token (
    resource_id integer NOT NULL,
    secure_token_type character varying(64) NOT NULL,
    secure_token_value character varying(64) NOT NULL,
    secure_link_expiry_param character varying(64) NULL,
    secure_link_token_param character varying(64) NULL,
    secure_link_pathlen_param character varying(64) NULL,
    secure_link_secret_param character varying(64) NULL,
    secure_link_rewrite_playlist boolean NOT NULL DEFAULT false
);
CREATE TABLE server (
    id integer NOT NULL,
    pop_id integer NOT NULL,
    max_bandwidth integer,
    paused boolean DEFAULT false NOT NULL,
    max_cache_size bigint,
    forced_state boolean,
    keys_size bigint DEFAULT 4194304000::bigint NOT NULL,
    uid bigint DEFAULT (0)::bigint NOT NULL,
    worker_count integer DEFAULT 0 NOT NULL,
    drive_count integer DEFAULT 1 NOT NULL
);
COMMENT ON COLUMN server.max_bandwidth IS 'max bandwidth of server in Mbps';
COMMENT ON COLUMN server.keys_size IS 'keys_size in nginx';
COMMENT ON COLUMN server.uid IS 'server unique ID';
CREATE TABLE server_http2 (
    server_id integer NOT NULL
);
CREATE SEQUENCE server_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE server_id_seq OWNED BY server.id;
CREATE TABLE ssl (
    resource_id integer NOT NULL,
    assigned_index integer,
    assigned_at timestamp(0) with time zone,
    requested_index integer,
    requested_at timestamp(0) with time zone
);
CREATE TABLE ssl_file (
    id integer NOT NULL,
    resource_id integer NOT NULL,
    index integer NOT NULL,
    created_at timestamp(0) with time zone DEFAULT now() NOT NULL,
    type character varying(15) DEFAULT 'custom'::character varying NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    domains json NOT NULL
);
CREATE SEQUENCE ssl_file_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE ssl_file_id_seq OWNED BY ssl_file.id;
CREATE TABLE used_resource_id (
    id integer NOT NULL
);
CREATE VIEW v_server_up AS
 SELECT s0.id AS server_id,
    (COALESCE(s0.forced_state, ip0.up, false) AND (NOT s0.paused)) AS up
   FROM (server s0
     LEFT JOIN ip ip0 ON (((ip0.server_id = s0.id) AND ip0."primary")));
CREATE VIEW v_pop_up AS
 SELECT p.id AS pop_id,
    COALESCE(bool_or(sup.up), false) AS up
   FROM ((pop p
     LEFT JOIN server s ON ((s.pop_id = p.id)))
     LEFT JOIN v_server_up sup ON ((s.id = sup.server_id)))
  GROUP BY p.id;
SET search_path = status, pg_catalog;
CREATE TABLE server_current (
    server_id integer NOT NULL,
    last_change_ts timestamp(0) with time zone NOT NULL,
    up boolean NOT NULL,
    reason character varying(6) NOT NULL
);
CREATE TABLE server_last_down (
    server_id integer NOT NULL,
    went_down_ts timestamp(0) with time zone NOT NULL,
    reason character varying(6) NOT NULL
);
SET search_path = access_protection, pg_catalog;
ALTER TABLE ONLY geo_protection ALTER COLUMN id SET DEFAULT nextval('geo_protection_id_seq'::regclass);
ALTER TABLE ONLY geo_protection_location ALTER COLUMN id SET DEFAULT nextval('geo_protection_location_id_seq'::regclass);
ALTER TABLE ONLY ip_protection ALTER COLUMN id SET DEFAULT nextval('ip_protection_id_seq'::regclass);
ALTER TABLE ONLY ip_protection_address ALTER COLUMN id SET DEFAULT nextval('ip_protection_address_id_seq'::regclass);
SET search_path = hotlink_protection, pg_catalog;
ALTER TABLE ONLY referer ALTER COLUMN id SET DEFAULT nextval('referer_id_seq'::regclass);
ALTER TABLE ONLY referer_address ALTER COLUMN id SET DEFAULT nextval('referer_address_id_seq'::regclass);
SET search_path = pop_redirection, pg_catalog;
ALTER TABLE ONLY redirection ALTER COLUMN id SET DEFAULT nextval('redirection_id_seq'::regclass);
ALTER TABLE ONLY target ALTER COLUMN id SET DEFAULT nextval('target_id_seq'::regclass);
SET search_path = public, pg_catalog;
ALTER TABLE ONLY ip ALTER COLUMN id SET DEFAULT nextval('ip_id_seq'::regclass);
ALTER TABLE ONLY location_group ALTER COLUMN id SET DEFAULT nextval('location_group_id_seq'::regclass);
ALTER TABLE ONLY pop ALTER COLUMN id SET DEFAULT nextval('pops_id_seq'::regclass);
ALTER TABLE ONLY resource_ignored_query_param ALTER COLUMN id SET DEFAULT nextval('resource_ignored_query_param_id_seq'::regclass);
ALTER TABLE ONLY server ALTER COLUMN id SET DEFAULT nextval('server_id_seq'::regclass);
ALTER TABLE ONLY ssl_file ALTER COLUMN id SET DEFAULT nextval('ssl_file_id_seq'::regclass);
SET search_path = access_protection, pg_catalog;
ALTER TABLE ONLY geo_protection
    ADD CONSTRAINT geo_protection_id_pk PRIMARY KEY (id);
ALTER TABLE ONLY geo_protection_location
    ADD CONSTRAINT geo_protection_location_id_pk PRIMARY KEY (id);
ALTER TABLE ONLY geo_protection
    ADD CONSTRAINT geo_protection_resource_id_uniq UNIQUE (resource_id);
ALTER TABLE ONLY ip_protection_address
    ADD CONSTRAINT ip_protection_address_id_pk PRIMARY KEY (id);
ALTER TABLE ONLY ip_protection
    ADD CONSTRAINT ip_protection_id_pk PRIMARY KEY (id);
ALTER TABLE ONLY ip_protection
    ADD CONSTRAINT ip_protection_resource_id_uniq UNIQUE (resource_id);
SET search_path = hotlink_protection, pg_catalog;
ALTER TABLE ONLY referer_address
    ADD CONSTRAINT referer_address_pkey PRIMARY KEY (id);
ALTER TABLE ONLY referer
    ADD CONSTRAINT referer_pkey PRIMARY KEY (id);
SET search_path = letsencrypt, pg_catalog;
ALTER TABLE ONLY queue_task
    ADD CONSTRAINT queue_task_id_pk PRIMARY KEY (id);
ALTER TABLE ONLY request
    ADD CONSTRAINT request_id_pk PRIMARY KEY (id);
ALTER TABLE ONLY result
    ADD CONSTRAINT result_id_pk PRIMARY KEY (id);
SET search_path = pop_redirection, pg_catalog;
ALTER TABLE ONLY redirection
    ADD CONSTRAINT redirection_pkey PRIMARY KEY (id);
ALTER TABLE ONLY redirection
    ADD CONSTRAINT redirection_pop_id_key UNIQUE (pop_id);
ALTER TABLE ONLY target
    ADD CONSTRAINT target_pkey PRIMARY KEY (id);
ALTER TABLE ONLY target
    ADD CONSTRAINT target_redirection_id_pop_id_key UNIQUE (redirection_id, pop_id);
ALTER TABLE ONLY target
    ADD CONSTRAINT target_redirection_id_priority_key UNIQUE (redirection_id, priority) DEFERRABLE INITIALLY DEFERRED;
SET search_path = public, pg_catalog;
ALTER TABLE ONLY account
    ADD CONSTRAINT account_pkey PRIMARY KEY (id);
ALTER TABLE ONLY custom_location
    ADD CONSTRAINT custom_location_pkey PRIMARY KEY (resource_id);
ALTER TABLE ONLY custom_location_relation
    ADD CONSTRAINT custom_location_relation_pkey PRIMARY KEY (resource_id, pop_id);
ALTER TABLE ONLY group_pop_relation
    ADD CONSTRAINT group_pop_relation_pkey PRIMARY KEY (group_id, pop_id);
ALTER TABLE ONLY ip
    ADD CONSTRAINT ip_pkey PRIMARY KEY (id);
ALTER TABLE ONLY location_group
    ADD CONSTRAINT location_group_pkey PRIMARY KEY (id);
ALTER TABLE ONLY location
    ADD CONSTRAINT location_pkey PRIMARY KEY (id);
ALTER TABLE ONLY pop
    ADD CONSTRAINT pops_pkey PRIMARY KEY (id);
ALTER TABLE ONLY resource_full_log
    ADD CONSTRAINT resource_full_log_pk PRIMARY KEY (resource_id);
ALTER TABLE ONLY resource_ignored_query_param
    ADD CONSTRAINT resource_ignored_query_param_pkey PRIMARY KEY (id);
ALTER TABLE ONLY resource_ignored_query_param
    ADD CONSTRAINT resource_ignored_query_param_resource_id_name_key UNIQUE (resource_id, name);
ALTER TABLE ONLY resource_secure_token
    ADD CONSTRAINT resource_secure_token_pkey PRIMARY KEY (resource_id);
ALTER TABLE ONLY resource
    ADD CONSTRAINT resource_pkey PRIMARY KEY (id);
ALTER TABLE ONLY server_http2
    ADD CONSTRAINT server_http2_server_id PRIMARY KEY (server_id);
ALTER TABLE ONLY server
    ADD CONSTRAINT server_pkey PRIMARY KEY (id);
ALTER TABLE server
    ADD CONSTRAINT server_uid UNIQUE (uid);
ALTER TABLE ONLY ssl_file
    ADD CONSTRAINT ssl_file_pkey PRIMARY KEY (id);
ALTER TABLE ONLY ssl_file
    ADD CONSTRAINT ssl_file_unique_key UNIQUE (resource_id, index);
ALTER TABLE ONLY ssl
    ADD CONSTRAINT ssl_pkey PRIMARY KEY (resource_id);
ALTER TABLE ONLY used_resource_id
    ADD CONSTRAINT used_resource_ids_pkey PRIMARY KEY (id);
SET search_path = status, pg_catalog;
ALTER TABLE ONLY server_current
    ADD CONSTRAINT status_server_current_pk PRIMARY KEY (server_id);
ALTER TABLE ONLY server_last_down
    ADD CONSTRAINT status_server_last_down_pk PRIMARY KEY (server_id);
SET search_path = access_protection, pg_catalog;
CREATE INDEX geo_protection_location_geo_protection_id_idx ON geo_protection_location USING btree (geo_protection_id);
CREATE INDEX ip_protection_address_ip_protection_id_idx ON ip_protection_address USING btree (ip_protection_id);
SET search_path = hotlink_protection, pg_catalog;
CREATE INDEX referer_address_resource_id_idx ON referer_address USING btree (referer_id);
CREATE UNIQUE INDEX referer_resource_id_uniq ON referer USING btree (resource_id);
SET search_path = letsencrypt, pg_catalog;
CREATE INDEX result_completed_at_idx ON result USING btree (completed_at DESC);
SET search_path = public, pg_catalog;;
CREATE INDEX custom_location_idx ON custom_location USING btree (group_id);
CREATE INDEX custom_location_relation_idx ON custom_location_relation USING btree (pop_id);
CREATE INDEX custom_location_relation_idx1 ON custom_location_relation USING btree (resource_id);
CREATE INDEX group_pop_relation_idx ON group_pop_relation USING btree (pop_id, prefered_order);
CREATE UNIQUE INDEX ip_idx ON ip USING btree (ip);
CREATE INDEX resource_ignored_query_param_resource_id_idx ON resource_ignored_query_param USING btree (resource_id);
CREATE INDEX server_idx ON server USING btree (pop_id);
SET search_path = access_protection, pg_catalog;
SET search_path = hotlink_protection, pg_catalog;
SET search_path = letsencrypt, pg_catalog;
SET search_path = public, pg_catalog;
CREATE TRIGGER group_pop_relation_tr BEFORE INSERT ON group_pop_relation FOR EACH ROW EXECUTE PROCEDURE group_pop_relation_tr();
CREATE TRIGGER group_pop_relation_tr1 AFTER DELETE ON group_pop_relation FOR EACH ROW EXECUTE PROCEDURE group_pop_relation_tr2();
CREATE TRIGGER resource_tr BEFORE UPDATE ON resource FOR EACH ROW EXECUTE PROCEDURE general_update_trigger();
CREATE TRIGGER resource_tr2 BEFORE UPDATE OF group_id ON resource FOR EACH ROW EXECUTE PROCEDURE resource_group_id_updated();
CREATE TRIGGER resource_tr3 AFTER DELETE ON resource FOR EACH ROW EXECUTE PROCEDURE remember_deleted_resource_id();
CREATE TRIGGER check_cnames_unique BEFORE INSERT OR UPDATE ON resource FOR EACH ROW EXECUTE PROCEDURE trg_resource_cnames_unique();
SET search_path = status, pg_catalog;
SET search_path = access_protection, pg_catalog;
ALTER TABLE ONLY geo_protection_location
    ADD CONSTRAINT geo_protection_location_geo_protection_id_fk FOREIGN KEY (geo_protection_id) REFERENCES geo_protection(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY geo_protection
    ADD CONSTRAINT geo_protection_resource_id_fk FOREIGN KEY (resource_id) REFERENCES public.resource(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY ip_protection_address
    ADD CONSTRAINT ip_protection_address_ip_protection_id_fk FOREIGN KEY (ip_protection_id) REFERENCES ip_protection(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY ip_protection
    ADD CONSTRAINT ip_protection_resource_id_fk FOREIGN KEY (resource_id) REFERENCES public.resource(id) ON UPDATE CASCADE ON DELETE CASCADE;
SET search_path = hotlink_protection, pg_catalog;
ALTER TABLE ONLY referer_address
    ADD CONSTRAINT referer_id_fk FOREIGN KEY (referer_id) REFERENCES referer(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY referer
    ADD CONSTRAINT resource_id_fk FOREIGN KEY (resource_id) REFERENCES public.resource(id) ON UPDATE CASCADE ON DELETE CASCADE;
SET search_path = letsencrypt, pg_catalog;
ALTER TABLE ONLY queue_task
    ADD CONSTRAINT queue_task_request_id_fk FOREIGN KEY (request_id) REFERENCES request(id) ON DELETE CASCADE;
ALTER TABLE ONLY request
    ADD CONSTRAINT resource_id_fk FOREIGN KEY (resource_id) REFERENCES public.resource(id) ON DELETE CASCADE;
ALTER TABLE ONLY result
    ADD CONSTRAINT result_request_id_fk FOREIGN KEY (request_id) REFERENCES request(id) ON DELETE CASCADE;
SET search_path = pop_redirection, pg_catalog;
ALTER TABLE ONLY redirection
    ADD CONSTRAINT redirection_pop_id_fkey FOREIGN KEY (pop_id) REFERENCES public.pop(id) ON DELETE CASCADE;
ALTER TABLE ONLY target
    ADD CONSTRAINT target_pop_id_fkey FOREIGN KEY (pop_id) REFERENCES public.pop(id) ON DELETE CASCADE;
ALTER TABLE ONLY target
    ADD CONSTRAINT target_redirection_id_fkey FOREIGN KEY (redirection_id) REFERENCES redirection(id) ON DELETE CASCADE;
SET search_path = public, pg_catalog;
ALTER TABLE ONLY custom_location_relation
    ADD CONSTRAINT custom_location_relation_fk FOREIGN KEY (resource_id) REFERENCES custom_location(resource_id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY custom_location_relation
    ADD CONSTRAINT custom_location_relation_fk1 FOREIGN KEY (pop_id) REFERENCES pop(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY group_pop_relation
    ADD CONSTRAINT group_pop_relation_fk FOREIGN KEY (group_id) REFERENCES location_group(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY group_pop_relation
    ADD CONSTRAINT group_pop_relation_fk1 FOREIGN KEY (pop_id) REFERENCES pop(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY resource
    ADD CONSTRAINT resource_account_id_fkey FOREIGN KEY (account_id) REFERENCES account(id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE ONLY resource_full_log
    ADD CONSTRAINT resource_full_log_fk FOREIGN KEY (resource_id) REFERENCES resource(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY resource_ignored_query_param
    ADD CONSTRAINT resource_ignored_query_param_resource_id_fkey FOREIGN KEY (resource_id) REFERENCES resource(id) ON DELETE CASCADE;
ALTER TABLE ONLY server_http2
    ADD CONSTRAINT server_http2_server_id_fkey FOREIGN KEY (server_id) REFERENCES server(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY ssl
    ADD CONSTRAINT ssl_fk FOREIGN KEY (resource_id) REFERENCES resource(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE resource_secure_token
    ADD FOREIGN KEY (resource_id) REFERENCES resource(id) ON DELETE CASCADE ON UPDATE CASCADE;
SET search_path = status, pg_catalog;
ALTER TABLE ONLY server_current
    ADD CONSTRAINT server_id_fk FOREIGN KEY (server_id) REFERENCES public.server(id) ON DELETE CASCADE;
ALTER TABLE ONLY server_last_down
    ADD CONSTRAINT server_id_fk FOREIGN KEY (server_id) REFERENCES public.server(id) ON DELETE CASCADE;
ALTER TABLE ONLY resource_origin
    ADD CONSTRAINT resource_id_fk FOREIGN KEY (resource_id) REFERENCES public.resource(id) ON DELETE CASCADE;
CREATE UNIQUE INDEX idx_priority_1_per_resource_id ON resource_origin (resource_id) WHERE priority = 1;
/*TODO: trigger prevent_last_row_deletion_and_resource_id_change */
