include:
    -   project: ci-cd/templates
        file: basic.yaml

stages:
    - Build Image
    - Build Dependencies
    - Quality Assurance
    - Release
    - Pre-Deployment
    - Deployment

default:
    image: registry.gitlab.cdn77.eu/purple-team/cdn77/nxg-api/build

.localJob:
    before_script:
        - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.cdn77.eu/".insteadOf "****************:"
    variables:
        COMPOSER_HOME: /builds/build-cache/composer
        GIT_CLONE_PATH: $CI_BUILDS_DIR/$CI_CONCURRENT_ID/$CI_PROJECT_PATH

.composerJob:
    extends: .localJob
    stage: Quality Assurance
    before_script:
        - !reference [.localJob, before_script]
        - make vendor

Build Image:
    extends: .build-image

Coding Standard:
    extends: .composerJob
    script: make cs

Composer Normalize:
    extends: .composerJob
    script:
        - composer bump && git diff --exit-code
        - composer normalize --dry-run

Integration Tests:
    extends: .composerJob
    services:
          -   name: postgres:10.11
              command: ['postgres', '-c', 'fsync=off', '-c', 'full_page_writes=off']
          - redis:3.2
    script:
        - psql ${DATABASE_URL_NO_QUERY} < sql/cdn77_schema.sql
        - make test-integration

Static Analysis:
    extends: .composerJob
    script: make static-analysis

Unit Tests:
    extends: .composerJob
    script: make test-unit

Unused Composer Requirements:
    extends: .composerJob
    script: make composer-unused

Build & Release:
    extends: .localJob
    stage: Release
    before_script:
        - !reference [ .localJob, before_script ]
        - rm -rf $releaseDir
        - mkdir $releaseDir
    script:
        - eval "$publicReleaseExpr"
        - export PUBLIC_RELEASE=$publicRelease
        - make release
    variables:
        release: release-$CI_JOB_ID
        releaseDir: release-$CI_JOB_ID
        ENV: prod
    artifacts:
        paths:
            - $releaseDir

Deploy Canary:
    extends: .deploy-swap-links
    stage: Pre-Deployment
    environment:
        name: Canary
    variables:
        DEPLOY_HOSTS: canary.nxg-api.cdn77.eu
        DEPLOY_USERS: nxgapi
        DEPLOY_PATHS: /var/www/nxg-api.cdn77.eu
        DEPLOY_SHARED_PATHS: .env logs:var/logs
        DEPLOY_HTTP_CHECK_APP_URLS: https://canary.nxg-api.cdn77.dev/version
    rules:
        -   if: "$CI_COMMIT_REF_NAME == 'master'"
            when: on_success
        -   when: manual
            allow_failure: true

Deploy Stage:
    extends: .deploy-swap-links
    stage: Deployment
    environment:
        name: Stage
    resource_group: Staging
    needs:
        - Build & Release
    variables:
        STAGE_NUMBER: 1 # you may change this in the UI before triggering the deploy
        DEPLOY_HOSTS: stage.nxg-api.cdn77.dev
        DEPLOY_USERS: stage.nxg-api.cdn77.dev
        DEPLOY_PATHS: /var/www/stage/nxg-api.cdn77.dev/$STAGE_NUMBER
        DEPLOY_SHARED_PATHS: .env logs:var/logs
        DEPLOY_HTTP_CHECK_APP_URLS: https://$STAGE_NUMBER.stage.nxg-api.cdn77.dev/version
        DEPLOY_NAME: deploy-stage
    rules:
        -   when: manual
            allow_failure: true

Deploy All Production:
    extends: .deploy-swap-links
    stage: Deployment
    variables:
        DEPLOY_HOSTS: 1.nxg-api.cdn77.eu 2.nxg-api.cdn77.eu 3.nxg-api.cdn77.eu
        DEPLOY_USERS: nxgapi
        DEPLOY_PATHS: /var/www/nxg-api.cdn77.eu
        DEPLOY_SHARED_PATHS: .env logs:var/logs
        DEPLOY_HTTP_CHECK_APP_URLS: https://1.nxg-api.cdn77.eu/version https://2.nxg-api.cdn77.eu/version https://3.nxg-api.cdn77.eu/version
    rules:
        -   when: manual
            allow_failure: true

Deploy HA 1 Production:
    extends: .deploy-swap-links
    stage: Deployment
    variables:
        DEPLOY_HOSTS: 1.nxg-api.cdn77.eu
        DEPLOY_USERS: nxgapi
        DEPLOY_PATHS: /var/www/nxg-api.cdn77.eu
        DEPLOY_SHARED_PATHS: .env logs:var/logs
        DEPLOY_HTTP_CHECK_APP_URLS: https://1.nxg-api.cdn77.eu/version
    rules:
        -   when: manual
            allow_failure: true

Deploy HA 2 Production:
    extends: .deploy-swap-links
    stage: Deployment
    variables:
        DEPLOY_HOSTS: 2.nxg-api.cdn77.eu
        DEPLOY_USERS: nxgapi
        DEPLOY_PATHS: /var/www/nxg-api.cdn77.eu
        DEPLOY_SHARED_PATHS: .env logs:var/logs
        DEPLOY_HTTP_CHECK_APP_URLS: https://2.nxg-api.cdn77.eu/version
    rules:
        -   when: manual
            allow_failure: true

Deploy HA 3 Production:
    extends: .deploy-swap-links
    stage: Deployment
    variables:
        DEPLOY_HOSTS: 3.nxg-api.cdn77.eu
        DEPLOY_USERS: nxgapi
        DEPLOY_PATHS: /var/www/nxg-api.cdn77.eu
        DEPLOY_SHARED_PATHS: .env logs:var/logs
        DEPLOY_HTTP_CHECK_APP_URLS: https://3.nxg-api.cdn77.eu/version
    rules:
        -   when: manual
            allow_failure: true

variables:
    APP_ENV: test
    APP_DEBUG: 1
    CERTIFICATES_PATH: /tmp/bucket
    CERTIFICATES_COLD_PATH: /tmp/cold
    CERTIFICATES_STORAGE_PATH: /tmp/storage
    DATABASE_URL: *****************************************************************
    DATABASE_URL_NO_QUERY: ************************************************
    LE_DEBUG_RESOURCES_LIST: '[]'
    KAFKA_BOOTSTRAP_SERVERS: 127.0.0.1
    MON_MASTER_PATH: /tmp
    POSTGRES_PASSWORD: postgres
    RABBITMQ_API_DSN: rabbitmq://test:test@rabbitmq:5672/test?connection_timeout=15&read_write_timeout=15
    RABBITMQ_DEFAULT_PASS: test
    RABBITMQ_DEFAULT_USER: test
    RABBITMQ_DEFAULT_VHOST: test
    ENABLE_NOTIFICATION_TO_RABBIT: "true"
    OPEN_API_SPEC_HOST: api.docs.example
    OPEN_API_SPEC_SCHEME: http
