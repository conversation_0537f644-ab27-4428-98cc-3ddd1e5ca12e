FROM php:8.3-cli

# hack to make postgresql-client install work on stretch-slim
RUN mkdir -p /usr/share/man/man1 && \
    mkdir -p /usr/share/man/man7

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        gnupg

RUN curl -sS https://packages.sury.org/php/apt.gpg | apt-key add -
RUN echo 'deb [trusted=yes] http://remote.cdn77.com/sury-buster buster main' > /etc/apt/sources.list.d/deb.sury.org.list

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        postgresql-client \
        dirmngr \
        git \
        iproute2 \
        libicu-dev  \
        libpq-dev \
        ssh \
        unzip \
        make \
        && \
    pecl install ds xdebug && \
    docker-php-ext-enable ds xdebug && \
    docker-php-ext-install \
        intl \
        pdo \
        pdo_pgsql \
        pcntl \
        sockets

RUN curl -sS https://getcomposer.org/installer | php -- --filename composer --install-dir /usr/local/bin

ADD php.ini /usr/local/etc/php

ENV LIBRDKAFKA_VERSION "v1.8.2"
ENV PHPRDKAFKA_VERSION "6.0.3"

RUN git clone \
        --quiet \
        --branch "${LIBRDKAFKA_VERSION}" \
        --depth 1 \
        https://github.com/edenhill/librdkafka.git \
        /tmp/librdkafka \
        && \
    cd /tmp/librdkafka && \
    ./configure && \
    make -j 4  && \
    make install && \
    git clone \
        --quiet \
        --branch "${PHPRDKAFKA_VERSION}" \
        --depth 1 \
        https://github.com/arnaud-lb/php-rdkafka.git \
        /tmp/php-rdkafka \
        && \
    cd /tmp/php-rdkafka && \
    phpize && \
    cat ./configure && \
    ./configure --with-php-config=/usr/local/bin/php-config && \
    make -j 4 all && \
    make install && \
    rm -rf /tmp/librdkafka /tmp/php-rdkafka

COPY entrypoint /entrypoint
ENTRYPOINT ["/entrypoint"]

CMD trap "exit 0;" TERM INT; sleep 99999999999d & wait
