#!/usr/bin/env bash

set -o errexit

# needed for xdebug
echo "$(ip route | grep "default via" | cut -d' ' -f 3) dockerhost" >> /etc/hosts

usermod -d /home/<USER>

USER_ID=$(stat --format %u .)
# some bug in MacOS - volume mounted as root ... it works, so let's not touch it ...
if [[ ${USER_ID} -ne 0 ]]; then
    usermod -u ${USER_ID} www-data
    groupmod -g $(stat --format %g .) www-data
fi

exec "$@"
