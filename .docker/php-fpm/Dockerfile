FROM php:8.3-fpm

# hack to make postgresql-client install work on stretch-slim
RUN mkdir -p /usr/share/man/man1 && \
    mkdir -p /usr/share/man/man7

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        postgresql-client \
        libicu-dev \
        libpq-dev \
        libxml2-dev \
        && \
    pecl install ds xdebug && \
    docker-php-ext-enable ds xdebug && \
    docker-php-ext-install \
        intl \
        pdo \
        pdo_pgsql \
        sockets

ADD php.ini /usr/local/etc/php

COPY entrypoint /entrypoint
ENTRYPOINT ["/entrypoint"]

CMD ["php-fpm"]
