server {
    listen 80;

    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;
    root /app/public;

    location / {
        try_files $uri /index.php$is_args$args;
    }

    location ~ ^/index\.php(/|$) {
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        fastcgi_pass nxgapi-php-fpm:9000;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        fastcgi_param DOCKER_ENV 1;
        internal;
    }

    location ~ \.php$ {
        return 404;
    }
}
