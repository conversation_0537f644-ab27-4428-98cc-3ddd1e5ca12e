FROM registry.gitlab.cdn77.eu/purple-team/ci-cd/images/php-node

ENV PHP_VERSION "8.3"
ENV LIBRDKAFKA_VERSION "v1.8.2"
ENV PHPRDKAFKA_VERSION "6.0.3"

RUN git clone \
        --quiet \
        --branch "${LIBRDKAFKA_VERSION}" \
        --depth 1 \
        https://github.com/edenhill/librdkafka.git \
        /tmp/librdkafka \
        && \
    cd /tmp/librdkafka && \
    ./configure && \
    make -j 4  && \
    make install && \
    git clone \
        --quiet \
        --branch "${PHPRDKAFKA_VERSION}" \
        --depth 1 \
        https://github.com/arnaud-lb/php-rdkafka.git \
        /tmp/php-rdkafka \
        && \
    cd /tmp/php-rdkafka && \
    phpize && \
    cat ./configure && \
    ./configure --with-php-config=/usr/bin/php-config${PHP_VERSION} && \
    make -j 4 all && \
    make install && \
    echo "extension = rdkafka.so" > /etc/php/${PHP_VERSION}/mods-available/rdkafka.ini && \
    rm -rf /tmp/librdkafka /tmp/php-rdkafka
