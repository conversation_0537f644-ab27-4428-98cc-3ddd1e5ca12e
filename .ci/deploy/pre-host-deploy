#!/usr/bin/env bash

# pass deployment version to the application
cat <<EOF >"${deployNewVersionDir}/config/deployment.php"
<?php

\$container->setParameter('app.deployment-version', '${DEPLOY_NEW_VERSION}');
EOF

PHP_PATH=/usr/bin/php8.3

sed -i "s|{{COMMAND}}|${PHP_PATH} ${deployNewVersionDir}/bin/console|g" "${deployNewVersionDir}/crontab"
sed -i "s|{{LOGPATH}}|${deploySharedDir}/logs|g" "${deployNewVersionDir}/crontab"

${PHP_PATH} bin/console cache:warmup -n
