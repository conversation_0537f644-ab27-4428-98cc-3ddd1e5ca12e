#!/usr/bin/env bash

set -o errexit -o errtrace -o nounset -o pipefail -o xtrace

crontab -l > .ci/crontab-backup

crontab crontab

if [ "${CI_ENVIRONMENT_NAME}" != "Canary" ]; then
  exit
fi

PHP_PATH=/usr/bin/php8.3

${PHP_PATH} bin/console canary:check

outputPath=".ci-deploy/canary"
mkdir -p "${outputPath}"

productionOutputFile="${outputPath}/production"
canaryOutputFile="${outputPath}/canary"
diffFile="${outputPath}/diff"

rm "${outputPath}"/* || true

productionDomain="https://nxg-api.cdn77.eu"
canaryDomain="https://canary.nxg-api.cdn77.eu"
endpoints=(
  "ngx-conf-gen/resources"
  "internal/server"
  "internal/servers/statuses"
  "ngx-conf-gen/resources/1464091238" #TT
  "ngx-conf-gen/resources/1480222913" #Xvid
)

set +o errexit

for endpoint in "${endpoints[@]}"; do
  unset passed
  for attempt in {1..3}; do
    productionHttpStatusCode=$(curl -sSL -w "%{http_code}" -o "${productionOutputFile}" "${productionDomain}/${endpoint}")
    canaryHttpStatusCode=$(curl -sSL -w "%{http_code}" -o "${canaryOutputFile}" "${canaryDomain}/${endpoint}")

    if [ "$productionHttpStatusCode" -ne 200 ]; then
        echo "Attempt $attempt: Production HTTP status code is not 200, but $productionHttpStatusCode."
    elif [ "$canaryHttpStatusCode" -ne 200 ]; then
        echo "Attempt $attempt: Canary HTTP status code is not 200, but $canaryHttpStatusCode."
    elif cmp -s "${productionOutputFile}" "${canaryOutputFile}"; then
        passed=1
        break
    else
        echo "Attempt $attempt: Production and Canary outputs differ."
    fi
  done

  if [[ -v passed ]]; then
    echo "Success: Production and Canary are identical."
    continue
  else
    echo "Error: The checks failed after 3 attempts."
  fi

  jq < "${productionOutputFile}" > "${productionOutputFile}.json"
  jq < "${canaryOutputFile}" > "${canaryOutputFile}.json"
  diff "${productionOutputFile}.json" "${canaryOutputFile}.json" > "${diffFile}"
  head -150 "${diffFile}"

  exit 1
done
