monolog:
    handlers:
        requests:
            type: stream
            level: info
            path: "%kernel.logs_dir%/%kernel.environment%.requests.log"
            channels: [requests]
            formatter: Cdn77\NxgApi\Log\Formatter\RequestsFormatter
        sql:
            type: stream
            level: debug
            path: "%kernel.logs_dir%/%kernel.environment%.sql.log"
            channels: [doctrine]

        main:
            type: fingers_crossed
            action_level: notice
            handler: grouped
            channels: ['!requests', '!purger', '!doctrine', '!notify', '!rmq.purge']
        grouped:
            type: group
            members: [file, sentry]
        file:
            type: stream
            path: "%kernel.logs_dir%/%kernel.environment%.log"
            level: debug
        sentry:
            type: sentry
            level: !php/const Monolog\Logger::ERROR
            hub_id: Sentry\State\HubInterface
            fill_extra_context: true
        console:
            type: console
