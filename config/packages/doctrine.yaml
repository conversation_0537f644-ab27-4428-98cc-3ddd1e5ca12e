doctrine:
    dbal:
        driver: pdo_pgsql
        url: '%env(DATABASE_URL)%'
        charset: UTF8
        mapping_types:
            bit: boolean
            tsvector: string
        types:
            resource_cnames_array:
                class: Cdn77\NxgApi\Types\ResourceCnamesArrayType

    orm:
        auto_generate_proxy_classes: "%kernel.debug%"
        default_entity_manager: mc_legacy
        entity_managers:
            mc_legacy:
                naming_strategy: doctrine.orm.naming_strategy.underscore
                mappings:
                    NxgApi:
                        type: annotation
                        dir: "%kernel.project_dir%/src/Entity"
                        prefix: Cdn77\NxgApi\Entity
                        is_bundle: false
                filters:
                    deleted_resource:
                        class: Cdn77\NxgApi\Filter\Resource\DeletedResourceFilter
                        enabled: true
                    suspended_resource:
                        class: Cdn77\NxgApi\Filter\Resource\SuspendedResourceFilter
                        enabled: false
                metadata_cache_driver:
                    id: Cache\Bridge\Doctrine\DoctrineCacheBridge.orm.metadata
                query_cache_driver:
                    id: Cache\Bridge\Doctrine\DoctrineCacheBridge.orm.query
                result_cache_driver:
                    id: Cache\Bridge\Doctrine\DoctrineCacheBridge.orm.result
