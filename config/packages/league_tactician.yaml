tactician:
    default_bus: commandbus
    commandbus:
        commandbus:
            middleware:
                - Cdn77\NxgApi\CoreLibrary\Tactician\Middleware\DoctrineClearMiddleware
                - Cdn77\NxgApi\CoreLibrary\Tactician\Middleware\DoctrineTransactionMiddleware
                - tactician.commandbus.commandbus.middleware.command_handler
        querybus:
            middleware:
                - tactician.commandbus.querybus.middleware.command_handler
