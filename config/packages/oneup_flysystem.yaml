oneup_flysystem:
    adapters:
        certificate_bucket_adapter:
            local:
                location: '%env(CERTIFICATES_PATH)%'

        certificate_cold_adapter:
            local:
                location: '%env(CERTIFICATES_COLD_PATH)%'
                
        certificate_storage_adapter:
            local:
                location: '%env(CERTIFICATES_STORAGE_PATH)%'

    filesystems:
        certificate_bucket:
            adapter: certificate_bucket_adapter

        certificate_cold:
            adapter: certificate_cold_adapter
            
        certificate_storage:
            adapter: certificate_storage_adapter

services:
    League\Flysystem\FilesystemOperator.certificate_bucket:
        alias: 'oneup_flysystem.certificate_bucket_filesystem'

    League\Flysystem\FilesystemOperator.certificate_cold:
        alias: 'oneup_flysystem.certificate_cold_filesystem'
        
    League\Flysystem\FilesystemOperator.certificate_storage:
        alias: 'oneup_flysystem.certificate_storage_filesystem'
