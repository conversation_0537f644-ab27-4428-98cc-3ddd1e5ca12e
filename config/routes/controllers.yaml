account_controllers:
    resource: ../../src/Account/Application/Controller/
    type: annotation
    prefix: /

certificate_controllers:
    resource: ../../src/Certificate/Application/Controller/
    type: annotation
    prefix: /

ip_controllers:
    resource: ../../src/Ip/Application/Controller/
    type: annotation
    prefix: /

location_controllers:
    resource: ../../src/Location/Application/Controller/
    type: annotation
    prefix: /

server_controllers:
    resource: ../../src/Server/Application/Controller/
    type: annotation
    prefix: /

resource_controllers:
    resource: ../../src/Resource/Application/Controller/
    type: annotation
    prefix: /

ngx_conf_gen_controllers:
    resource: ../../src/NgxConfGen/Application/Controller/
    type: annotation
    prefix: /

core_controllers:
    resource: ../../src/Core/Application/Controller/
    type: annotation
    prefix: /

full_log_controllers:
    resource: ../../src/FullLogs/Application/Controller/
    type: annotation
    prefix: /

letsencrypt_controllers:
    resource: ../../src/LetsEncrypt/Application/Controller/
    type: annotation
    prefix: /
