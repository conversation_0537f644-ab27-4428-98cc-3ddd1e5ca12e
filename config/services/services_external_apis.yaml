services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\:
        resource: '%kernel.project_dir%/src/Service/ExternalApi/CertificateBucket/{CertificateBucket,DefaultNamingStrategy}.php'
    Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\NamingStrategy: '@Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\DefaultNamingStrategy'

    Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\CertificateBucket:
        arguments:
            $filesystem: '@League\Flysystem\FilesystemOperator.certificate_bucket'

    Cdn77\NxgApi\Clap\Infrastructure\GraphQLClient:
        arguments:
            $graphqlUrl: '%env(CLAP_GRAPHQL_API_URL)%'
            $bearerToken: '%env(CLAP_GRAPHQL_BEARER_TOKEN)%'

    Cdn77\NxgApi\Clap\Infrastructure\GraphQLAccountFinder:
        arguments:
            $client: '@Cdn77\NxgApi\Clap\Infrastructure\GraphQLClient'
