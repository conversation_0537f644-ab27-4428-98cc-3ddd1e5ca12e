services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    Metadata\Cache\DoctrineCacheAdapter:
        arguments:
            $prefix: 'meta'
            $cache: '@doctrine_cache.providers.serializer_cache'

    Cache\Bridge\Doctrine\DoctrineCacheBridge.orm.metadata:
        class: Cache\Bridge\Doctrine\DoctrineCacheBridge
        arguments:
            $cachePool: '@app.cache.infrastructure.orm.metadata'

    Cache\Bridge\Doctrine\DoctrineCacheBridge.orm.query:
        class: Cache\Bridge\Doctrine\DoctrineCacheBridge
        arguments:
            $cachePool: '@app.cache.infrastructure.orm.query'

    Cache\Bridge\Doctrine\DoctrineCacheBridge.orm.result:
        class: Cache\Bridge\Doctrine\DoctrineCacheBridge
        arguments:
            $cachePool: '@app.cache.infrastructure.orm.result'
