services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: true

    JMS\Serializer\SerializerInterface:
        alias: 'jms_serializer.serializer'

    Cdn77\NxgApi\Resource\Domain\Finder\ResourcesForPermanentRemoveFinder:
        alias: Cdn77\NxgApi\Resource\Infrastructure\Finder\DoctrineResourcesForPermanentRemoveFinder

    Cdn77\NxgApi\Certificate\Domain\CertificateCleaner:
        $coldFilesystem: '@League\Flysystem\FilesystemOperator.certificate_cold'
        $bucketFilesystem: '@League\Flysystem\FilesystemOperator.certificate_bucket'
        $pathBucket: '%env(CERTIFICATES_PATH)%'
        $pathCold: '%env(CERTIFICATES_COLD_PATH)%'

    Cdn77\NxgApi\Clap\Infrastructure\GraphQLClient:
      arguments:
        $graphqlUrl: '%env(CLAP_GRAPHQL_API_URL)%'
        $bearerToken: '%env(CLAP_GRAPHQL_BEARER_TOKEN)%'

    Cdn77\NxgApi\Clap\Infrastructure\GraphQLAccountFinder:
      arguments:
        $client: '@Cdn77\NxgApi\Clap\Infrastructure\GraphQLClient'

    Cdn77\NxgApi\Certificate\Infrastructure\Finder\DbalSslFilesForCleanupFinder:
    Cdn77\NxgApi\Certificate\Infrastructure\Finder\DbalUsedResourceIdsFinder:
    Cdn77\NxgApi\Certificate\Infrastructure\Finder\DbalInactiveFinder:

    Cdn77\NxgApi\LetsEncrypt\Infrastructure\Finder\DoctrineRequestFinder:
