services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: true

    Cdn77\NxgApi\Account\Application\Controller\:
        resource: '%kernel.project_dir%/src/Account/Application/Controller/'

    Cdn77\NxgApi\Certificate\Application\Controller\:
        resource: '%kernel.project_dir%/src/Certificate/Application/Controller/'

    Cdn77\NxgApi\Core\Application\Controller\:
        resource: '%kernel.project_dir%/src/Core/Application/Controller/'

    Cdn77\NxgApi\FullLogs\Application\Controller\:
        resource: '%kernel.project_dir%/src/FullLogs/Application/Controller/'

    Cdn77\NxgApi\Ip\Application\Controller\:
        resource: '%kernel.project_dir%/src/Ip/Application/Controller/'

    Cdn77\NxgApi\LetsEncrypt\Application\Controller\:
        resource: '%kernel.project_dir%/src/LetsEncrypt/Application/Controller/'

    Cdn77\NxgApi\Location\Application\Controller\:
        resource: '%kernel.project_dir%/src/Location/Application/Controller/'

    Cdn77\NxgApi\Server\Application\Controller\:
        resource: '%kernel.project_dir%/src/Server/Application/Controller/'

    Cdn77\NxgApi\Resource\Application\Controller\:
        resource: '%kernel.project_dir%/src/Resource/Application/Controller/'

    Cdn77\NxgApi\NgxConfGen\Application\Controller\:
        resource: '%kernel.project_dir%/src/NgxConfGen/Application/Controller/'
