services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    Cdn77\NxgApi\Service\Legacy\Edit\Constraints\UniqueCnameValidator:
        tags:
            - { name: validator.constraint_validator, alias: service_resource_edit_unique_cname}

    Cdn77\NxgApi\Validator\Constraints\Resource\LocationGroupValidator:
        tags:
            - { name: validator.constraint_validator, alias: location_group}

    Cdn77\NxgApi\Validator\Constraints\Resource\UniqueCnameValidator:
        tags:
            - { name: validator.constraint_validator, alias: schema_resource_add_unique_cname}

    Cdn77\NxgApi\Validator\Constraints\Resource\ExistsValidator:
        tags:
            - { name: validator.constraint_validator, alias: service_validator_resource_exists}

    Cdn77\NxgApi\Validator\Constraints\ExistingPopValidator:
        tags:
            - { name: validator.constraint_validator, alias: existing_pop_validator}
