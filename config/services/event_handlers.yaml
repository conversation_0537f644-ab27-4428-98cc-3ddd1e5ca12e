services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false
        tags:
            - { name: kernel.event_subscriber }

    Cdn77\NxgApi\EventListener\:
        resource: '%kernel.project_dir%/src/EventListener/'

    Cdn77\NxgApi\EventListener\ExceptionListener:
        arguments:
            $logger: '@logger'
        tags:
            - { name: monolog.logger, channel: runtime_error }

    Cdn77\NxgApi\EventListener\InputOutputLoggerListener:
        arguments:
            $logger: '@logger'
        tags:
            - { name: monolog.logger, channel: requests }
