services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    Cdn77\NxgApi\Repository\Legacy\AccountRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\Account

    Cdn77\NxgApi\Repository\Legacy\CustomLocationRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\CustomLocation

    Cdn77\NxgApi\Repository\Legacy\IgnoredQueryParamRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\IgnoredQueryParam

    Cdn77\NxgApi\Repository\Legacy\IpRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\Ip

    Cdn77\NxgApi\Repository\Legacy\LocationRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\Location

    Cdn77\NxgApi\Repository\Legacy\LocationGroupRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\LocationGroup

    Cdn77\NxgApi\Repository\Legacy\PopRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\Pop

    Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\CdnResource

    Cdn77\NxgApi\Repository\Legacy\ResourceFullLogRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\ResourceFullLog

    Cdn77\NxgApi\Repository\Legacy\ResourceGeoProtectionRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtection

    Cdn77\NxgApi\Repository\Legacy\ResourceGeoProtectionLocationRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtectionLocation

    Cdn77\NxgApi\Repository\Legacy\ResourceIpProtectionRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\ResourceIpProtection

    Cdn77\NxgApi\Repository\Legacy\ResourceIpProtectionAddressRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\ResourceIpProtectionAddress

    Cdn77\NxgApi\Repository\Legacy\ResourceRefererProtectionRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtection

    Cdn77\NxgApi\Repository\Legacy\ResourceRefererProtectionAddressRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtectionAddress

    Cdn77\NxgApi\Repository\Legacy\ResourceSecureTokenUserAgentBypassRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\ResourceSecureTokenUserAgentBypass

    Cdn77\NxgApi\Repository\Legacy\ResourceSecureTokenUserAgentBypassNameRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\ResourceSecureTokenUserAgentBypassName

    Cdn77\NxgApi\Repository\Legacy\ServerRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\Server

    Cdn77\NxgApi\Repository\Legacy\ServerCurrentStatusRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\ServerCurrentStatus

    Cdn77\NxgApi\Repository\Legacy\ServerHttp2Repository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\ServerHttp2

    Cdn77\NxgApi\Repository\Legacy\ServerLastDownStatusRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\ServerLastDownStatus

    Cdn77\NxgApi\Repository\Legacy\SslRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\Ssl

    Cdn77\NxgApi\Repository\Legacy\SslFileRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\SslFile

    Cdn77\NxgApi\Repository\Legacy\SslFileRelationRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\Legacy\SslFileRelation

    Cdn77\NxgApi\Repository\LetsEncrypt\RequestRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\LetsEncrypt\Request

    Cdn77\NxgApi\Repository\LetsEncrypt\ResultRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\LetsEncrypt\Result

    Cdn77\NxgApi\Repository\LetsEncrypt\TaskRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\LetsEncrypt\Task

    Cdn77\NxgApi\Repository\PopRedirection\RedirectionRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\PopRedirection\Redirection

    Cdn77\NxgApi\Repository\PopRedirection\TargetRepository:
        factory: ['@doctrine', getRepository]
        arguments:
            - Cdn77\NxgApi\Entity\PopRedirection\Target
