services:
    _defaults:
        public: false
        autowire: true
        autoconfigure: true

    Cdn77\NxgApi\Service\LetsEncrypt\RenewalManager:
        public: true
    Cdn77\NxgApi\Service\LetsEncrypt\RequestManager:
        public: true

    Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser\DomainChooser:
        alias: Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser\DomainChooserChain

    Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser\DefaultDomainChooser:
        tags:
            - { name: letsencrypt_domain_chooser, priority: 0 }
    Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser\CraDomainChooser:
        tags:
            - { name: letsencrypt_domain_chooser, priority: 100 }

    Cdn77\NxgApi\LetsEncrypt\Domain\Query\FindAllQueuedTasksHandler:
        arguments:
            $debugResourceIds: '%env(json:LE_DEBUG_RESOURCES_LIST)%'
