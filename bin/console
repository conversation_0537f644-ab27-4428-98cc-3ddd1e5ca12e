#!/usr/bin/env php
<?php

declare(strict_types=1);

namespace Cdn77\NxgApi;

use RuntimeException;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Input\ArgvInput;
use Symfony\Component\Dotenv\Dotenv;
use Symfony\Component\ErrorHandler\Debug;

use function set_time_limit;

set_time_limit(0);

require __DIR__ . '/../vendor/autoload.php';

if (!class_exists(Application::class)) {
    throw new RuntimeException('You need to add "symfony/framework-bundle" as a Composer dependency.');
}

$debug = (bool) getenv('APP_DEBUG');

if (getenv('APP_ENV') === false) {
    (new Dotenv())->usePutenv()->load(__DIR__ . '/../.env');
}

$input = new ArgvInput();
$env = $input->getParameterOption(['--env', '-e'], getenv('APP_ENV') === false ? 'dev' : getenv('APP_ENV'));
$debug = $debug && !$input->hasParameterOption(['--no-debug', '']);

if ($debug && class_exists(Debug::class)) {
    Debug::enable();
}

$kernel = new Kernel($env, $debug);
$application = new Application($kernel);
$application->run($input);
