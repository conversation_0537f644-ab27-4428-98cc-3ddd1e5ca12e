parameters:
	ignoreErrors:
		-
			message: "#^Function set_time_limit is unsafe to use\\. It can return FALSE instead of throwing an exception\\. Please add 'use function Safe\\\\set_time_limit;' at the beginning of the file to use the variant provided by the 'thecodingmachine/safe' library\\.$#"
			count: 1
			path: bin/console

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Core\\\\Domain\\\\Bus\\\\CommandBus\\:\\:handle\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/Core/Domain/Bus/CommandBus.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Core\\\\Domain\\\\Bus\\\\QueryBus\\:\\:handle\\(\\) has no return type specified\\.$#"
			count: 1
			path: src/Core/Domain/Bus/QueryBus.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Ipv6\\:\\:\\$ip is never written, only read\\.$#"
			count: 1
			path: src/Entity/Legacy/Ipv6.php

		-
			message: "#^Return type \\(array\\<array\\>\\) of method Cdn77\\\\NxgApi\\\\EventListener\\\\ExceptionListener\\:\\:getSubscribedEvents\\(\\) should be covariant with return type \\(array\\<string, array\\<int\\|string, array\\<int\\|string, int\\|string\\>\\|int\\|string\\>\\|string\\>\\) of method Symfony\\\\Component\\\\EventDispatcher\\\\EventSubscriberInterface\\:\\:getSubscribedEvents\\(\\)$#"
			count: 1
			path: src/EventListener/ExceptionListener.php

		-
			message: "#^Return type \\(array\\<array\\>\\) of method Cdn77\\\\NxgApi\\\\EventListener\\\\InputOutputLoggerListener\\:\\:getSubscribedEvents\\(\\) should be covariant with return type \\(array\\<string, array\\<int\\|string, array\\<int\\|string, int\\|string\\>\\|int\\|string\\>\\|string\\>\\) of method Symfony\\\\Component\\\\EventDispatcher\\\\EventSubscriberInterface\\:\\:getSubscribedEvents\\(\\)$#"
			count: 1
			path: src/EventListener/InputOutputLoggerListener.php

		-
			message: "#^Return type \\(array\\<array\\>\\) of method Cdn77\\\\NxgApi\\\\EventListener\\\\RequestIdListener\\:\\:getSubscribedEvents\\(\\) should be covariant with return type \\(array\\<string, array\\<int\\|string, array\\<int\\|string, int\\|string\\>\\|int\\|string\\>\\|string\\>\\) of method Symfony\\\\Component\\\\EventDispatcher\\\\EventSubscriberInterface\\:\\:getSubscribedEvents\\(\\)$#"
			count: 1
			path: src/EventListener/RequestIdListener.php

		-
			message: "#^Return type \\(array\\<string\\>\\) of method Cdn77\\\\NxgApi\\\\EventListener\\\\Service\\\\LetsEncrypt\\\\ResourceListener\\:\\:getSubscribedEvents\\(\\) should be covariant with return type \\(array\\<string, array\\<int\\|string, array\\<int\\|string, int\\|string\\>\\|int\\|string\\>\\|string\\>\\) of method Symfony\\\\Component\\\\EventDispatcher\\\\EventSubscriberInterface\\:\\:getSubscribedEvents\\(\\)$#"
			count: 1
			path: src/EventListener/Service/LetsEncrypt/ResourceListener.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Resource\\\\Application\\\\Payload\\\\ChangeLocationGroupSchema\\:\\:\\$groupId is never written, only read\\.$#"
			count: 1
			path: src/Resource/Application/Payload/ChangeLocationGroupSchema.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Schema\\\\Legacy\\\\FieldsErrorsSchema\\:\\:\\$fields \\(array\\<string, array\\<string\\|Stringable\\>\\>\\) on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: src/Schema/Legacy/FieldsErrorsSchema.php

		-
			message: "#^Only booleans are allowed in a negated boolean, mixed given\\.$#"
			count: 1
			path: src/Service/Legacy/Certificate/OpenSSLCertificateMetadataParser.php

		-
			message: "#^Cannot call method getResource\\(\\) on class\\-string\\|object\\.$#"
			count: 1
			path: src/Validator/Constraints/Resource/Mp4Validator.php

		-
			message: "#^Access to an undefined property Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\AddControllerTest\\:\\:\\$context\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Access to an undefined property Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\FollowRedirect\\\\AddTest\\:\\:\\$context\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Access to an undefined property Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\AddTest\\:\\:\\$context\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/AddTest.php

		-
			message: "#^Access to an undefined property Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\Origins\\\\FollowRedirect\\\\AddTest\\:\\:\\$context\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/Origins/FollowRedirect/AddTest.php
