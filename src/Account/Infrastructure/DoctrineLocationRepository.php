<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Account\Infrastructure;

use Cdn77\NxgApi\Account\Domain\DTO\AccountDatacenterLocation;
use Cdn77\NxgApi\Account\Domain\Repository\LocationRepository;
use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;

use function array_map;

final class DoctrineLocationRepository implements LocationRepository
{
    use EntityManagerConstructor;

    /** @return list<AccountDatacenterLocation> */
    public function findDatacenterLocationsForAccount(int $accountId): array
    {
        $result = $this->entityManager->getConnection()->fetchAllAssociative(
            <<<'PSQL'
SELECT DISTINCT
    l.city AS location_id,
    substring(p.description from 1 for 3) as city_code
FROM location l
JOIN pop p ON l.id = p.location_id
JOIN group_pop_relation gpr ON (gpr.pop_id = p.id)
JOIN resource r USING(group_id)
WHERE r.account_id = :accountId
ORDER BY city_code
PSQL
            ,
            ['accountId' => $accountId],
        );

        return array_map(
            static fn (array $location): AccountDatacenterLocation => AccountDatacenterLocation::fromArray($location),
            $result,
        );
    }
}
