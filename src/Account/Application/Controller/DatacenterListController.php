<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Account\Application\Controller;

use Cdn77\NxgApi\Account\Application\Payload\DatacentersSchema;
use Cdn77\NxgApi\Account\Domain\Query\FindAccountDatacenterLocations;
use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Domain\Bus\QueryBus;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class DatacenterListController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'account.datacenters.list';
    public const ROUTE_SUMMARY = 'List of all account Datacenter Locations';

    private PathGenerator $pathGenerator;

    private QueryBus $queryBus;

    private SerializerInterface $serializer;

    public function __construct(PathGenerator $pathGenerator, QueryBus $queryBus, SerializerInterface $serializer)
    {
        $this->pathGenerator = $pathGenerator;
        $this->queryBus = $queryBus;
        $this->serializer = $serializer;
    }

    /**
     * @Route(
     *     path="/account/{accountId}/datacenters",
     *     name=self::ROUTE_NAME,
     *     methods={Request::METHOD_GET}
     * )
     */
    public function execute(int $accountId): JsonResponse
    {
        $locations = $this->queryBus->handle(new FindAccountDatacenterLocations($accountId));

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(DatacentersSchema::fromAccountDatacenterLocationsList($locations), 'json'),
        );
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $get = new Operation([
            'tags' => [Tags::ACCOUNT],
            'summary' => self::ROUTE_SUMMARY,
            'parameters' => [
                new Parameter([
                    'name' => 'accountId',
                    'in' => 'path',
                    'required' => true,
                    'schema' => new Schema(['type' => Type::INTEGER]),
                ]),
            ],
            'responses' => new Responses([
                Response::HTTP_OK => new \cebe\openapi\spec\Response(
                    ['description' => 'Datacenters returned'],
                ),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['get' => $get])];
    }
}
