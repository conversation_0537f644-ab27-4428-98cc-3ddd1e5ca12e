<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Account\Application\Payload;

use Cdn77\NxgApi\Account\Domain\DTO\AccountDatacenterLocation;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

use function array_map;

final class DatacentersSchema
{
    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Account\Domain\DTO\AccountDatacenterLocation>")
     * @Serializer\Inline
     * @var list<AccountDatacenterLocationSchema>
     */
    public array $datacenters;

    /** @param list<AccountDatacenterLocationSchema> $datacenters */
    public function __construct(array $datacenters)
    {
        $this->datacenters = $datacenters;
    }

    /** @param list<AccountDatacenterLocation> $accountLocations */
    public static function fromAccountDatacenterLocationsList(array $accountLocations): self
    {
        return new self(
            array_map(
                static fn (
                    AccountDatacenterLocation $location,
                ): AccountDatacenterLocationSchema => AccountDatacenterLocationSchema::fromAccountDatacenterLocation(
                    $location,
                ),
                $accountLocations,
            ),
        );
    }
}
