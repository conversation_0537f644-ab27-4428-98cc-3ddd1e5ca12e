<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Account\Application\Payload;

use Cdn77\NxgApi\Account\Domain\DTO\AccountDatacenterLocation;

final class AccountDatacenterLocationSchema
{
    public const FIELD_CITY = 'city';
    public const FIELD_CITY_CODE = 'city_code';

    public string $city;

    public string $cityCode;

    public function __construct(string $city, string $cityCode)
    {
        $this->city = $city;
        $this->cityCode = $cityCode;
    }

    public static function fromAccountDatacenterLocation(AccountDatacenterLocation $accountDatacenterLocation): self
    {
        return new self(
            $accountDatacenterLocation->city,
            $accountDatacenterLocation->cityCode,
        );
    }
}
