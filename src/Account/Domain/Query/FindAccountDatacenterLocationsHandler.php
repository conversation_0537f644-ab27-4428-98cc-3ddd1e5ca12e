<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Account\Domain\Query;

use Cdn77\NxgApi\Account\Domain\DTO\AccountDatacenterLocation;
use Cdn77\NxgApi\Account\Domain\Repository\LocationRepository;
use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;

final class FindAccountDatacenterLocationsHandler implements QueryHandler
{
    private LocationRepository $locationRepository;

    public function __construct(LocationRepository $locationRepository)
    {
        $this->locationRepository = $locationRepository;
    }

    /** @return list<AccountDatacenterLocation> */
    public function handle(FindAccountDatacenterLocations $query): array
    {
        return $this->locationRepository->findDatacenterLocationsForAccount($query->accountId);
    }
}
