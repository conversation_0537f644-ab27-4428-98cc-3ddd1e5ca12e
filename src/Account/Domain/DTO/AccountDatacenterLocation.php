<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Account\Domain\DTO;

use Webmozart\Assert\Assert;

final class AccountDatacenterLocation
{
    public string $city;

    public string $cityCode;

    public function __construct(string $city, string $cityCode)
    {
        $this->city = $city;
        $this->cityCode = $cityCode;
    }

    /** @param array<string, string|bool> $location */
    public static function fromArray(array $location): self
    {
        Assert::keyExists($location, 'location_id');
        Assert::string($location['location_id']);
        Assert::keyExists($location, 'city_code');
        Assert::string($location['city_code']);

        return new self($location['location_id'], $location['city_code']);
    }
}
