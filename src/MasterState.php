<?php

declare(strict_types=1);

namespace Cdn77\MonMasterDetector;

use DateTimeImmutable;

final class MasterState
{
    private bool $master;

    private bool $forced;

    private DateTimeImmutable $checkStamp;

    public function __construct(bool $master, bool $forced, DateTimeImmutable $checkStamp)
    {
        $this->master = $master;
        $this->forced = $forced;
        $this->checkStamp = $checkStamp;
    }

    public function isMaster(): bool
    {
        return $this->master;
    }

    public function isForced(): bool
    {
        return $this->forced;
    }

    public function getCheckStamp(): DateTimeImmutable
    {
        return $this->checkStamp;
    }
}
