<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Command\LetsEncrypt;

use Cdn77\MonMasterDetector\MasterStateDetectorInterface;
use Cdn77\NxgApi\Entity\LetsEncrypt\Request;
use Cdn77\NxgApi\Service\LetsEncrypt\RenewalManager;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

use function array_map;
use function assert;
use function explode;
use function implode;
use function is_bool;
use function is_string;
use function Safe\date;

class RenewalCommand extends Command
{
    private const TIME_FORMAT = 'Y-m-d H:i:s';

    /** @var RenewalManager */
    private $renewalManager;

    /** @var MasterStateDetectorInterface */
    private $masterStateDetector;

    public function __construct(RenewalManager $renewalManager, MasterStateDetectorInterface $masterStateDetector)
    {
        $this->renewalManager = $renewalManager;
        $this->masterStateDetector = $masterStateDetector;

        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName('letsencrypt:renew')
            ->setDescription('Adds Lets Encrypt certificates to the queue for renewal.')
            ->addOption(
                'resourceIds',
                'r',
                InputOption::VALUE_OPTIONAL,
                'List of resource IDs separated by comma',
            )
            ->addOption(
                'asap',
                null,
                InputOption::VALUE_NONE,
                'Sets run_at time to past so that the requests are executed as soon as possible',
            );

        parent::configure();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln(date(self::TIME_FORMAT) . ' Starting renewal...');

        if (! $this->masterStateDetector->getState()->isMaster()) {
            $output->writeln(date(self::TIME_FORMAT) . ' I am not master. Exiting.');

            return 0;
        }

        $resourceIds = $this->getResourceIdsFromOption($input);
        if ($resourceIds !== []) {
            $runAsSoonAsPossible = $input->getOption('asap');
            assert(is_bool($runAsSoonAsPossible));
            $requestsAddedToQueue = $this->renewalManager->enqueueByResourceIds($resourceIds, $runAsSoonAsPossible);
        } else {
            $requestsAddedToQueue = $this->renewalManager->enqueueOldCertificates();
        }

        $this->writeResult($output, $requestsAddedToQueue);

        return 0;
    }

    /** @param Request[] $requestsAddedToQueue */
    private function writeResult(OutputInterface $output, iterable $requestsAddedToQueue): void
    {
        $queuedResourcesIds = $this->getQueuedResourcesIds($requestsAddedToQueue);

        $message = ' Resource IDs enqueued for renewal: ' . implode(',', $queuedResourcesIds);

        $output->writeln(date(self::TIME_FORMAT) . $message);
    }

    /** @return int[] */
    private function getResourceIdsFromOption(InputInterface $input): array
    {
        $resourceIds = $input->getOption('resourceIds');

        if ($resourceIds === null) {
            return [];
        }

        assert(is_string($resourceIds));
        $split = explode(',', $resourceIds);

        return array_map('intval', $split);
    }

    /**
     * @param Request[] $requestsAddedToQueue
     *
     * @return int[]
     */
    private function getQueuedResourcesIds(iterable $requestsAddedToQueue): array
    {
        $resourcesIds = [];

        foreach ($requestsAddedToQueue as $request) {
            $resourcesIds[] = $request->getResource()->getId();
        }

        return $resourcesIds;
    }
}
