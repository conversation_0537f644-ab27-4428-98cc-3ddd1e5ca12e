<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Command;

use function sprintf;

final class Font
{
    private const TEXT_DEFAULT = "\033[0m";
    private const TEXT_BOLD = "\033[1m";
    private const COLOR_GREEN = "\033[32m";
    private const COLOR_RED = "\033[31m";
    private const COLOR_YELLOW = "\033[33m";

    /** @param string|int $value */
    public static function red($value): string
    {
        return sprintf('%s%s%s', self::COLOR_RED, $value, self::TEXT_DEFAULT);
    }

    /** @param string|int $value */
    public static function green($value): string
    {
        return sprintf('%s%s%s%s', self::TEXT_BOLD, self::COLOR_GREEN, $value, self::TEXT_DEFAULT);
    }

    /** @param string|int $value */
    public static function yellow($value): string
    {
        return sprintf('%s%s%s%s', self::TEXT_BOLD, self::COLOR_YELLOW, $value, self::TEXT_DEFAULT);
    }

    /** @param string|int $value */
    public static function bold($value): string
    {
        return sprintf('%s%s%s', self::TEXT_BOLD, $value, self::TEXT_DEFAULT);
    }
}
