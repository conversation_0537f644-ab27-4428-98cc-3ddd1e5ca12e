<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Command;

use DateTimeImmutable;
use Symfony\Component\Console\Formatter\OutputFormatterStyle;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Output\OutputInterface;

use function Safe\date;
use function sprintf;
use function str_pad;

class OutputWriter
{
    private OutputInterface $output;

    public function __construct(OutputInterface $output)
    {
        $this->output = $output;

        $output->getFormatter()->setStyle('red_color', new OutputFormatterStyle('red', 'default'));
        $output->getFormatter()->setStyle('white_color', new OutputFormatterStyle(null));
        $output->getFormatter()->setStyle('green_color', new OutputFormatterStyle('green'));
    }

    public function message(string $message, int $numberOfNewLinesAfterMessage = 0): void
    {
        $this->output->writeln(sprintf('%s %s', date(DateTimeImmutable::ATOM), $message));

        $this->newLine($numberOfNewLinesAfterMessage);
    }

    public function messageRed(string $message, int $numberOfNewLinesAfterMessage = 0): void
    {
        $this->message(Font::red($message), $numberOfNewLinesAfterMessage);
    }

    public function messageYellow(string $message, int $numberOfNewLinesAfterMessage = 0): void
    {
        $this->message(Font::yellow($message), $numberOfNewLinesAfterMessage);
    }

    /**
     * @param array<string> $headers
     * @param array<mixed> $rows
     */
    public function table(string $headerTitle, array $headers, array $rows): void
    {
        $this->newLine();

        $table = new Table($this->output);
        $table
            ->setHeaderTitle($headerTitle)
            ->setHeaders($headers)
            ->setRows($rows);
        $table->render();

        $this->newLine();
    }

    public function separator(): void
    {
        $this->newLine();
        $this->output->writeln(str_pad('|', 60, '|'));
        $this->newLine();
    }

    private function newLine(int $numberOfNewLines = 1): void
    {
        for ($i = 0; $i < $numberOfNewLines; $i++) {
            $this->output->writeln("\n \n");
        }
    }
}
