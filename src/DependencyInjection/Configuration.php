<?php

declare(strict_types=1);

namespace Cdn77\MonMasterDetectorBundle\DependencyInjection;

use Symfony\Component\Config\Definition\Builder\TreeBuilder;
use Symfony\Component\Config\Definition\ConfigurationInterface;

class Configuration implements ConfigurationInterface
{
    public const TYPE_FILE = 'file';
    public const TYPE_CONSTANT = 'constant';
    public const TYPE_MUTABLE = 'mutable';
    private const TYPES = [self::TYPE_FILE, self::TYPE_CONSTANT, self::TYPE_MUTABLE];

    public function getConfigTreeBuilder(): TreeBuilder
    {
        $treeBuilder = new TreeBuilder('mon_master_detector');
        $rootNode = $treeBuilder->getRootNode();

        // @codingStandardsIgnoreStart
        $rootNode
            ->beforeNormalization()
                ->ifTrue(function (array $v) : bool {
                    return isset($v['type'])
                        && $v['type'] === self::TYPE_FILE
                        && !isset($v['path']);
                })
                ->thenInvalid('File type requires a path.')
                ->end()
            ->beforeNormalization()
                ->ifTrue(function (array $v) : bool {
                    return isset($v['type'])
                        && ($v['type'] === self::TYPE_CONSTANT || $v['type'] === self::TYPE_MUTABLE)
                        && (!isset($v['master']) || !isset($v['forced']));
                })
                ->thenInvalid('Constant or mutable types require a master & forced to be explicitly defined.')
                ->end()
            ->beforeNormalization()
                ->ifTrue(function (array $v) : bool {
                    return isset($v['type'])
                        && ($v['type'] === self::TYPE_CONSTANT || $v['type'] === self::TYPE_MUTABLE)
                        && isset($v['master'], $v['forced'])
                        && $v['forced'] === true
                        && $v['master'] === false;
                })
                ->thenInvalid('Setting forced to true implies master to be true.')
                ->end()
            ->children()
                ->scalarNode('type')
                    ->isRequired()
                    ->validate()
                        ->ifNotInArray(self::TYPES)
                        ->thenInvalid('Type should be one of: ' . implode(', ', self::TYPES) . '.')
                        ->end()
                    ->end()
                ->scalarNode('path')
                ->end()
                ->booleanNode('master')
                ->end()
                ->booleanNode('forced')
                ->end()
            ->end();
        // @codingStandardsIgnoreEnd

        return $treeBuilder;
    }
}
