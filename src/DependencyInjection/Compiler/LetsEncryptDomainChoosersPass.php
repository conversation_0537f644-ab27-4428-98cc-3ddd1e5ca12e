<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\DependencyInjection\Compiler;

use Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser\DomainChooserChain;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Reference;

class LetsEncryptDomainChoosersPass implements CompilerPassInterface
{
    private const TAG_NAME = 'letsencrypt_domain_chooser';

    public function process(ContainerBuilder $container): void
    {
        $chain = $container->getDefinition(DomainChooserChain::class);

        foreach ($container->findTaggedServiceIds(self::TAG_NAME) as $serviceId => $tags) {
            foreach ($tags as $tag) {
                $chain->addMethodCall('add', [new Reference($serviceId), $tag['priority'] ?? 0]);
            }
        }
    }
}
