<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\DependencyInjection\Compiler;

use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;

final class CacheLifetimePass implements CompilerPassInterface
{
    private const SERVICE_NAME = 'cache.adapter.redis';
    private const LIFETIME = 60 * 60 * 24 * 14; // 2 weeks

    public function process(ContainerBuilder $container): void
    {
        $redisAdapter = $container->getDefinition(self::SERVICE_NAME);
        $redisAdapter->setArgument(2, self::LIFETIME);
    }
}
