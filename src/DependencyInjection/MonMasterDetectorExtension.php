<?php

declare(strict_types=1);

namespace Cdn77\MonMasterDetectorBundle\DependencyInjection;

use Cdn77\MonMasterDetector\Authority\FileAuthorityDetector;
use Cdn77\MonMasterDetector\MasterStateDetectorInterface;
use Cdn77\MonMasterDetectorBundle\Detector\ConstantDetector;
use Cdn77\MonMasterDetectorBundle\Detector\MutableDetector;
use RuntimeException;
use Symfony\Component\DependencyInjection\Alias;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Definition;
use Symfony\Component\HttpKernel\DependencyInjection\Extension;

class MonMasterDetectorExtension extends Extension
{
    /** @param mixed[][] $configs */
    public function load(array $configs, ContainerBuilder $container): void
    {
        $configuration = new Configuration();
        $config = $this->processConfiguration($configuration, $configs);

        $this->resolveDetector($config, $container);
    }

    /** @param mixed[] $config */
    private function resolveDetector(array $config, ContainerBuilder $container): void
    {
        switch ($config['type']) {
            case Configuration::TYPE_FILE:
                $container->setDefinition(
                    FileAuthorityDetector::class,
                    (new Definition(FileAuthorityDetector::class))
                        ->setArgument('$path', $config['path'])
                        ->setPublic(true)
                );
                $container->setAlias(
                    MasterStateDetectorInterface::class,
                    new Alias(FileAuthorityDetector::class, true)
                );

                return;
            case Configuration::TYPE_CONSTANT:
                $container->setDefinition(
                    ConstantDetector::class,
                    (new Definition(ConstantDetector::class))
                        ->setArgument('$master', $config['master'])
                        ->setArgument('$forced', $config['forced'])
                        ->setPublic(true)
                );
                $container->setAlias(MasterStateDetectorInterface::class, new Alias(ConstantDetector::class, true));

                return;
            case Configuration::TYPE_MUTABLE:
                $container->setDefinition(
                    MutableDetector::class,
                    (new Definition(MutableDetector::class))
                        ->setArgument('$master', $config['master'])
                        ->setArgument('$forced', $config['forced'])
                        ->setPublic(true)
                );
                $container->setAlias(MasterStateDetectorInterface::class, new Alias(MutableDetector::class, true));

                return;
        }

        throw new RuntimeException('Unknown type.');
    }
}
