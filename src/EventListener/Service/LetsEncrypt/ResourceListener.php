<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\EventListener\Service\LetsEncrypt;

use Cdn77\NxgApi\Service\Event\ResourceChangedEvent;
use Cdn77\NxgApi\Service\Event\ResourceCreatedEvent;
use Cdn77\NxgApi\Service\LetsEncrypt\RequestManagerRequestSchedulerEvaluator;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class ResourceListener implements EventSubscriberInterface
{
    /** @var RequestManagerRequestSchedulerEvaluator */
    private $requestSchedulerEvaluator;

    /** @return string[] */
    public static function getSubscribedEvents(): array
    {
        return [
            ResourceCreatedEvent::class => 'onResourceCreated',
            ResourceChangedEvent::class => 'onResourceChanged',
        ];
    }

    public function __construct(RequestManagerRequestSchedulerEvaluator $requestSchedulerEvaluator)
    {
        $this->requestSchedulerEvaluator = $requestSchedulerEvaluator;
    }

    public function onResourceCreated(ResourceCreatedEvent $event): void
    {
        $this->requestSchedulerEvaluator->handleNewResource($event->getResource());
    }

    public function onResourceChanged(ResourceChangedEvent $event): void
    {
        $this->requestSchedulerEvaluator->handleChangedResource($event->getOld(), $event->getNew());
    }
}
