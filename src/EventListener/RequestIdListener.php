<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\EventListener;

use Cdn77\NxgApi\RequestId\RequestIdManager;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\KernelEvents;

class RequestIdListener implements EventSubscriberInterface
{
    /** @var RequestIdManager */
    private $requestIdManager;

    /** @return mixed[][] */
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::RESPONSE => ['onKernelResponse', 20],
        ];
    }

    public function __construct(RequestIdManager $requestIdManager)
    {
        $this->requestIdManager = $requestIdManager;
    }

    public function onKernelResponse(ResponseEvent $event): void
    {
        $event->getResponse()->headers->set('Request-Id', $this->requestIdManager->get());
    }
}
