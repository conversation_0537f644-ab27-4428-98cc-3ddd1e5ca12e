<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\EventListener;

use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\Event\TerminateEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Symfony\Component\HttpKernel\KernelEvents;

class InputOutputLoggerListener implements EventSubscriberInterface
{
    /** @var LoggerInterface */
    private $logger;

    /** @return mixed[][] */
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => ['onKernelRequest', 500],
            KernelEvents::TERMINATE => ['onKernelTerminate', -500],
        ];
    }

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function onKernelRequest(RequestEvent $event): void
    {
        if ($event->getRequestType() !== HttpKernelInterface::MAIN_REQUEST) {
            return;
        }

        $this->logger->info(
            'HTTP request received.',
            [
                'payload' => $event->getRequest(),
            ],
        );
    }

    public function onKernelTerminate(TerminateEvent $event): void
    {
        $this->logger->info(
            'HTTP response sent.',
            [
                'payload' => $event->getResponse(),
            ],
        );
    }
}
