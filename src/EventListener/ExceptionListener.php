<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\EventListener;

use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\KernelEvents;

use function sprintf;

class ExceptionListener implements EventSubscriberInterface
{
    /** @var LoggerInterface */
    private $logger;

    /** @return mixed[][] */
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::EXCEPTION => ['onKernelException', -200],
        ];
    }

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function onKernelException(ExceptionEvent $event): void
    {
        $exception = $event->getThrowable();

        $this->logger->critical(
            sprintf(
                'Unhandled %s: %s at %s:%d',
                $exception::class,
                $exception->getMessage(),
                $exception->getFile(),
                $exception->getLine(),
            ),
            ['exception' => $exception],
        );

        $response = new Response('', Response::HTTP_INTERNAL_SERVER_ERROR);

        $event->setResponse($response);
    }
}
