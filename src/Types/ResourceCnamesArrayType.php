<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Types;

use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\Type;

use function explode;
use function implode;
use function trim;

class ResourceCnamesArrayType extends Type
{
    public const NAME = 'resource_cnames_array';
    public const SEPARATOR = ' ';

    public function getName(): string
    {
        return self::NAME;
    }

    /** @inheritDoc */
    public function convertToDatabaseValue($value, AbstractPlatform $platform): string|null
    {
        if ($value === null) {
            return null;
        }

        if ($value === []) {
            return '';
        }

        return implode(self::SEPARATOR, $value);
    }

    /**
     * @return string[]|null
     *
     * @inheritDoc
     */
    public function convertToPHPValue($value, AbstractPlatform $platform): array|null
    {
        if ($value === null) {
            return null;
        }

        if ($value === '') {
            return [];
        }

        return explode(self::SEPARATOR, trim($value));
    }

    /** @param mixed[] $fieldDeclaration */
    public function getSQLDeclaration(array $fieldDeclaration, AbstractPlatform $platform): string
    {
        return $platform->getClobTypeDeclarationSQL($fieldDeclaration);
    }
}
