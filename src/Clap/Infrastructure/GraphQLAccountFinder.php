<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Clap\Infrastructure;

use Cdn77\NxgApi\Clap\Domain\AccountFinder;
use Cdn77\NxgApi\Clap\Domain\Client;
use RuntimeException;

use function array_merge;
use function count;
use function sprintf;

final readonly class GraphQLAccountFinder implements AccountFinder
{
    private const VIP_RATING = 'vip';
    private const TOP_RATING = 'top';
    public const MIN_ACCOUNTS = 50; // In october 2024 we have about 100 VIP and TOP accounts, so half is safe minimum

    public function __construct(private Client $client, private int $minimumAccounts = self::MIN_ACCOUNTS)
    {
    }

    /** @return array<int> */
    public function findVipAndTop(): array
    {
        $query = sprintf('
            query Customers { 
                %s: customers(filter: { rating: Vip }) { edges { node { oldId fullName } } } 
                %s: customers(filter: { rating: Top }) { edges { node { oldId fullName } } }
            }
        ', self::VIP_RATING, self::TOP_RATING);

        $response = $this->client->query($query);

        $accounts = $this->getAccountIdsFromResponse($response);

        if (count($accounts) < $this->minimumAccounts) {
            throw new RuntimeException('Not enough accounts found');
        }

        return $this->getAccountIdsFromResponse($response);
    }

    /**
     * @param array{data: array<mixed>} $response
     *
     * @return array<int>
     */
    private function getAccountIdsFromResponse(array $response): array
    {
        return array_merge(
            $this->getIds($response['data'][self::VIP_RATING]['edges']),
            $this->getIds($response['data'][self::TOP_RATING]['edges']),
        );
    }

    /**
     * @param array<int, array<string, array<string, string>>> $edges
     *
     * @return array<int>
     */
    private function getIds(array $edges): array
    {
        $ids = [];

        foreach ($edges as $edge) {
            $ids[] = (int) $edge['node']['oldId'];
        }

        return $ids;
    }
}
