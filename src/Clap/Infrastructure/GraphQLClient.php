<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Clap\Infrastructure;

use Cdn77\NxgApi\Clap\Domain\Client;
use Cdn77\NxgApi\Exception\Runtime;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Throwable;
use <PERSON><PERSON>zart\Assert\Assert;

use function array_key_exists;
use function Safe\json_encode;
use function sprintf;

final readonly class GraphQLClient implements Client
{
    public function __construct(
        private HttpClientInterface $client,
        private string $graphqlUrl,
        private string $bearerToken,
    ) {
    }

    /** @return array{data: array<mixed>} */
    public function query(string $query): array
    {
        try {
            $response = $this->client->request('POST', $this->graphqlUrl, [
                'headers' => $this->buildHeaders(),
                'json' => ['query' => $query],
            ]);
        } catch (Throwable $e) {
            throw new Runtime(sprintf('Failed to query GraphQL API. Exception: %s', $e->getMessage()));
        }

        if ($response->getStatusCode() !== 200) {
            throw new Runtime(sprintf('Failed to query GraphQL API. Status code: %d', $response->getStatusCode()));
        }

        $data = $response->toArray();

        if (array_key_exists('errors', $data)) {
            throw new Runtime(sprintf('Failed to query GraphQL API. Errors: %s', json_encode($data['errors'])));
        }

        Assert::keyExists($data, 'data');

        return $data;
    }

    /** @return array<string, string> */
    private function buildHeaders(): array
    {
        return [
            'Authorization' => 'Bearer ' . $this->bearerToken,
            'Content-Type' => 'application/json',
        ];
    }
}
