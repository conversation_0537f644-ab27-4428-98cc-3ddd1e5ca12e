<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Internal\Server;

use Cdn77\NxgApi\Entity\Legacy\Server;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

use function array_map;

class ServerListSchema
{
    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Schema\Internal\Server\ServerDetailSchema>")
     * @Serializer\Inline
     * @var list<ServerDetailSchema>
     */
    public array $servers = [];

    /** @param list<ServerDetailSchema> $servers */
    private function __construct(array $servers)
    {
        $this->servers = $servers;
    }

    /** @param list<Server> $servers */
    public static function fromServers(array $servers): self
    {
        return new self(
            array_map(
                static fn (Server $server): ServerDetailSchema => ServerDetailSchema::fromServer($server),
                $servers,
            ),
        );
    }
}
