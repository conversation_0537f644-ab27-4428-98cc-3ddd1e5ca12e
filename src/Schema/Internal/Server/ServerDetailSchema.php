<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Internal\Server;

use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Server\Application\Payload\ServerIpv6AddressesSchema;

class ServerDetailSchema
{
    public int $uid;

    public ServerIpAddressesDetail $ipAddresses;

    public ServerIpv6AddressesSchema $ipv6Addresses;

    public int|null $maxBandwidth;

    public int|null $maxCacheSize;

    public int $keysSize;

    public int $workerCount;

    public int $driveCount;

    public ServerPopDetail|null $pop;

    private function __construct(
        int $uid,
        ServerIpAddressesDetail $ipAddresses,
        ServerIpv6AddressesSchema $ipv6Addresses,
        int|null $maxBandwidth,
        int|null $maxCacheSize,
        int $keysSize,
        int $workerCount,
        int $driveCount,
        ServerPopDetail|null $pop,
    ) {
        $this->uid = $uid;
        $this->ipAddresses = $ipAddresses;
        $this->ipv6Addresses = $ipv6Addresses;
        $this->maxBandwidth = $maxBandwidth;
        $this->maxCacheSize = $maxCacheSize;
        $this->keysSize = $keysSize;
        $this->workerCount = $workerCount;
        $this->driveCount = $driveCount;
        $this->pop = $pop;
    }

    public static function fromServer(Server $server): ServerDetailSchema
    {
        return new self(
            $server->getUid(),
            ServerIpAddressesDetail::fromServer($server),
            ServerIpv6AddressesSchema::fromServer($server),
            $server->getMaxBandwidth(),
            $server->getMaxCacheSize(),
            $server->getKeysSize(),
            $server->getWorkerCount(),
            $server->getDriveCount(),
            new ServerPopDetail(
                $server->getPop()->getId(),
                new ServerPopLocationDetail($server->getPop()->getLocation()->getId()),
            ),
        );
    }
}
