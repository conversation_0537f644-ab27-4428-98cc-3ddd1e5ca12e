<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Internal\Server;

use Cdn77\NxgApi\Entity\Legacy\Ip;
use Cdn77\NxgApi\Entity\Legacy\Server;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

use function array_map;
use function array_values;

class ServerIpAddressesDetail
{
    public string|null $primary;

    /**
     * @Serializer\Type("array<string>")
     * @var list<string>
     */
    public array $secondary = [];

    /** @param array<string> $secondary */
    private function __construct(string|null $primary, array $secondary)
    {
        $this->primary = $primary;
        $this->secondary = $secondary;
    }

    public static function fromServer(Server $server): self
    {
        $primary = $server->getPrimaryIp();
        $secondary = $server->getSecondaryIps();

        return new self(
            $primary?->getIp(),
            array_values(
                array_map(
                    static fn (Ip $ip): string => $ip->getIp(),
                    $secondary,
                ),
            ),
        );
    }
}
