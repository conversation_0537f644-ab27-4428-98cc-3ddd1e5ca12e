<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Internal\Resource\Servers;

use JMS\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

class ResourceDetail
{
    /**
     * @Serializer\Type("int")
     * @Assert\NotBlank
     * @var int
     */
    private $id;

    public function __construct(int $id)
    {
        $this->id = $id;
    }

    public function getId(): int
    {
        return $this->id;
    }
}
