<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Internal\Ip\Status;

use Cdn77\NxgApi\Core\Application\Payload\ServerStatusSchema;
use J<PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

class IpStatusSchema
{
    /**
     * @Serializer\Type("string")
     * @Assert\NotNull
     * @var string
     */
    private $ip;

    /**
     * @Serializer\Type("bool")
     * @Assert\NotNull
     * @var bool
     */
    private $up;

    /**
     * @Serializer\Type("bool")
     * @Assert\NotNull
     * @var bool
     */
    private $primary;

    /**
     * @Assert\NotBlank
     * @Serializer\Type("Cdn77\NxgApi\Core\Application\Payload\ServerStatusSchema")
     * @Assert\Valid
     * @var ServerStatusSchema
     */
    private $server;

    public function __construct(string $ip, bool $up, bool $primary, ServerStatusSchema $server)
    {
        $this->ip = $ip;
        $this->up = $up;
        $this->primary = $primary;
        $this->server = $server;
    }

    public function getIp(): string
    {
        return $this->ip;
    }

    public function isUp(): bool
    {
        return $this->up;
    }

    public function isPrimary(): bool
    {
        return $this->primary;
    }

    public function getServer(): ServerStatusSchema
    {
        return $this->server;
    }
}
