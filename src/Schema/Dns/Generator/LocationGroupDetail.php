<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Dns\Generator;

use <PERSON><PERSON>\Serializer\Annotation as Serializer;

class LocationGroupDetail
{
    /**
     * @Serializer\Type("int")
     * @var int
     */
    private $id;

    /**
     * @Serializer\Type("bool")
     * @var bool
     */
    private $backup;

    /**
     * @Serializer\Type("array<int>")
     * @var int[]
     */
    private $popIds;

    /** @param int[] $popIds */
    public function __construct(int $id, bool $backup, array $popIds)
    {
        $this->id = $id;
        $this->backup = $backup;
        $this->popIds = $popIds;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function hasBackup(): bool
    {
        return $this->backup;
    }

    /** @return int[] */
    public function getPopIds(): array
    {
        return $this->popIds;
    }
}
