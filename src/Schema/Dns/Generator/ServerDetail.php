<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Dns\Generator;

use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

class ServerDetail
{
    /**
     * @Serializer\Type("int")
     * @var int
     */
    private $id;

    /**
     * @Serializer\Type("int")
     * @var int
     */
    private $oldId;

    /**
     * @Serializer\Type("bool")
     * @var bool
     */
    private $paused;

    /**
     * @Serializer\Type("bool")
     * @var bool|null
     */
    private $forced;

    /**
     * @Serializer\Type("int")
     * @var int|null
     */
    private $popId;

    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Schema\Dns\Generator\ServerIpDetail>")
     * @Assert\Valid
     * @var ServerIpDetail[]
     */
    private $ipAddresses;

    /**
     * @Serializer\Type("Cdn77\NxgApi\Schema\Dns\Generator\ServerLastDownDetail")
     * @Assert\Valid
     * @var ServerLastDownDetail|null
     */
    private $lastDown;

    /** @param ServerIpDetail[] $ipAddresses */
    public function __construct(
        int $id,
        int $oldId,
        bool $paused,
        bool|null $forced,
        int|null $popId,
        array $ipAddresses,
        ServerLastDownDetail|null $lastDown,
    ) {
        $this->id = $id;
        $this->oldId = $oldId;
        $this->paused = $paused;
        $this->forced = $forced;
        $this->popId = $popId;
        $this->ipAddresses = $ipAddresses;
        $this->lastDown = $lastDown;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getOldId(): int
    {
        return $this->oldId;
    }

    public function isPaused(): bool
    {
        return $this->paused;
    }

    public function getForced(): bool|null
    {
        return $this->forced;
    }

    public function getPopId(): int|null
    {
        return $this->popId;
    }

    /** @return ServerIpDetail[] */
    public function getIpAddresses(): array
    {
        return $this->ipAddresses;
    }

    public function getLastDown(): ServerLastDownDetail|null
    {
        return $this->lastDown;
    }
}
