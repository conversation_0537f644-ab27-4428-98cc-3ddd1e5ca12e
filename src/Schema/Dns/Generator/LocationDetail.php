<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Dns\Generator;

use <PERSON><PERSON>\Serializer\Annotation as Serializer;

class LocationDetail
{
    /**
     * @Serializer\Type("string")
     * @var string
     */
    private $id;

    /**
     * @Serializer\Type("float")
     * @var float
     */
    private $latitude;

    /**
     * @Serializer\Type("float")
     * @var float
     */
    private $longitude;

    /**
     * @Serializer\Type("string")
     * @var string
     */
    private $continent;

    public function __construct(string $id, float $latitude, float $longitude, string $continent)
    {
        $this->id = $id;
        $this->latitude = $latitude;
        $this->longitude = $longitude;
        $this->continent = $continent;
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getLatitude(): float
    {
        return $this->latitude;
    }

    public function getLongitude(): float
    {
        return $this->longitude;
    }

    public function getContinent(): string
    {
        return $this->continent;
    }
}
