<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Dns\Generator;

use <PERSON><PERSON>\Serializer\Annotation as Serializer;

class CustomLocationDetail
{
    /**
     * @Serializer\Type("int")
     * @var int
     */
    private $resourceId;

    /**
     * @Serializer\Type("int")
     * @var int
     */
    private $groupId;

    /**
     * @Serializer\Type("array<int>")
     * @var int[]
     */
    private $popIds;

    /** @param int[] $popIds */
    public function __construct(int $resourceId, int $groupId, array $popIds)
    {
        $this->resourceId = $resourceId;
        $this->groupId = $groupId;
        $this->popIds = $popIds;
    }

    public function getResourceId(): int
    {
        return $this->resourceId;
    }

    public function getGroupId(): int
    {
        return $this->groupId;
    }

    /** @return int[] */
    public function getPopIds(): array
    {
        return $this->popIds;
    }
}
