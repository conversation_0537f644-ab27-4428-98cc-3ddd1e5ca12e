<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Dns\Generator;

use DateTimeImmutable;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

class ServerLastDownDetail
{
    /**
     * @Serializer\Type("DateTimeImmutable")
     * @var DateTimeImmutable
     */
    private $lastDownAt;

    /**
     * @Serializer\Type("string")
     * @var string
     */
    private $reason;

    public function __construct(DateTimeImmutable $lastDownAt, string $reason)
    {
        $this->lastDownAt = $lastDownAt;
        $this->reason = $reason;
    }

    public function getLastDownAt(): DateTimeImmutable
    {
        return $this->lastDownAt;
    }

    public function getReason(): string
    {
        return $this->reason;
    }
}
