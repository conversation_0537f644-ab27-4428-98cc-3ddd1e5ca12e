<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Dns\Generator;

use <PERSON><PERSON>\Serializer\Annotation as Serializer;

class PopDetail
{
    /**
     * @Serializer\Type("int")
     * @var int
     */
    private $id;

    /**
     * @Serializer\Type("string")
     * @var string|null
     */
    private $description;

    /**
     * @Serializer\Type("bool")
     * @var bool
     */
    private $backup;

    /**
     * @Serializer\Type("string")
     * @var string
     */
    private $locationId;

    public function __construct(int $id, string|null $description, bool $backup, string $locationId)
    {
        $this->id = $id;
        $this->description = $description;
        $this->backup = $backup;
        $this->locationId = $locationId;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getDescription(): string|null
    {
        return $this->description;
    }

    public function hasBackup(): bool
    {
        return $this->backup;
    }

    public function getLocationId(): string
    {
        return $this->locationId;
    }
}
