<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Dns\Generator;

use DateTimeImmutable;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

class SnapshotSchema
{
    /**
     * @Serializer\Type("DateTimeImmutable")
     * @var DateTimeImmutable
     */
    private $snapshotTime;

    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Schema\Dns\Generator\ServerDetail>")
     * @Assert\Valid
     * @var ServerDetail[]
     */
    private $servers;

    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Schema\Dns\Generator\PopDetail>")
     * @Assert\Valid
     * @var PopDetail[]
     */
    private $pops;

    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Schema\Dns\Generator\LocationDetail>")
     * @Assert\Valid
     * @var LocationDetail[]
     */
    private $locations;

    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Schema\Dns\Generator\LocationGroupDetail>")
     * @Assert\Valid
     * @var LocationGroupDetail[]
     */
    private $locationGroups;

    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Schema\Dns\Generator\ResourceDetail>")
     * @Assert\Valid
     * @var ResourceDetail[]
     */
    private $resources;

    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Schema\Dns\Generator\CustomLocationDetail>")
     * @Assert\Valid
     * @var CustomLocationDetail[]
     */
    private $customLocations;

    /**
     * @param ServerDetail[]         $servers
     * @param PopDetail[]            $pops
     * @param LocationDetail[]       $locations
     * @param LocationGroupDetail[]  $locationGroups
     * @param ResourceDetail[]       $resources
     * @param CustomLocationDetail[] $customLocations
     */
    public function __construct(
        DateTimeImmutable $snapshotTime,
        array $servers,
        array $pops,
        array $locations,
        array $locationGroups,
        array $resources,
        array $customLocations,
    ) {
        $this->snapshotTime = $snapshotTime;
        $this->servers = $servers;
        $this->pops = $pops;
        $this->locations = $locations;
        $this->locationGroups = $locationGroups;
        $this->resources = $resources;
        $this->customLocations = $customLocations;
    }

    public function getSnapshotTime(): DateTimeImmutable
    {
        return $this->snapshotTime;
    }

    /** @return ServerDetail[] */
    public function getServers(): array
    {
        return $this->servers;
    }

    /** @return PopDetail[] */
    public function getPops(): array
    {
        return $this->pops;
    }

    /** @return LocationDetail[] */
    public function getLocations(): array
    {
        return $this->locations;
    }

    /** @return LocationGroupDetail[] */
    public function getLocationGroups(): array
    {
        return $this->locationGroups;
    }

    /** @return ResourceDetail[] */
    public function getResources(): array
    {
        return $this->resources;
    }

    /** @return CustomLocationDetail[] */
    public function getCustomLocations(): array
    {
        return $this->customLocations;
    }
}
