<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Dns\Generator;

use <PERSON><PERSON>\Serializer\Annotation as Serializer;

class ServerIpDetail
{
    /**
     * @Serializer\Type("string")
     * @var string
     */
    private $ip;

    /**
     * @Serializer\Type("bool")
     * @var bool
     */
    private $primary;

    /**
     * @Serializer\Type("bool")
     * @var bool
     */
    private $up;

    /**
     * @Serializer\Type("array<int>")
     * @var int[]
     */
    private $assignedAccountIds;

    /** @param int[] $assignedAccountIds */
    public function __construct(string $ip, bool $primary, bool $up, array $assignedAccountIds)
    {
        $this->ip = $ip;
        $this->primary = $primary;
        $this->up = $up;
        $this->assignedAccountIds = $assignedAccountIds;
    }

    public function getIp(): string
    {
        return $this->ip;
    }

    public function getPrimary(): bool
    {
        return $this->primary;
    }

    public function getUp(): bool
    {
        return $this->up;
    }

    /** @return int[] */
    public function getAssignedAccountIds(): array
    {
        return $this->assignedAccountIds;
    }
}
