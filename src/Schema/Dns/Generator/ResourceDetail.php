<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Dns\Generator;

use <PERSON><PERSON>\Serializer\Annotation as Serializer;

class ResourceDetail
{
    /**
     * @Serializer\Type("int")
     * @var int
     */
    private $id;

    /**
     * @Serializer\Type("int")
     * @var int
     */
    private $accountId;

    /**
     * @Serializer\Type("int")
     * @var int
     */
    private $groupId;

    public function __construct(int $id, int $accountId, int $groupId)
    {
        $this->id = $id;
        $this->accountId = $accountId;
        $this->groupId = $groupId;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getAccountId(): int
    {
        return $this->accountId;
    }

    public function getGroupId(): int
    {
        return $this->groupId;
    }
}
