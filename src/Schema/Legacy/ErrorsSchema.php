<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy;

use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Stringable;

use function count;

class ErrorsSchema
{
    public const FIELD_ERRORS = 'errors';

    private const DESERIALIZATION_FAILED_MESSAGE = 'Request deserialization failed. Check the endpoint documentation.';

    /**
     * @Serializer\Type("array<string>")
     * @var array<string|Stringable>
     */
    private $errors = [];

    public static function getSchemaSpec(string $example): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                self::FIELD_ERRORS => new Schema([
                    'type' => Type::ARRAY,
                    'items' => new Schema(['type' => Type::STRING, 'example' => $example]),
                ]),
            ],
        ]);
    }

    /** @param array<string|Stringable> $errors */
    public function __construct(array $errors = [])
    {
        foreach ($errors as $error) {
            $this->addError($error);
        }
    }

    public static function deserializationFailed(): self
    {
        return new self([self::DESERIALIZATION_FAILED_MESSAGE]);
    }

    /** @param string|Stringable $error */
    public function addError($error): void
    {
        $this->errors[] = $error;
    }

    /** @return array<string|Stringable> */
    public function getErrors(): array
    {
        return $this->errors;
    }

    public function hasErrors(): bool
    {
        return count($this->errors) > 0;
    }

    public function getErrorsCount(): int
    {
        return count($this->errors);
    }
}
