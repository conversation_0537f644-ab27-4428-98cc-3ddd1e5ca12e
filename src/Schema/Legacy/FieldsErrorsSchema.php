<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy;

use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Stringable;

class FieldsErrorsSchema extends ErrorsSchema
{
    /**
     * @Serializer\Type("array<string, array<string>>")
     * @var array<string, array<string|Stringable>>
     */
    private $fields;

    /**
     * @param array<string|Stringable> $errors
     * @param array<string, array<string|Stringable>> $fields
     */
    public function __construct(array $errors = [], array $fields = [])
    {
        parent::__construct($errors);

        foreach ($fields as $fieldName => $fieldErrors) {
            foreach ($fieldErrors as $fieldError) {
                $this->addField($fieldName, $fieldError);
            }
        }
    }

    /** @return array<string, array<string|Stringable>> */
    public function getFields(): array
    {
        return $this->fields ?? [];
    }

    /** @param string|Stringable $error */
    public function addField(string $name, $error): void
    {
        if (! isset($this->fields[$name])) {
            $this->fields[$name] = [];
        }

        $this->fields[$name][] = $error;
    }
}
