<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources;

use J<PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

class ResourceDetail
{
    /**
     * @Serializer\Type("Cdn77\NxgApi\Schema\Legacy\Resources\ResourceDetailInfo")
     * @Assert\Valid
     */
    private ResourceDetailInfo $cdnResource;

    public function __construct(ResourceDetailInfo $resourceDetailInfo)
    {
        $this->cdnResource = $resourceDetailInfo;
    }

    public function getResource(): ResourceDetailInfo
    {
        return $this->cdnResource;
    }
}
