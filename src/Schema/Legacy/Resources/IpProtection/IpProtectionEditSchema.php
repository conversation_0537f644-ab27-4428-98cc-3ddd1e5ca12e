<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources\IpProtection;

use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use <PERSON>ymfony\Component\Validator\Constraints as Assert;

class IpProtectionEditSchema
{
    public const TYPE_WHITELIST = 'whitelist';
    public const TYPE_BLACKLIST = 'blacklist';
    public const TYPES = [self::TYPE_WHITELIST, self::TYPE_BLACKLIST];

    /**
     * @Serializer\Type("string")
     * @Assert\Choice(
     *     {"whitelist", "blacklist"},
     *     message="The value should be either whitelist or blacklist.",
     *     strict=true
     * )
     * @var string|null
     */
    private $type;

    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Schema\Legacy\Resources\IpProtection\IpProtectionAddressDetail>")
     * @Assert\Valid
     * @var IpProtectionAddressDetail[]|null
     */
    private $addresses;

    /** @param IpProtectionAddressDetail[]|null $addresses */
    public function __construct(string|null $type, array|null $addresses)
    {
        $this->type = $type;
        $this->addresses = $addresses;
    }

    public function getType(): string|null
    {
        return $this->type;
    }

    /** @return IpProtectionAddressDetail[]|null */
    public function getAddresses(): array|null
    {
        return $this->addresses;
    }
}
