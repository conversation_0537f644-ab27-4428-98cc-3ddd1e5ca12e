<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources\IpProtection;

use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use <PERSON>ymfony\Component\Validator\Constraints as Assert;

class IpProtectionAddSchema
{
    public const TYPE_WHITELIST = 'whitelist';
    public const TYPE_BLACKLIST = 'blacklist';
    public const TYPES = [self::TYPE_WHITELIST, self::TYPE_BLACKLIST];

    /**
     * @Assert\NotBlank
     * @Serializer\Type("string")
     * @Assert\Choice(
     *     {"whitelist", "blacklist"},
     *     message="The value should be either whitelist or blacklist.",
     *     strict=true
     * )
     * @var string
     */
    private $type;

    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Schema\Legacy\Resources\IpProtection\IpProtectionAddressDetail>")
     * @Assert\NotNull
     * @Assert\Valid
     * @var IpProtectionAddressDetail[]
     */
    private $addresses;

    /** @param IpProtectionAddressDetail[] $addresses */
    public function __construct(string $type, array $addresses)
    {
        $this->type = $type;
        $this->addresses = $addresses;
    }

    public function getType(): string
    {
        return $this->type;
    }

    /** @return IpProtectionAddressDetail[] */
    public function getAddresses(): array
    {
        return $this->addresses;
    }
}
