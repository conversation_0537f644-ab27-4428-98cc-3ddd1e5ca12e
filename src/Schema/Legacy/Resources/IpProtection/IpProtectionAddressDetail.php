<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources\IpProtection;

use Cdn77\NxgApi\Validator\Constraints as CustomAssert;
use JMS\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

class IpProtectionAddressDetail
{
    /**
     * @Serializer\Type("string")
     * @Assert\NotBlank
     * @CustomAssert\Ip\Cidr
     * @Assert\NotIdenticalTo("0.0.0.0/0")
     * @var string
     */
    private $network;

    public function __construct(string $network)
    {
        $this->network = $network;
    }

    public function getNetwork(): string
    {
        return $this->network;
    }
}
