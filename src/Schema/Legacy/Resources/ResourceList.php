<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use <PERSON>ymfony\Component\Validator\Constraints as Assert;

use function array_map;

class ResourceList
{
    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Schema\Legacy\Resources\ResourceDetailInfo>")
     * @Serializer\Inline
     * @Assert\Valid
     * @var array<ResourceDetailInfo>
     */
    public array $resources = [];

    /** @param array<ResourceDetailInfo> $resources */
    public function __construct(array $resources = [])
    {
        $this->resources = $resources;
    }

    /** @param array<CdnResource> $resources */
    public static function fromResources(array $resources): self
    {
        return new self(array_map(
            static fn (CdnResource $resource): ResourceDetailInfo => ResourceDetailInfo::fromResource($resource),
            $resources,
        ));
    }
}
