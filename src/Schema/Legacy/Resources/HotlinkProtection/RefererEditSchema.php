<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources\HotlinkProtection;

use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use <PERSON><PERSON>fony\Component\Validator\Constraints as Assert;

class RefererEditSchema
{
    /**
     * @Serializer\Type("string")
     * @Assert\Choice({"whitelist", "blacklist"}, min=1, max=1, strict=true)
     * @var string|null
     */
    private $type;

    /**
     * @Serializer\Type("bool")
     * @var bool|null
     */
    private $denyEmpty;

    /**
     * @Serializer\Type(
     *     "array<Cdn77\NxgApi\Schema\Legacy\Resources\HotlinkProtection\RefererAddressDetail>"
     * )
     * @Assert\Count(max=300)
     * @Assert\Valid
     * @var RefererAddressDetail[]|null
     */
    private $addresses;

    /** @param RefererAddressDetail[]|null $addresses */
    public function __construct(string|null $type, bool|null $denyEmpty, array|null $addresses)
    {
        $this->type = $type;
        $this->denyEmpty = $denyEmpty;
        $this->addresses = $addresses;
    }

    public function getType(): string|null
    {
        return $this->type;
    }

    public function isEmptyDenied(): bool|null
    {
        return $this->denyEmpty;
    }

    /** @return RefererAddressDetail[]|null */
    public function getAddresses(): array|null
    {
        return $this->addresses;
    }
}
