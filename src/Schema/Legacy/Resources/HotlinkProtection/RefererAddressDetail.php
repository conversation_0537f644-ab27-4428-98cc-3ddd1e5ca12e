<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources\HotlinkProtection;

use Cdn77\NxgApi\Validator\Constraints as CustomAssert;
use J<PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

class RefererAddressDetail
{
    /**
     * @Serializer\Type("string")
     * @Assert\NotNull
     * @CustomAssert\Any(
     *     message="Address should be either a domain or IP.",
     *     {
     *         @CustomAssert\Resource\Referer\DomainFormat,
     *         @Assert\Ip(version="4")
     *     }
     * )
     * @var string
     */
    private $domain;

    public function __construct(string $domain)
    {
        $this->domain = $domain;
    }

    public function getDomain(): string
    {
        return $this->domain;
    }
}
