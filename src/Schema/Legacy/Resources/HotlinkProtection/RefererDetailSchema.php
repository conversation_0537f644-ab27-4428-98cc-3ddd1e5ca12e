<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources\HotlinkProtection;

use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use <PERSON>ymfony\Component\Validator\Constraints as Assert;

class RefererDetailSchema
{
    /**
     * @Serializer\Type("string")
     * @Assert\NotBlank
     * @Assert\Choice({"whitelist", "blacklist"}, min=1, max=1, strict=true)
     * @var string
     */
    private $type;

    /**
     * @Serializer\Type("bool")
     * @Assert\NotNull
     * @var bool
     */
    private $denyEmpty;

    /**
     * @Serializer\Type(
     *     "array<Cdn77\NxgApi\Schema\Legacy\Resources\HotlinkProtection\RefererAddressDetail>"
     * )
     * @Assert\NotNull
     * @Assert\Count(max=300)
     * @Assert\Valid
     * @var RefererAddressDetail[]
     */
    private $addresses = [];

    /** @param RefererAddressDetail[] $addresses */
    public function __construct(string $type, bool $denyEmpty, array $addresses)
    {
        $this->type = $type;
        $this->denyEmpty = $denyEmpty;
        $this->addresses = $addresses;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function isEmptyDenied(): bool
    {
        return $this->denyEmpty;
    }

    /** @return RefererAddressDetail[] */
    public function getAddresses(): array
    {
        return $this->addresses;
    }
}
