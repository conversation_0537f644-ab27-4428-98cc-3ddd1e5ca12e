<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources\Ssl;

use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Exception\InvalidArgument;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use DateTimeImmutable;
use J<PERSON>\Serializer\Annotation as Serializer;

use function in_array;

final class CertificatesCertificateItem
{
    public const FIELD_DOMAINS = 'domains';
    public const FIELD_EXPIRES_AT = 'expires_at';
    public const FIELD_RESOURCE = 'resource';
    public const FIELD_TYPE = 'type';

    public const TYPE_CUSTOM = 'custom';
    public const TYPE_INSTANT_SSL = 'instant_ssl';

    public const TYPES = [
        self::TYPE_CUSTOM,
        self::TYPE_INSTANT_SSL,
    ];

    public CertificatesCertificateResourceItem $resource;

    public string $type;

    /** @Serializer\Type("DateTimeImmutable<'Y-m-d\TH:i:s\Z'>") */
    public DateTimeImmutable|null $expiresAt;

    /**
     * @Serializer\Type("array<string>")
     * @var list<string>|null
     */
    public array|null $domains;

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                self::FIELD_RESOURCE => CertificatesCertificateResourceItem::getSchemaSpec(),
                self::FIELD_TYPE => new Schema(['type' => Type::STRING, 'enum' => self::TYPES]),
                self::FIELD_EXPIRES_AT => new Schema(['type' => Type::STRING, 'format' => 'datetime']),
                self::FIELD_DOMAINS => new Schema([
                    'type' => Type::ARRAY,
                    'items' => new Schema([
                        'type' => Type::STRING,
                    ]),
                ]),
            ],
        ]);
    }

    /** @param list<string>|null $domains */
    public function __construct(
        CertificatesCertificateResourceItem $resource,
        string $type,
        DateTimeImmutable $expiresAt,
        array|null $domains,
    ) {
        if (! in_array($type, self::TYPES, true)) {
            throw new InvalidArgument('Invalid type given.');
        }

        $this->resource = $resource;
        $this->type = $type;
        $this->expiresAt = $expiresAt;
        $this->domains = $domains;
    }

    public static function fromEntity(SslFile $certificate): self
    {
        $type = $certificate->getType();

        return new self(
            new CertificatesCertificateResourceItem($certificate->getSsl()->getResource()->getId()),
            $type === SslFile::TYPE_LETSENCRYPT ? self::TYPE_INSTANT_SSL : $type,
            $certificate->getExpiresAt(),
            $certificate->getDomains(),
        );
    }
}
