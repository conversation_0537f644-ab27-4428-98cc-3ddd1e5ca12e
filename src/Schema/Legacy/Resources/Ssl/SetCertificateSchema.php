<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources\Ssl;

use Cdn77\NxgApi\Core\Application\Payload\Schema;
use cebe\openapi\spec\Schema as OASchema;
use cebe\openapi\spec\Type;
use Symfony\Component\Validator\Constraints as Assert;

final class SetCertificateSchema implements Schema
{
    public const FIELD_CERTIFICATE = 'certificate';
    public const FIELD_KEY = 'key';

    /** @Assert\NotBlank */
    public string $certificate;

    /** @Assert\NotBlank */
    public string $key;

    public static function schemaSpec(): OASchema
    {
        return new OASchema([
            'type' => Type::OBJECT,
            'properties' => [
                self::FIELD_CERTIFICATE => new OASchema(['type' => Type::STRING]),
                self::FIELD_KEY => new OASchema(['type' => Type::STRING]),
            ],
        ]);
    }

    private function __construct()
    {
    }
}
