<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources\Ssl;

use Cdn77\NxgApi\Core\Application\Payload\QueryBusResultSchema;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use JMS\Serializer\Annotation as Serializer;
use Webmozart\Assert\Assert;

use function array_map;

final class CertificatesSchema implements QueryBusResultSchema
{
    public const FIELD_CERTIFICATES = 'certificates';

    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Schema\Legacy\Resources\Ssl\CertificatesCertificateItem>")
     * @var list<CertificatesCertificateItem>
     */
    public array $certificates;

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                self::FIELD_CERTIFICATES => new Schema([
                    'type' => Type::ARRAY,
                    'items' => CertificatesCertificateItem::getSchemaSpec(),
                ]),
            ],
        ]);
    }

    /** @param list<CertificatesCertificateItem> $certificates */
    public function __construct(array $certificates = [])
    {
        $this->certificates = $certificates;
    }

    /** @inheritDoc */
    public static function fromQueryBusResult($result): self
    {
        Assert::isArray($result);
        Assert::allIsInstanceOf($result, SslFile::class);

        return new self(
            array_map(
                static fn (SslFile $sslFile) => CertificatesCertificateItem::fromEntity($sslFile),
                $result,
            ),
        );
    }
}
