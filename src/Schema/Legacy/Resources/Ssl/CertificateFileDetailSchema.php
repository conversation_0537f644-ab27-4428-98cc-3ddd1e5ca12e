<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources\Ssl;

use Cdn77\NxgApi\Core\Application\Payload\CommandBusResultSchema;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Exception\InvalidArgument;
use cebe\openapi\spec\Schema;
use DateTimeImmutable;
use JMS\Serializer\Annotation as Serializer;
use Webmozart\Assert\Assert;

use function in_array;

final class CertificateFileDetailSchema implements CommandBusResultSchema
{
    public const FIELD_ID = 'id';
    public const FIELD_TYPE = 'type';
    public const FIELD_INDEX = 'index';
    public const FIELD_CREATED_AT = 'created_at';

    public const TYPE_CUSTOM = 'custom';
    public const TYPE_LETSENCRYPT = 'letsencrypt';

    public int $id;

    public string $type;

    public int $index;

    /** @Serializer\Type("DateTimeImmutable<'Y-m-d\TH:i:s\Z'>") */
    public DateTimeImmutable $createdAt;

    public static function schemaSpec(): Schema
    {
        return new Schema([
            'type' => 'object',
            'properties' => [
                self::FIELD_ID => new Schema(['type' => 'integer']),
                self::FIELD_TYPE => new Schema(['type' => 'string']),
                self::FIELD_INDEX => new Schema(['type' => 'integer']),
                self::FIELD_CREATED_AT => new Schema(['type' => 'string', 'format' => 'date-time']),
            ],
            'required' => [self::FIELD_ID, self::FIELD_TYPE, self::FIELD_INDEX, self::FIELD_CREATED_AT],
        ]);
    }

    public function __construct(int $id, string $type, int $index, DateTimeImmutable $createdAt)
    {
        $this->id = $id;
        $this->index = $index;
        $this->createdAt = $createdAt;

        if (! in_array($type, [self::TYPE_CUSTOM, self::TYPE_LETSENCRYPT], true)) {
            throw new InvalidArgument('Invalid certificate type.');
        }

        $this->type = $type;
    }

    /** @inheritDoc */
    public static function fromCommandBusResult($result): self
    {
        Assert::isInstanceOf($result, SslFile::class);

        return self::fromSslFile($result);
    }

    public static function fromSslFile(SslFile $sslFile): self
    {
        $type = [
            SslFile::TYPE_CUSTOM => self::TYPE_CUSTOM,
            SslFile::TYPE_LETSENCRYPT => self::TYPE_LETSENCRYPT,
        ][$sslFile->getType()];

        return new self(
            $sslFile->getId(),
            $type,
            $sslFile->getIndex(),
            $sslFile->getCreatedAt(),
        );
    }
}
