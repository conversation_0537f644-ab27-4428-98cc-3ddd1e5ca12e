<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources\Ssl;

use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

final class CertificatesCertificateResourceItem
{
    public const FIELD_ID = 'id';

    public int $id;

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                self::FIELD_ID => new Schema(['type' => Type::INTEGER]),
            ],
        ]);
    }

    public function __construct(int $id)
    {
        $this->id = $id;
    }
}
