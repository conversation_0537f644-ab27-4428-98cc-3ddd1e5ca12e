<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources;

use JMS\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

class PrefetchListSchema
{
    /**
     * @Serializer\Type("array<string>")
     * @Serializer\SerializedName("prefetch_paths")
     * @Assert\Count(
     *     min=1,
     *     max=2000,
     *     minMessage="Too few paths, specify at least one.",
     *     maxMessage="Too many paths. Keep it up to {{ limit }}."
     * )
     * @Assert\All({
     *     @Assert\NotIdenticalTo("", message="Sent path must not be empty."),
     *     @Assert\Length(max=2048, maxMessage="Sent path too long. Keep it under {{ limit }} characters.")
     * })
     * @var string[]
     */
    private $paths = [];

    /** @return string[] */
    public function getPaths(): array
    {
        return $this->paths;
    }
}
