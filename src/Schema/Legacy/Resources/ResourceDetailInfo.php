<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\IgnoredQueryParam;
use Cdn77\NxgApi\Resource\Application\Payload\FollowRedirectSchema;
use Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeadersSchema;
use Cdn77\NxgApi\Resource\Application\Payload\SecureTokenDetailSchema;
use Cdn77\NxgApi\Validator\Constraints as CustomAssert;
use DateTimeImmutable;
use J<PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

final class ResourceDetailInfo
{
    public int $id;
    public int $cdnReference;

    public int|null $accountId;

    /**
     * @Serializer\Type("array<string>")
     * @var array<string>
     */
    public array $cnames;

    /** @Serializer\Type("DateTimeImmutable<'c'>") */
    public DateTimeImmutable $createdAt;

    public string $resourceType;

    /** @Serializer\Type("DateTimeImmutable<'c'>") */
    public DateTimeImmutable $updatedAt;

    /** @Serializer\Type("DateTimeImmutable<'c'>") */
    public DateTimeImmutable|null $suspendedAt;

    /**
     * @Assert\NotBlank
     * @CustomAssert\Any(
     *     message="%value% is not a domain or an IPv4.",
     *     {
     *         @CustomAssert\Resource\Domain,
     *         @Assert\Ip(version="4_public")
     *     }
     * )
     */
    public string $originUrl;

    /**
     * @Assert\NotBlank
     * @Assert\Choice({"http", "https"}, strict=true)
     */
    public string $originScheme;

    public int|null $originPort;

    /** @Assert\NotBlank */
    public string $cdnUrl;

    /**
     * @Serializer\Type("array<int>")
     * @Assert\NotBlank
     * @var array<int>
     */
    public array $groupId;

    public int|null $cacheExpiry;

    /** @Serializer\SerializedName("cache_expiry_404") */
    public int|null $cacheExpiry404;

    public int $disableQueryString;

    public int $ignoreSetCookie;

    public int $mp4PseudoOn;

    public int $instantSsl;

    /**
     * @Serializer\Type("array<string>")
     * @var array<string>
     */
    public array $ignoredQueryParams;

    /** @Assert\Choice({0, 301, 302}) */
    public int $httpsRedirectCode;

    /** @Assert\NotNull */
    public string $originBasedir;

    public int $forwardHostHeader;

    public int $streamingPlaylistBypass;

    public int|null $originTimeout;

    /**
     * @Serializer\Type("array")
     * @var array<mixed>|null
     */
    public array|null $customData;

    public int $waf;

    public int $quic;

    public int $corsOriginHeader;

    public bool $corsTimingEnabled;

    public bool $corsWildcardEnabled;

    public string|null $awsAccessKeyId;

    public string|null $awsSecret;

    public string|null $awsRegion;

    /** @Serializer\SerializedName("s3_bucket_name") */
    public string|null $s3BucketName;

    /** @Serializer\SerializedName("s3_type") */
    public string|null $s3Type;

    public bool $sslVerifyDisable;

    public bool $rateLimit;

    public bool $contentDispositionByParam;

    /**
     * @Serializer\Type("array")
     * @var array<string, string>|null
     */
    public array|null $originHeaders;

    /** @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeadersSchema") */
    public ResponseHeadersSchema $responseHeaders;

    /** @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\SecureTokenDetailSchema") */
    public SecureTokenDetailSchema|null $secureToken = null;

    /** @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\FollowRedirectSchema") */
    public FollowRedirectSchema $followRedirect;

    /**
     * @param array<string>     $cnames
     * @param array<int>        $groupId
     * @param array<string>     $ignoredQueryParams
     * @param array<mixed>|null $customData
     * @param array<string, string>|null $originHeaders
     */
    final public function __construct(
        int $resourceId,
        int|null $accountId,
        array $cnames,
        DateTimeImmutable $createdAt,
        DateTimeImmutable $updatedAt,
        DateTimeImmutable|null $suspendedAt,
        string $originUrl,
        string $originScheme,
        int|null $originPort,
        string $cdnUrl,
        array $groupId,
        int|null $cacheExpiry,
        int|null $cacheExpiry404,
        int $disableQueryString,
        int $ignoreSetCookie,
        int $mp4PseudoOn,
        int $instantSsl,
        array $ignoredQueryParams,
        int $httpsRedirectCode,
        string $originBasedir,
        int $forwardHostHeader,
        int $streamingPlaylistBypass,
        int|null $originTimeout,
        array|null $customData,
        int $waf,
        int $quic,
        int $corsOriginHeader,
        bool $corsTimingEnabled,
        bool $corsWildcardEnabled,
        string|null $awsAccessKeyId,
        string|null $awsSecret,
        string|null $awsRegion,
        string|null $s3BucketName,
        string|null $s3Type,
        bool $sslVerifyDisable,
        bool $rateLimit,
        bool $contentDispositionByParam,
        array|null $originHeaders,
        ResponseHeadersSchema $responseHeadersSchema,
        SecureTokenDetailSchema|null $secureTokenDetailSchema,
        FollowRedirectSchema $followRedirectSchema,
    ) {
        $this->id = $resourceId;
        $this->cdnReference = $resourceId;
        $this->accountId = $accountId;
        $this->cnames = $cnames;
        $this->createdAt = $createdAt;
        $this->resourceType = 'HTTP_PULL';
        $this->updatedAt = $updatedAt;
        $this->suspendedAt = $suspendedAt;
        $this->originUrl = $originUrl;
        $this->originScheme = $originScheme;
        $this->originPort = $originPort;
        $this->cdnUrl = $cdnUrl;
        $this->groupId = $groupId;
        $this->cacheExpiry = $cacheExpiry;
        $this->cacheExpiry404 = $cacheExpiry404;
        $this->disableQueryString = $disableQueryString;
        $this->ignoreSetCookie = $ignoreSetCookie;
        $this->mp4PseudoOn = $mp4PseudoOn;
        $this->instantSsl = $instantSsl;
        $this->ignoredQueryParams = $ignoredQueryParams;
        $this->httpsRedirectCode = $httpsRedirectCode;
        $this->originBasedir = $originBasedir;
        $this->forwardHostHeader = $forwardHostHeader;
        $this->streamingPlaylistBypass = $streamingPlaylistBypass;
        $this->originTimeout = $originTimeout;
        $this->customData = $customData;
        $this->waf = $waf;
        $this->quic = $quic;
        $this->corsOriginHeader = $corsOriginHeader;
        $this->corsTimingEnabled = $corsTimingEnabled;
        $this->corsWildcardEnabled = $corsWildcardEnabled;
        $this->awsAccessKeyId = $awsAccessKeyId;
        $this->awsSecret = $awsSecret;
        $this->awsRegion = $awsRegion;
        $this->s3BucketName = $s3BucketName;
        $this->s3Type = $s3Type;
        $this->sslVerifyDisable = $sslVerifyDisable;
        $this->rateLimit = $rateLimit;
        $this->contentDispositionByParam = $contentDispositionByParam;
        $this->originHeaders = $originHeaders;
        $this->responseHeaders = $responseHeadersSchema;
        $this->secureToken = $secureTokenDetailSchema;
        $this->followRedirect = $followRedirectSchema;
    }

    public static function fromResource(CdnResource $resource): ResourceDetailInfo
    {
        $mainOrigin = $resource->getMainOrigin();
        $caching = $resource->getCaching();

        return new static(
            $resource->getId(),
            $resource->getAccount()->getId(),
            $resource->getCnames(),
            $resource->getCreated(),
            $resource->getUpdated(),
            $resource->getSuspended(),
            $mainOrigin->getHost(),
            $mainOrigin->getScheme(),
            $mainOrigin->getPort(),
            $resource->getCdnUrl(),
            [$resource->getGroup()->getId()],
            $caching->getExpiry(),
            $caching->getExpiry404(),
            $resource->isDisableQueryString() === true ? 1 : 0,
            $resource->isIgnoreSetCookie() === true ? 1 : 0,
            $resource->isMp4PseudoStreaming() === true ? 1 : 0,
            $resource->hasInstantSsl() === true ? 1 : 0,
            $resource->getIgnoredQueryParams()->map(
                static fn (IgnoredQueryParam $param): string => $param->getName(),
            )->toArray(),
            (int) $resource->getHttpsRedirectCode(),
            $mainOrigin->getBasedir() ?? '',
            $mainOrigin->hasForwardHostHeader() === true ? 1 : 0,
            $resource->hasStreamingPlaylistBypass() === true ? 1 : 0,
            $mainOrigin->getTimeout(),
            $resource->getCustomData(),
            $resource->hasWaf() === true ? 1 : 0,
            $resource->hasQuic() === true ? 1 : 0,
            $resource->hasCorsOriginHeader() === true ? 1 : 0,
            $resource->hasCorsTimingEnabled(),
            $resource->hasCorsWildcardEnabled(),
            $mainOrigin->getS3()->getAccessKeyId(),
            $mainOrigin->getS3()->getSecret(),
            $mainOrigin->getS3()->getRegion(),
            $mainOrigin->getS3()->getBucketName(),
            $mainOrigin->getS3()->getType(),
            $mainOrigin->hasSslVerifyDisable(),
            $resource->hasRateLimit(),
            $resource->hasContentDispositionByParam(),
            $mainOrigin->getOriginHeaders(),
            ResponseHeadersSchema::fromResponseHeaders($resource->getResponseHeaders()),
            $resource->getResourceSecureToken() === null
                ? null : SecureTokenDetailSchema::fromSecureToken($resource->getResourceSecureToken()),
            new FollowRedirectSchema($mainOrigin->hasFollowRedirectOrigin(), $mainOrigin->getFollowRedirectCodes()),
        );
    }
}
