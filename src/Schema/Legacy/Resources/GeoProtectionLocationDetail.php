<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Legacy\Resources;

use Cdn77\NxgApi\Validator\Constraints as CustomAssert;
use JMS\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

class GeoProtectionLocationDetail
{
    // Choice is specially used for countries not in the ISO 3166-2 but still valid. Example: XK = Kosovo
    /**
     * @Serializer\Type("string")
     * @Assert\NotBlank
     * @CustomAssert\Any(
     *      message="%value% is not a valid country.",
     *      {
     *          @Assert\Choice({"XK"}),
     *          @Assert\Country
     *      }
     *  )
     * @var string
     */
    private $country;

    public function __construct(string $country)
    {
        $this->country = $country;
    }

    public function getCountry(): string
    {
        return $this->country;
    }
}
