<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Schema\Search;

use JMS\Serializer\Annotation as Serializer;

class SearchResourcesMapSchema
{
    /**
     * @Serializer\Type("array<int>")
     * @var int[]
     */
    private $resources;

    /** @param int[] $resources */
    public function __construct(array $resources)
    {
        $this->resources = $resources;
    }

    /** @return int[] */
    public function getResources(): array
    {
        return $this->resources;
    }
}
