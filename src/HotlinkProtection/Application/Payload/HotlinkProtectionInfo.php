<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\HotlinkProtection\Application\Payload;

use Cdn77\NxgApi\HotlinkProtection\Domain\Value\HotlinkProtectionType;
use Cdn77\NxgApi\Schema\Legacy\Resources\HotlinkProtection\RefererAddressDetail;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

final class HotlinkProtectionInfo
{
    /**
     * @Assert\NotBlank
     * @Assert\Choice(
     *     {"whitelist", "blacklist", "disabled"},
     *     min=1,
     *     max=1,
     *     message="Type must be either 'whitelist', 'blacklist' or 'disabled'.",
     *     strict=true
     * )
     */
    private string $type;

    /** @Assert\NotNull */
    private bool $denyEmpty;

    /**
     * @Serializer\Type(
     *     "array<Cdn77\NxgApi\Schema\Legacy\Resources\HotlinkProtection\RefererAddressDetail>"
     * )
     * @Assert\NotNull
     * @Assert\Count(max=300)
     * @Assert\Valid
     * @var list<RefererAddressDetail>
     */
    private array $addresses = [];

    /** @param list<RefererAddressDetail> $addresses */
    public function __construct(string $type, bool $denyEmpty, array $addresses)
    {
        $this->type = $type;
        $this->denyEmpty = $denyEmpty;
        $this->addresses = $addresses;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function isEmptyDenied(): bool
    {
        return $this->denyEmpty;
    }

    /** @return list<RefererAddressDetail> */
    public function getAddresses(): array
    {
        return $this->type === HotlinkProtectionType::TYPE_DISABLED ? [] : $this->addresses;
    }
}
