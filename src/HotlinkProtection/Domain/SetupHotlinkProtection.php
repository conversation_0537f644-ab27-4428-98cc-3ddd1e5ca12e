<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\HotlinkProtection\Domain;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\HotlinkProtection\Application\Payload\HotlinkProtectionInfo;
use Cdn77\NxgApi\HotlinkProtection\Domain\Value\HotlinkProtectionType;
use Cdn77\NxgApi\Schema\Legacy\Resources\HotlinkProtection\RefererAddressDetail;
use Cdn77\NxgApi\Service\Legacy\HotlinkProtection\Exception\RefererNotFound;
use Cdn77\NxgApi\Service\Legacy\HotlinkProtection\RefererManager;

use function array_map;

final class SetupHotlinkProtection
{
    private RefererManager $refererManager;

    public function __construct(RefererManager $refererManager)
    {
        $this->refererManager = $refererManager;
    }

    public function setup(CdnResource $cdnResource, HotlinkProtectionInfo $hotlinkProtectionInfo): void
    {
        $isEnabled = true;
        $shouldDisable = $hotlinkProtectionInfo->getType() === HotlinkProtectionType::TYPE_DISABLED
            && ! $hotlinkProtectionInfo->isEmptyDenied();

        try {
            $this->refererManager->getReferer($cdnResource);
        } catch (RefererNotFound) {
            $isEnabled = false;
        }

        if (! $isEnabled && $shouldDisable) {
            return;
        }

        if (! $isEnabled) {
            $this->refererManager->enable(
                $cdnResource,
                $hotlinkProtectionInfo->getType(),
                $hotlinkProtectionInfo->isEmptyDenied(),
                array_map(
                    static fn (RefererAddressDetail $refererAddressDetail) => $refererAddressDetail->getDomain(),
                    $hotlinkProtectionInfo->getAddresses(),
                ),
            );

            return;
        }

        if ($shouldDisable) {
            $this->refererManager->disable($cdnResource);

            return;
        }

        $this->refererManager->update(
            $cdnResource,
            $hotlinkProtectionInfo->getType(),
            $hotlinkProtectionInfo->isEmptyDenied(),
            array_map(
                static fn (RefererAddressDetail $refererAddressDetail) => $refererAddressDetail->getDomain(),
                $hotlinkProtectionInfo->getAddresses(),
            ),
        );
    }
}
