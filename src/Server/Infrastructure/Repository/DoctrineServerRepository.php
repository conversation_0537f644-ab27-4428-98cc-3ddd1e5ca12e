<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Infrastructure\Repository;

use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Server\Domain\Exception\ServerNotFound;
use Cdn77\NxgApi\Server\Domain\Repository\ServerRepository;

final class DoctrineServerRepository implements ServerRepository
{
    use EntityManagerConstructor;

    public function get(int $uid): Server
    {
        $server = $this->find($uid);

        if ($server === null) {
            throw ServerNotFound::fromUid($uid);
        }

        return $server;
    }

    public function find(int $uid): Server|null
    {
        return $this->entityManager->getRepository(Server::class)->findOneBy(['uid' => $uid]);
    }

    /** @return list<Server> */
    public function findAll(): array
    {
        return $this->entityManager->createQueryBuilder()
            ->select('s')
            ->from(Server::class, 's')
            ->getQuery()
            ->getResult();
    }

    public function getById(int $id): Server
    {
        $server = $this->findById($id);

        if ($server === null) {
            throw ServerNotFound::fromId($id);
        }

        return $server;
    }

    public function findById(int $id): Server|null
    {
        return $this->entityManager->getRepository(Server::class)->find($id);
    }
}
