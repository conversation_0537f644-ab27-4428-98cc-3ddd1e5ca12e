<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Infrastructure\Finder;

use Cdn77\NxgApi\Server\Domain\Finder\ServerIdFinder;
use Doctrine\DBAL\Connection;

final class DoctrineServerIdFinder implements ServerIdFinder
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    /** @inheritDoc */
    public function findAllForResourcesWithoutCustomLocations(int $resourceId): array
    {
        /** @var list<array{id: int, uid: int}> $result */
        $result = $this->connection->fetchAllAssociative(
            <<<'PSQL'
SELECT r.id, s.uid
FROM server s
INNER JOIN ip ip ON ip.server_id = s.id
INNER JOIN group_pop_relation gpr ON gpr.pop_id = s.pop_id
INNER JOIN resource r ON r.group_id = gpr.group_id
WHERE r.id = :resourceId
    AND ip.primary = true
GROUP BY r.id, s.uid
ORDER BY r.id, s.uid
PSQL,
            ['resourceId' => $resourceId],
        );

        return $result;
    }

    /** @inheritDoc */
    public function findAllForResourcesWithCustomLocations(int $resourceId): array
    {
        /** @var list<array{id: int, uid: int}> $result */
        $result = $this->connection->fetchAllAssociative(
            <<<'PSQL'
SELECT r.id, s.uid
FROM custom_location_relation clr
INNER JOIN resource r ON r.id = clr.resource_id
INNER JOIN pop p ON clr.pop_id = p.id
INNER JOIN server s ON s.pop_id = p.id
INNER JOIN ip ip ON ip.server_id = s.id
WHERE r.id = :resourceId
    AND ip.primary = true
GROUP BY r.id, s.uid
ORDER BY r.id, s.uid;
PSQL,
            ['resourceId' => $resourceId],
        );

        return $result;
    }
}
