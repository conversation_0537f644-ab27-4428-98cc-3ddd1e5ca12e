<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Application\Controller;

use Cdn77\NxgApi\Core\Domain\Bus\QueryBus;
use Cdn77\NxgApi\Schema\Internal\Server\ServerDetailSchema;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Server\Domain\Query\FindServerByUid;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

use function sprintf;

class DetailController
{
    public const ROUTE_NAME = 'server.detail';
    public const ROUTE_SUMMARY = 'Server detail';

    private QueryBus $queryBus;

    private SerializerInterface $serializer;

    public function __construct(QueryBus $queryBus, SerializerInterface $serializer)
    {
        $this->queryBus = $queryBus;
        $this->serializer = $serializer;
    }

    /** @Route(path="/internal/server/{uid}", name=self::ROUTE_NAME, methods={Request::METHOD_GET}) */
    public function detailAction(int $uid): JsonResponse
    {
        $server = $this->queryBus->handle(new FindServerByUid($uid));

        if ($server === null) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema([sprintf('Server with uid #%d does not exist.', $uid)]),
                    'json',
                ),
                Response::HTTP_NOT_FOUND,
            );
        }

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                ServerDetailSchema::fromServer($server),
                'json',
            ),
            Response::HTTP_OK,
        );
    }
}
