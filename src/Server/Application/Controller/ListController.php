<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Application\Controller;

use Cdn77\NxgApi\Core\Domain\Bus\QueryBus;
use Cdn77\NxgApi\Schema\Internal\Server\ServerListSchema;
use Cdn77\NxgApi\Server\Domain\Query\FindAllWithIpsAndLocations;
use J<PERSON>\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class ListController
{
    public const ROUTE_NAME = 'server.list';
    public const ROUTE_SUMMARY = 'Servers list';

    private QueryBus $queryBus;

    private SerializerInterface $serializer;

    public function __construct(QueryBus $queryBus, SerializerInterface $serializer)
    {
        $this->queryBus = $queryBus;
        $this->serializer = $serializer;
    }

    /** @Route(path="/internal/server", name=self::ROUTE_NAME, methods={Request::METHOD_GET}) */
    public function listAction(): JsonResponse
    {
        $servers = $this->queryBus->handle(new FindAllWithIpsAndLocations());

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(ServerListSchema::fromServers($servers), 'json'),
            Response::HTTP_OK,
        );
    }
}
