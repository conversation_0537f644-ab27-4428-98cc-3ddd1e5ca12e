<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Application\Controller;

use Cdn77\NxgApi\Core\Domain\Bus\QueryBus;
use Cdn77\NxgApi\Server\Application\Payload\ResourceIdsSchema;
use Cdn77\NxgApi\Server\Application\Payload\ResourceServerIdListSchema;
use Cdn77\NxgApi\Server\Domain\Query\FindServerIdsForResources;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Webmozart\Assert\Assert;

final class ServerIdListController
{
    public const ROUTE_NAME = 'internal.server-id.list';

    private QueryBus $queryBus;

    private SerializerInterface $serializer;

    public function __construct(QueryBus $queryBus, SerializerInterface $serializer)
    {
        $this->queryBus = $queryBus;
        $this->serializer = $serializer;
    }

    /**
     * @Route(
     *     path="/internal/resource/servers",
     *     name=ServerIdListController::ROUTE_NAME,
     *     methods={Request::METHOD_POST}
     * )
     */
    public function execute(Request $request): Response
    {
        $content = $request->getContent();
        Assert::string($content);

        $resourceIdsSchema = $this->serializer->deserialize($content, ResourceIdsSchema::class, 'json');

        if ($resourceIdsSchema->resourceIds === []) {
            return new Response('', Response::HTTP_BAD_REQUEST);
        }

        $resourcesServerIds = $this->queryBus->handle(FindServerIdsForResources::fromSchema($resourceIdsSchema));

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(new ResourceServerIdListSchema($resourcesServerIds), 'json'),
            Response::HTTP_OK,
        );
    }
}
