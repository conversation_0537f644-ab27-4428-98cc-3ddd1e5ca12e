<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Application\Controller;

use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Server\Domain\Command\ForceServerDown;
use Cdn77\NxgApi\Server\Domain\Exception\ServerNotFound;
use Cdn77\NxgApi\Service\Server\Exception\ServerAlreadyForcedDown;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class ForceDownController
{
    public const ROUTE_NAME = 'server.force-down';
    public const ROUTE_SUMMARY = 'Force server down';

    private CommandBus $commandBus;

    private SerializerInterface $serializer;

    public function __construct(CommandBus $commandBus, SerializerInterface $serializer)
    {
        $this->commandBus = $commandBus;
        $this->serializer = $serializer;
    }

    /** @Route(path="/internal/server/{uid}/force-down", name=self::ROUTE_NAME, methods={Request::METHOD_PUT}) */
    public function forceDownAction(int $uid): Response
    {
        try {
            $this->commandBus->handle(new ForceServerDown($uid));
        } catch (ServerNotFound $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(new ErrorsSchema([$e->getMessage()]), 'json'),
                Response::HTTP_NOT_FOUND,
            );
        } catch (ServerAlreadyForcedDown $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(new ErrorsSchema([$e->getMessage()]), 'json'),
                Response::HTTP_CONFLICT,
            );
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }
}
