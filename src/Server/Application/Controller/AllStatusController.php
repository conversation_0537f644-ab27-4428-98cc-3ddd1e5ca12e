<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Application\Controller;

use Cdn77\NxgApi\Core\Domain\Bus\QueryBus;
use Cdn77\NxgApi\Server\Application\Payload\AllServersStatusSchema;
use Cdn77\NxgApi\Server\Domain\Query\FindStatusForAllServers;
use J<PERSON>\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class AllStatusController
{
    public const ROUTE_NAME = 'server.status';
    public const ROUTE_SUMMARY = 'All servers status';

    private QueryBus $queryBus;

    private SerializerInterface $serializer;

    public function __construct(QueryBus $queryBus, SerializerInterface $serializer)
    {
        $this->queryBus = $queryBus;
        $this->serializer = $serializer;
    }

    /** @Route(path="/internal/servers/statuses", name=self::ROUTE_NAME, methods={Request::METHOD_GET}) */
    public function allStatusAction(): JsonResponse
    {
        $statuses = $this->queryBus->handle(new FindStatusForAllServers());

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                AllServersStatusSchema::fromServerStatuses($statuses),
                'json',
            ),
            Response::HTTP_OK,
        );
    }
}
