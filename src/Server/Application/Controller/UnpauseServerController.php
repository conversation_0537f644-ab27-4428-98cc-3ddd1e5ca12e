<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Application\Controller;

use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Server\Domain\Command\UnpauseServer;
use Cdn77\NxgApi\Server\Domain\Exception\ServerNotFound;
use Cdn77\NxgApi\Service\Server\Exception\ServerAlreadyUnpaused;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class UnpauseServerController
{
    public const ROUTE_NAME = 'server.unpause';
    public const ROUTE_SUMMARY = 'Unpause a server.';

    private CommandBus $commandBus;

    private SerializerInterface $serializer;

    public function __construct(CommandBus $commandBus, SerializerInterface $serializer)
    {
        $this->commandBus = $commandBus;
        $this->serializer = $serializer;
    }

    /** @Route(path="/internal/server/{uid}/unpause", name=self::ROUTE_NAME, methods={Request::METHOD_PUT}) */
    public function pauseAction(int $uid): Response
    {
        try {
            $this->commandBus->handle(new UnpauseServer($uid));
        } catch (ServerNotFound $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(new ErrorsSchema([$e->getMessage()]), 'json'),
                Response::HTTP_NOT_FOUND,
            );
        } catch (ServerAlreadyUnpaused $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(new ErrorsSchema([$e->getMessage()]), 'json'),
                Response::HTTP_CONFLICT,
            );
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }
}
