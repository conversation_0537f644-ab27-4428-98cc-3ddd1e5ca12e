<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Application\Controller;

use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Server\Domain\Command\UnforceServer;
use Cdn77\NxgApi\Server\Domain\Exception\ServerNotFound;
use Cdn77\NxgApi\Service\Server\Exception\ServerAlreadyNotForced;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class UnforceController
{
    public const ROUTE_NAME = 'server.unforce';
    public const ROUTE_SUMMARY = 'Remove server forced state';

    private CommandBus $commandBus;

    private SerializerInterface $serializer;

    public function __construct(CommandBus $commandBus, SerializerInterface $serializer)
    {
        $this->commandBus = $commandBus;
        $this->serializer = $serializer;
    }

    /** @Route(path="/internal/server/{uid}/unforce", name=self::ROUTE_NAME, methods={Request::METHOD_PUT}) */
    public function unforceAction(int $uid): Response
    {
        try {
            $this->commandBus->handle(new UnforceServer($uid));
        } catch (ServerNotFound $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(new ErrorsSchema([$e->getMessage()]), 'json'),
                Response::HTTP_NOT_FOUND,
            );
        } catch (ServerAlreadyNotForced $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(new ErrorsSchema([$e->getMessage()]), 'json'),
                Response::HTTP_CONFLICT,
            );
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }
}
