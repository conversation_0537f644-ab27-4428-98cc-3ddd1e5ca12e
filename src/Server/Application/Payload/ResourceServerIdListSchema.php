<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Application\Payload;

use J<PERSON>\Serializer\Annotation as Serializer;

final class ResourceServerIdListSchema
{
    /**
     * @Serializer\Type("array<int, array<int>>")
     * @Serializer\Inline
     * @var array<int, list<int>>
     */
    public array $serverIdsPerResource;

    /** @param array<int, list<int>> $serverIdsPerResource */
    public function __construct(array $serverIdsPerResource)
    {
        $this->serverIdsPerResource = $serverIdsPerResource;
    }
}
