<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\ServerStatusSchema;
use Cdn77\NxgApi\Service\Server\ServerStatus;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

use function array_map;

class AllServersStatusSchema
{
    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Core\Application\Payload\ServerStatusSchema>")
     * @Serializer\Inline
     * @var list<ServerStatusSchema>
     */
    public array $servers;

    /** @param list<ServerStatusSchema> $servers */
    public function __construct(array $servers)
    {
        $this->servers = $servers;
    }

    /** @param list<ServerStatus> $statuses */
    public static function fromServerStatuses(array $statuses): self
    {
        return new self(array_map(
            static fn (ServerStatus $status) => ServerStatusSchema::fromServerStatus($status),
            $statuses,
        ));
    }
}
