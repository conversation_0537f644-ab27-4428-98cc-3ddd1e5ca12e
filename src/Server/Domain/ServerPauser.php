<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Domain;

use Cdn77\NxgApi\Core\Domain\ServerStatusCalculator;
use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Service\Server\Exception\ServerAlreadyPaused;
use Cdn77\NxgApi\Service\Server\Exception\ServerAlreadyUnpaused;
use Cdn77\NxgApi\Service\Server\ServerStatusManager;
use Doctrine\DBAL\LockMode;
use Doctrine\ORM\EntityManagerInterface;

use function sprintf;

final class ServerPauser
{
    private EntityManagerInterface $entityManager;

    private ServerStatusCalculator $serverStatusCalculator;

    private ServerStatusManager $serverStatusManager;

    public function __construct(
        EntityManagerInterface $entityManager,
        ServerStatusCalculator $serverStatusCalculator,
        ServerStatusManager $serverStatusManager,
    ) {
        $this->entityManager = $entityManager;
        $this->serverStatusCalculator = $serverStatusCalculator;
        $this->serverStatusManager = $serverStatusManager;
    }

    public function pause(Server $server): void
    {
        $this->lockAndRefresh($server);

        if ($server->isPaused() === true) {
            throw new ServerAlreadyPaused(sprintf('Server #%d is already paused.', $server->getUid()));
        }

        $oldStatus = $this->serverStatusCalculator->getServerStatus($server);

        $server->setPaused(true);
        $this->entityManager->flush();

        $newStatus = $this->serverStatusCalculator->getServerStatus($server);
        $this->serverStatusManager->handleStatusChange($server, $oldStatus, $newStatus);
    }

    public function unpause(Server $server): void
    {
        $this->lockAndRefresh($server);

        if ($server->isPaused() === false) {
            throw new ServerAlreadyUnpaused(sprintf('Server #%d is already unpaused.', $server->getUid()));
        }

        $oldStatus = $this->serverStatusCalculator->getServerStatus($server);

        $server->setPaused(false);
        $this->entityManager->flush();

        $newStatus = $this->serverStatusCalculator->getServerStatus($server);
        $this->serverStatusManager->handleStatusChange($server, $oldStatus, $newStatus);
    }

    private function lockAndRefresh(Server $server): void
    {
        $this->entityManager->lock($server, LockMode::PESSIMISTIC_WRITE);
        $this->entityManager->refresh($server);
    }
}
