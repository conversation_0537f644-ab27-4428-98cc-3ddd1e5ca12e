<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Domain;

use Cdn77\NxgApi\Core\Domain\ServerStatusCalculator;
use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Service\Server\Exception\ServerAlreadyForcedDown;
use Cdn77\NxgApi\Service\Server\Exception\ServerAlreadyForcedUp;
use Cdn77\NxgApi\Service\Server\Exception\ServerAlreadyNotForced;
use Cdn77\NxgApi\Service\Server\ServerStatusManager;
use Doctrine\DBAL\LockMode;
use Doctrine\ORM\EntityManagerInterface;

use function sprintf;

class ServerForcer
{
    private EntityManagerInterface $entityManager;

    private ServerStatusCalculator $serverStatusCalculator;

    private ServerStatusManager $serverStatusManager;

    public function __construct(
        EntityManagerInterface $entityManager,
        ServerStatusCalculator $serverStatusCalculator,
        ServerStatusManager $serverStatusManager,
    ) {
        $this->entityManager = $entityManager;
        $this->serverStatusCalculator = $serverStatusCalculator;
        $this->serverStatusManager = $serverStatusManager;
    }

    public function forceUp(Server $server): void
    {
        $this->force($server, true);
    }

    public function forceDown(Server $server): void
    {
        $this->force($server, false);
    }

    public function unforce(Server $server): void
    {
        $this->force($server, null);
    }

    private function force(Server $server, bool|null $state): void
    {
        $this->lockAndRefresh($server);

        if ($server->getForcedState() === $state) {
            switch (true) {
                case $state === true:
                    throw new ServerAlreadyForcedUp(
                        sprintf('Server #%d is already forced up.', $server->getUid()),
                    );
                case $state === false:
                    throw new ServerAlreadyForcedDown(
                        sprintf('Server #%d is already forced down.', $server->getUid()),
                    );
                default:
                    throw new ServerAlreadyNotForced(
                        sprintf('Server #%d is already not forced.', $server->getUid()),
                    );
            }
        }

        $oldStatus = $this->serverStatusCalculator->getServerStatus($server);

        $server->setForcedState($state);
        $this->entityManager->flush();

        $newStatus = $this->serverStatusCalculator->getServerStatus($server);
        $this->serverStatusManager->handleStatusChange($server, $oldStatus, $newStatus);
    }

    private function lockAndRefresh(Server $server): void
    {
        $this->entityManager->lock($server, LockMode::PESSIMISTIC_WRITE);
        $this->entityManager->refresh($server);
    }
}
