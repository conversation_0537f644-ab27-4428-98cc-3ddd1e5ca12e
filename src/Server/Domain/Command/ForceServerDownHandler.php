<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Server\Domain\Repository\ServerRepository;
use Cdn77\NxgApi\Server\Domain\ServerForcer;

final class ForceServerDownHandler implements CommandHandler
{
    private ServerForcer $serverForcer;

    private ServerRepository $serverRepository;

    public function __construct(ServerForcer $serverForcer, ServerRepository $serverRepository)
    {
        $this->serverForcer = $serverForcer;
        $this->serverRepository = $serverRepository;
    }

    public function handle(ForceServerDown $command): void
    {
        $this->serverForcer->forceDown($this->serverRepository->get($command->uid));
    }
}
