<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Server\Domain\Repository\ServerRepository;
use Cdn77\NxgApi\Server\Domain\ServerPauser;

final class UnpauseServerHandler implements CommandHandler
{
    private ServerPauser $serverPauser;

    private ServerRepository $serverRepository;

    public function __construct(ServerPauser $serverPauser, ServerRepository $serverRepository)
    {
        $this->serverPauser = $serverPauser;
        $this->serverRepository = $serverRepository;
    }

    public function handle(UnpauseServer $command): void
    {
        $this->serverPauser->unpause($this->serverRepository->get($command->uid));
    }
}
