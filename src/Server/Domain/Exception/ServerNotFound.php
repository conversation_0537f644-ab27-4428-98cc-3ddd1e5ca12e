<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Domain\Exception;

use Cdn77\NxgApi\Exception\Exception;
use DomainException;

use function sprintf;

final class ServerNotFound extends DomainException implements Exception
{
    public static function fromUid(int $uid): self
    {
        return new self(sprintf('Server with uid #%d does not exist.', $uid));
    }

    public static function fromId(int $id): self
    {
        return new self(sprintf('Server with ID #%d does not exist.', $id));
    }
}
