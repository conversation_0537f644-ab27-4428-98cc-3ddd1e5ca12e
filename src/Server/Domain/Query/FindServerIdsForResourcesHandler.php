<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Resource\Domain\Repository\CustomLocationRepository;
use Cdn77\NxgApi\Server\Domain\Finder\ServerIdFinder;

final class FindServerIdsForResourcesHandler implements QueryHandler
{
    private CdnResourceRepository $cdnResourceRepository;

    private ServerIdFinder $serverIdFinder;

    private CustomLocationRepository $customLocationRepository;

    public function __construct(
        CdnResourceRepository $cdnResourceRepository,
        CustomLocationRepository $customLocationRepository,
        ServerIdFinder $serverIdFinder,
    ) {
        $this->cdnResourceRepository = $cdnResourceRepository;
        $this->customLocationRepository = $customLocationRepository;
        $this->serverIdFinder = $serverIdFinder;
    }

    /** @return array<int, list<int>> */
    public function handle(FindServerIdsForResources $query): array
    {
        $serverIdsPerResource = [];

        foreach ($query->resourceIds as $resourceId) {
            $resource = $this->cdnResourceRepository->find($resourceId);

            if ($resource === null) {
                $serverIdsPerResource[$resourceId] = [];

                continue;
            }

            $resourceServerRelations = $this->customLocationRepository->hasResourceCustomLocation($resourceId)
                ? $this->serverIdFinder->findAllForResourcesWithCustomLocations($resourceId)
                : $this->serverIdFinder->findAllForResourcesWithoutCustomLocations($resourceId);

            foreach ($resourceServerRelations as $resourceServer) {
                $serverIdsPerResource[$resourceId][] = $resourceServer['uid'];
            }
        }

        return $serverIdsPerResource;
    }
}
