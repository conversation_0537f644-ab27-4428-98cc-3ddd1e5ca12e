<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\Core\Domain\ServerStatusCalculator;
use Cdn77\NxgApi\Service\Server\ServerStatus;

final class FindStatusForAllServersHandler implements QueryHandler
{
    private ServerStatusCalculator $serverStatusCalculator;

    public function __construct(ServerStatusCalculator $serverStatusCalculator)
    {
        $this->serverStatusCalculator = $serverStatusCalculator;
    }

    /** @return list<ServerStatus> */
    public function handle(FindStatusForAllServers $query): array
    {
        return $this->serverStatusCalculator->getStatusForAllServers();
    }
}
