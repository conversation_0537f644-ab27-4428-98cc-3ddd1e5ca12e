<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Repository\Legacy\ServerRepository;

final class FindAllWithIpsAndLocationsHandler implements QueryHandler
{
    private ServerRepository $serverRepository;

    public function __construct(ServerRepository $serverRepository)
    {
        $this->serverRepository = $serverRepository;
    }

    /** @return list<Server> */
    public function handle(FindAllWithIpsAndLocations $query): array
    {
        return $this->serverRepository->findAllWithIpsAndLocations();
    }
}
