<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Server\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\Query;
use Cdn77\NxgApi\Server\Application\Payload\ResourceIdsSchema;

final class FindServerIdsForResources implements Query
{
    /** @var list<int> */
    public array $resourceIds;

    /** @param list<int> $resourceIds */
    private function __construct(array $resourceIds)
    {
        $this->resourceIds = $resourceIds;
    }

    public static function fromSchema(ResourceIdsSchema $schema): self
    {
        return new self($schema->resourceIds);
    }
}
