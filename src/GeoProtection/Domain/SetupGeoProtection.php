<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\GeoProtection\Domain;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\GeoProtection\Application\Payload\GeoProtectionInfo;
use Cdn77\NxgApi\GeoProtection\Domain\Value\GeoProtectionType;
use Cdn77\NxgApi\Schema\Legacy\Resources\GeoProtectionLocationDetail;
use Cdn77\NxgApi\Service\Legacy\GeoProtection\Exception\GeoProtectionNotEnabled;
use Cdn77\NxgApi\Service\Legacy\GeoProtection\GeoProtectionManager;

use function array_map;

final class SetupGeoProtection
{
    private GeoProtectionManager $geoProtectionManager;

    public function __construct(GeoProtectionManager $geoProtectionManager)
    {
        $this->geoProtectionManager = $geoProtectionManager;
    }

    public function setup(CdnResource $cdnResource, GeoProtectionInfo $geoProtectionInfo): void
    {
        $isEnabled = true;
        $shouldDisable = $geoProtectionInfo->getType() === GeoProtectionType::TYPE_DISABLED;

        try {
            $this->geoProtectionManager->get($cdnResource);
        } catch (GeoProtectionNotEnabled) {
            $isEnabled = false;
        }

        if (! $isEnabled && $shouldDisable) {
            return;
        }

        // @phpstan-ignore booleanNot.alwaysTrue
        if (! $isEnabled && ! $shouldDisable) {
            $this->geoProtectionManager->enable(
                $cdnResource,
                $geoProtectionInfo->getType(),
                array_map(
                    static fn (GeoProtectionLocationDetail $location) => $location->getCountry(),
                    $geoProtectionInfo->getLocations(),
                ),
            );

            return;
        }

        if ($isEnabled && $shouldDisable) {
            $this->geoProtectionManager->disable($cdnResource);

            return;
        }

        $this->geoProtectionManager->modify(
            $cdnResource,
            $geoProtectionInfo->getType(),
            array_map(
                static fn (GeoProtectionLocationDetail $location) => $location->getCountry(),
                $geoProtectionInfo->getLocations(),
            ),
        );
    }
}
