<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\GeoProtection\Application\Payload;

use Cdn77\NxgApi\Schema\Legacy\Resources\GeoProtectionLocationDetail;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use <PERSON><PERSON>fo<PERSON>\Component\Validator\Constraints as Assert;

final class GeoProtectionInfo
{
    /**
     * @Assert\NotBlank
     * @Assert\Choice(
     *     {"whitelist", "blacklist", "disabled"},
     *     message="The value should be either 'whitelist', 'blacklist' or 'disabled'.",
     *     strict=true
     * )
     */
    private string $type;

    /**
     * @Serializer\Type(
     *     "array<Cdn77\NxgApi\Schema\Legacy\Resources\GeoProtectionLocationDetail>"
     * )
     * @Assert\Valid
     * @var list<GeoProtectionLocationDetail>
     */
    private array $locations;

    /** @param list<GeoProtectionLocationDetail> $locations */
    public function __construct(string $type, array $locations)
    {
        $this->type = $type;
        $this->locations = $locations;
    }

    public function getType(): string
    {
        return $this->type;
    }

    /** @return list<GeoProtectionLocationDetail> */
    public function getLocations(): array
    {
        return $this->locations;
    }
}
