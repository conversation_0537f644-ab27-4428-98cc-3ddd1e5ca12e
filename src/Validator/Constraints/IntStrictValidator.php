<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function is_int;

class IntStrictValidator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof IntStrict) {
            throw new UnexpectedTypeException($constraint, IntStrict::class);
        }

        if (is_int($value)) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->addViolation();
    }
}
