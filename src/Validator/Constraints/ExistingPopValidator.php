<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints;

use Cdn77\NxgApi\Repository\Legacy\PopRepository;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function is_int;

final class ExistingPopValidator extends ConstraintValidator
{
    /** @var PopRepository */
    private $popRepository;

    public function __construct(PopRepository $popRepository)
    {
        $this->popRepository = $popRepository;
    }

    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof ExistingPop) {
            throw new UnexpectedTypeException($constraint, ExistingPop::class);
        }

        if ($value === null) {
            return;
        }

        if (! is_int($value)) {
            throw new UnexpectedTypeException($value, 'int');
        }

        $this->doValidate($value, $constraint);
    }

    private function doValidate(int $value, ExistingPop $constraint): void
    {
        if ($this->popRepository->find($value) !== null) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->setParameter('{{ id }}', (string) $value)
            ->addViolation();
    }
}
