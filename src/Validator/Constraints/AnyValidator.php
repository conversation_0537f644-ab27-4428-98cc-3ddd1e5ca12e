<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class AnyValidator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof Any) {
            throw new UnexpectedTypeException($constraint, Any::class);
        }

        if ($value === null) {
            return;
        }

        foreach ($constraint->constraints as $nestedConstraint) {
            $validator = $this->context->getValidator();
            $context = $validator->startContext();

            $context->validate($value, $nestedConstraint);

            if ($context->getViolations()->count() === 0) {
                return;
            }
        }

        $this->context->buildViolation($constraint->message)
            ->setParameter('%value%', (string) $value)
            ->addViolation();
    }
}
