<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints;

use Doctrine\Common\Annotations\Annotation\Target;
use Symfony\Component\Validator\Constraint;

/**
 * @Annotation
 * @Target({"PROPERTY", "ANNOTATION"})
 */
final class ExistingPop extends Constraint
{
    /** @var string */
    public $message = 'Invalid pop id {{ id }}.';

    public function validatedBy(): string
    {
        return 'existing_pop_validator';
    }
}
