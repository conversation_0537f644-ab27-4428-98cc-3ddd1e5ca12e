<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints\Composite;

/**
 * @Annotation
 * @Target({"PROPERTY", "ANNOTATION"})
 */
class Any extends Composite
{
    /** @var Constraint[] */
    public $constraints = [];

    /** @var string */
    public $message = '%value% is not valid.';

    public function getDefaultOption(): string
    {
        return 'constraints';
    }

    protected function getCompositeOption(): string
    {
        return 'constraints';
    }
}
