<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function is_array;

class ArrayStrictValidator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof ArrayStrict) {
            throw new UnexpectedTypeException($constraint, ArrayStrict::class);
        }

        if (is_array($value)) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->addViolation();
    }
}
