<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function is_array;
use function strtolower;

class UniqueSetValidator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof UniqueSet) {
            throw new UnexpectedTypeException($constraint, UniqueSet::class);
        }

        if ($value === null) {
            return;
        }

        if (! is_array($value)) {
            throw new UnexpectedTypeException($value, 'array');
        }

        $this->doValidate($value, $constraint);
    }

    /** @param string[] $value */
    private function doValidate(array $value, UniqueSet $constraint): void
    {
        $found = [];
        foreach ($value as $i => $name) {
            $key = $constraint->caseSensitive ? $name : strtolower($name);

            if (isset($found[$key])) {
                $this->addDuplicateViolation($constraint, $name, $found[$key], $i);

                continue;
            }

            $found[$key] = $name;
        }
    }

    /**
     * @param string|int $found
     * @param string|int $reference
     */
    private function addDuplicateViolation(UniqueSet $constraint, $found, $reference, int $path): void
    {
        $this->context->buildViolation($constraint->duplicateFoundMessage)
            ->setParameter('%found%', (string) $found)
            ->setParameter('%ref%', (string) $reference)
            ->atPath('[' . $path . ']')
            ->addViolation();
    }
}
