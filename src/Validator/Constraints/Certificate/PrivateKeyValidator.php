<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Certificate;

use Safe\Exceptions\OpensslException;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function Safe\openssl_pkey_get_private;

class PrivateKeyValidator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof PrivateKey) {
            throw new UnexpectedTypeException($constraint, PrivateKey::class);
        }

        if ($value === null) {
            return;
        }

        if ($value === '') {
            $this->context->buildViolation($constraint->messagePrivateKeyCannotBeEmpty)->addViolation();
        }

        try {
            openssl_pkey_get_private($value);
        } catch (OpensslException) {
            $this->context->buildViolation($constraint->messagePrivateKeyMustBeValid)->addViolation();
        }
    }
}
