<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Certificate;

use Cdn77\NxgApi\Entity\LetsEncrypt;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function openssl_x509_check_private_key;

class CertificatePairValidator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof CertificatePair) {
            throw new UnexpectedTypeException($constraint, CertificatePair::class);
        }

        if (! $value instanceof LetsEncrypt\CertificatePair) {
            throw new UnexpectedTypeException($value, LetsEncrypt\CertificatePair::class);
        }

        $isValid = @openssl_x509_check_private_key($value->getCertificate(), $value->getPrivateKey()); // intentional @
        if ($isValid) {
            return;
        }

        $this->context->buildViolation($constraint->messageCertificatePairMustBeValid)->addViolation();
    }
}
