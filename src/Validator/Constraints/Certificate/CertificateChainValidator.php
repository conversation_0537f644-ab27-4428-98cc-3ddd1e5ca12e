<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Certificate;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function count;
use function is_string;
use function Safe\preg_split;

class CertificateChainValidator extends ConstraintValidator
{
    use PemCertificateValidator;

    private const PROLOG = '-----BEGIN CERTIFICATE-----';
    private const EPILOG = '-----END CERTIFICATE-----';

    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof CertificateChain) {
            throw new UnexpectedTypeException($constraint, CertificateChain::class);
        }

        if (! is_string($value)) {
            throw new UnexpectedTypeException($value, 'string');
        }

        $parts = $this->splitChain($value);

        // a chain with a single member is not really a chain...
        if (count($parts) === 1) {
            return;
        }

        for ($i = 1, $count = count($parts); $i < $count; $i++) {
            $this->validatePart($parts[$i], $i, $constraint);
        }
    }

    /** @return string[] */
    private function splitChain(string $certificate): array
    {
        return preg_split(
            '~(?<=' . self::EPILOG . ")(?:\r\n|[\r\n])(?=" . self::PROLOG . ')~s',
            $certificate,
        );
    }

    private function validatePart(string $certificate, int $offset, CertificateChain $constraint): void
    {
        if ($this->isPemCertificateValid($certificate)) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->setParameter('{{ position }}', (string) $offset)
            ->addViolation();
    }
}
