<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Certificate;

use Safe\Exceptions\OpensslException;

use function Safe\openssl_x509_read;

trait PemCertificateValidator
{
    private function isPemCertificateValid(string $certificate): bool
    {
        try {
            @openssl_x509_read($certificate);
        } catch (OpensslException) {
            return false;
        }

        return true;
    }
}
