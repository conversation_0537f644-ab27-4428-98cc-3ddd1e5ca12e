<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Certificate;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class CertificateValidator extends ConstraintValidator
{
    use PemCertificateValidator;

    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof Certificate) {
            throw new UnexpectedTypeException($constraint, Certificate::class);
        }

        if ($value === null) {
            return;
        }

        if ($value === '') {
            $this->context->buildViolation($constraint->messageCertificateCannotBeEmpty)->addViolation();
        }

        if ($this->isPemCertificateValid($value)) {
            return;
        }

        $this->context->buildViolation($constraint->messageCertificateMustBeValid)->addViolation();
    }
}
