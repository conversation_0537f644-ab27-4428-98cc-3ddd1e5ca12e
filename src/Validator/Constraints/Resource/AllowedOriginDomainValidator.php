<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginUrlSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function array_merge;
use function assert;
use function in_array;
use function is_string;
use function Safe\preg_match;

class AllowedOriginDomainValidator extends ConstraintValidator
{
    use AllowedDomainValidator;

    private const PUSHZONE_PATTERN = '~^\w+\.push-\d+\.cdn77\.com\z~i';
    private const OBJECT_STORAGE_PATTERN = '~^.+\.cdn77-storage\.com\z~i';

    private const FORBIDDEN_DOMAINS = ['c.cdn77.org'];

    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof AllowedOriginDomain) {
            throw new UnexpectedTypeException($constraint, AllowedCnameDomain::class);
        }

        assert(
            $this->context->getObject() instanceof ResourceAddInfo
            || $this->context->getObject() instanceof ResourceEditInfo
            || $this->context->getObject() instanceof OriginUrlSchema
            || $this->context->getObject() === null,
        );

        if ($this->context->getObject() instanceof OriginUrlSchema) {
            $accountId = $this->context->getRoot()->cdnResource->accountId;
        } elseif ($this->context->getObject() === null) {
            $accountId = 0;
        } else {
            $accountId = $this->context->getObject()->accountId;
        }

        if ($value === null || $this->isAllowedAccount($accountId)) {
            return;
        }

        if (! is_string($value)) {
            throw new UnexpectedTypeException($value, 'string');
        }

        if ($this->isSpecialAllowedOriginUrl($value)) {
            return;
        }

        $this->checkForbidenDomains($value, $constraint, $this->getForbiddenDomains());
    }

    /** @return string[] */
    private function getForbiddenDomains(): array
    {
        return array_merge(AllowedCnameDomainValidator::FORBIDDEN_DOMAINS, self::FORBIDDEN_DOMAINS);
    }

    /** @param mixed $value */
    private function isSpecialAllowedOriginUrl($value): bool
    {
        return preg_match(self::PUSHZONE_PATTERN, $value) === 1
            || preg_match(self::OBJECT_STORAGE_PATTERN, $value) === 1
            || preg_match(ResourceOrigin::PATTERN_STREAMING_HOST, $value) === 1
            || in_array($value, ResourceOrigin::ALLOWED_STREAMING_ORIGINS, true);
    }
}
