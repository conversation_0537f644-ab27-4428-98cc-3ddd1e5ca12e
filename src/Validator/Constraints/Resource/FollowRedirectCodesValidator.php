<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Cdn77\NxgApi\Core\Application\Utility\ValueTester;
use Cdn77\NxgApi\Resource\Application\Payload\FollowRedirectSchema;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Webmozart\Assert\Assert;

class FollowRedirectCodesValidator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof FollowRedirectCodes) {
            throw new UnexpectedTypeException($constraint, FollowRedirectCodes::class);
        }

        $followRedirect = $this->context->getObject();
        Assert::nullOrIsInstanceOf($followRedirect, FollowRedirectSchema::class);

        if ($followRedirect === null || $followRedirect->enabled === true) {
            return;
        }

        if (ValueTester::emptyArrayOrNull($followRedirect->codes)) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->addViolation();
    }
}
