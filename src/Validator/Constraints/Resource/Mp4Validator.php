<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function method_exists;

class Mp4Validator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof Mp4) {
            throw new UnexpectedTypeException($constraint, Mp4::class);
        }

        if ($value === null && $this->getQueryStringIgnored() === null) {
            return;
        }

        if (! $this->isSetMp4AndDisabledQueryString($value)) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->addViolation();
    }

    private function isSetMp4AndDisabledQueryString(int $mp4): bool
    {
        return $mp4 === 1 && $this->getQueryStringIgnored() === false;
    }

    private function getQueryStringIgnored(): bool|null
    {
        $schema = $this->context->getRoot();

        if (method_exists($schema, 'getResource')) {
            return $schema->getResource()->isDisableQueryString();
        }

        $schemaVal = $schema->cdnResource->disableQueryString;

        return $schemaVal === null ? null : ValueReplacer::intToBoolean($schemaVal);
    }
}
