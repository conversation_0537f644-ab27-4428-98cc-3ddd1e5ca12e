<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource\Referer;

use Cdn77\NxgApi\Validator\Constraints\Resource\DomainValidator;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function is_string;
use function Safe\preg_match;

class DomainFormatValidator extends ConstraintValidator
{
    private const REGEX = '
            ~
                (?:
                    (' . DomainValidator::REGEX_DOMAIN . '\.){1,126}
                    ' . DomainValidator::REGEX_TLD . self::REGEX_PORT . '
                    |
                    \*\.(' . DomainValidator::REGEX_DOMAIN . '\.){0,125}
                    ' . DomainValidator::REGEX_TLD . '
                    |
                    (' . DomainValidator::REGEX_DOMAIN . '\.){1,126}
                    \*
                )
            \z~Aix';

    // Port range: 1-65535, https://regex101.com/r/DnupLr/1
    private const REGEX_PORT = '(:[1-9]\d{0,3}|:[1-5]\d{4}|:6[0-4]\d{3}|:65[0-4]\d{2}|:655[0-2]\d|:6553[0-5])?';

    private const REGEX_LOCALHOST = '~^localhost' . self::REGEX_PORT . '$~';

    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof DomainFormat) {
            throw new UnexpectedTypeException($constraint, DomainFormat::class);
        }

        if ($value === null) {
            return;
        }

        if (! is_string($value)) {
            throw new UnexpectedTypeException($value, 'string');
        }

        $this->doValidate($value, $constraint);
    }

    private function doValidate(string $value, DomainFormat $constraint): void
    {
        if (preg_match(self::REGEX, $value) === 1) {
            return;
        }

        if (preg_match(self::REGEX_LOCALHOST, $value) === 1) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->setParameter('%string%', $value)
            ->addViolation();
    }
}
