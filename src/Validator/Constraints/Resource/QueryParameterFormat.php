<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Symfony\Component\Validator\Constraints\Regex;

/**
 * @Annotation
 * @Target({"PROPERTY", "ANNOTATION"})
 */
class QueryParameterFormat extends Regex
{
    public const ALLOWED_FORMAT_RE = '
        ~^
            [a-zA-Z0-9_-]+
            (?:\[[a-zA-Z0-9_-]*\])*
        \z~x';

    /** @param mixed[]|null $options */
    public function __construct(array|null $options = [])
    {
        parent::__construct((array) $options + ['pattern' => self::ALLOWED_FORMAT_RE]);
    }
}
