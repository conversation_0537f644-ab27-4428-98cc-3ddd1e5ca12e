<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function is_string;
use function str_ends_with;
use function str_starts_with;

class OriginBaseDirValidator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof OriginBaseDir) {
            throw new UnexpectedTypeException($constraint, OriginBaseDir::class);
        }

        if ($value === null) {
            return;
        }

        if (! is_string($value)) {
            $this->context
                ->buildViolation('Origin base dir must be of type string.')
                ->addViolation();

            return;
        }

        $startsWithSlash = str_starts_with($value, '/');
        $endsWithSlash = str_ends_with($value, '/');

        if (! $startsWithSlash && ! $endsWithSlash) {
            return;
        }

        $this->context
            ->buildViolation($constraint->message)
            ->addViolation();
    }
}
