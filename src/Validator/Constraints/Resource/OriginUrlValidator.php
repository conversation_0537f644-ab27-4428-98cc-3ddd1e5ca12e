<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function is_string;
use function Safe\preg_match;

class OriginUrlValidator extends ConstraintValidator
{
    private const REGEX_SUBDOMAIN = '[a-z0-9_]([a-z0-9-_]{0,61}[a-z0-9_])?';
    private const REGEX_SUBDOMAIN_MIN = 0;
    private const REGEX_SUBDOMAIN_MAX = 125;

    // https://regex101.com/r/qLsVl3/1
    private const REGEX = '~^' .
        '(' . self::REGEX_SUBDOMAIN . '\.)' .
        '{' . self::REGEX_SUBDOMAIN_MIN . ',' . self::REGEX_SUBDOMAIN_MAX . '}' .
        DomainValidator::REGEX_DOMAIN . '\.' .
        DomainValidator::REGEX_TLD . '
    $~ix';

    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof OriginUrl) {
            throw new UnexpectedTypeException($constraint, OriginUrl::class);
        }

        if ($value === null) {
            return;
        }

        if (! is_string($value)) {
            throw new UnexpectedTypeException($value, 'string');
        }

        $this->doValidate($value, $constraint);
    }

    private function doValidate(string $value, OriginUrl $constraint): void
    {
        if (preg_match(self::REGEX, $value) === 1) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->setParameter('%string%', $value)
            ->addViolation();
    }
}
