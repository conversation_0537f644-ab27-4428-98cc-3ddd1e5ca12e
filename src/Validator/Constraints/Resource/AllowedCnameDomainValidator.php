<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Webmozart\Assert\Assert;

use function assert;
use function is_string;

class AllowedCnameDomainValidator extends ConstraintValidator
{
    use AllowedDomainValidator;

    public const FORBIDDEN_DOMAINS = [
        'rsc.cdn77.org',
        'ripe.cdn77.org',
        'cdn77.com',
        'cdn77.eu',
        'cdn77.dev',
        'cdn77-storage.com',
    ];

    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof AllowedCnameDomain) {
            throw new UnexpectedTypeException($constraint, AllowedCnameDomain::class);
        }

        assert(
            $this->context->getObject() instanceof ResourceAddInfo
            || $this->context->getObject() instanceof ResourceEditInfo
            || $this->context->getObject() === null,
        );

        if ($this->context->getObject() === null) {
            $accountId = 0;
        } else {
            $accountId = $this->context->getObject()->accountId;
        }

        Assert::integer($accountId);

        if ($value === null || $this->isAllowedAccount($accountId)) {
            return;
        }

        if (! is_string($value)) {
            throw new UnexpectedTypeException($value, 'string');
        }

        $this->checkForbidenDomains($value, $constraint, self::FORBIDDEN_DOMAINS);
    }
}
