<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;
use Cdn77\NxgApi\Resource\Domain\Value\S3ConnectionInfo;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Webmozart\Assert\Assert;

use function Safe\preg_match;

final class Cdn77CephRgwOriginHostValidator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof Cdn77CephRgwOriginHost) {
            throw new UnexpectedTypeException($constraint, Cdn77CephRgwOriginHost::class);
        }

        Assert::isInstanceOf($value, ResourceOrigin::class);

        $s3BucketName = $value->getS3()->getBucketName();
        $originHost = $value->getHost();
        $isValidHost = preg_match(CdnResource::PATTERN_CDN77_CEPH_RGW_ORIGIN_HOST, $originHost) === 1;
        $isValidS3Type = $value->getS3()->getType() === S3ConnectionInfo::TYPE_CDN77_CEPH_RGW;

        if ($s3BucketName === null && ! $isValidHost && ! $isValidS3Type) {
            return;
        }

        if ($s3BucketName !== null && $isValidHost && $isValidS3Type) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->setParameter('%string%', $originHost)
            ->addViolation();
    }
}
