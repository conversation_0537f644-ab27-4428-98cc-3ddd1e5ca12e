<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Cdn77\NxgApi\Resource\Application\Payload\ResourceSslSchema;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class SslValidator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof Ssl) {
            throw new UnexpectedTypeException($constraint, Ssl::class);
        }

        if ($value === null) {
            return;
        }

        $instantSslValue = $this->getInstantSslSetting();
        $valueContainsEmptyCertificateData = $value instanceof ResourceSslSchema
            && $value->certificate === null
            && $value->key === null;
        if ($valueContainsEmptyCertificateData && $instantSslValue !== null) {
            return;
        }

        $valueContainsCertificateData = $value instanceof ResourceSslSchema
            && $value->certificate !== null
            && $value->key !== null;
        if ($valueContainsCertificateData && $instantSslValue === false) {
            return;
        }

        $this->context
            ->buildViolation($constraint->message)
            ->addViolation();
    }

    private function getInstantSslSetting(): bool|null
    {
        $schema = $this->context->getRoot();

        return $schema->resourceDto()->instantSsl;
    }
}
