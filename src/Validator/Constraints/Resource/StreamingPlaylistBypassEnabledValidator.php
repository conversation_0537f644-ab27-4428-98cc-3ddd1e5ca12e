<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Webmozart\Assert\Assert;

final class StreamingPlaylistBypassEnabledValidator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof StreamingPlaylistBypassEnabled) {
            throw new UnexpectedTypeException($constraint, StreamingPlaylistBypassEnabled::class);
        }

        Assert::isInstanceOf($value, CdnResource::class);

        if (! $value->getMainOrigin()->isStreamingOrigin()) {
            return;
        }

        if ($value->hasStreamingPlaylistBypass()) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->addViolation();
    }
}
