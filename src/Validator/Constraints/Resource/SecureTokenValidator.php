<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Cdn77\NxgApi\Service\Legacy\Edit\ResourceChanger;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class SecureTokenValidator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof SecureToken) {
            throw new UnexpectedTypeException($constraint, SecureToken::class);
        }

        $schema = $this->context->getRoot();

        if ($schema->cdnResource->secureToken === null) {
            return;
        }

        if ($schema->cdnResource->secureToken->secureTokenType === ResourceChanger::DISABLE_SECURE_TOKEN_TYPE_STRING) {
            return;
        }

        if ($value !== null && $value !== '') {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->addViolation();
    }
}
