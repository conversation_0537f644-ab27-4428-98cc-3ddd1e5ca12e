<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function Safe\preg_match;
use function strlen;

class DomainValidator extends ConstraintValidator
{
    // based on RFC 952 & RFC 1123
    // also see https://en.wikipedia.org/wiki/Domain_Name_System#Domain_name_syntax
    public const REGEX_DOMAIN = '(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)';
    public const REGEX_TLD = '(?:[a-z][a-z0-9-]{0,61}[a-z0-9])';
    private const REGEX_DOMAIN_MIN = 1;
    private const REGEX_DOMAIN_MAX = 126;
    private const REGEX = '~
            ^
                (' . self::REGEX_DOMAIN . '\.){' . self::REGEX_DOMAIN_MIN . ',' . self::REGEX_DOMAIN_MAX . '}
                ' . self::REGEX_TLD . '
            $
        ~ix';

    private const MAX_LENGTH = 253;

    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof Domain) {
            throw new UnexpectedTypeException($constraint, Domain::class);
        }

        if ($value === null) {
            return;
        }

        if (strlen($value) <= self::MAX_LENGTH && preg_match(self::REGEX, $value) === 1) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->setParameter('%string%', $value)
            ->addViolation();
    }
}
