<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Cdn77\NxgApi\Repository\Legacy\LocationGroupRepository;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function in_array;

class LocationGroupValidator extends ConstraintValidator
{
    /** @var LocationGroupRepository */
    protected $locationGroupRepository;

    public function __construct(LocationGroupRepository $locationGroupRepository)
    {
        $this->locationGroupRepository = $locationGroupRepository;
    }

    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof LocationGroup) {
            throw new UnexpectedTypeException($constraint, LocationGroup::class);
        }

        if ($value === null) {
            return;
        }

        $validGroupIds = $this->getValidGroupIds();

        foreach ($value as $groupId) {
            if (in_array($groupId, $validGroupIds, true)) {
                continue;
            }

            $this->context->buildViolation($constraint->message)
                ->setParameter('%string%', (string) $groupId)
                ->addViolation();
        }
    }

    /** @return int[] */
    private function getValidGroupIds(): array
    {
        $groups = $this->locationGroupRepository->findAll();

        $groupIds = [];
        foreach ($groups as $g) {
            $groupIds[] = $g->getId();
        }

        return $groupIds;
    }
}
