<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use function in_array;
use function preg_quote;
use function Safe\preg_match;

trait AllowedDomainValidator
{
    /**
     * @param mixed                                  $value
     * @param AllowedCnameDomain|AllowedOriginDomain $constraint
     * @param string[]                               $forbiddenDomains
     */
    private function checkForbidenDomains($value, $constraint, array $forbiddenDomains): void
    {
        foreach ($forbiddenDomains as $domain) {
            if ($domain !== $value && preg_match($this->createPattern($domain), $value) !== 1) {
                continue;
            }

            $this->context->buildViolation($constraint->message)->setParameter('%domain%', $domain)->addViolation();
        }
    }

    private function createPattern(string $domain): string
    {
        return '~^.*\.' . preg_quote($domain, '~') . '\z~i';
    }

    private function isAllowedAccount(int $accountId): bool
    {
        return in_array($accountId, $this->getAllowedAccounts(), true);
    }

    /** @return int[] */
    private function getAllowedAccounts(): array
    {
        return [
            85, // <EMAIL>
            199, // <EMAIL>
            1554, // <EMAIL>
            18534, // <EMAIL>
            20940, // <EMAIL>
            29736, // <EMAIL>
            61509, // <EMAIL>
            85478, // <EMAIL>
            88380, // <EMAIL>
            79342, // <EMAIL>
            97250, // <EMAIL>
        ];
    }
}
