<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class ExistsValidator extends ConstraintValidator
{
    /** @var CdnResourceRepository */
    private $resourceRepository;

    public function __construct(CdnResourceRepository $resourceRepository)
    {
        $this->resourceRepository = $resourceRepository;
    }

    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! ($constraint instanceof Exists)) {
            throw new UnexpectedTypeException($constraint, Exists::class);
        }

        if ($value === null) {
            return;
        }

        $resource = $this->resourceRepository->find($value);

        if ($resource !== null) {
            return;
        }

        $this->context->addViolation($constraint->message, ['{{ id }}' => $value]);
    }
}
