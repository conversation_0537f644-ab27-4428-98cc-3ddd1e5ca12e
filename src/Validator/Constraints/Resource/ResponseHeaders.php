<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Symfony\Component\Validator\Constraint;

/** @Annotation */
class ResponseHeaders extends Constraint
{
    public string $forbiddenPattern = 'Response header name containing "x-77-" is forbidden.';
    public string $forbiddenName = 'Response header name "%string%" is forbidden.';
    public string $invalidName = 'Response header name "%string%" contains invalid characters.';
    public string $invalidValue = 'Response header value "%string%" contains invalid characters.';
    public string $corsUsed = 'Response header name "%string%" is not allowed when cors_origin_header is enabled.';
}
