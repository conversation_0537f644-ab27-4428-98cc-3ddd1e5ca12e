<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Webmozart\Assert\Assert;

use function array_map;
use function array_search;
use function count;
use function sprintf;
use function strtolower;

class UniqueCnameValidator extends ConstraintValidator
{
    /** @var CdnResourceRepository */
    protected $resourceRepository;

    public function __construct(CdnResourceRepository $resourceRepository)
    {
        $this->resourceRepository = $resourceRepository;
    }

    /**
     * @param mixed $value
     *
     * @phpcsSuppress SlevomatCodingStandard.TypeHints.TypeHintDeclaration.MissingParameterTypeHint
     */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof UniqueCname) {
            throw new UnexpectedTypeException($constraint, UniqueCname::class);
        }

        if ($value === null) {
            return;
        }

        if (count($value) === 0) {
            return;
        }

        $existingCames = $this->resourceRepository->findUsedCnames($value);

        if (count($existingCames) === 0) {
            return;
        }

        foreach ($existingCames as $cname) {
            $this->context->buildViolation($constraint->message)
                ->setParameter('%string%', $cname)
                ->atPath(sprintf('[%d]', $this->getCnameIndex($cname, $value)))
                ->addViolation();
        }
    }

    /** @param string[] $cnames */
    private function getCnameIndex(string $cname, array $cnames): int
    {
        $key = array_search(strtolower($cname), array_map('strtolower', $cnames), true);
        Assert::integer($key);

        return $key;
    }
}
