<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function Safe\preg_match;

class DomainOrIpValidator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof DomainOrIp) {
            throw new UnexpectedTypeException($constraint, DomainOrIp::class);
        }

        if ($value === null) {
            return;
        }

//        $sDomainLevelRegex = "[a-zA-Z0-9][a-zA-Z0-9-]*";
//        $sDomainEndRegex = "[a-zA-Z]{2,6}";
//        $sDomainRegex = "(($sDomainLevelRegex)\\.)+($sDomainEndRegex)";
//        $sIpNumber009Regex = "[0-9]";
//        $sIpNumber099Regex = "[1-9][0-9]";
//        $sIpNumber199Regex = "1[0-9]{2}";
//        $sIpNumber249Regex = "2[0-4][0-9]";
//        $sIpNumber255Regex = "25[0-5]";
//        $sIpNumberRegex = "($sIpNumber009Regex)|($sIpNumber099Regex)|($sIpNumber199Regex)|"
//            .."($sIpNumber249Regex)|($sIpNumber255Regex)";
//        $sIpRegex = "(($sIpNumberRegex)\\.){3}($sIpNumberRegex)";
//        $sIpDomainRegex = "(($sDomainRegex)|($sIpRegex))";
//        $sRegex = "#^($sIpDomainRegex)$#";
        $sRegex = '#^((((([a-zA-Z0-9][a-zA-Z0-9-]*)\\.)+'
            . '([a-zA-Z0-9-]{2,}))|(((([0-9])|([1-9][0-9])|(1[0-9]{2})|(2[0-4][0-9])|(25[0-5]))\\.){3}'
            . '(([0-9])|([1-9][0-9])|(1[0-9]{2})|(2[0-4][0-9])|(25[0-5])))))$#';

        if (preg_match($sRegex, $value) === 1) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->setParameter('%string%', $value)
            ->addViolation();
    }
}
