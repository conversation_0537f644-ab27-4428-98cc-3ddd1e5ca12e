<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function in_array;
use function is_array;
use function Safe\preg_match;
use function strtolower;

final class OriginHeadersValidator extends ConstraintValidator
{
    private const FORBIDDEN_HEADER_NAMES = [
        'via',
        'accept',
        'range',
    ];

    private const PATTERN_ALLOWED_NAME_CHARS = '~^[a-zA-Z0-9. \-/_\[\]+%*=;]*$~';
    private const PATTERN_ALLOWED_VALUE_CHARS = '~^[a-zA-Z0-9.: \-/_\[\]+%*=;]*$~';

    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof OriginHeaders) {
            throw new UnexpectedTypeException($constraint, OriginHeaders::class);
        }

        if ($value === null) {
            return;
        }

        if (! is_array($value)) {
            throw new UnexpectedTypeException($value, 'array');
        }

        if ($value === []) {
            return;
        }

        foreach ($value as $headerName => $headerValue) {
            if (in_array(strtolower($headerName), self::FORBIDDEN_HEADER_NAMES, true)) {
                $this->context->buildViolation($constraint->forbiddenName)
                    ->setParameter('%string%', $headerName)
                    ->addViolation();
            }

            if (preg_match(self::PATTERN_ALLOWED_NAME_CHARS, $headerName) === 0) {
                $this->context->buildViolation($constraint->invalidName)
                    ->setParameter('%string%', $headerName)
                    ->addViolation();
            }

            if (preg_match(self::PATTERN_ALLOWED_VALUE_CHARS, $headerValue) === 1) {
                continue;
            }

            $this->context->buildViolation($constraint->invalidValue)
                ->setParameter('%string%', $headerValue)
                ->addViolation();
        }
    }
}
