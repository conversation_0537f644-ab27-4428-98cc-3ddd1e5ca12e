<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Core\Application\Utility\ValueTester;
use Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeaderSchema;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Webmozart\Assert\Assert;

use function in_array;
use function Safe\preg_match;
use function strtolower;

final class ResponseHeadersValidator extends ConstraintValidator
{
    private const ACCESS_CONTROL_ALLOW_ORIGIN = 'access-control-allow-origin';

    private const FORBIDDEN_HEADER_NAMES = [
        'accept-ranges',
        'alt-svc',
        'connection',
        'content-encoding',
        'content-length',
        'content-range',
        'date',
        'trailer',
        'transfer-encoding',
        'upgrade',
        'server',
        'www-authenticate',
    ];

    private const FORBIDDEN_PATTERN = '~^x-77-~';
    private const PATTERN_ALLOWED_NAME_CHARS = '~^[a-zA-Z0-9. \-/_\[\]+%*=;]*$~';
    private const PATTERN_ALLOWED_VALUE_CHARS = '~^[a-zA-Z0-9.,:;!?"\' \-/_\[\](){}<>`|^@$&+%*=]*$~';

    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof ResponseHeaders) {
            throw new UnexpectedTypeException($constraint, ResponseHeaders::class);
        }

        if (ValueTester::emptyArrayOrNull($value?->headers)) {
            return;
        }

        Assert::allIsInstanceOf($value->headers, ResponseHeaderSchema::class);

        foreach ($value->headers as $responseHeader) {
            if ($this->isCorsEnabledAndAccessControlHeaderIsUsed($responseHeader->name)) {
                $this->context->buildViolation($constraint->corsUsed)
                    ->setParameter('%string%', $responseHeader->name)
                    ->addViolation();

                continue;
            }

            if (in_array(strtolower($responseHeader->name), self::FORBIDDEN_HEADER_NAMES, true)) {
                $this->context->buildViolation($constraint->forbiddenName)
                    ->setParameter('%string%', $responseHeader->name)
                    ->addViolation();

                continue;
            }

            if (preg_match(self::FORBIDDEN_PATTERN, $responseHeader->name) === 1) {
                $this->context->buildViolation($constraint->forbiddenPattern)
                    ->addViolation();

                continue;
            }

            if (preg_match(self::PATTERN_ALLOWED_NAME_CHARS, $responseHeader->name) === 0) {
                $this->context->buildViolation($constraint->invalidName)
                    ->setParameter('%string%', $responseHeader->name)
                    ->addViolation();

                continue;
            }

            if (preg_match(self::PATTERN_ALLOWED_VALUE_CHARS, $responseHeader->value) === 0) {
                $this->context->buildViolation($constraint->invalidValue)
                    ->setParameter('%string%', $responseHeader->value)
                    ->addViolation();

                continue;
            }
        }
    }

    private function isCorsEnabledAndAccessControlHeaderIsUsed(string $headerName): bool
    {
        return strtolower($headerName) === self::ACCESS_CONTROL_ALLOW_ORIGIN
            && ValueReplacer::intToBoolean($this->context->getRoot()->cdnResource->corsOriginHeader);
    }
}
