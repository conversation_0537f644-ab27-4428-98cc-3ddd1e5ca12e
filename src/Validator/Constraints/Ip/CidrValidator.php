<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Ip;

use Cdn77\NxgApi\Core\Domain\Value;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Throwable;
use Webmozart\Assert\InvalidArgumentException;

use function is_string;

class CidrValidator extends ConstraintValidator
{
    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof Cidr) {
            throw new UnexpectedTypeException($constraint, Cidr::class);
        }

        if ($value === null) {
            return;
        }

        if (! is_string($value)) {
            throw new UnexpectedTypeException($value, 'string');
        }

        $this->doValidate($value);
    }

    private function doValidate(string $value): void
    {
        try {
            Value\Cidr::fromCidr($value);
        } catch (InvalidArgumentException | Throwable) {
            $this->context
                ->buildViolation(Value\Cidr::MESSAGE_INVALID_FORMAT)
                ->addViolation();

            return;
        }
    }
}
