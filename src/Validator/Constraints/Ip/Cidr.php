<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Ip;

use Symfony\Component\Validator\Constraint;

/**
 * @Annotation
 * @Target({"PROPERTY", "ANNOTATION"})
 */
class Cidr extends Constraint
{
    /** @var string */
    public $invalidFormatMessage = 'Value is not a valid IPv4/IPv6 CIDR notation.';

    /** @var string */
    public $invalidNetworkAddressMessage = 'IP address is not a prefix of the network.';
}
