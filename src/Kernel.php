<?php

declare(strict_types=1);

namespace Cdn77\NxgApi;

use Cdn77\MonMasterDetectorBundle\MonMasterDetectorBundle;
use Cdn77\NxgApi\Core\Application\Container\OpenApiPathsCompilerPass;
use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\CoreLibrary\Symfony\KernelContainerConfigurator;
use Cdn77\NxgApi\DependencyInjection\Compiler\CacheLifetimePass;
use Cdn77\NxgApi\DependencyInjection\Compiler\LetsEncryptDomainChoosersPass;
use DAMA\DoctrineTestBundle\DAMADoctrineTestBundle;
use Doctrine\Bundle\DoctrineBundle\DoctrineBundle;
use JMS\SerializerBundle\JMSSerializerBundle;
use League\Tactician\Bundle\TacticianBundle;
use Oneup\FlysystemBundle\OneupFlysystemBundle;
use Sentry\SentryBundle\SentryBundle;
use SimPod\KafkaBundle\SimPodKafkaBundle;
use Symfony\Bundle\FrameworkBundle\FrameworkBundle;
use Symfony\Bundle\FrameworkBundle\Kernel\MicroKernelTrait;
use Symfony\Bundle\MonologBundle\MonologBundle;
use Symfony\Component\Config\Loader\Loader;
use Symfony\Component\Config\Loader\LoaderInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\HttpKernel\Bundle\BundleInterface;
use Symfony\Component\HttpKernel\Kernel as BaseKernel;
use Symfony\Component\Routing\Loader\Configurator\RoutingConfigurator;

use function assert;
use function date_default_timezone_set;
use function dirname;
use function is_dir;

class Kernel extends BaseKernel
{
    use MicroKernelTrait;

    private const CONFIG_EXT = '.yaml';
    private const TACTICIAN_HANDLER_TAG = 'tactician.handler';
    private const SYMFONY_ARGUMENT_TYPEHINTS = 'typehints';

    public function __construct(string $environment, bool $debug)
    {
        date_default_timezone_set('UTC');

        parent::__construct($environment, $debug);
    }

    /** @return iterable|BundleInterface[] */
    public function registerBundles(): iterable
    {
        yield new MonMasterDetectorBundle();
        yield new DoctrineBundle();
        yield new JMSSerializerBundle();
        yield new OneupFlysystemBundle();
        yield new FrameworkBundle();
        yield new MonologBundle();
        yield new SentryBundle();
        yield new SimPodKafkaBundle();
        yield new TacticianBundle();

        if ($this->environment !== 'test') {
            return;
        }

        yield new DAMADoctrineTestBundle();
    }

    public function getName(): string
    {
        return 'nxgApi';
    }

    public function getRootDir(): string
    {
        return __DIR__;
    }

    public function getProjectDir(): string
    {
        return dirname(__DIR__);
    }

    public function getCacheDir(): string
    {
        return $this->getProjectDir() . '/var/cache/' . $this->environment;
    }

    public function getLogDir(): string
    {
        return $this->getProjectDir() . '/var/logs';
    }

    protected function build(ContainerBuilder $container): void
    {
        $container->registerForAutoconfiguration(CommandHandler::class)
            ->addTag(self::TACTICIAN_HANDLER_TAG, [self::SYMFONY_ARGUMENT_TYPEHINTS => true]);
        $container->registerForAutoconfiguration(QueryHandler::class)
            ->addTag(self::TACTICIAN_HANDLER_TAG, [self::SYMFONY_ARGUMENT_TYPEHINTS => true]);
        $container->registerForAutoconfiguration(HasOpenApiPaths::class)
            ->addTag(OpenApiPathsCompilerPass::TAG_NAME);
    }

    protected function configureContainer(ContainerBuilder $containerBuilder, LoaderInterface $loader): void
    {
        $confDir = $this->getProjectDir() . '/config';

        assert($loader instanceof Loader);

        $loader->load(static function (ContainerBuilder $container): void {
            $container->setParameter('container.autowiring.strict_mode', true);
            $container->setParameter('container.dumper.inline_class_loader', true);
        });

        $configurator = new KernelContainerConfigurator($loader);
        $configurator->configure();

        $loader->import($confDir . '/packages/*' . self::CONFIG_EXT, 'glob');
        if (is_dir($confDir . '/packages/' . $this->environment)) {
            $loader->import($confDir . '/packages/' . $this->environment . '/**/*' . self::CONFIG_EXT, 'glob');
        }

        $loader->load($confDir . '/services/*' . self::CONFIG_EXT, 'glob');
        if (is_dir($confDir . '/services/' . $this->environment)) {
            $loader->load($confDir . '/services/' . $this->environment . '/*' . self::CONFIG_EXT, 'glob');
        }

        $this->configureContainerPasses($containerBuilder);
    }

    protected function configureRoutes(RoutingConfigurator $routes): void
    {
        $confDir = $this->getProjectDir() . '/config';

        if (is_dir($confDir . '/routes')) {
            $routes->import($confDir . '/routes/*' . self::CONFIG_EXT, 'yaml');
        }

        if (! is_dir($confDir . '/routes/' . $this->environment)) {
            return;
        }

        $routes->import($confDir . '/routes/' . $this->environment . '/**/*' . self::CONFIG_EXT, 'yaml');
    }

    private function configureContainerPasses(ContainerBuilder $containerBuilder): void
    {
        $containerBuilder->addCompilerPass(new LetsEncryptDomainChoosersPass());
        $containerBuilder->addCompilerPass(new CacheLifetimePass());
        $containerBuilder->addCompilerPass(new OpenApiPathsCompilerPass());
    }
}
