<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\NgxConfGen\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\Schema as OASchema;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

final class ResourceIdsSchema implements OASchema
{
    public const PARAMETER_RESOURCE_IDS = 'ids';

    /**
     * @Serializer\SerializedName(self::PARAMETER_RESOURCE_IDS)
     * @Serializer\Type("commaSeparatedInteger")
     * @var list<int>|null
     */
    public array|null $ids = null;

    /** @return list<Parameter> */
    public static function getParametersSpec(): array
    {
        return [
            new Parameter([
                'name' => self::PARAMETER_RESOURCE_IDS,
                'in' => 'query',
                'required' => false,
                'schema' => new Schema(['type' => Type::STRING]),
            ]),
        ];
    }
}
