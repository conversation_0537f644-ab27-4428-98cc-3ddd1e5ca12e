<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\NgxConfGen\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\QueryBusResultSchema;
use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;
use Cdn77\NxgApi\NgxConfGen\Domain\Value\ResourceConfiguration;
use Cdn77\NxgApi\NgxConfGen\Domain\Value\ResourceOriginConfiguration;
use DateTimeImmutable;
use J<PERSON>\Serializer\Annotation as Serializer;

use function in_array;

final class ResourceSchema implements QueryBusResultSchema
{
    public string|null $s3BucketName;

    public string|null $s3Type;

    public int $id;

    public string $cdnUrl;

    public string $originUrl;

    public string $originScheme;

    /**
     * @Serializer\Type("array<string>")
     * @var list<string>
     */
    public array $cnames = [];

    /** @Serializer\Type("DateTimeImmutable<'Y-m-d\TH:i:s\Z'>") */
    public DateTimeImmutable|null $suspended;

    public bool $disableQueryString;

    public bool $ignoreSetCookie;

    public bool $mp4PseudoStreaming;

    public int|null $cacheExpiry;

    /** @Serializer\SerializedName("cache_expiry_404") */
    public int|null $cacheExpiry404;

    public bool $cacheBypass;

    public string $secureToken;

    public string $secureTokenPath;

    public int $purgeAllKey;

    public int|null $cacheStatKey;

    public int|null $sslFileIndex;

    public bool $fullLogs;

    /**
     * @Serializer\Type("array<string>")
     * @var list<string>|null
     */
    public array|null $ignoredQueryParams;

    public int|null $httpsRedirectCode;

    public string|null $originBasedir;

    public bool|null $refererDenyEmpty;

    public string|null $refererType;

    /**
     * @Serializer\Type("array<string>")
     * @var list<string>|null
     */
    public array|null $refererDomains;

    public int|null $originPort;

    public string|null $originProxyIp = null; // legacy value

    public bool|null $originProxyCached = null; // legacy value

    public string|null $ipProtectionType;

    /**
     * @Serializer\Type("array<string>")
     * @var list<string>|null
     */
    public $ipProtectionAddresses;

    public string|null $geoProtectionType;

    /**
     * @Serializer\Type("array<string>")
     * @var list<string>|null
     */
    public array|null $geoProtectionCountries;

    /**
     * @Serializer\Type("array<string>")
     * @var list<string>|null
     */
    public array|null $secureTokenUaBypass = null;

    public int|null $originTimeout;

    public int|null $cacheMinUses;

    public bool $forwardHostHeader;

    public bool $streamingPlaylistBypass;

    public int|null $cacheLockAge;

    public int|null $cacheLockTimeout;

    public int|null $cacheContentLengthLimit;

    public int|null $cacheNoContentLengthLimit;

    public int|null $upstreamFailTimeout;

    public int|null $upstreamNextAttempts;

    /**
     * @Serializer\Type("array")
     * @var array<mixed>|null
     */
    public array|null $customData;

    public bool $quic;

    public bool $waf;

    public bool $corsOriginHeader;

    public bool $corsTimingEnabled;

    public bool $corsWildcardEnabled;

    public string|null $awsAccessKeyId;

    public string|null $s3AccessKeyId;

    public string|null $awsSecret;

    public string|null $s3Secret;

    public string|null $awsRegion;

    public string|null $s3Region;

    public bool $sslVerifyDisable;

    public int $groupId;

    public int $accountId;

    public bool $rateLimit;

    public bool $contentDispositionByParam;

    /**  @var array<string, string>|null */
    public array|null $originHeaders;

    /**  @var array<string, string>|null */
    public array|null $responseHeaders;

    public string|null $secureTokenType;

    public string|null $secureTokenValue;

    public string|null $secureLinkExpiryParam;

    public string|null $secureLinkTokenParam;

    public string|null $secureLinkPathlenParam;

    public string|null $secureLinkSecretParam;

    public bool $secureLinkRewritePlaylist;

    public bool $followRedirectOrigin;

    /**
     * @Serializer\Type("array<int>")
     * @var list<int>|null
     */
    public array|null $followRedirectCodes;

    public OriginsSchema $origins;

    /**
     * @param list<string> $cnames
     * @param list<string>|null $ignoredQueryParams
     * @param list<string>|null $refererDomains
     * @param list<string>|null $ipProtectionAddresses
     * @param list<string>|null $geoProtectionCountries
     * @param array<mixed>|null $customData
     * @param array<int>|null $followRedirectCodes
     * @param array<string, string>|null $originHeaders
     * @param array<string, string>|null $responseHeaders
     * @param array<ResourceOriginConfiguration> $origins
     */
    public function __construct(
        int $id,
        int $accountId,
        string $cdnUrl,
        int $groupId,
        string $originUrl,
        string $originScheme,
        array $cnames,
        DateTimeImmutable|null $suspended,
        bool $disableQueryString,
        bool $ignoreSetCookie,
        bool $mp4PseudoStreaming,
        int|null $cacheExpiry,
        int|null $cacheExpiry404,
        bool $cacheBypass,
        int $purgeAllKey,
        int|null $cacheStatKey,
        int|null $sslFileIndex,
        bool $fullLogs,
        array|null $ignoredQueryParams,
        int|null $httpsRedirectCode,
        string|null $originBasedir,
        bool|null $refererDenyEmpty,
        string|null $refererType,
        array|null $refererDomains,
        int|null $originPort,
        string|null $ipProtectionType,
        array|null $ipProtectionAddresses,
        string|null $geoProtectionType,
        array|null $geoProtectionCountries,
        int|null $originTimeout,
        int|null $cacheMinUses,
        bool $forwardHostHeader,
        bool $streamingPlaylistBypass,
        int|null $cacheLockAge,
        int|null $cacheLockTimeout,
        int|null $cacheContentLengthLimit,
        int|null $cacheNoContentLengthLimit,
        int|null $upstreamFailTimeout,
        int|null $upstreamNextAttempts,
        array|null $customData,
        bool $quic,
        bool $waf,
        bool $corsOriginHeader,
        bool $corsTimingEnabled,
        bool $corsWildcardEnabled,
        string|null $s3AccessKeyId,
        string|null $s3Secret,
        string|null $s3Region,
        string|null $s3BucketName,
        string|null $s3Type,
        bool $sslVerifyDisable,
        bool $rateLimit,
        bool $contentDispositionByParam,
        array|null $originHeaders,
        array|null $responseHeaders,
        string|null $secureTokenType,
        string|null $secureTokenValue,
        string|null $secureLinkExpiryParam,
        string|null $secureLinkTokenParam,
        string|null $secureLinkPathlenParam,
        string|null $secureLinkSecretParam,
        bool $secureLinkRewritePlaylist,
        bool $followRedirectOrigin,
        array|null $followRedirectCodes,
        array $origins,
    ) {
        $this->id = $id;
        $this->accountId = $accountId;
        $this->cdnUrl = $cdnUrl;
        $this->groupId = $groupId;
        $this->originUrl = $originUrl;
        $this->originScheme = $originScheme;
        $this->cnames = $cnames;
        $this->suspended = $suspended;
        $this->disableQueryString = $disableQueryString;
        $this->ignoreSetCookie = $ignoreSetCookie;
        $this->mp4PseudoStreaming = $mp4PseudoStreaming;
        $this->cacheExpiry = $cacheExpiry;
        $this->cacheExpiry404 = $cacheExpiry404;
        $this->cacheBypass = $cacheBypass;
        $this->purgeAllKey = $purgeAllKey;
        $this->cacheStatKey = $cacheStatKey;
        $this->sslFileIndex = $sslFileIndex;
        $this->fullLogs = $fullLogs;
        $this->ignoredQueryParams = $ignoredQueryParams;
        $this->httpsRedirectCode = $httpsRedirectCode;
        $this->originBasedir = $originBasedir;
        $this->refererDenyEmpty = $refererDenyEmpty;
        $this->refererType = $refererType;
        $this->refererDomains = $refererDomains;
        $this->originPort = $originPort;
        $this->ipProtectionType = $ipProtectionType;
        $this->ipProtectionAddresses = $ipProtectionAddresses;
        $this->geoProtectionType = $geoProtectionType;
        $this->geoProtectionCountries = $geoProtectionCountries;
        $this->originTimeout = $originTimeout;
        $this->cacheMinUses = $cacheMinUses;
        $this->forwardHostHeader = $forwardHostHeader;
        $this->streamingPlaylistBypass = $streamingPlaylistBypass;
        $this->cacheLockAge = $cacheLockAge;
        $this->cacheLockTimeout = $cacheLockTimeout;
        $this->cacheContentLengthLimit = $cacheContentLengthLimit;
        $this->cacheNoContentLengthLimit = $cacheNoContentLengthLimit;
        $this->upstreamFailTimeout = $upstreamFailTimeout;
        $this->upstreamNextAttempts = $upstreamNextAttempts;
        $this->customData = $customData;
        $this->quic = $quic;
        $this->waf = $waf;
        $this->corsOriginHeader = $corsOriginHeader;
        $this->corsTimingEnabled = $corsTimingEnabled;
        $this->corsWildcardEnabled = $corsWildcardEnabled;
        $this->awsAccessKeyId = $s3AccessKeyId;
        $this->s3AccessKeyId = $s3AccessKeyId;
        $this->awsSecret = $s3Secret;
        $this->s3Secret = $s3Secret;
        $this->awsRegion = $s3Region;
        $this->s3Region = $s3Region;
        $this->s3BucketName = $s3BucketName;
        $this->s3Type = $s3Type;
        $this->sslVerifyDisable = $sslVerifyDisable;
        $this->rateLimit = $rateLimit;
        $this->contentDispositionByParam = $contentDispositionByParam;
        $this->originHeaders = ValueReplacer::emptyArrayToNull($originHeaders);
        $this->responseHeaders = ValueReplacer::emptyArrayToNull($responseHeaders);
        $this->secureTokenType = $secureTokenType;
        $this->secureTokenValue = ValueReplacer::nullToEmptyString($secureTokenValue);
        $this->secureLinkExpiryParam = $secureLinkExpiryParam;
        $this->secureLinkTokenParam = $secureLinkTokenParam;
        $this->secureLinkPathlenParam = $secureLinkPathlenParam;
        $this->secureLinkSecretParam = $secureLinkSecretParam;
        $this->secureLinkRewritePlaylist = $secureLinkRewritePlaylist;
        $this->followRedirectOrigin = $followRedirectOrigin;

        /** @var array<int>|null $followRedirectCodes */
        $followRedirectCodes = ValueReplacer::emptyArrayToNull($followRedirectCodes);
        $this->followRedirectCodes = $followRedirectCodes;

        $this->secureToken = in_array(
            $secureTokenType,
            [CdnResource::SECURE_TOKEN_TYPE_PARAMETER, CdnResource::SECURE_TOKEN_TYPE_HIGHWINDS],
            true,
        ) ? ValueReplacer::nullToEmptyString($secureTokenValue) : '';

        $this->secureTokenPath = $secureTokenType === CdnResource::SECURE_TOKEN_TYPE_PATH
            ? ValueReplacer::nullToEmptyString($secureTokenValue) : '';

        $this->origins = OriginsSchema::fromConfigurationOrigins($origins);
    }

    /** @param mixed $result */
    public static function fromQueryBusResult($result): self
    {
        return self::fromDto($result);
    }

    public static function fromDto(ResourceConfiguration $resourceConfiguration): self
    {
        $mainOrigin = $resourceConfiguration->origins[ResourceOrigin::MAIN_PRIORITY];

        return new self(
            $resourceConfiguration->id,
            $resourceConfiguration->accountId,
            $resourceConfiguration->cdnUrl,
            $resourceConfiguration->groupId,
            $mainOrigin->host,
            $mainOrigin->scheme,
            $resourceConfiguration->cnames,
            $resourceConfiguration->suspended,
            $resourceConfiguration->disableQueryString,
            $resourceConfiguration->ignoreSetCookie,
            $resourceConfiguration->mp4PseudoStreaming,
            $resourceConfiguration->cacheExpiry,
            $resourceConfiguration->cacheExpiry404,
            $resourceConfiguration->cacheBypass,
            $resourceConfiguration->purgeAllKey,
            $resourceConfiguration->id,
            $resourceConfiguration->sslFileIndex,
            $resourceConfiguration->fullLogs,
            $resourceConfiguration->ignoredQueryParams,
            $resourceConfiguration->httpsRedirectCode,
            $mainOrigin->basedir,
            $resourceConfiguration->refererDenyEmpty,
            $resourceConfiguration->refererType,
            $resourceConfiguration->refererDomains,
            $mainOrigin->port,
            $resourceConfiguration->ipProtectionType,
            $resourceConfiguration->ipProtectionAddresses,
            $resourceConfiguration->geoProtectionType,
            $resourceConfiguration->geoProtectionCountries,
            $mainOrigin->timeout,
            $resourceConfiguration->cacheMinUses,
            $mainOrigin->forwardHostHeader,
            $resourceConfiguration->streamingPlaylistBypass,
            $resourceConfiguration->cacheLockAge,
            $resourceConfiguration->cacheLockTimeout,
            $resourceConfiguration->cacheContentLengthLimit,
            $resourceConfiguration->cacheNoContentLengthLimit,
            $resourceConfiguration->upstreamFailTimeout,
            $resourceConfiguration->upstreamNextAttempts,
            $resourceConfiguration->customData,
            $resourceConfiguration->quic,
            $resourceConfiguration->waf,
            $resourceConfiguration->corsOriginHeader,
            $resourceConfiguration->corsTimingEnabled,
            $resourceConfiguration->corsWildcardEnabled,
            $mainOrigin->s3AccessKeyId,
            $mainOrigin->s3Secret,
            $mainOrigin->s3Region,
            $mainOrigin->s3BucketName,
            $mainOrigin->s3Type,
            $mainOrigin->sslVerifyDisable,
            $resourceConfiguration->rateLimit,
            $resourceConfiguration->contentDispositionByParam,
            $mainOrigin->originHeaders,
            $resourceConfiguration->responseHeaders,
            $resourceConfiguration->secureTokenType,
            $resourceConfiguration->secureTokenValue,
            $resourceConfiguration->secureLinkExpiryParam,
            $resourceConfiguration->secureLinkTokenParam,
            $resourceConfiguration->secureLinkPathlenParam,
            $resourceConfiguration->secureLinkSecretParam,
            $resourceConfiguration->secureLinkRewritePlaylist,
            $mainOrigin->followRedirectOrigin,
            $mainOrigin->followRedirectCodes,
            $resourceConfiguration->origins,
        );
    }
}
