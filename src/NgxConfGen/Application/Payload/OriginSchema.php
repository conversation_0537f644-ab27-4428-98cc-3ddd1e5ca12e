<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\NgxConfGen\Application\Payload;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\NgxConfGen\Domain\Value\ResourceOriginConfiguration;
use J<PERSON>\Serializer\Annotation as Serializer;

final class OriginSchema
{
    public int $priority;

    public string $host;

    public string $scheme;

    public string|null $basedir;

    public int|null $port;

    public int|null $timeout;

    public string|null $s3AccessKeyId;

    public string|null $s3Secret;

    public string|null $s3Region;

    public string|null $s3BucketName;

    public string|null $s3Type;

    public bool|null $forwardHostHeader;

    public bool|null $sslVerifyDisable;

    /** @var array<string, string>|null */
    public array|null $originHeaders;

    public bool|null $followRedirectOrigin;

    /**
     * @Serializer\Type("array<int>")
     * @var list<int>|null
     */
    public array|null $followRedirectCodes;

    /**
     * @param array<string, string>|null $originHeaders
     * @param list<int>|null $followRedirectCodes
     */
    public function __construct(
        int $priority,
        string $host,
        string $scheme,
        string|null $basedir = null,
        int|null $port = null,
        int|null $timeout = null,
        string|null $s3AccessKeyId = null,
        string|null $s3Secret = null,
        string|null $s3Region = null,
        string|null $s3BucketName = null,
        string|null $s3Type = null,
        bool|null $forwardHostHeader = null,
        bool|null $sslVerifyDisable = null,
        array|null $originHeaders = null,
        bool|null $followRedirectOrigin = null,
        array|null $followRedirectCodes = null,
    ) {
        $this->priority = $priority;
        $this->host = $host;
        $this->scheme = $scheme;
        $this->basedir = $basedir;
        $this->port = $port;
        $this->timeout = $timeout;
        $this->s3AccessKeyId = $s3AccessKeyId;
        $this->s3Secret = $s3Secret;
        $this->s3Region = $s3Region;
        $this->s3BucketName = $s3BucketName;
        $this->s3Type = $s3Type;
        $this->forwardHostHeader = $forwardHostHeader;
        $this->sslVerifyDisable = $sslVerifyDisable;
        $this->originHeaders = $originHeaders;
        $this->followRedirectOrigin = $followRedirectOrigin;
        $this->followRedirectCodes = $followRedirectCodes;
    }

    public static function fromOriginConfiguration(ResourceOriginConfiguration $originConfiguration): self
    {
        return new self(
            $originConfiguration->priority,
            $originConfiguration->host,
            $originConfiguration->scheme,
            $originConfiguration->basedir,
            $originConfiguration->port,
            $originConfiguration->timeout,
            $originConfiguration->s3AccessKeyId,
            $originConfiguration->s3Secret,
            $originConfiguration->s3Region,
            $originConfiguration->s3BucketName,
            $originConfiguration->s3Type,
            $originConfiguration->forwardHostHeader,
            $originConfiguration->sslVerifyDisable,
            ValueReplacer::emptyArrayToNull($originConfiguration->originHeaders),
            $originConfiguration->followRedirectOrigin,
            ValueReplacer::emptyArrayToNull($originConfiguration->followRedirectCodes),
        );
    }
}
