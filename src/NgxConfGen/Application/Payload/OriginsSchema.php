<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\NgxConfGen\Application\Payload;

use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;
use Cdn77\NxgApi\NgxConfGen\Domain\Value\ResourceOriginConfiguration;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Webmozart\Assert\Assert;

use function ksort;

final class OriginsSchema
{
    /**
     * @Serializer\Type("array<Cdn77\NxgApi\NgxConfGen\Application\Payload\OriginSchema>")
     * @Serializer\Inline
     * @var list<OriginSchema>
     */
    public array $origins = [];

    /** @param list<OriginSchema> $origins */
    public function __construct(array $origins = [])
    {
        $this->origins = $origins;
    }

    /** @param array<ResourceOriginConfiguration> $origins */
    public static function fromConfigurationOrigins(array $origins): self
    {
        Assert::keyExists($origins, ResourceOrigin::MAIN_PRIORITY);

        $schemas = [];

        foreach ($origins as $origin) {
            $schemas[$origin->priority] = OriginSchema::fromOriginConfiguration($origin);
        }

        ksort($schemas);

        return new self($schemas);
    }
}
