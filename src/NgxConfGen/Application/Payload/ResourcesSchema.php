<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\NgxConfGen\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\QueryBusResultSchema;
use Cdn77\NxgApi\NgxConfGen\Domain\Value\ResourceConfiguration;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Webmozart\Assert\Assert;

final class ResourcesSchema implements QueryBusResultSchema
{
    /**
     * @Serializer\Type("array<Cdn77\NxgApi\NgxConfGen\Application\Payload\ResourceSchema>")
     * @Serializer\Inline
     * @var list<ResourceSchema>
     */
    public $resources = [];

    /** @param list<ResourceSchema> $resources */
    public function __construct(array $resources = [])
    {
        $this->resources = $resources;
    }

    /** @param mixed $result */
    public static function fromQueryBusResult($result): self
    {
        $resources = [];

        foreach ($result as $resourceConfiguration) {
            Assert::isInstanceOf($resourceConfiguration, ResourceConfiguration::class);
            $resources[] = ResourceSchema::fromDto($resourceConfiguration);
        }

        return new self($resources);
    }
}
