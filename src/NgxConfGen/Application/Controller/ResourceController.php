<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\NgxConfGen\Application\Controller;

use Cdn77\NxgApi\Core\Application\Response\ControllerQueryHandler;
use Cdn77\NxgApi\NgxConfGen\Application\Payload\ResourceSchema;
use Cdn77\NxgApi\NgxConfGen\Domain\Query\FindConfigurationForResourceId;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class ResourceController
{
    public const ROUTE_NAME = 'resource.nginx-conf-gen.detail';

    private ControllerQueryHandler $controllerQueryHandler;

    public function __construct(ControllerQueryHandler $controllerQueryHandler)
    {
        $this->controllerQueryHandler = $controllerQueryHandler;
    }

    /** @Route(path="/ngx-conf-gen/resources/{resourceId}", name=self::ROUTE_NAME, methods={Request::METHOD_GET}) */
    public function detailAction(int $resourceId): JsonResponse
    {
        return $this->controllerQueryHandler->handle(
            new FindConfigurationForResourceId($resourceId),
            ResourceSchema::class,
            Response::HTTP_OK,
            true,
        );
    }
}
