<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\NgxConfGen\Application\Controller;

use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Application\Response\ControllerQueryHandler;
use Cdn77\NxgApi\NgxConfGen\Application\Payload\ResourceIdsSchema;
use Cdn77\NxgApi\NgxConfGen\Application\Payload\ResourcesSchema;
use Cdn77\NxgApi\NgxConfGen\Domain\Query\FindConfigurationsForResourceIds;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class ResourcesController
{
    public const ROUTE_NAME = 'resource.ngx-conf-gen.list';

    private SerializerInterface $serializer;
    private ControllerQueryHandler $controllerQueryHandler;
    private ControllerSchemaSerializer $controllerSchemaSerializer;

    public function __construct(
        ControllerSchemaSerializer $controllerSchemaSerializer,
        ControllerQueryHandler $controllerQueryHandler,
        SerializerInterface $serializer,
    ) {
        $this->serializer = $serializer;
        $this->controllerQueryHandler = $controllerQueryHandler;
        $this->controllerSchemaSerializer = $controllerSchemaSerializer;
    }

    /** @Route(path="/ngx-conf-gen/resources", name=self::ROUTE_NAME, methods={Request::METHOD_GET}) */
    public function listAction(Request $request): JsonResponse
    {
        $schema = $this->controllerSchemaSerializer->deserializeQueryString($request, ResourceIdsSchema::class);

        if ($schema instanceof ErrorsSchema) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema(['Invalid ids parameter. It should be integers separated with comma.']),
                    'json',
                ),
                Response::HTTP_BAD_REQUEST,
            );
        }

        return $this->controllerQueryHandler->handle(
            new FindConfigurationsForResourceIds($schema->ids),
            ResourcesSchema::class,
            Response::HTTP_OK,
            true,
        );
    }
}
