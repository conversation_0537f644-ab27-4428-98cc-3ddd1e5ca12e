<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\NgxConfGen\Domain\Value;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\HotlinkProtection\Domain\Value\HotlinkProtectionType;
use Cdn77\NxgApi\Types\ResourceCnamesArrayType;
use DateTimeImmutable;

use function explode;
use function Safe\json_decode;

final class ResourceConfiguration
{
    public int $accountId;

    public int $groupId;

    public int $id;

    public string $cdnUrl;

    /** @var list<string> */
    public array $cnames = [];

    public DateTimeImmutable|null $suspended;

    public bool $disableQueryString;

    public bool $ignoreSetCookie;

    public bool $mp4PseudoStreaming;

    public int|null $cacheExpiry;

    public int|null $cacheExpiry404;

    public bool $cacheBypass;

    public int $purgeAllKey;

    public int|null $sslFileIndex;

    public bool $fullLogs;

    /** @var list<string>|null */
    public array|null $ignoredQueryParams;

    public int|null $httpsRedirectCode;

    public bool|null $refererDenyEmpty;

    public string|null $refererType;

    /** @var list<string>|null */
    public array|null $refererDomains;

    public string|null $ipProtectionType;

    /** @var list<string>|null */
    public array|null $ipProtectionAddresses;

    public string|null $geoProtectionType;

    /** @var list<string>|null */
    public array|null $geoProtectionCountries;

    public int|null $cacheMinUses;

    public bool $streamingPlaylistBypass;

    public int|null $cacheLockAge;

    public int|null $cacheLockTimeout;

    public int|null $cacheContentLengthLimit;

    public int|null $cacheNoContentLengthLimit;

    public int|null $upstreamFailTimeout;

    public int|null $upstreamNextAttempts;

    /** @var array<mixed>|null */
    public array|null $customData;

    public bool $quic;

    public bool $waf;

    public bool $corsOriginHeader;

    public bool $corsTimingEnabled;

    public bool $corsWildcardEnabled;

    public bool $rateLimit;

    public bool $contentDispositionByParam;

    public string|null $secureTokenType;

    public string|null $secureTokenValue;

    public string|null $secureLinkExpiryParam;

    public string|null $secureLinkTokenParam;

    public string|null $secureLinkPathlenParam;

    public string|null $secureLinkSecretParam;

    public bool $secureLinkRewritePlaylist;

    /** @var array<ResourceOriginConfiguration> */
    public array $origins;

    /** @var array<string, string>|null */
    public array|null $responseHeaders;

    /**
     * @param list<string> $cnames
     * @param list<string>|null $ignoredQueryParams
     * @param list<string>|null $refererDomains
     * @param list<string>|null $ipProtectionAddresses
     * @param list<string>|null $geoProtectionCountries
     * @param array<mixed>|null $customData
     * @param array<int, array<string, mixed>> $origins
     * @param array<string, string>|null $responseHeaders
     */
    public function __construct(
        int $id,
        int $accountId,
        string $cdnUrl,
        int $groupId,
        array $cnames,
        DateTimeImmutable|null $suspended,
        bool $disableQueryString,
        bool $ignoreSetCookie,
        bool $mp4PseudoStreaming,
        int|null $cacheExpiry,
        int|null $cacheExpiry404,
        bool $cacheBypass,
        int $purgeAllKey,
        int|null $sslFileIndex,
        bool $fullLogs,
        array|null $ignoredQueryParams,
        int|null $httpsRedirectCode,
        bool|null $refererDenyEmpty,
        string|null $refererType,
        array|null $refererDomains,
        string|null $ipProtectionType,
        array|null $ipProtectionAddresses,
        string|null $geoProtectionType,
        array|null $geoProtectionCountries,
        int|null $cacheMinUses,
        bool $streamingPlaylistBypass,
        int|null $cacheLockAge,
        int|null $cacheLockTimeout,
        int|null $cacheContentLengthLimit,
        int|null $cacheNoContentLengthLimit,
        int|null $upstreamFailTimeout,
        int|null $upstreamNextAttempts,
        array|null $customData,
        bool $quic,
        bool $waf,
        bool $corsOriginHeader,
        bool $corsTimingEnabled,
        bool $corsWildcardEnabled,
        bool $rateLimit,
        bool $contentDispositionByParam,
        string|null $secureTokenType,
        string|null $secureTokenValue,
        string|null $secureLinkExpiryParam,
        string|null $secureLinkTokenParam,
        string|null $secureLinkPathlenParam,
        string|null $secureLinkSecretParam,
        bool $secureLinkRewritePlaylist,
        array $origins,
        array|null $responseHeaders,
    ) {
        $this->id = $id;
        $this->accountId = $accountId;
        $this->cdnUrl = $cdnUrl;
        $this->groupId = $groupId;
        $this->cnames = $cnames;
        $this->suspended = $suspended;
        $this->disableQueryString = $disableQueryString;
        $this->ignoreSetCookie = $ignoreSetCookie;
        $this->mp4PseudoStreaming = $mp4PseudoStreaming;
        $this->cacheExpiry = $cacheExpiry;
        $this->cacheExpiry404 = $cacheExpiry404;
        $this->cacheBypass = $cacheBypass;
        $this->purgeAllKey = $purgeAllKey;
        $this->sslFileIndex = $sslFileIndex;
        $this->fullLogs = $fullLogs;
        $this->ignoredQueryParams = $ignoredQueryParams;
        $this->httpsRedirectCode = $httpsRedirectCode;
        $this->refererDenyEmpty = $refererDenyEmpty;
        $this->refererType = $refererType;
        $this->refererDomains = $refererDomains;
        $this->ipProtectionType = $ipProtectionType;
        $this->ipProtectionAddresses = $ipProtectionAddresses;
        $this->geoProtectionType = $geoProtectionType;
        $this->geoProtectionCountries = $geoProtectionCountries;
        $this->cacheMinUses = $cacheMinUses;
        $this->streamingPlaylistBypass = $streamingPlaylistBypass;
        $this->cacheLockAge = $cacheLockAge;
        $this->cacheLockTimeout = $cacheLockTimeout;
        $this->cacheContentLengthLimit = $cacheContentLengthLimit;
        $this->cacheNoContentLengthLimit = $cacheNoContentLengthLimit;
        $this->upstreamFailTimeout = $upstreamFailTimeout;
        $this->upstreamNextAttempts = $upstreamNextAttempts;
        $this->customData = $customData;
        $this->quic = $quic;
        $this->waf = $waf;
        $this->corsOriginHeader = $corsOriginHeader;
        $this->corsTimingEnabled = $corsTimingEnabled;
        $this->corsWildcardEnabled = $corsWildcardEnabled;
        $this->rateLimit = $rateLimit;
        $this->contentDispositionByParam = $contentDispositionByParam;
        $this->secureTokenType = $secureTokenType;
        $this->secureTokenValue = $secureTokenValue;
        $this->secureLinkExpiryParam = $secureLinkExpiryParam;
        $this->secureLinkTokenParam = $secureLinkTokenParam;
        $this->secureLinkPathlenParam = $secureLinkPathlenParam;
        $this->secureLinkSecretParam = $secureLinkSecretParam;
        $this->secureLinkRewritePlaylist = $secureLinkRewritePlaylist;
        $this->origins = $this->setOrigins($origins);
        $this->responseHeaders = $responseHeaders;
    }

    /** @param array<string, mixed> $result */
    public static function fromFetchResult(array $result): self
    {
        $ignoredQueryParams = $result['ignored_query_params'] ?? null;
        $refererDomains = $result['referer_domains'] ?? null;
        $geoProtectionCountries = $result['geo_protection_countries'] ?? null;
        $ipProtectionAddresses = $result['ip_protection_addresses'] ?? null;
        $cnames = $result[CdnResource::COLUMN_CNAMES] === ''
            ? [] : explode(ResourceCnamesArrayType::SEPARATOR, $result[CdnResource::COLUMN_CNAMES]);
        $customData = $result[CdnResource::COLUMN_CUSTOM_DATA] === null
            ? null
            : json_decode($result[CdnResource::COLUMN_CUSTOM_DATA], true);
        $suspended = $result[CdnResource::COLUMN_SUSPENDED] === null
            ? null
            : new DateTimeImmutable($result[CdnResource::COLUMN_SUSPENDED]);

        return new self(
            $result[CdnResource::COLUMN_ID],
            $result[CdnResource::COLUMN_ACCOUNT],
            $result[CdnResource::COLUMN_CDN_URL],
            $result[CdnResource::COLUMN_GROUP_ID],
            $cnames,
            $suspended,
            $result[CdnResource::COLUMN_DISABLE_QUERY_STRING],
            $result[CdnResource::COLUMN_IGNORE_SET_COOKIE],
            $result[CdnResource::COLUMN_MP4_PSEUDO_STREAMING],
            $result[CdnResource::COLUMN_CACHE_EXPIRY],
            $result[CdnResource::COLUMN_CACHE_EXPIRY_404],
            $result[CdnResource::COLUMN_CACHE_BYPASS],
            $result[CdnResource::COLUMN_PURGE_ALL_KEY],
            $result['ssl_file_index'],
            $result['full_logs'],
            $ignoredQueryParams === null
                ? null
                : json_decode($ignoredQueryParams),
            $result[CdnResource::COLUMN_HTTPS_REDIRECT_CODE],
            $result['referer_deny_empty'],
            $result['referer_type'] === HotlinkProtectionType::TYPE_DISABLED ? null : $result['referer_type'],
            $refererDomains === null
                ? null
                : json_decode($refererDomains),
            $result['ip_protection_type'],
            $ipProtectionAddresses === null
                ? null
                : json_decode($ipProtectionAddresses),
            $result['geo_protection_type'],
            $geoProtectionCountries === null
                ? null
                : json_decode($geoProtectionCountries),
            $result[CdnResource::COLUMN_CACHE_MIN_USES],
            $result[CdnResource::COLUMN_STREAMING_PLAYLIST_BYPASS],
            $result[CdnResource::COLUMN_CACHE_LOCK_AGE],
            $result[CdnResource::COLUMN_CACHE_LOCK_TIMEOUT],
            $result[CdnResource::COLUMN_CACHE_CONTENT_LENGHT_LIMIT],
            $result[CdnResource::COLUMN_CACHE_MISSING_CONTENT_LENGHT_LIMIT],
            $result[CdnResource::COLUMN_UPSTREAM_FAIL_TIMEOUT],
            $result[CdnResource::COLUMN_UPSTREAM_NEXT_ATTEMPTS],
            $customData,
            $result[CdnResource::COLUMN_QUIC],
            $result[CdnResource::COLUMN_WAF],
            $result[CdnResource::COLUMN_CORS_ORIGIN_HEADER],
            $result[CdnResource::COLUMN_CORS_TIMING_ENABLED],
            $result[CdnResource::COLUMN_CORS_WILDCARD_ENABLED],
            $result[CdnResource::COLUMN_RATE_LIMIT] === true,
            $result[CdnResource::COLUMN_CONTENT_DISPOSITION_BY_PARAM] === true,
            $result['secure_token_type'],
            $result['secure_token_value'],
            $result['secure_link_expiry_param'],
            $result['secure_link_token_param'],
            $result['secure_link_pathlen_param'],
            $result['secure_link_secret_param'],
            $result['secure_link_rewrite_playlist'] === true,
            json_decode($result['origins'], true),
            ValueReplacer::jsonStringToArray($result['response_headers']),
        );
    }

    /**
     * @param array<int, array<string, mixed>> $origins
     *
     * @return array<ResourceOriginConfiguration>
     */
    private function setOrigins(array $origins): array
    {
        $result = [];

        foreach ($origins as $origin) {
            $result[$origin['priority']] = ResourceOriginConfiguration::fromFetchResult($origin);
        }

        return $result;
    }
}
