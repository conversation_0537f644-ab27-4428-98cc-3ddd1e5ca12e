<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\NgxConfGen\Domain\Value;

use <PERSON><PERSON>\Serializer\Annotation as Serializer;

final class ResourceOriginConfiguration
{
    public int $priority;

    public string $host;

    public string $scheme;

    public string|null $basedir;

    public int|null $port;

    public int|null $timeout;

    public string|null $s3AccessKeyId;

    public string|null $s3Secret;

    public string|null $s3Region;

    public string|null $s3BucketName;

    public string|null $s3Type;

    public bool $forwardHostHeader;

    public bool $sslVerifyDisable;

    /**  @var array<string, string>|null */
    public array|null $originHeaders;

    public bool $followRedirectOrigin;

    /**
     * @Serializer\Type("array<int>")
     * @var list<int>|null
     */
    public array|null $followRedirectCodes;

    /**
     * @param array<string, string>|null $originHeaders
     * @param list<int>|null $followRedirectCodes
     */
    public function __construct(
        int $priority,
        string $host,
        string $scheme,
        string|null $basedir = null,
        int|null $port = null,
        int|null $timeout = null,
        string|null $s3AccessKeyId = null,
        string|null $s3Secret = null,
        string|null $s3Region = null,
        string|null $s3BucketName = null,
        string|null $s3Type = null,
        bool $forwardHostHeader = false,
        bool $sslVerifyDisable = false,
        array|null $originHeaders = null,
        bool $followRedirectOrigin = false,
        array|null $followRedirectCodes = null,
    ) {
        $this->priority = $priority;
        $this->host = $host;
        $this->scheme = $scheme;
        $this->basedir = $basedir;
        $this->port = $port;
        $this->timeout = $timeout;
        $this->s3AccessKeyId = $s3AccessKeyId;
        $this->s3Secret = $s3Secret;
        $this->s3Region = $s3Region;
        $this->s3BucketName = $s3BucketName;
        $this->s3Type = $s3Type;
        $this->forwardHostHeader = $forwardHostHeader;
        $this->sslVerifyDisable = $sslVerifyDisable;
        $this->originHeaders = $originHeaders;
        $this->followRedirectOrigin = $followRedirectOrigin;
        $this->followRedirectCodes = $followRedirectCodes;
    }

    /** @param array<string, mixed> $result */
    public static function fromFetchResult(array $result): self
    {
        return new self(
            $result['priority'],
            $result['host'],
            $result['scheme'],
            $result['basedir'],
            $result['port'],
            $result['timeout'],
            $result['s3_access_key_id'],
            $result['s3_secret'],
            $result['s3_region'],
            $result['s3_bucket_name'],
            $result['s3_type'],
            (bool) $result['forward_host_header'],
            (bool) $result['ssl_verify_disable'],
            $result['origin_headers'],
            (bool) $result['follow_redirect_origin'],
            $result['follow_redirect_codes'],
        );
    }
}
