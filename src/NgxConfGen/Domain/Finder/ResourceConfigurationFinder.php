<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\NgxConfGen\Domain\Finder;

use Cdn77\NxgApi\NgxConfGen\Domain\Value\ResourceConfiguration;
use Generator;

interface ResourceConfigurationFinder
{
    /**
     * @param list<int>|null $filteredIds
     *
     * @return list<int>
     */
    public function findResourcesWithoutOrigin(array|null $filteredIds = null): array;

    public function findConfigurationForResourceId(int $resourceId): ResourceConfiguration|null;

    /**
     * @param list<int>|null $filteredIds
     *
     * @return Generator<ResourceConfiguration>
     */
    public function findAllResourcesForNgxConfGen(array|null $filteredIds = null): Generator;
}
