<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\NgxConfGen\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\NgxConfGen\Domain\Finder\ResourceConfigurationFinder;
use Cdn77\NxgApi\NgxConfGen\Domain\Value\ResourceConfiguration;
use Cdn77\NxgApi\Resource\Domain\Exception\CdnResourceNotFound;
use Cdn77\NxgApi\Resource\Domain\Exception\ResourceOriginNotFound;

use function count;

final class FindConfigurationForResourceIdHandler implements QueryHandler
{
    private ResourceConfigurationFinder $resourceConfigurationFinder;

    public function __construct(ResourceConfigurationFinder $resourceConfigurationFinder)
    {
        $this->resourceConfigurationFinder = $resourceConfigurationFinder;
    }

    public function handle(FindConfigurationForResourceId $query): ResourceConfiguration|null
    {
        $resourceConfiguration = $this->resourceConfigurationFinder->findConfigurationForResourceId($query->resourceId);

        if ($resourceConfiguration === null) {
            throw CdnResourceNotFound::fromResourceId($query->resourceId);
        }

        if (count($resourceConfiguration->origins) === 0) {
            throw ResourceOriginNotFound::fromResourceId($query->resourceId);
        }

        return $resourceConfiguration;
    }
}
