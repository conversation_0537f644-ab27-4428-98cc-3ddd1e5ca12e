<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\NgxConfGen\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\NgxConfGen\Domain\Finder\ResourceConfigurationFinder;
use Cdn77\NxgApi\NgxConfGen\Domain\Value\ResourceConfiguration;
use Cdn77\NxgApi\Resource\Domain\Exception\ResourceOriginNotFound;
use Generator;

use function count;

final class FindConfigurationsForResourceIdsHandler implements QueryHandler
{
    private ResourceConfigurationFinder $resourceConfigurationFinder;

    public function __construct(ResourceConfigurationFinder $resourceConfigurationFinder)
    {
        $this->resourceConfigurationFinder = $resourceConfigurationFinder;
    }

    /** @return Generator<ResourceConfiguration> */
    public function handle(FindConfigurationsForResourceIds $query): Generator
    {
        $this->checkAllResourcesHaveOrigin($query->resourceIds);

        $resourcesConfigurations = $this->resourceConfigurationFinder->findAllResourcesForNgxConfGen(
            $query->resourceIds,
        );

        foreach ($resourcesConfigurations as $configuration) {
            yield $configuration;
        }
    }

    /** @param list<int>|null $filteredIds */
    private function checkAllResourcesHaveOrigin(array|null $filteredIds): void
    {
        $resourcesWithoutOrigin = $this->resourceConfigurationFinder->findResourcesWithoutOrigin($filteredIds);

        if (count($resourcesWithoutOrigin) > 0) {
            throw ResourceOriginNotFound::fromResourceIds($resourcesWithoutOrigin);
        }
    }
}
