<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\NgxConfGen\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\Query;

final class FindConfigurationsForResourceIds implements Query
{
    /** @var list<int>|null */
    public array|null $resourceIds;

    /** @param list<int>|null $resourceIds */
    public function __construct(array|null $resourceIds)
    {
        $this->resourceIds = $resourceIds;
    }
}
