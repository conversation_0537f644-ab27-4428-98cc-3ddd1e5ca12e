<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\NgxConfGen\Infrastructure\Finder;

use Cdn77\NxgApi\Filter\Resource\DeletedResourceFilter;
use Cdn77\NxgApi\NgxConfGen\Domain\Finder\ResourceConfigurationFinder;
use Cdn77\NxgApi\NgxConfGen\Domain\Value\ResourceConfiguration;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\ORM\EntityManagerInterface;
use Generator;
use Webmozart\Assert\Assert;

use function array_map;

final class DbalResourceConfigurationFinder implements ResourceConfigurationFinder
{
    public const string SUSPENDED_CONDITION = "r.suspended IS NULL OR (now() - '30 days'::INTERVAL) < r.suspended";

    private Connection $connection;

    private EntityManagerInterface $entityManager;

    public function __construct(Connection $connection, EntityManagerInterface $entityManager)
    {
        $this->connection = $connection;
        $this->entityManager = $entityManager;
    }

    /**
     * @param list<int>|null $filteredIds
     *
     * @return list<int>
     */
    public function findResourcesWithoutOrigin(array|null $filteredIds = null): array
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('r.id')
            ->from('resource', 'r')
            ->where('r.id NOT IN (SELECT DISTINCT resource_id FROM resource_origin WHERE priority = 1)');

        $this->addWhereConditions($queryBuilder, $filteredIds);

        $result = $queryBuilder->executeQuery();
        $results = $result->fetchAllAssociative();

        $ids = [];
        foreach ($results as $row) {
            $ids[] = (int) $row['id'];
        }

        return $ids;
    }

    public function findConfigurationForResourceId(int $resourceId): ResourceConfiguration|null
    {
        $queryBuilder = $this->createNxgConfGenQueryBuilder()
            ->andWhere('r.id = :id')
            ->setParameter('id', $resourceId);

        $results = $queryBuilder->fetchAllAssociative();

        if ($results === []) {
            return null;
        }

        Assert::count($results, 1);

        return ResourceConfiguration::fromFetchResult($results[0]);
    }

    /**
     * @param list<int>|null $filteredIds
     *
     * @return Generator<ResourceConfiguration>
     */
    public function findAllResourcesForNgxConfGen(array|null $filteredIds = null): Generator
    {
        $queryBuilder = $this->createNxgConfGenQueryBuilder($filteredIds);

        $results = $queryBuilder->fetchAllAssociative();
        foreach ($results as $row) {
            yield ResourceConfiguration::fromFetchResult($row);
        }
    }

    /** @param list<int>|null $filteredIds */
    public function createNxgConfGenQueryBuilder(array|null $filteredIds = null): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('r.*')
            ->addSelect('ssl.assigned_index ssl_file_index')
            ->addSelect('(fl.resource_id IS NOT NULL) full_logs')
            ->addSelect('iqp.params ignored_query_params')
            ->addSelect('hlp.deny_empty referer_deny_empty')
            ->addSelect('hlp.type referer_type')
            ->addSelect('hlp.domains referer_domains')
            ->addSelect('ip.type ip_protection_type')
            ->addSelect('ip.addresses ip_protection_addresses')
            ->addSelect('gp.type geo_protection_type')
            ->addSelect('gp.countries geo_protection_countries')
            ->addSelect('ro.origins')
            ->addSelect('st.*')
            ->from('resource', 'r')
            ->leftJoin('r', 'resource_full_log', 'fl', 'fl.resource_id = r.id')
            ->leftJoin('r', 'ssl', 'ssl', 'ssl.resource_id = r.id')
            ->leftJoin(
                'r',
                "(
                    SELECT resource_id,
                        json_agg(
                            json_build_object(
                                'clap_origin_id', clap_origin_id,
                                'priority', priority,
                                'host', host,
                                'scheme', scheme,
                                'port', port,
                                'basedir', basedir,
                                'timeout', timeout,
                                's3_access_key_id', s3_access_key_id,
                                's3_secret', s3_secret,
                                's3_region', s3_region,
                                's3_bucket_name', s3_bucket_name,
                                's3_type', s3_type,
                                'forward_host_header', forward_host_header,
                                'ssl_verify_disable', ssl_verify_disable,
                                'origin_headers', origin_headers,
                                'follow_redirect_origin', follow_redirect_origin,
                                'follow_redirect_codes', follow_redirect_codes
                            )
                        ) origins
                    FROM resource_origin
                    GROUP BY resource_id
                )",
                'ro',
                'ro.resource_id = r.id',
            )
        ->leftJoin(
            'r',
            '(
                    SELECT hpr.resource_id,
                        hpr.type,
                        hpr.deny_empty,
                        (CASE WHEN COUNT(hpra.domain) > 0 THEN json_agg(hpra.domain) ELSE NULL END) domains
                    FROM hotlink_protection.referer hpr
                    LEFT JOIN hotlink_protection.referer_address hpra ON hpr.id = hpra.referer_id
                    GROUP BY hpr.id
                )',
            'hlp',
            'hlp.resource_id = r.id',
        )
            ->leftJoin(
                'r',
                '(
                    SELECT iqp.resource_id, json_agg(iqp.name) params
                    FROM resource_ignored_query_param iqp
                    GROUP BY iqp.resource_id
                )',
                'iqp',
                'iqp.resource_id = r.id',
            )
            ->leftJoin(
                'r',
                '(
                    SELECT gp.resource_id, gp.type, json_agg(gpl.country) countries
                    FROM access_protection.geo_protection gp
                    LEFT JOIN access_protection.geo_protection_location gpl ON gpl.geo_protection_id = gp.id
                       GROUP BY gp.id
                )',
                'gp',
                'gp.resource_id = r.id',
            )
            ->leftJoin(
                'r',
                '(
                    SELECT ip.resource_id, ip.type, json_agg(ipa.address) addresses
                    FROM access_protection.ip_protection ip
                    LEFT JOIN access_protection.ip_protection_address ipa ON ipa.ip_protection_id = ip.id
                    GROUP BY ip.id
                )',
                'ip',
                'ip.resource_id = r.id',
            )
            ->leftJoin(
                'r',
                '(
                    SELECT resource_id, secure_token_type, secure_token_value, secure_link_expiry_param, 
                        secure_link_token_param, secure_link_pathlen_param, secure_link_secret_param, 
                        secure_link_rewrite_playlist
                    FROM resource_secure_token
                )',
                'st',
                'st.resource_id = r.id',
            );

        $this->addWhereConditions($queryBuilder, $filteredIds);

        return $queryBuilder;
    }

    /** @param list<int>|null $filteredIds */
    private function addWhereConditions(QueryBuilder $queryBuilder, array|null $filteredIds): void
    {
        $queryBuilder->andWhere(self::SUSPENDED_CONDITION);

        if ($filteredIds !== null) {
            $queryBuilder->andWhere($queryBuilder->expr()->in('r.id', array_map('strval', $filteredIds)));
        }

        if (! $this->shouldFilterDeleted()) {
            return;
        }

        $queryBuilder->andWhere('r.deleted IS NULL');
    }

    private function shouldFilterDeleted(): bool
    {
        return $this->entityManager->getFilters()->isEnabled(DeletedResourceFilter::NAME);
    }
}
