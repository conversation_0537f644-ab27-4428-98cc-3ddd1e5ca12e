<?php

declare(strict_types=1);

namespace Cdn77\MonMasterDetector\Authority;

use Cdn77\MonMasterDetector\Exception\MasterDetectionFailedException;
use Cdn77\MonMasterDetector\MasterState;
use Cdn77\MonMasterDetector\MasterStateDetectorInterface;
use DateTimeImmutable;
use DateTimeZone;

use function file_get_contents;
use function microtime;
use function sprintf;
use function trim;

use const DIRECTORY_SEPARATOR;

class FileAuthorityDetector implements MasterStateDetectorInterface
{
    private const IS_MASTER_FILE = 'is_master';
    private const IS_MASTER_EPOCH_FILE = 'is_master.epoch';
    private const IS_MASTER_FORCED_FILE = 'is_master_forced';

    private const ALLOWED_THRESHOLD = 2;

    private string $path;

    public function __construct(string $path)
    {
        $this->path = $path;
    }

    public function getState(): MasterState
    {
        $masterFile = $this->readFile(self::IS_MASTER_FILE);
        $masterEpochFile = $this->readFile(self::IS_MASTER_EPOCH_FILE);
        $masterForcedFile = $this->readFile(self::IS_MASTER_FORCED_FILE);
        $currentEpoch = microtime(true);

        $forced = $masterForcedFile !== null && trim($masterForcedFile) === '1';

        if ($forced === true) {
            return $this->createMasterState(true, true, $currentEpoch);
        }

        if ($masterFile === null) {
            throw new MasterDetectionFailedException('Unable to read master file.');
        }

        if ($masterEpochFile === null) {
            throw new MasterDetectionFailedException('Unable to read master stamp file.');
        }

        $master = trim($masterFile) === '1';
        $masterEpoch = (int) trim($masterEpochFile);

        if ($master === false) {
            return $this->createMasterState(false, false, $currentEpoch);
        }

        $masterValid = $currentEpoch - $masterEpoch <= 60 + self::ALLOWED_THRESHOLD;

        return $this->createMasterState($masterValid, false, $currentEpoch);
    }

    private function readFile(string $file): ?string
    {
        $file = @file_get_contents($this->path . DIRECTORY_SEPARATOR . $file);

        if ($file === false) {
            return null;
        }

        return $file;
    }

    private function createMasterState(bool $master, bool $forced, float $checkEpoch): MasterState
    {
        return new MasterState(
            $master,
            $forced,
            $this->createDateTimeFromEpoch($checkEpoch)
        );
    }

    private function createDateTimeFromEpoch(float $epoch): DateTimeImmutable
    {
        return DateTimeImmutable::createFromFormat('U.u', sprintf('%.6f', $epoch), new DateTimeZone('UTC'));
    }
}
