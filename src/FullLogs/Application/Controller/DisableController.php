<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\FullLogs\Application\Controller;

use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\FullLogs\Domain\Command\DisableFullLogs;
use Cdn77\NxgApi\Resource\Domain\Exception\CdnResourceNotFound;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class DisableController
{
    public const LEGACY_ROUTE_NAME = 'legacy.full-logs.disable';
    public const ROUTE_NAME = 'full-logs.disable';

    private CommandBus $commandBus;

    private SerializerInterface $serializer;

    public function __construct(CommandBus $commandBus, SerializerInterface $serializer)
    {
        $this->commandBus = $commandBus;
        $this->serializer = $serializer;
    }

    /**
     * @Route(
     *     path="/cdn_resources/{resourceId}/full_log/disable.json",
     *     name=self::LEGACY_ROUTE_NAME,
     *     methods={Request::METHOD_PUT}
     * )
     * @Route(path="/cdn_resources/{resourceId}/full-log/disable", name=self::ROUTE_NAME, methods={Request::METHOD_PUT})
     */
    public function disableAction(int $resourceId): JsonResponse
    {
        try {
            $this->commandBus->handle(new DisableFullLogs($resourceId));
        } catch (CdnResourceNotFound) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(new ErrorsSchema(['Resource not found.']), 'json'),
                Response::HTTP_NOT_FOUND,
            );
        }

        return new JsonResponse('', Response::HTTP_NO_CONTENT);
    }
}
