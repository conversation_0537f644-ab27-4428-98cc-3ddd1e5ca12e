<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\FullLogs\Application\Controller;

use Cdn77\NxgApi\Core\Domain\Bus\QueryBus;
use Cdn77\NxgApi\FullLogs\Domain\Query\GetFullLogStatus;
use Cdn77\NxgApi\Resource\Domain\Exception\CdnResourceNotFound;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Schema\Legacy\Resources\ResourceFullLogStatusSchema;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Webmozart\Assert\Assert;

final class StatusController
{
    public const LEGACY_ROUTE_NAME = 'legacy.full-logs.status';
    public const ROUTE_NAME = 'full-logs.status';

    private QueryBus $queryBus;

    private SerializerInterface $serializer;

    public function __construct(QueryBus $queryBus, SerializerInterface $serializer)
    {
        $this->queryBus = $queryBus;
        $this->serializer = $serializer;
    }

    /**
     * @Route(
     *     path="/cdn_resources/{resourceId}/full_log.json",
     *     name=self::LEGACY_ROUTE_NAME,
     *     methods={Request::METHOD_GET}
     * )
     * @Route(path="/cdn_resources/{resourceId}/full-log", name=self::ROUTE_NAME, methods={Request::METHOD_GET})
     */
    public function disableAction(int $resourceId): JsonResponse
    {
        try {
            $result = $this->queryBus->handle(new GetFullLogStatus($resourceId));
        } catch (CdnResourceNotFound) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(new ErrorsSchema(['Resource not found.']), 'json'),
                Response::HTTP_NOT_FOUND,
            );
        }

        Assert::boolean($result);

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(new ResourceFullLogStatusSchema($result), 'json'),
            Response::HTTP_OK,
        );
    }
}
