<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\FullLogs\Domain;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceFullLog;
use Cdn77\NxgApi\Service\Event\ResourceEvent;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Webmozart\Assert\Assert;

class FullLogsManager
{
    private EntityManagerInterface $entityManager;

    private EventDispatcherInterface $eventDispatcher;

    public function __construct(EntityManagerInterface $entityManager, EventDispatcherInterface $eventDispatcher)
    {
        $this->entityManager = $entityManager;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function isEnabledForResource(CdnResource $resource): bool
    {
        return $this->findForResource($resource) !== null;
    }

    public function enable(CdnResource $resource): void
    {
        if ($this->isEnabledForResource($resource)) {
            return;
        }

        $this->entityManager->persist(new ResourceFullLog($resource));
        $this->eventDispatcher->dispatch(new ResourceEvent($resource));
    }

    public function disable(CdnResource $resource): void
    {
        if (! $this->isEnabledForResource($resource)) {
            return;
        }

        $fullLog = $this->findForResource($resource);
        Assert::notNull($fullLog);

        $this->entityManager->remove($fullLog);
        $this->eventDispatcher->dispatch(new ResourceEvent($resource));
    }

    private function findForResource(CdnResource $resource): ResourceFullLog|null
    {
        return $this->entityManager->find(ResourceFullLog::class, $resource);
    }
}
