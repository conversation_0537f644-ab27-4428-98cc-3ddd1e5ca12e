<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\FullLogs\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Core\Domain\Repository\CdnResourceRepository;
use Cdn77\NxgApi\FullLogs\Domain\FullLogsManager;

final class EnableFullLogsHandler implements CommandHandler
{
    private CdnResourceRepository $cdnResourceRepository;

    private FullLogsManager $fullLogsManager;

    public function __construct(CdnResourceRepository $cdnResourceRepository, FullLogsManager $fullLogsManager)
    {
        $this->cdnResourceRepository = $cdnResourceRepository;
        $this->fullLogsManager = $fullLogsManager;
    }

    public function handle(EnableFullLogs $command): void
    {
        $resource = $this->cdnResourceRepository->get($command->resourceId);

        $this->fullLogsManager->enable($resource);
    }
}
