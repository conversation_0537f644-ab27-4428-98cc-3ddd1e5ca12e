<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\FullLogs\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\Core\Domain\Repository\CdnResourceRepository;
use Cdn77\NxgApi\FullLogs\Domain\FullLogsManager;

final class GetFullLogStatusHandler implements QueryHandler
{
    private CdnResourceRepository $cdnResourceRepository;

    private FullLogsManager $fullLogsManager;

    public function __construct(CdnResourceRepository $cdnResourceRepository, FullLogsManager $fullLogsManager)
    {
        $this->cdnResourceRepository = $cdnResourceRepository;
        $this->fullLogsManager = $fullLogsManager;
    }

    public function handle(GetFullLogStatus $query): bool
    {
        $resource = $this->cdnResourceRepository->get($query->resourceId);

        return $this->fullLogsManager->isEnabledForResource($resource);
    }
}
