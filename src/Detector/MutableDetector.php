<?php

declare(strict_types=1);

namespace Cdn77\MonMasterDetectorBundle\Detector;

use Cdn77\MonMasterDetector\MasterState;
use Cdn77\MonMasterDetector\MasterStateDetectorInterface;
use DateTimeImmutable;

class MutableDetector implements MasterStateDetectorInterface
{
    private bool $master;

    private bool $forced;

    public function __construct(bool $master, bool $forced)
    {
        $this->master = $master;
        $this->forced = $forced;
    }

    public function getState(): MasterState
    {
        return new MasterState($this->master, $this->forced, new DateTimeImmutable());
    }

    public function becomeMaster(): void
    {
        $this->master = true;
        $this->forced = false;
    }

    public function becomeForcedMaster(): void
    {
        $this->master = true;
        $this->forced = true;
    }

    public function abandonMaster(): void
    {
        $this->master = false;
        $this->forced = false;
    }
}
