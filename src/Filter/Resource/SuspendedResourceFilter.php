<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Filter\Resource;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Doctrine\ORM\Mapping\ClassMetadata;
use Doctrine\ORM\Query\Filter\SQLFilter;

/**
 * Filter out suspended resources.
 */
class SuspendedResourceFilter extends SQLFilter
{
    public const NAME = 'suspended_resource';

    /**
     * @param string $targetTableAlias
     *
     * @phpcsSuppress SlevomatCodingStandard.TypeHints.TypeHintDeclaration.MissingParameterTypeHint
     */
    public function addFilterConstraint(ClassMetadata $targetEntity, $targetTableAlias): string
    {
        if ($targetEntity->getName() !== CdnResource::class) {
            return '';
        }

        return $targetTableAlias . '.suspended IS NULL';
    }
}
