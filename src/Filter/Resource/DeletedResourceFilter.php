<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Filter\Resource;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Doctrine\ORM\Mapping\ClassMetadata;
use Doctrine\ORM\Query\Filter\SQLFilter;

/**
 * Filter out deleted resources.
 */
class DeletedResourceFilter extends SQLFilter
{
    public const NAME = 'deleted_resource';

    /**
     * @param string $targetTableAlias
     *
     * @phpcsSuppress SlevomatCodingStandard.TypeHints.TypeHintDeclaration.MissingParameterTypeHint
     */
    public function addFilterConstraint(ClassMetadata $targetEntity, $targetTableAlias): string
    {
        if ($targetEntity->getName() !== CdnResource::class) {
            return '';
        }

        return $targetTableAlias . '.deleted IS NULL';
    }
}
