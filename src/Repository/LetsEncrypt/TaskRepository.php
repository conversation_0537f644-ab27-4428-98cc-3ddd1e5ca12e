<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Repository\LetsEncrypt;

use Cdn77\NxgApi\Entity\LetsEncrypt\Request;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Doctrine\ORM\EntityRepository;

/** @extends EntityRepository<Task> */
class TaskRepository extends EntityRepository
{
    public function add(Task $task): void
    {
        $this->getEntityManager()->persist($task);
    }

    public function getRequestTask(Request $request): Task
    {
        $result = $this->createQueryBuilder('t')
            ->orderBy('t.createdAt', 'ASC')
            ->where('t.request = :request')
            ->setParameter('request', $request)
            ->getQuery()
            ->getResult();

        return $result[0];
    }

    public function removeRequestTask(Request $request): void
    {
        $this->getEntityManager()->createQueryBuilder()
            ->delete($this->getEntityName(), 't')
            ->where('t.request = :request')
            ->setParameter('request', $request)
            ->getQuery()
            ->execute();
    }
}
