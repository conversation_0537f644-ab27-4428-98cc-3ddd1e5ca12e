<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Repository\LetsEncrypt;

use Cdn77\NxgApi\Entity\LetsEncrypt\Result;
use Cdn77\NxgApi\Entity\LetsEncrypt\ResultStatus;
use DateTimeImmutable;
use Doctrine\ORM\EntityRepository;
use Webmozart\Assert\Assert;

use function Safe\json_encode;

/** @extends EntityRepository<Result> */
class ResultRepository extends EntityRepository
{
    /** @param string[] $domains */
    public function countSuccessCertificateGenerationForDomainsSinceDate(array $domains, DateTimeImmutable $since): int
    {
        $result = $this->getEntityManager()->getConnection()->fetchAssociative(
            <<<'PSQL'
    SELECT COUNT(res.id) AS successes
    FROM letsencrypt.result res
    JOIN letsencrypt.request rq ON res.request_id = rq.id
    WHERE res.completed_at >= :since AND rq.domains::text = :domains AND res.status = :status
    PSQL
            ,
            [
                'since' => $since->format('Y-m-d H:i:s'),
                'domains' => json_encode($domains),
                'status' => ResultStatus::SUCCESS,
            ],
        );

        Assert::isArray($result);

        return $result['successes'];
    }
}
