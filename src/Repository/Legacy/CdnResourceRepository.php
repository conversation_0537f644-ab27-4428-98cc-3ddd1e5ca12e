<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Repository\Legacy;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Filter\Resource\DeletedResourceFilter;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\NativeQuery;
use Doctrine\ORM\Query\ResultSetMappingBuilder;
use DomainException;
use Webmozart\Assert\Assert;

use function array_column;
use function array_map;
use function assert;
use function count;
use function strtolower;

/** @extends EntityRepository<CdnResource> */
class CdnResourceRepository extends EntityRepository
{
    public function get(int $id): CdnResource
    {
        $resource = $this->find($id);

        if ($resource === null) {
            throw new DomainException('Cdn resource not found.');
        }

        return $resource;
    }

    /**
     * @param list<int> $resourceIds
     *
     * @return list<CdnResource>
     */
    public function getForIdList(array $resourceIds): array
    {
        $cdnResources = $this->getEntityManager()->createQueryBuilder()
            ->select('cdnResource')
            ->from(CdnResource::class, 'cdnResource')
            ->where('cdnResource.id IN (:resourceIds)')
            ->setParameter('resourceIds', $resourceIds)
            ->orderBy('cdnResource.id', 'ASC')
            ->getQuery()
            ->getResult();

        Assert::isList($cdnResources);
        Assert::allIsInstanceOf($cdnResources, CdnResource::class);
        Assert::eq(count($cdnResources), count($resourceIds));

        return $cdnResources;
    }

    /** @return string[] Cnames from all resources */
    public function getAllCnames(): array
    {
        return $this->doGetAllCnames();
    }

    /** @return string[] Cnames from all resources, except given one */
    public function getAllCnamesExceptOne(CdnResource $except): array
    {
        return $this->doGetAllCnames($except);
    }

    /**
     * @param string[] $cnames
     *
     * @return string[]
     */
    public function findUsedCnames(array $cnames): array
    {
        return $this->findUsedCnamesExcludingResources($cnames, []);
    }

    /**
     * @param string[] $cnames
     *
     * @return string[]
     */
    public function findUsedCnamesExcludingResource(array $cnames, CdnResource $resource): array
    {
        return $this->findUsedCnamesExcludingResources($cnames, [$resource]);
    }

    /**
     * @param string[]      $cnames
     * @param CdnResource[] $resources
     *
     * @return string[]
     */
    public function findUsedCnamesExcludingResources(array $cnames, array $resources = []): array
    {
        assert(count($cnames) > 0);

        $cnames = array_map('strtolower', $cnames);

        $query = new NativeQuery($this->getEntityManager());
        $sql = "
            SELECT c.cname
            FROM (
                SELECT
                    r.id resource_id,
                    unnest(string_to_array(lower(r.cnames), ' ')) cname
                FROM resource r
                " . ($this->shouldFilterDeleted() ? 'WHERE r.deleted IS NULL' : '') . '
            ) c
            WHERE c.cname IN (:cnames)
';
        $query->setParameter('cnames', $cnames, ArrayParameterType::STRING);

        if (count($resources) > 0) {
            $sql .= ' AND c.resource_id NOT IN (:resources)';
            $resourceIds = array_map(
                static fn (CdnResource $resource): int => $resource->getId(),
                $resources,
            );
            $query->setParameter('resources', $resourceIds, ArrayParameterType::INTEGER);
        }

        $query->setSQL($sql);

        $rsm = new ResultSetMappingBuilder($this->getEntityManager());
        $rsm->addScalarResult('cname', 'cname', Types::STRING);
        $query->setResultSetMapping($rsm);

        return array_column($query->getArrayResult(), 'cname');
    }

    public function findByDomain(string $domain): CdnResource|null
    {
        $sql = "
            SELECT r.*
            FROM resource r
            WHERE
                (
                    lower(r.cdn_url) = :domain
                    OR (
                        r.cnames != ''
                        AND :domain = ANY(string_to_array(lower(r.cnames), ' '))
                    )
                )
                " . ($this->shouldFilterDeleted() ? 'AND r.deleted IS NULL' : '');

        $rsm = new ResultSetMappingBuilder($this->getEntityManager());
        $rsm->addRootEntityFromClassMetadata(CdnResource::class, 'r');

        $query = $this->getEntityManager()->createNativeQuery($sql, $rsm);
        $query->setParameter('domain', strtolower($domain), Types::STRING);

        return $query->getOneOrNullResult();
    }

    /** @return string[] */
    private function doGetAllCnames(CdnResource|null $except = null): array
    {
        $query = $this->createQueryBuilder('r')
            ->select('r.cnames');

        if ($except !== null) {
            $query->where('r != :except')
                ->setParameter('except', $except);
        }

        $cnames = [];
        foreach ($query->getQuery()->getArrayResult() as $resourceCnames) {
            foreach ($resourceCnames['cnames'] as $cname) {
                $cnames[] = $cname;
            }
        }

        return $cnames;
    }

    private function shouldFilterDeleted(): bool
    {
        return $this->getEntityManager()->getFilters()->isEnabled(DeletedResourceFilter::NAME);
    }
}
