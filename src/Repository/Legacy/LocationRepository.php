<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Repository\Legacy;

use Cdn77\NxgApi\DTO\Legacy\ResourceLocationDTO;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Location;
use Cdn77\NxgApi\Resource\Domain\DTO\ResourceDatacenterLocation;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Query\ResultSetMappingBuilder;

use function array_map;

/** @extends EntityRepository<Location> */
class LocationRepository extends EntityRepository
{
    /** @return ResourceLocationDTO[] */
    public function findCustomLocationsForResource(CdnResource $resource): array
    {
        $sql = <<<'SQL'
            WITH possible_pops AS (
                SELECT p.id pop_id
                FROM pop p
                JOIN custom_location_relation clr
                    ON clr.pop_id = p.id
                JOIN custom_location cl
                    ON cl.resource_id = clr.resource_id
                WHERE cl.resource_id = :id
                UNION
                SELECT p.id pop_id
                FROM pop p
                JOIN group_pop_relation gpr
                    ON gpr.pop_id = p.id
                JOIN location_group g
                    ON gpr.group_id = g.id
                JOIN custom_location cl
                    ON cl.group_id = g.id
                WHERE cl.resource_id = :id
            )
            SELECT
                l.id,
                LOWER(l.city) location_id,
                l.city,
                l.country_code country,
                l.region_code region,
                l.continent,
                l.longitude,
                l.latitude,
                COALESCE(bool_or(pup.up), false) active,
                COALESCE(bool_or(clr.pop_id IS NOT NULL), false) assigned
            FROM possible_pops pp
            JOIN pop p
                ON pp.pop_id = p.id
            JOIN v_pop_up pup
                ON p.id = pup.pop_id
            LEFT JOIN custom_location_relation clr
                ON
                    clr.pop_id = p.id
                    AND clr.resource_id = :id
            JOIN location l
                ON l.id = p.location_id
            GROUP BY
                l.id,
                l.city,
                l.country_code,
                l.region_code,
                l.continent,
                l.longitude,
                l.latitude
SQL;

        return $this->processLocationsQuery($sql, $resource);
    }

    /** @return list<ResourceDatacenterLocation> */
    public function findCustomDatacenterLocationsForResource(CdnResource $resource): array
    {
        $result = $this->getEntityManager()->getConnection()->fetchAllAssociative(
            <<<'PSQL'
WITH available_pops AS (
    SELECT p.id AS pop_id
    FROM pop p
    JOIN custom_location_relation clr
        ON clr.pop_id = p.id
    JOIN custom_location cl
        ON cl.resource_id = clr.resource_id
    WHERE cl.resource_id = :id
    UNION
    SELECT p.id AS pop_id
    FROM pop p
    JOIN group_pop_relation gpr 
        ON gpr.pop_id = p.id
    JOIN location_group g
        ON gpr.group_id = g.id
    JOIN custom_location cl
        ON cl.group_id = g.id
    WHERE cl.resource_id = :id
)
SELECT
    LOWER(l.city) AS location_id,
    substring(p.description from 1 for 3) as city_code,
    COALESCE(bool_or(clr.pop_id IS NOT NULL), false) AS enabled
FROM available_pops ap
JOIN pop p 
    ON ap.pop_id = p.id
JOIN v_pop_up pup 
    ON p.id = pup.pop_id
LEFT JOIN custom_location_relation clr
    ON clr.pop_id = p.id AND clr.resource_id = :id
JOIN location l
    ON l.id = p.location_id
WHERE EXISTS (
    SELECT 1
    FROM server s
    WHERE s.pop_id = p.id
)
GROUP BY
    l.city,
    l.country_code,
    l.region_code,
    l.continent,
    city_code
ORDER BY city_code
PSQL
            ,
            ['id' => $resource->getId()],
        );

        return array_map(
            static fn (array $location): ResourceDatacenterLocation => ResourceDatacenterLocation::fromArray($location),
            $result,
        );
    }

    /** @return list<ResourceDatacenterLocation> */
    public function findDatacenterLocationsForResource(CdnResource $resource): array
    {
        $result = $this->getEntityManager()->getConnection()->fetchAllAssociative(
            <<<'PSQL'
WITH possible_pops AS (
    SELECT p.id AS pop_id
    FROM pop p
    JOIN group_pop_relation gpr
        ON gpr.pop_id = p.id
    JOIN location_group g
        ON gpr.group_id = g.id
    JOIN resource r
        ON r.group_id = g.id
    WHERE r.id = :id
        AND EXISTS (
        SELECT 1
        FROM server s
        WHERE s.pop_id = p.id
    )
)
SELECT
    lower(l.city) AS location_id,
    substring(p.description from 1 for 3) as city_code,
    true AS enabled
FROM possible_pops pp
JOIN pop p
    ON pp.pop_id = p.id
JOIN v_pop_up pup
    ON p.id = pup.pop_id
JOIN location l
    ON l.id = p.location_id
GROUP BY
    l.city,
    l.country_code,
    l.region_code,
    l.continent,
    city_code
ORDER BY city_code
PSQL
            ,
            ['id' => $resource->getId()],
        );

        return array_map(
            static fn (array $location): ResourceDatacenterLocation => ResourceDatacenterLocation::fromArray($location),
            $result,
        );
    }

    /**
     * @param list<string> $datacenterLocations
     *
     * @return list<Location>
     */
    public function findAllForCities(array $datacenterLocations): array
    {
        return $this->createQueryBuilder('l')
            ->select('l')
            ->where('LOWER(l.city) IN (:cities)')
            ->setParameter('cities', $datacenterLocations, ArrayParameterType::STRING)
            ->getQuery()
            ->getResult();
    }

    /** @return ResourceLocationDTO[] */
    private function processLocationsQuery(string $sql, CdnResource $resource): array
    {
        $rsm = new ResultSetMappingBuilder($this->getEntityManager());
        $rsm->addEntityResult(Location::class, 'l', 'location');
        $rsm->addFieldResult('l', 'id', 'id');
        $rsm->addFieldResult('l', 'city', 'city');
        $rsm->addFieldResult('l', 'country', 'countryCode');
        $rsm->addFieldResult('l', 'region', 'regionCode');
        $rsm->addFieldResult('l', 'continent', 'continent');
        $rsm->addFieldResult('l', 'longitude', 'coordinate.longitude');
        $rsm->addFieldResult('l', 'latitude', 'coordinate.latitude');
        $rsm->addScalarResult('location_id', 'locationId', 'string');
        $rsm->addScalarResult('active', 'active', 'boolean');
        $rsm->addScalarResult('assigned', 'assigned', 'boolean');

        $query = $this->getEntityManager()->createNativeQuery($sql, $rsm);
        $query->setParameter('id', $resource->getId());

        $result = [];
        foreach ($query->getResult() as $item) {
            $result[] = new ResourceLocationDTO(
                $item['location'],
                $item['locationId'],
                $item['active'],
                $item['assigned'],
            );
        }

        return $result;
    }
}
