<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Repository\Legacy;

use Cdn77\NxgApi\DTO\Legacy\ServerUpIpListDTO;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ip;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Query\ResultSetMappingBuilder;

/** @extends EntityRepository<Ip> */
class IpRepository extends EntityRepository
{
    /** @return ServerUpIpListDTO[] */
    public function getResourceServerUpIpList(CdnResource $resource): array
    {
        $query = <<<'EOQ'
            SELECT
                pop.location_id,
                ip.ip,
                server.id server_id,
                (
                    CASE
                        WHEN COALESCE(server.forced_state, ip."up") AND NOT server.paused
                        THEN 1
                        ELSE 0
                    END
                )::integer in_dns,
                (
                    CASE
                        WHEN COALESCE(server.forced_state, ip."up")
                        THEN 1
                        ELSE 0
                        END
                    )::integer ok,
                (
                    CASE
                        WHEN server.paused
                        THEN 1
                        ELSE 0
                        END
                )::integer paused
            FROM ip
            JOIN server
                ON server.id = ip.server_id
            JOIN pop
                ON pop.id = server.pop_id
            JOIN group_pop_relation
                ON group_pop_relation.pop_id = pop.id
            JOIN location_group
                ON location_group.id = group_pop_relation.group_id
            JOIN resource
                ON resource.group_id = location_group.id
                    AND resource.id = :resource
            LEFT JOIN custom_location cl
                ON cl.resource_id = resource.id
            LEFT JOIN custom_location_relation clr
                ON clr.resource_id = cl.resource_id
                    AND clr.pop_id = pop.id
            WHERE ip."primary"
                AND (
                    cl.resource_id IS NULL
                    OR (
                        cl.group_id = resource.group_id
                        AND NOT clr.pop_id IS NULL
                    )
                )
            ORDER BY pop.location_id
EOQ;

        $rsm = new ResultSetMappingBuilder($this->getEntityManager());
        $rsm->addScalarResult('location_id', 'locationId', Types::INTEGER);
        $rsm->addScalarResult('server_id', 'serverId', Types::INTEGER);
        $rsm->addScalarResult('ip', 'ip', Types::STRING);
        $rsm->addScalarResult('in_dns', 'inDns', Types::BOOLEAN);
        $rsm->addScalarResult('ok', 'ok', Types::BOOLEAN);
        $rsm->addScalarResult('paused', 'paused', Types::BOOLEAN);

        $query = $this->getEntityManager()->createNativeQuery($query, $rsm);
        $query->setParameter('resource', $resource->getId());

        $r = $query->getArrayResult();

        $result = [];
        foreach ($r as $item) {
            $result[] = new ServerUpIpListDTO(
                $item['locationId'],
                $item['serverId'],
                $item['inDns'],
                $item['ok'],
                $item['paused'],
            );
        }

        return $result;
    }
}
