<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Repository\Legacy;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\CustomLocation;
use Doctrine\ORM\EntityRepository;

/** @extends EntityRepository<CustomLocation> */
class CustomLocationRepository extends EntityRepository
{
    public function hasResourceCustomLocation(CdnResource $resource): bool
    {
        return (bool) $this->createQueryBuilder('cl')
            ->select('COUNT(1) num')
            ->where('cl.resource = :resource')
            ->setParameter('resource', $resource)
            ->getQuery()
            ->getSingleScalarResult();
    }
}
