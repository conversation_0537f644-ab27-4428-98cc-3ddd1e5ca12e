<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Repository\Legacy;

use Cdn77\NxgApi\DTO\Internal\Server\ServerWithPrimaryIpPairDTO;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\CustomLocation;
use Cdn77\NxgApi\Entity\Legacy\Ip;
use Cdn77\NxgApi\Entity\Legacy\Server;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Query\Expr;
use Doctrine\ORM\QueryBuilder;

use function count;

/** @extends EntityRepository<Server> */
class ServerRepository extends EntityRepository
{
    /** @return ServerWithPrimaryIpPairDTO[] */
    public function findAllWithPrimaryIp(): array
    {
        $result = $this->createQueryBuilder('s')
            ->select('s')
            ->innerJoin(Ip::class, 'ip', Expr\Join::WITH, 'ip.server = s AND ip.primary = TRUE')
            ->addSelect('ip')
            ->getQuery()
            ->getResult();

        $dtos = [];
        for ($i = 0, $c = count($result); $i < $c; $i += 2) {
            $dtos[] = new ServerWithPrimaryIpPairDTO($result[$i], $result[$i + 1]);
        }

        return $dtos;
    }

    /** @return Server[] */
    public function findUsedByResource(CdnResource $resource): array
    {
        return $this->createQueryBuilder('s')
            ->innerJoin('s.ips', 'ip', Expr\Join::WITH, 'ip.primary = TRUE')
            ->addSelect('ip')
            ->innerJoin('s.pop', 'pop')
            ->innerJoin('pop.groups', 'g')
            ->innerJoin(CdnResource::class, 'r', Expr\Join::WITH, 'r = :resource AND r.group = g')
            ->leftJoin(CustomLocation::class, 'cl', Expr\Join::WITH, 'cl.resource = r AND cl.group = r.group')
            ->leftJoin('cl.pops', 'cpop', Expr\Join::WITH, 'cpop = pop')
            ->where('cl.resource IS NULL')
            ->orWhere('cl.resource IS NOT NULL AND cpop.id IS NOT NULL')
            ->setParameter('resource', $resource)
            ->getQuery()
            ->getResult();
    }

    public function findOneByUidWithIpsAndLocations(int $uid): Server|null
    {
        return $this->createWithIpsAndLocationsQuery()
            ->andWhere('s.uid = :uid')
            ->setParameter('uid', $uid)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /** @return Server[] */
    public function findAllWithIpsAndLocations(): array
    {
        return $this->createWithIpsAndLocationsQuery()
            ->getQuery()
            ->getResult();
    }

    /** @return Server[] */
    public function findAllIndexedById(): array
    {
        return $this->createQueryBuilder('s', 's.id')
            ->getQuery()
            ->getResult();
    }

    /** @return Server[] */
    public function findAllIndexedByUid(): array
    {
        return $this->createQueryBuilder('s', 's.uid')
            ->getQuery()
            ->getResult();
    }

    private function createWithIpsAndLocationsQuery(): QueryBuilder
    {
        return $this->createQueryBuilder('s')
            ->leftJoin('s.pop', 'p')
            ->addSelect('p')
            ->leftJoin('p.location', 'l')
            ->addSelect('l')
            ->leftJoin('s.ips', 'i')
            ->addSelect('i')
            ->leftJoin('s.ipv6Addresses', 'ipv6')
            ->addSelect('ipv6')
            ->addOrderBy('s.uid', 'ASC')
            ->addOrderBy('p.id', 'ASC')
            ->addOrderBy('l.id', 'ASC')
            ->addOrderBy('i.id', 'ASC')
            ->addOrderBy('ipv6.id', 'ASC');
    }
}
