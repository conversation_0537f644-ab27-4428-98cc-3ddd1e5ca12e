<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Repository\Legacy;

use Cdn77\NxgApi\Entity\Legacy\LocationGroup;
use Doctrine\ORM\EntityRepository;
use DomainException;

use function sprintf;

/** @extends EntityRepository<LocationGroup> */
class LocationGroupRepository extends EntityRepository
{
    public function get(int $groupId): LocationGroup
    {
        $group = $this->find($groupId);

        if ($group === null) {
            throw new DomainException(sprintf('Location group with id "%d" was not found.', $groupId));
        }

        return $group;
    }
}
