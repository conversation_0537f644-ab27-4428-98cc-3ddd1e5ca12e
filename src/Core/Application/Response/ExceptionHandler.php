<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Response;

use Cdn77\NxgApi\Core\Domain\Exception\NoContent;
use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Service\Legacy\Certificate\Exception\InvalidCertificatePair;
use JMS\Serializer\SerializerInterface;
use Stringable;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Throwable;

use function array_map;
use function iterator_to_array;
use function sprintf;

final class ExceptionHandler
{
    private SerializerInterface $serializer;
    private DomainExceptionResponseResolver $domainExceptionResponseResolver;

    public function __construct(
        SerializerInterface $serializer,
        DomainExceptionResponseResolver $domainExceptionResponseResolver,
    ) {
        $this->serializer = $serializer;
        $this->domainExceptionResponseResolver = $domainExceptionResponseResolver;
    }

    public function handle(Throwable $exception): JsonResponse
    {
        if ($exception instanceof NoContent) {
            return new JsonResponse('', Response::HTTP_NO_CONTENT);
        }

        if ($exception instanceof InvalidCertificatePair) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema(array_map(
                        /** @return string|Stringable */
                        static fn (ConstraintViolationInterface $violation) => $violation->getMessage(),
                        iterator_to_array($exception->getViolations()),
                    )),
                    'json',
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY,
            );
        }

        if ($exception instanceof NxgApiDomainException) {
            return $this->domainExceptionResponseResolver->resolve($exception);
        }

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                new ErrorsSchema([sprintf('Unknown error: %s', $exception->getMessage())]),
                'json',
            ),
            Response::HTTP_INTERNAL_SERVER_ERROR,
        );
    }
}
