<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Response\PropertyNaming;

use Exception;
use <PERSON><PERSON>\Serializer\Metadata\ClassMetadata;
use <PERSON><PERSON>\Serializer\Naming\PropertyNamingStrategyInterface;
use Metadata\MetadataFactoryInterface;
use Symfony\Component\PropertyAccess\PropertyAccess;
use Symfony\Component\PropertyAccess\PropertyPathBuilder;
use Symfony\Component\PropertyAccess\PropertyPathInterface;
use Symfony\Component\PropertyAccess\PropertyPathIterator;
use Symfony\Component\PropertyAccess\PropertyPathIteratorInterface;
use Webmozart\Assert\Assert;

use function assert;

class PropertyPathConverter implements PropertyPathConverterInterface
{
    private MetadataFactoryInterface $metadataFactory;

    private PropertyNamingStrategyInterface $propertyNamingStrategy;

    public function __construct(
        MetadataFactoryInterface $metadataFactory,
        PropertyNamingStrategyInterface $propertyNamingStrategy,
    ) {
        $this->metadataFactory = $metadataFactory;
        $this->propertyNamingStrategy = $propertyNamingStrategy;
    }

    public function toSourcePath(string $rootClass, PropertyPathInterface $path): PropertyPathInterface
    {
        $metadata = $this->getMetadata($rootClass);

        $translatedPathBuilder = new PropertyPathBuilder();

        $iterator = new PropertyPathIterator($path);
        assert($iterator->isProperty());

        $currentRoot = $metadata;
        $currentProperty = $iterator->current();

        do {
            $typePathBuilder = new PropertyPathBuilder();

            if ($iterator->isProperty()) {
                $this->walkSourceProperty($iterator, $translatedPathBuilder, $currentRoot, $currentProperty);
            }

            if (! $iterator->valid()) {
                break;
            }

            if ($iterator->isIndex()) {
                $this->walkSourceIndex($iterator, $translatedPathBuilder, $typePathBuilder);
            }

            if (! $iterator->valid()) {
                break;
            }

            $currentRoot = $this->getNextRoot($typePathBuilder, $currentRoot, $currentProperty);
            $currentProperty = $iterator->current();
        } while ($iterator->valid());

        Assert::isInstanceOf($translatedPathBuilder->getPropertyPath(), PropertyPathInterface::class);

        return $translatedPathBuilder->getPropertyPath();
    }

    private function walkSourceProperty(
        PropertyPathIteratorInterface $iterator,
        PropertyPathBuilder $translatedPathBuilder,
        ClassMetadata $currentRoot,
        string $currentProperty,
    ): void {
        $translatedPathBuilder->appendProperty($this->resolveSerializedName($currentRoot, $currentProperty));
        $iterator->next();
    }

    private function walkSourceIndex(
        PropertyPathIteratorInterface $iterator,
        PropertyPathBuilder $translatedPathBuilder,
        PropertyPathBuilder $typePathBuilder,
    ): void {
        // find next property or the end of chain
        do {
            $index = $iterator->current();

            $translatedPathBuilder->appendIndex($index);
            $typePathBuilder->appendIndex('params');
            $typePathBuilder->appendIndex('0');

            $iterator->next();
        } while ($iterator->valid() && $iterator->isIndex());
    }

    private function getNextRoot(
        PropertyPathBuilder $typePathBuilder,
        ClassMetadata $root,
        string $property,
    ): ClassMetadata {
        $typePathBuilder->appendIndex('name');
        $accessor = PropertyAccess::createPropertyAccessor(); // TODO DIC?

        $propertyMetadata = $root->propertyMetadata[$property];
        Assert::isArray($propertyMetadata->type);
        Assert::isInstanceOf($typePathBuilder->getPropertyPath(), PropertyPathInterface::class);
        $type = $accessor->getValue($propertyMetadata->type, $typePathBuilder->getPropertyPath());

        // We could theoretically encounter a non-class type here,
        // but if we do, something is probably terribly broken elsewhere.
        return $this->getMetadata($type);
    }

    private function getMetadata(string $class): ClassMetadata
    {
        $metadata = $this->metadataFactory->getMetadataForClass($class);

        if ($metadata === null) {
            throw new Exception(); // TODO
        }

        assert($metadata instanceof ClassMetadata);

        return $metadata;
    }

    private function resolveSerializedName(ClassMetadata $classMetadata, string $name): string
    {
        $property = $classMetadata->propertyMetadata[$name];

        if ($property->serializedName !== null) {
            return $property->serializedName;
        }

        return $this->propertyNamingStrategy->translateName($property);
    }
}
