<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Response;

use Cdn77\NxgApi\Core\Application\Response\PropertyNaming\PropertyPathConverter;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Schema\Legacy\FieldsErrorsSchema;
use Symfony\Component\PropertyAccess\PropertyPath;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;

use function is_object;

final class ErrorResponseResolver
{
    private PropertyPathConverter $propertyPathConverter;

    public function __construct(PropertyPathConverter $propertyPathConverter)
    {
        $this->propertyPathConverter = $propertyPathConverter;
    }

    /**
     * @param ConstraintViolationListInterface<ConstraintViolationInterface> $violationsList
     * @param class-string<FieldsErrorsSchema|ErrorsSchema> $errorSchemaClass
     */
    public function resolve(
        ConstraintViolationListInterface $violationsList,
        string $errorSchemaClass,
    ): ErrorsSchema|FieldsErrorsSchema {
        $errors = new $errorSchemaClass();

        foreach ($violationsList as $violation) {
            $errors->addError($violation->getMessage());

            if (! $errors instanceof FieldsErrorsSchema) {
                continue;
            }

            $propertyPath = $this->formatFieldPath($violation);
            if ($propertyPath === null) {
                continue;
            }

            $errors->addField($propertyPath, $violation->getMessage());
        }

        return $errors;
    }

    private function formatFieldPath(ConstraintViolationInterface $violation): string|null
    {
        if ($violation->getPropertyPath() === '') {
            return null;
        }

        $root = $violation->getRoot();

        if (! is_object($root)) {
            // this doesn't make sense...
            return null;
        }

        $rootClass = $root::class;
        $propertyPath = new PropertyPath($violation->getPropertyPath());

        return (string) $this->propertyPathConverter->toSourcePath($rootClass, $propertyPath);
    }
}
