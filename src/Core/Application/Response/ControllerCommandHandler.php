<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Response;

use Cdn77\NxgApi\Core\Application\Payload\CommandBusResultSchema;
use Cdn77\NxgApi\Core\Domain\Bus\Command;
use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use J<PERSON>\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

final class ControllerCommandHandler
{
    private CommandBus $commandBus;

    private ExceptionHandler $exceptionHandler;

    private SerializerInterface $serializer;

    public function __construct(
        CommandBus $commandBus,
        ExceptionHandler $exceptionHandler,
        SerializerInterface $serializer,
    ) {
        $this->commandBus = $commandBus;
        $this->exceptionHandler = $exceptionHandler;
        $this->serializer = $serializer;
    }

    public function handle(Command $command): Response
    {
        try {
            $this->commandBus->handle($command);
        } catch (Throwable $exception) {
            return $this->exceptionHandler->handle($exception);
        }

        return new Response(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * @see CommandBusResultSchema
     *
     * @param class-string<CommandBusResultSchema> $schemaClassName
     */
    public function handleWithResult(Command $command, string $schemaClassName, int $responseCode): JsonResponse
    {
        try {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    $schemaClassName::fromCommandBusResult($this->commandBus->handle($command)),
                    'json',
                ),
                $responseCode,
            );
        } catch (Throwable $exception) {
            return $this->exceptionHandler->handle($exception);
        }
    }
}
