<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Response;

use Cdn77\NxgApi\Core\Application\Payload\QueryBusResultSchema;
use Cdn77\NxgApi\Core\Domain\Bus\Query;
use Cdn77\NxgApi\Core\Domain\Bus\QueryBus;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

final class ControllerQueryHandler
{
    private ExceptionHandler $exceptionHandler;
    private QueryBus $queryBus;
    private SerializerFactory $serializerFactory;

    public function __construct(
        ExceptionHandler $exceptionHandler,
        QueryBus $queryBus,
        SerializerFactory $serializerFactory,
    ) {
        $this->exceptionHandler = $exceptionHandler;
        $this->queryBus = $queryBus;
        $this->serializerFactory = $serializerFactory;
    }

    /**
     * @see QueryBusResultSchema
     *
     * @param class-string<QueryBusResultSchema> $schemaClassName
     */
    public function handle(
        Query $query,
        string $schemaClassName,
        int $responseCode = Response::HTTP_OK,
        bool $serializeNull = false,
    ): JsonResponse {
        try {
            return JsonResponse::fromJsonString(
                $this->serializerFactory->serialize(
                    $schemaClassName::fromQueryBusResult($this->queryBus->handle($query)),
                    $serializeNull,
                ),
                $responseCode,
            );
        } catch (Throwable $exception) {
            return $this->exceptionHandler->handle($exception);
        }
    }
}
