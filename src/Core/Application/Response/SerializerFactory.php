<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Response;

use Cdn77\NxgApi\Core\Application\Serializer\UuidHandler;
use <PERSON><PERSON>\Serializer\Handler\DateHandler;
use <PERSON><PERSON>\Serializer\Handler\HandlerRegistryInterface;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON><PERSON>\Serializer\Serializer;
use <PERSON><PERSON>\Serializer\SerializerBuilder;
use <PERSON><PERSON>\Serializer\Visitor\Factory\JsonDeserializationVisitorFactory;
use <PERSON><PERSON>\Serializer\Visitor\Factory\JsonSerializationVisitorFactory;

use function class_exists;

use const JSON_PRESERVE_ZERO_FRACTION;
use const JSON_UNESCAPED_SLASHES;
use const JSON_UNESCAPED_UNICODE;

final class SerializerFactory
{
    public Serializer $serializer;

    public function __construct()
    {
        $this->create();
    }

    /** @param mixed $data */
    public function serialize($data, bool $serializeNull = false): string
    {
        $context = new SerializationContext();
        $context->setSerializeNull($serializeNull);

        return $this->serializer->serialize(
            $data,
            'json',
            $context,
        );
    }

    private function create(): void
    {
        $builder = new SerializerBuilder();

        $this->configureVisitors($builder);
        $this->configureHandlers($builder);

        $this->serializer = $builder->build();
    }

    private function configureVisitors(SerializerBuilder $builder): void
    {
        $serializationVisitorFactory = new JsonSerializationVisitorFactory();
        $deserializationVisitorFactory = new JsonDeserializationVisitorFactory();

        $serializationOptions = JSON_UNESCAPED_UNICODE | JSON_PRESERVE_ZERO_FRACTION | JSON_UNESCAPED_SLASHES;

        $serializationVisitorFactory->setOptions($serializationOptions);

        $builder->setSerializationVisitor('json', $serializationVisitorFactory);
        $builder->setDeserializationVisitor('json', $deserializationVisitorFactory);
    }

    private function configureHandlers(SerializerBuilder $builder): void
    {
        $builder->configureHandlers(static function (HandlerRegistryInterface $registry): void {
            $registry->registerSubscribingHandler(new DateHandler());

            if (! class_exists(UuidHandler::class)) {
                return;
            }

            $registry->registerSubscribingHandler(new UuidHandler());
        });
    }
}
