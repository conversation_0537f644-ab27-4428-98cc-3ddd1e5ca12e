<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Payload;

use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Service\Server\ServerStatus;

use function in_array;

class ServerStatusSchema
{
    public const FORCED_UP = 'up';
    public const FORCED_DOWN = 'down';
    public const FORCED_NONE = null;
    public const FORCEDS = [self::FORCED_UP, self::FORCED_DOWN, self::FORCED_NONE];

    public int $uid;

    private bool $autoUp;

    private bool $up;

    private bool $paused;

    private string|null $forced;

    public function __construct(int $uid, bool $autoUp, bool $up, bool $paused, string|null $forced)
    {
        if (! in_array($forced, self::FORCEDS, true)) {
            throw new InvalidArgument('Invalid forced.');
        }

        $this->uid = $uid;
        $this->autoUp = $autoUp;
        $this->up = $up;
        $this->paused = $paused;
        $this->forced = $forced;
    }

    public static function fromServerStatus(ServerStatus $status): self
    {
        return new self(
            $status->getServer()->getUid(),
            $status->isAutoUp(),
            $status->isUp(),
            $status->isPaused(),
            $status->forcedStatus(),
        );
    }

    public function getUid(): int
    {
        return $this->uid;
    }

    public function getAutoUp(): bool
    {
        return $this->autoUp;
    }

    public function isUp(): bool
    {
        return $this->up;
    }

    public function isPaused(): bool
    {
        return $this->paused;
    }

    public function getForced(): string|null
    {
        return $this->forced;
    }
}
