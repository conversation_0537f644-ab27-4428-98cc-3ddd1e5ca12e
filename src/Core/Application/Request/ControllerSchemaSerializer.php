<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Request;

use Cdn77\NxgApi\Core\Application\Payload\Schema;
use Cdn77\NxgApi\Core\Application\Response\ErrorResponseResolver;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Schema\Legacy\FieldsErrorsSchema;
use JMS\Serializer\ArrayTransformerInterface;
use JMS\Serializer\Exception\RuntimeException;
use J<PERSON>\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Validator\ValidatorInterface;

final class ControllerSchemaSerializer
{
    private ErrorResponseResolver $errorResponseResolver;

    private SerializerInterface $serializer;

    private ValidatorInterface $validator;
    private ArrayTransformerInterface $arrayTransformer;

    public function __construct(
        ArrayTransformerInterface $arrayTransformer,
        ErrorResponseResolver $errorResponseResolver,
        SerializerInterface $serializer,
        ValidatorInterface $validator,
    ) {
        $this->errorResponseResolver = $errorResponseResolver;
        $this->serializer = $serializer;
        $this->validator = $validator;
        $this->arrayTransformer = $arrayTransformer;
    }

    /**
     * @param class-string<TSchema> $schemaClassName
     *
     * @return ErrorsSchema|FieldsErrorsSchema|TSchema
     *
     * @template TSchema of Schema
     */
    public function deserialize(Request $request, string $schemaClassName)
    {
        try {
            $schema = $this->serializer->deserialize($request->getContent(), $schemaClassName, 'json');
        } catch (RuntimeException) {
            return ErrorsSchema::deserializationFailed();
        }

        $violations = $this->validator->validate($schema);

        return $violations->count() === 0
            ? $schema
            : $this->errorResponseResolver->resolve($violations, FieldsErrorsSchema::class);
    }

    /**
     * @param class-string<T> $schemaClassName
     *
     * @return ErrorsSchema|FieldsErrorsSchema|T
     *
     * @template T of Schema
     */
    public function deserializeQueryString(Request $request, string $schemaClassName)
    {
        try {
            $schema = $this->arrayTransformer->fromArray($request->query->all(), $schemaClassName);
        } catch (RuntimeException) {
            return ErrorsSchema::deserializationFailed();
        }

        $violations = $this->validator->validate($schema);

        return $violations->count() === 0
            ? $schema
            : $this->errorResponseResolver->resolve($violations, FieldsErrorsSchema::class);
    }

    public function serializeToResponse(ErrorsSchema $errorsSchema, int $responseCode = 422): JsonResponse
    {
        return JsonResponse::fromJsonString(
            $this->serializer->serialize($errorsSchema, 'json'),
            $responseCode,
        );
    }
}
