<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Utility;

use function Safe\json_decode;
use function Safe\json_encode;

final class ValueReplacer
{
    public static function emptyStringToNull(string|null $value): string|null
    {
        return $value === '' ? null : $value;
    }

    public static function nullToEmptyString(string|null $value): string
    {
        return $value ?? '';
    }

    public static function zeroToNull(int|null $value): int|null
    {
        return $value === 0 ? null : $value;
    }

    public static function nullToZero(int|null $value): int
    {
        return $value ?? 0;
    }

    /**
     * @param array<K,V>|null $value
     *
     * @return array<K,V>|null
     *
     * @template K
     * @template V
     */
    public static function emptyArrayToNull(array|null $value): array|null
    {
        return $value === [] ? null : $value;
    }

    /** @param int|string|array<mixed>|null $value */
    public static function notNullValueToJson($value): string|null
    {
        if ($value === null) {
            return null;
        }

        return json_encode($value);
    }

    /** @return array<string, mixed>|null */
    public static function notNullValueToDecodedJson(string|null $value): array|null
    {
        if ($value === null) {
            return null;
        }

        return json_decode($value, true);
    }

    /**
     * @param array<K,V>|null $value
     *
     * @return array<K,V>
     *
     * @template K
     * @template V
     */
    public static function nullToEmptyArray(array|null $value): array
    {
        return $value ?? [];
    }

    public static function intToBoolean(int|null $value): bool
    {
        return $value === 1;
    }

    /** @return array<string, mixed> */
    public static function jsonStringToArray(string|null $value): array|null
    {
        if ($value === null) {
            return null;
        }

        return json_decode($value, true);
    }
}
