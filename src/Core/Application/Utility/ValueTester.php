<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Utility;

final class ValueTester
{
    /** @param array<mixed> $value */
    public static function emptyArrayOrNull(array|null $value): bool
    {
        return $value === null || $value === [];
    }

    /**
     * @param mixed $valueA
     * @param mixed $valueB
     */
    public static function notSameOrBothNull($valueA, $valueB): bool
    {
        if ($valueA === null && $valueB === null) {
            return true;
        }

        return $valueA !== $valueB;
    }
}
