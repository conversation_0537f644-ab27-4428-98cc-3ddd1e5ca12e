<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Container;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\OpenApiPaths;
use ReflectionClass;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Reference;

use function array_keys;

final class OpenApiPathsCompilerPass implements CompilerPassInterface
{
    public const TAG_NAME = 'nxgapi.open_api_paths';

    public function process(ContainerBuilder $container): void
    {
        $definition = $container->findDefinition(OpenApiPaths::class);

        /**
         * @see HasOpenApiPaths
         *
         * @var array<class-string<HasOpenApiPaths>, mixed> $tagged
         */
        $tagged = $container->findTaggedServiceIds(self::TAG_NAME);
        foreach (array_keys($tagged) as $id) {
            $class = new ReflectionClass($id);
            if ($class->isAbstract()) {
                continue;
            }

            $definition->addMethodCall('add', [new Reference($id)]);
        }
    }
}
