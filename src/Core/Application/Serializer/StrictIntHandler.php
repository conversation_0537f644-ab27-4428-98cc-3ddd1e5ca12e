<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Serializer;

use <PERSON><PERSON>\Serializer\Annotation as J<PERSON>;
use <PERSON><PERSON>\Serializer\DeserializationContext;
use <PERSON><PERSON>\Serializer\GraphNavigatorInterface;
use <PERSON><PERSON>\Serializer\Handler\SubscribingHandlerInterface;
use <PERSON><PERSON>\Serializer\JsonDeserializationVisitor;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\SerializationContext;

use function is_int;

/** @JMS\HandlerCallback("json", direction = "deserialization", method = "deserializeString") */
class StrictIntHandler implements SubscribingHandlerInterface
{
    /** @return array<array<string, string|int>> */
    public static function getSubscribingMethods(): array
    {
        return [
            [
                'direction' => GraphNavigatorInterface::DIRECTION_DESERIALIZATION,
                'format' => 'json',
                'type' => 'intStrict',
                'method' => 'deserializeInt',
            ],
            [
                'direction' => GraphNavigatorInterface::DIRECTION_SERIALIZATION,
                'format' => 'json',
                'type' => 'intStrict',
                'method' => 'serializeInt',
            ],
        ];
    }

    /**
     * @param mixed $value
     * @param list<string> $type
     */
    public function deserializeInt(
        JsonDeserializationVisitor $visitor,
        $value,
        array $type,
        DeserializationContext $context,
    ): int|null {
        if (is_int($value)) {
            return $value;
        }

        return null;
    }

    /** @param list<string> $type */
    public function serializeInt(
        JsonSerializationVisitor $visitor,
        int $value,
        array $type,
        SerializationContext $context,
    ): int {
        return $value;
    }
}
