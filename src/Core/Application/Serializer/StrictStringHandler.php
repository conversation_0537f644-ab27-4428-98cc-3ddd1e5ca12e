<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Serializer;

use <PERSON><PERSON>\Serializer\Annotation as J<PERSON>;
use <PERSON><PERSON>\Serializer\DeserializationContext;
use <PERSON><PERSON>\Serializer\GraphNavigatorInterface;
use <PERSON><PERSON>\Serializer\Handler\SubscribingHandlerInterface;
use <PERSON><PERSON>\Serializer\JsonDeserializationVisitor;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\SerializationContext;

use function is_string;

/** @JMS\HandlerCallback("json", direction = "deserialization", method = "deserializeString") */
class StrictStringHandler implements SubscribingHandlerInterface
{
    /** @return array<array<string, string|int>> */
    public static function getSubscribingMethods(): array
    {
        return [
            [
                'direction' => GraphNavigatorInterface::DIRECTION_DESERIALIZATION,
                'format' => 'json',
                'type' => 'stringStrict',
                'method' => 'deserializeString',
            ],
            [
                'direction' => GraphNavigatorInterface::DIRECTION_SERIALIZATION,
                'format' => 'json',
                'type' => 'stringStrict',
                'method' => 'serializeString',
            ],
        ];
    }

    /**
     * @param mixed $value
     * @param list<string> $type
     */
    public function deserializeString(
        JsonDeserializationVisitor $visitor,
        $value,
        array $type,
        DeserializationContext $context,
    ): string|null {
        if (is_string($value)) {
            return $value;
        }

        return null;
    }

    /** @param list<string> $type */
    public function serializeString(
        JsonSerializationVisitor $visitor,
        string $value,
        array $type,
        SerializationContext $context,
    ): string {
        return $value;
    }
}
