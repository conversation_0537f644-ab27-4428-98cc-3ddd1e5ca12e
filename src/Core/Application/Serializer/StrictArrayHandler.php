<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Serializer;

use <PERSON><PERSON>\Serializer\Annotation as J<PERSON>;
use <PERSON><PERSON>\Serializer\DeserializationContext;
use <PERSON><PERSON>\Serializer\GraphNavigatorInterface;
use <PERSON><PERSON>\Serializer\Handler\SubscribingHandlerInterface;
use <PERSON><PERSON>\Serializer\JsonDeserializationVisitor;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\SerializationContext;

use function is_array;

/** @JMS\HandlerCallback("json", direction = "deserialization", method = "deserializeArray") */
class StrictArrayHandler implements SubscribingHandlerInterface
{
    /** @return array<array<string, string|int>> */
    public static function getSubscribingMethods(): array
    {
        return [
            [
                'direction' => GraphNavigatorInterface::DIRECTION_DESERIALIZATION,
                'format' => 'json',
                'type' => 'arrayStrict',
                'method' => 'deserializeArray',
            ],
            [
                'direction' => GraphNavigatorInterface::DIRECTION_SERIALIZATION,
                'format' => 'json',
                'type' => 'arrayStrict',
                'method' => 'serializeArray',
            ],
        ];
    }

    /**
     * @param mixed $value
     * @param list<string> $type
     *
     * @return array<mixed>|null
     */
    public function deserializeArray(
        JsonDeserializationVisitor $visitor,
        $value,
        array $type,
        DeserializationContext $context,
    ): array|null {
        if (is_array($value)) {
            return $value;
        }

        return null;
    }

    /** @param list<string> $type */
    public function serializeArray(
        JsonSerializationVisitor $visitor,
        int $value,
        array $type,
        SerializationContext $context,
    ): int {
        return $value;
    }
}
