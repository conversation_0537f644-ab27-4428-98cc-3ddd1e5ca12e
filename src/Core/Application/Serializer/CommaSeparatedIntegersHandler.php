<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Serializer;

use <PERSON><PERSON>\Serializer\Annotation as J<PERSON>;
use <PERSON><PERSON>\Serializer\DeserializationContext;
use <PERSON><PERSON>\Serializer\Exception\RuntimeException;
use <PERSON><PERSON>\Serializer\GraphNavigatorInterface;
use <PERSON><PERSON>\Serializer\Handler\SubscribingHandlerInterface;
use <PERSON><PERSON>\Serializer\JsonDeserializationVisitor;

use function array_map;
use function explode;
use function Safe\preg_match;

/** @JMS\HandlerCallback("json", direction = "deserialization", method = "deserialize") */
class CommaSeparatedIntegersHandler implements SubscribingHandlerInterface
{
    private const FILTERED_IDS_DELIMITER = ',';
    private const INTEGERS_COMMA_SEPARATED_PATTERN = '/^(\d+,)*\d+\z/';

    /** @return array<array<string, string|int>> */
    public static function getSubscribingMethods(): array
    {
        return [
            [
                'direction' => GraphNavigatorInterface::DIRECTION_DESERIALIZATION,
                'format' => 'json',
                'type' => 'commaSeparatedInteger',
                'method' => 'deserialize',
            ],
        ];
    }

    /**
     * @param mixed $value
     * @param list<string> $type
     *
     * @return array<int>|null
     */
    public function deserialize(
        JsonDeserializationVisitor $visitor,
        $value,
        array $type,
        DeserializationContext $context,
    ): array|null {
        if ($value !== null && preg_match(self::INTEGERS_COMMA_SEPARATED_PATTERN, $value) === 0) {
            throw new RuntimeException();
        }

        if ($value !== null) {
            return array_map('intval', explode(self::FILTERED_IDS_DELIMITER, $value));
        }

        return null;
    }
}
