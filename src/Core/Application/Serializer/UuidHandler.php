<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Serializer;

use <PERSON><PERSON>\Serializer\DeserializationContext;
use <PERSON><PERSON>\Serializer\GraphNavigatorInterface;
use <PERSON><PERSON>\Serializer\Handler\SubscribingHandlerInterface;
use <PERSON><PERSON>\Serializer\JsonDeserializationVisitor;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\SerializationContext;
use <PERSON>\Uuid\Uuid;
use <PERSON>\Uuid\UuidInterface;

use function is_string;

class UuidHandler implements SubscribingHandlerInterface
{
    /** @return array<array<string, string|int>> */
    public static function getSubscribingMethods(): array
    {
        return [
            [
                'direction' => GraphNavigatorInterface::DIRECTION_DESERIALIZATION,
                'format' => 'json',
                'type' => 'uuid',
                'method' => 'deserializeUuid',
            ],
            [
                'direction' => GraphNavigatorInterface::DIRECTION_SERIALIZATION,
                'format' => 'json',
                'type' => 'uuid',
                'method' => 'serializeUuid',
            ],
        ];
    }

    /**
     * @param mixed $value
     * @param list<string> $type
     */
    public function deserializeUuid(
        JsonDeserializationVisitor $visitor,
        $value,
        array $type,
        DeserializationContext $context,
    ): UuidInterface|null {
        if (is_string($value)) {
            return Uuid::fromString($value);
        }

        return null;
    }

    /** @param list<string> $type */
    public function serializeUuid(
        JsonSerializationVisitor $visitor,
        UuidInterface $value,
        array $type,
        SerializationContext $context,
    ): string {
        return $value->toString();
    }
}
