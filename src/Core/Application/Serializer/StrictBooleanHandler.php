<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Serializer;

use <PERSON><PERSON>\Serializer\Annotation as J<PERSON>;
use <PERSON><PERSON>\Serializer\DeserializationContext;
use <PERSON><PERSON>\Serializer\GraphNavigatorInterface;
use <PERSON><PERSON>\Serializer\Handler\SubscribingHandlerInterface;
use <PERSON><PERSON>\Serializer\JsonDeserializationVisitor;
use <PERSON><PERSON>\Serializer\JsonSerializationVisitor;
use <PERSON><PERSON>\Serializer\SerializationContext;

use function is_bool;

/** @JMS\HandlerCallback("json", direction = "deserialization", method = "deserializeBoolean") */
class StrictBooleanHandler implements SubscribingHandlerInterface
{
    /** @return array<array<string, string|int>> */
    public static function getSubscribingMethods(): array
    {
        return [
            [
                'direction' => GraphNavigatorInterface::DIRECTION_DESERIALIZATION,
                'format' => 'json',
                'type' => 'boolStrict',
                'method' => 'deserializeBoolean',
            ],
            [
                'direction' => GraphNavigatorInterface::DIRECTION_SERIALIZATION,
                'format' => 'json',
                'type' => 'boolStrict',
                'method' => 'serializeBoolean',
            ],
        ];
    }

    /**
     * @param mixed $value
     * @param list<string> $type
     */
    public function deserializeBoolean(
        JsonDeserializationVisitor $visitor,
        $value,
        array $type,
        DeserializationContext $context,
    ): bool|null {
        if (is_bool($value)) {
            return $value;
        }

        return null;
    }

    /** @param list<string> $type */
    public function serializeBoolean(
        JsonSerializationVisitor $visitor,
        bool $value,
        array $type,
        SerializationContext $context,
    ): bool {
        return $value;
    }
}
