<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\OpenApi;

use Ds\Set;

final class OpenApiPaths
{
    /** @var Set<HasOpenApiPaths> */
    private Set $paths;

    public function __construct()
    {
        $this->paths = new Set();
    }

    public function add(HasOpenApiPaths $hasOpenApiPaths): void
    {
        $this->paths->add($hasOpenApiPaths);
    }

    /** @return Set<HasOpenApiPaths> */
    public function paths(): Set
    {
        return $this->paths;
    }
}
