<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\OpenApi;

use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use cebe\openapi\spec\Info;
use cebe\openapi\spec\OpenApi;
use cebe\openapi\spec\PathItem;
use Ds\Set;
use Exception;

use function array_merge;
use function implode;
use function sprintf;

use const PHP_EOL;

final class OpenApiSpec
{
    private OpenApiPaths $openApiPaths;

    private string $scheme;

    private string $host;

    public function __construct(OpenApiPaths $openApiPaths, string $scheme, string $host)
    {
        $this->openApiPaths = $openApiPaths;
        $this->scheme = $scheme;
        $this->host = $host;
    }

    public function create(): OpenApi
    {
        $config = $this->getConfig(Tags::get());
        $config['paths'] = $this->getPaths($this->openApiPaths->paths());

        return $this->getSpecForConfig($config);
    }

    /** @param array<string, string|Info|array<mixed>> $config */
    private function getSpecForConfig(array $config): OpenApi
    {
        $openApi = new OpenApi($config);
        if (! $openApi->validate()) {
            throw new Exception(implode(PHP_EOL, $openApi->getErrors()));
        }

        return $openApi;
    }

    /**
     * @param Set<HasOpenApiPaths> $openApiPathsList
     *
     * @return array<PathItem>
     */
    private function getPaths(Set $openApiPathsList): array
    {
        $paths = [];

        foreach ($openApiPathsList as $openApiPaths) {
            foreach ($openApiPaths->getPathItems() as $path => $pathItem) {
                if (isset($paths[$path])) {
                    $existingOperations = $paths[$path]->getOperations();
                    $paths[$path] = new PathItem(array_merge($existingOperations, $pathItem->getOperations()));

                    continue;
                }

                $paths[$path] = $pathItem;
            }
        }

        return $paths;
    }

    /**
     * @param array<array<string, string>> $tags
     *
     * @return array<string, string|Info|array<mixed>>
     */
    private function getConfig(array $tags): array
    {
        return [
            'openapi' => '3.0.3',
            'info' => new Info(
                [
                    'title' => 'NXG API Documentation',
                    'version' => '1.0.0',
                ],
            ),
            'servers' => [
                ['url' => sprintf('%s://%s', $this->scheme, $this->host)],
            ],
            'components' => [
                'securitySchemes' => [
                    'ApiKeyAuth' => [
                        'type' => 'apiKey',
                        'name' => 'Authorization',
                        'description' => 'MC-Token {token}',
                        'in' => 'header',
                    ],
                ],
            ],
            'security' => [['ApiKeyAuth' => []]],
            'tags' => $tags,
        ];
    }
}
