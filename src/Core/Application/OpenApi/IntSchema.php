<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\OpenApi;

use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

final class IntSchema
{
    public static function spec(string|null $description = null): Schema
    {
        $data = [
            'example' => 666,
            'type' => Type::INTEGER,
        ];

        if ($description !== null) {
            $data['description'] = $description;
        }

        return new Schema($data);
    }
}
