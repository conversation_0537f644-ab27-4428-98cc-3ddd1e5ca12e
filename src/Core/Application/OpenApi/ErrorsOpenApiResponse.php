<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\OpenApi;

use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Response;

final class ErrorsOpenApiResponse
{
    public static function spec(string $description): Response
    {
        return new Response([
            'description' => $description,
            'content' => [
                'application/json' => new MediaType([
                    'schema' => ErrorsSchema::getSchemaSpec($description),
                ]),
            ],
        ]);
    }
}
