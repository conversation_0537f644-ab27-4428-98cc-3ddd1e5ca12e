<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\OpenApi;

use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Response;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

final class FieldsErrorsOpenApiResponse
{
    public static function spec(string $description): Response
    {
        return new Response([
            'description' => $description,
            'content' => [
                'application/json' => new MediaType([
                    'schema' => new Schema([
                        'type' => Type::OBJECT,
                        'properties' => [
                            'fields' => new Schema([
                                'type' => Type::ARRAY,
                                'items' => new Schema(
                                    ['type' => Type::STRING, 'example' => 'Field is not a valid e-mail address.'],
                                ),
                            ]),
                        ],
                    ]),
                ]),
            ],
        ]);
    }
}
