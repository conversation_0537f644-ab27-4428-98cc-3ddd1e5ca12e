<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\OpenApi\Model;

final class Tags
{
    public const ACCOUNT = 'Account';
    public const LETS_ENCRYPT = 'LetsEncrypt';
    public const LOCATION = 'Location';
    public const RESOURCE = 'Resource';
    public const CERTIFICATE = 'Certificate';

    /** @return array<array<string, string>> */
    public static function get(): array
    {
        return [
            ['name' => self::ACCOUNT],
            ['name' => self::LOCATION],
            ['name' => self::RESOURCE],
        ];
    }
}
