<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\OpenApi;

use Cdn77\NxgApi\CoreLibrary\Reflection\Reflection;
use Symfony\Bundle\FrameworkBundle\Routing\Router;
use Symfony\Component\Routing\Generator\CompiledUrlGenerator;
use Symfony\Component\Routing\RouterInterface;
use Webmozart\Assert\Assert;

use function array_combine;
use function array_map;
use function sprintf;
use function strtr;

final class PathGenerator
{
    private RouterInterface $router;

    /** @var array<string, array<int, mixed>> */
    private array $compiledRoutes;

    public function __construct(RouterInterface $router)
    {
        Assert::isInstanceOf($router, Router::class);
        $generator = $router->getGenerator();
        Assert::isInstanceOf($generator, CompiledUrlGenerator::class);

        $this->router = $router;
        $this->compiledRoutes = Reflection::getPropertyValue($generator, 'compiledRoutes');
    }

    public function generate(string $name): string
    {
        [$parameters] = $this->compiledRoutes[$name];

        return strtr(
            $this->router->generate(
                $name,
                array_map(
                    static fn (string $routeName): string => sprintf('{%s}', $routeName),
                    array_combine($parameters, $parameters),
                ),
                RouterInterface::ABSOLUTE_PATH,
            ),
            [
                '%7B' => '{',
                '%7D' => '}',
            ],
        );
    }
}
