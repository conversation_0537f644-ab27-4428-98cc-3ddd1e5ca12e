<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\OpenApi;

use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

final class UuidSchema
{
    public static function spec(string|null $description = null, bool|null $nullable = false): Schema
    {
        $data = [
            'example' => '9880280c-0507-46d1-88f3-71cfee83fa57',
            'format' => 'UUIDv4',
            'type' => Type::STRING,
        ];

        if ($description !== null) {
            $data['description'] = $description;
        }

        $data['nullable'] = $nullable;

        return new Schema($data);
    }
}
