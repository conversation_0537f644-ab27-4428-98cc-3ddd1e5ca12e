<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Controller;

use Cdn77\NxgApi\Schema\VersionSchema;
use Cdn77\NxgApi\Versioning\Versions;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class VersionController
{
    public const ROUTE_NAME = 'version';

    /** @var SerializerInterface */
    private $serializer;

    public function __construct(SerializerInterface $serializer)
    {
        $this->serializer = $serializer;
    }

    /** @Route(path="/version", name=self::ROUTE_NAME, methods={Request::METHOD_GET}) */
    public function execute(): JsonResponse
    {
        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                new VersionSchema(Versions::VERSION_1_0_0),
                'json',
            ),
            Response::HTTP_OK,
        );
    }
}
