<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\OpenApiSpec;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

final class DocsController
{
    public const ROUTE_NAME = 'open-api.doc.json';

    private OpenApiSpec $openApiSpec;

    public function __construct(OpenApiSpec $openApiSpec)
    {
        $this->openApiSpec = $openApiSpec;
    }

    /** @Route(path="/docs/openapi.json", name=self::ROUTE_NAME, methods={Request::METHOD_GET}) */
    public function execute(): JsonResponse
    {
        return new JsonResponse($this->openApiSpec->create()->getSerializableData());
    }
}
