<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Infrastructure\Repository;

use Cdn77\NxgApi\Core\Domain\Repository\SslRepository;
use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\Entity\Legacy\Ssl;

final class DoctrineSslRepository implements SslRepository
{
    use EntityManagerConstructor;

    public function add(Ssl $ssl): void
    {
        $this->entityManager->persist($ssl);
    }

    public function remove(Ssl $ssl): void
    {
        $this->entityManager->remove($ssl);
    }

    public function findForResource(int $resourceId): Ssl|null
    {
        return $this->entityManager->createQueryBuilder()
            ->select('ssl')
            ->from(Ssl::class, 'ssl')
            ->where('ssl.resource = :resourceId')
            ->setParameter('resourceId', $resourceId)
            ->getQuery()
            ->getOneOrNullResult();
    }
}
