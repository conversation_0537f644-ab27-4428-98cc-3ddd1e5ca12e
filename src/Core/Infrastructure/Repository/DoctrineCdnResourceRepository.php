<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Infrastructure\Repository;

use Cdn77\NxgApi\Core\Domain\Repository\CdnResourceRepository;
use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Resource\Domain\Exception\CdnResourceNotFound;

final class DoctrineCdnResourceRepository implements CdnResourceRepository
{
    use EntityManagerConstructor;

    public function get(int $resourceId): CdnResource
    {
        $resource = $this->find($resourceId);

        if ($resource === null) {
            throw CdnResourceNotFound::fromResourceId($resourceId);
        }

        return $resource;
    }

    public function find(int $resourceId): CdnResource|null
    {
        return $this->entityManager->find(CdnResource::class, $resourceId);
    }
}
