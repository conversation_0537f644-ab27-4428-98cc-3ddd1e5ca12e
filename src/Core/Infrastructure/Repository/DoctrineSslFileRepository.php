<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Infrastructure\Repository;

use Cdn77\NxgApi\Core\Domain\Repository\SslFileRepository;
use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\Entity\Legacy\SslFile;

final class DoctrineSslFileRepository implements SslFileRepository
{
    use EntityManagerConstructor;

    public function add(SslFile $sslFile): void
    {
        $this->entityManager->persist($sslFile);
    }
}
