<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Infrastructure\Tactician;

use Cdn77\NxgApi\Core\Domain\Bus\Query;
use Cdn77\NxgApi\Core\Domain\Bus\QueryBus;
use League\Tactician\CommandBus;

final class TacticianQueryBus implements QueryBus
{
    private CommandBus $bus;

    public function __construct(CommandBus $commandBus)
    {
        $this->bus = $commandBus;
    }

    public function handle(Query $command): mixed
    {
        return $this->bus->handle($command);
    }
}
