<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Infrastructure\Tactician;

use Cdn77\NxgApi\Core\Domain\Bus\Command;
use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;

final class TacticianCommandBus implements CommandBus
{
    private \League\Tactician\CommandBus $bus;

    public function __construct(\League\Tactician\CommandBus $commandBus)
    {
        $this->bus = $commandBus;
    }

    public function handle(Command $command): mixed
    {
        return $this->bus->handle($command);
    }
}
