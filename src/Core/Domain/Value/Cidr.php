<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Domain\Value;

use IPTools\Network;
use Webmozart\Assert\Assert;

final class Cidr
{
    public const MESSAGE_INVALID_FORMAT = 'Value is not a valid IPv4/IPv6 CIDR notation.';

    private string $value;

    private function __construct(string $value)
    {
        $this->value = $value;
    }

    public static function fromCidr(string $value): self
    {
        $network = Network::parse($value);
        Assert::same($value, $network->getCIDR());
        Assert::notContains($value, ' ');

        return new self($value);
    }

    public function toString(): string
    {
        return $this->value;
    }
}
