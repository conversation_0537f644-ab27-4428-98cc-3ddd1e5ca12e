<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Domain;

use Cdn77\NxgApi\Entity\Legacy\Ip;
use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Repository\Legacy\ServerRepository;
use Cdn77\NxgApi\Service\Server\Exception\MissingServerPrimaryIpAddress;
use Cdn77\NxgApi\Service\Server\ServerStatus;

use function array_map;

class ServerStatusCalculator
{
    /** @var ServerRepository */
    private $serverRepository;

    public function __construct(ServerRepository $serverRepository)
    {
        $this->serverRepository = $serverRepository;
    }

    public function getServerStatus(Server $server): ServerStatus
    {
        $primaryIp = $server->getPrimaryIp();

        if ($primaryIp === null) {
            throw new MissingServerPrimaryIpAddress('Server must have primary address.');
        }

        return $this->createServerStatus($server, $primaryIp);
    }

    /** @return ServerStatus[] */
    public function getStatusForAllServers(): array
    {
        $servers = [];
        foreach ($this->serverRepository->findAllWithPrimaryIp() as $server) {
            $servers[] = $this->createServerStatus($server->getServer(), $server->getPrimaryIp());
        }

        return $servers;
    }

    /**
     * @param Server[] $servers
     *
     * @return ServerStatus[]
     */
    public function getStatusForServers(array $servers): array
    {
        return array_map(
            fn (Server $server): ServerStatus => $this->getServerStatus($server),
            $servers,
        );
    }

    private function createServerStatus(Server $server, Ip $primaryIp): ServerStatus
    {
        return new ServerStatus(
            $server,
            $primaryIp->isUp(),
            $this->calculateServerUpState($server, $primaryIp),
            $server->isPaused(),
            $server->getForcedState(),
        );
    }

    private function calculateServerUpState(Server $server, Ip $primaryIp): bool
    {
        if ($server->isPaused()) {
            return false;
        }

        if ($server->getForcedState() !== null) {
            return $server->getForcedState();
        }

        return $primaryIp->isUp();
    }
}
