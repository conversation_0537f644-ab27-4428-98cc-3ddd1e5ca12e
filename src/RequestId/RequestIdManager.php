<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\RequestId;

class RequestIdManager
{
    /** @var string */
    private $id;

    /** @var RequestIdGenerator */
    private $idGenerator;

    public function __construct(RequestIdGenerator $idGenerator)
    {
        $this->idGenerator = $idGenerator;
    }

    public function get(): string
    {
        if ($this->id === null) {
            $this->regenerate();
        }

        return $this->id;
    }

    public function regenerate(): void
    {
        $this->id = $this->idGenerator->generate();
    }
}
