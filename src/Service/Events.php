<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service;

use Cdn77\NxgApi\Exception\Runtime;

final class Events
{
    public const ACCOUNT_CREATED = 'service.account.created';

    public const IP_CREATED = 'service.ip.created';
    public const IP_DELETED = 'service.ip.deleted';
    public const IP_PRIMARY_STATE_CHANGED = 'service.ip.primary_state_changed';

    public function __construct()
    {
        throw new Runtime(static::class . ' is static and could not be instantiated.');
    }
}
