<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Messaging\Message;

use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Exception\InvalidState;
use Safe\Exceptions\JsonException;

use function in_array;
use function Safe\json_decode;
use function Safe\json_encode;
use function sprintf;

class ResourceChangeMessage
{
    public const TYPE_CREATED = 'C';
    public const TYPE_UPDATED = 'U';
    public const TYPE_DELETED = 'D';
    public const TYPE_RESTORED = 'R';
    private const TYPES = [
        self::TYPE_CREATED,
        self::TYPE_UPDATED,
        self::TYPE_DELETED,
        self::TYPE_RESTORED,
    ];

    /** @var int */
    private $id;

    /** @var string */
    private $type;

    /** @var string */
    private $date;

    public function __construct(int $id, string $type, string $date)
    {
        if (! in_array($type, self::TYPES, true)) {
            throw new InvalidArgument('Invalid resource change type supplied.');
        }

        $this->id = $id;
        $this->type = $type;
        $this->date = $date;
    }

    public static function deserialize(string $payload): self
    {
        try {
            $data = json_decode($payload);
        } catch (JsonException $exception) {
            throw new InvalidState(sprintf('Malformed JSON payload (%s).', $exception->getMessage()));
        }

        return new self(
            $data->id,
            $data->type,
            $data->date,
        );
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getDate(): string
    {
        return $this->date;
    }

    public function serialize(): string
    {
        try {
            return json_encode(
                [
                    'id' => $this->id,
                    'type' => $this->type,
                    'date' => $this->date,
                ],
            );
        } catch (JsonException $exception) {
            throw new InvalidState(sprintf('Malformed JSON payload (%s).', $exception->getMessage()));
        }
    }
}
