<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\ExternalApi\CertificateBucket;

use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Exception\Runtime;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\Exception\FileCorrupted;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\Exception\FileWriteFailed;
use League\Flysystem\FilesystemOperator;
use Throwable;

use function sprintf;

class CertificateBucket
{
    private NamingStrategy $namingStrategy;
    private FilesystemOperator $filesystem;

    public function __construct(NamingStrategy $namingStrategy, FilesystemOperator $filesystem)
    {
        $this->namingStrategy = $namingStrategy;
        $this->filesystem = $filesystem;
    }

    public function delete(SslFile $sslFile): void
    {
        try {
            @$this->filesystem->delete($this->getCertificateFilename($sslFile));
        } catch (Throwable) {
            // ignore all errors - best effort clean up
        }

        try {
            @$this->filesystem->delete($this->getKeyFilename($sslFile));
        } catch (Throwable) {
            // ignore all errors - best effort clean up
        }
    }

    /**
     * @throws FileWriteFailed
     * @throws FileCorrupted
     */
    public function save(SslFile $sslFile, CertificatePair $certificatePair): bool
    {
        try {
            $this->filesystem->write($this->getCertificateFilename($sslFile), $certificatePair->getCertificate());
            $this->filesystem->write($this->getKeyFilename($sslFile), $certificatePair->getPrivateKey());
        } catch (Throwable) {
            throw FileWriteFailed::forCdnResource($sslFile->getSsl()->getResource());
        }

        try {
            if (
                $this->filesystem->read($this->getCertificateFilename($sslFile)) !== $certificatePair->getCertificate()
                || $this->filesystem->read($this->getKeyFilename($sslFile)) !== $certificatePair->getPrivateKey()
            ) {
                throw FileCorrupted::forCdnResource($sslFile->getSsl()->getResource());
            }
        } catch (Throwable) {
            throw FileCorrupted::forCdnResource($sslFile->getSsl()->getResource());
        }

        return true;
    }

    public function get(SslFile $sslFile): CertificatePair
    {
        try {
            $certificate = $this->filesystem->read($this->getCertificateFilename($sslFile));
            $key = $this->filesystem->read($this->getKeyFilename($sslFile));
        } catch (Throwable) {
            throw new Runtime(sprintf('Failed to read certificate files for file #%s.', $sslFile->getId()));
        }

        return new CertificatePair($certificate, $key);
    }

    private function getCertificateFilename(SslFile $file): string
    {
        $baseName = $this->namingStrategy->getNameForFile($file);

        return sprintf('%s.%s', $baseName, $this->namingStrategy->getCertificateExtension());
    }

    private function getKeyFilename(SslFile $file): string
    {
        $baseName = $this->namingStrategy->getNameForFile($file);

        return sprintf('%s.%s', $baseName, $this->namingStrategy->getKeyExtension());
    }
}
