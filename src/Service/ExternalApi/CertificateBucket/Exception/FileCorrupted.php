<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\Exception;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Exception;

use function sprintf;

final class FileCorrupted extends Exception
{
    public static function forCdnResource(CdnResource $cdnResource): self
    {
        return new static(
            sprintf(
                'Saved certificate files are corrupted for CDN Resource ID %d',
                $cdnResource->getId(),
            ),
        );
    }
}
