<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\ExternalApi\CertificateBucket;

use Cdn77\NxgApi\Entity\Legacy\SslFile;

use function sprintf;

class DefaultNamingStrategy implements NamingStrategy
{
    public const NAMING_PATTERN = '%s_%s';

    public function getNameForFile(SslFile $sslFile): string
    {
        // <resourceId>_<sslFileIndex>.<pem|key>
        return sprintf(self::NAMING_PATTERN, $sslFile->getSsl()->getResource()->getId(), $sslFile->getIndex());
    }

    public function getCertificateExtension(): string
    {
        return 'pem';
    }

    public function getKeyExtension(): string
    {
        return 'key';
    }
}
