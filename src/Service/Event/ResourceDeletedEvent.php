<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Event;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Symfony\Contracts\EventDispatcher\Event;

final class ResourceDeletedEvent extends Event
{
    /** @var CdnResource */
    private $resource;

    public function __construct(CdnResource $resource)
    {
        $this->resource = $resource;
    }

    public function getResource(): CdnResource
    {
        return $this->resource;
    }
}
