<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Event;

use Cdn77\NxgApi\Entity\Legacy\Account;
use Symfony\Contracts\EventDispatcher\Event;

final class AccountEvent extends Event
{
    /** @var Account */
    private $account;

    public function __construct(Account $account)
    {
        $this->account = $account;
    }

    public function getAccount(): Account
    {
        return $this->account;
    }
}
