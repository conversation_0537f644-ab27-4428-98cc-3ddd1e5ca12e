<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Event;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Symfony\Contracts\EventDispatcher\Event;

final class ResourceChangedEvent extends Event
{
    /** @var CdnResource */
    private $old;

    /** @var CdnResource */
    private $new;

    public function __construct(CdnResource $old, CdnResource $new)
    {
        $this->old = $old;
        $this->new = $new;
    }

    public function getOld(): CdnResource
    {
        return $this->old;
    }

    public function getNew(): CdnResource
    {
        return $this->new;
    }
}
