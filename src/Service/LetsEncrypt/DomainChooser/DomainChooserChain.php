<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Exception\InvalidState;

use function array_merge;
use function count;
use function uksort;

class DomainChooserChain implements DomainChooser
{
    /** @var DomainChooser[][] */
    private $prioritizedChoosers = [];

    public function supports(CdnResource $resource): bool
    {
        return true;
    }

    /** @return string[] */
    public function choose(CdnResource $resource): array
    {
        foreach ($this->getOrderedChoosers() as $chooser) {
            if (! $chooser->supports($resource)) {
                continue;
            }

            return $chooser->choose($resource);
        }

        throw new InvalidState('No matching chooser found.');
    }

    public function add(DomainChooser $chooser, int $priority = 0): void
    {
        if (! isset($this->prioritizedChoosers[$priority])) {
            $this->prioritizedChoosers[$priority] = [];
        }

        $this->prioritizedChoosers[$priority][] = $chooser;
    }

    /** @return DomainChooser[][] */
    public function getPrioritizedChoosers(): array
    {
        return $this->prioritizedChoosers;
    }

    /** @return DomainChooser[] */
    public function getOrderedChoosers(): array
    {
        $choosers = $this->prioritizedChoosers;

        if (count($choosers) === 0) {
            return [];
        }

        uksort(
            $choosers,
            static fn (int $a, int $b): int => $b - $a,
        );

        return array_merge(...$choosers);
    }
}
