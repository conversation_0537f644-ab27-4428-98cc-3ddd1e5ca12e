<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Exception\InvalidArgument;

use function array_merge;
use function count;
use function sort;

class DefaultDomainChooser implements DomainChooser
{
    public function supports(CdnResource $resource): bool
    {
        return true;
    }

    /** @return string[] */
    public function choose(CdnResource $resource): array
    {
        $secondaryNames = $resource->getCnames();
        sort($secondaryNames); //with sorted domains we can later check Lets Encrypt limits

        $domains = array_merge([$resource->getCdnUrl()], $secondaryNames);

        // when only one domain is present, no cnames exist
        if (count($domains) <= 1) {
            throw new InvalidArgument('The resource has no cnames.');
        }

        return $domains;
    }
}
