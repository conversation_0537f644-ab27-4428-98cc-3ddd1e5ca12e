<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Exception\InvalidArgument;

use function array_filter;
use function array_merge;
use function count;
use function in_array;
use function sort;
use function strcasecmp;

class CraDomainChooser implements DomainChooser
{
    public const ACCOUNT_IDS = [39724];
    private const PRIMARY_DOMAIN_SUFFIX = '.ssl.cdn.cra.cz';

    public function supports(CdnResource $resource): bool
    {
        return in_array($resource->getAccount()->getId(), self::ACCOUNT_IDS, true);
    }

    /** @return string[] */
    public function choose(CdnResource $resource): array
    {
        $cnames = $resource->getCnames();

        if (count($cnames) === 0) {
            throw new InvalidArgument('CRA resource must have a cname.');
        }

        return $this->determineDomains($resource);
    }

    /** @return string[] */
    private function determineDomains(CdnResource $resource): array
    {
        $primaryName = $resource->getId() . self::PRIMARY_DOMAIN_SUFFIX;

        $secondaryNames = array_filter(
            $resource->getCnames(),
            static fn (string $name): bool => strcasecmp($name, $primaryName) !== 0,
        );

        if (count($secondaryNames) === count($resource->getCnames())) {
            // hmm, strange, it doesn't look like a valid resource...
            throw new InvalidArgument('Incorrectly configured CRA resource.');
        }

        sort($secondaryNames); //with sorted domains we can later check Lets Encrypt limits

        return array_merge([$primaryName], $secondaryNames);
    }
}
