<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\LetsEncrypt;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\LetsEncrypt\Domain\Value\RequestStateReason;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateManager;
use Cdn77\NxgApi\Service\Legacy\Certificate\Exception\CertificateDoesNotExist;
use DateTimeImmutable;

use function array_diff;
use function count;

class RequestManagerRequestSchedulerEvaluator implements RequestSchedulerEvaluator
{
    /** @var RequestManager */
    private $requestManager;

    /** @var CertificateManager */
    private $certificateManager;

    public function __construct(RequestManager $requestManager, CertificateManager $certificateManager)
    {
        $this->requestManager = $requestManager;
        $this->certificateManager = $certificateManager;
    }

    public function handleNewResource(CdnResource $resource): void
    {
        // only generate request for cdns with any custom cnames
        if (! $resource->hasInstantSsl() || ! $resource->hasCustomCnames()) {
            return;
        }

        $this->requestManager->createAndScheduleRequest($resource, new DateTimeImmutable());
    }

    public function handleChangedResource(CdnResource $old, CdnResource $new): void
    {
        $instantSslDisabled = $old->hasInstantSsl() && ! $new->hasInstantSsl();
        $instantSslEnabled = ! $old->hasInstantSsl() && $new->hasInstantSsl();
        $instantSslChanged = $instantSslDisabled || $instantSslEnabled;

        // instant ssl was disabled, so just clear any pending requests
        if ($instantSslDisabled) {
            $this->requestManager->cancelAllPendingRequests($new, RequestStateReason::CancelledInstantSslDisabled);

            return;
        }

        // instant ssl is disabled - no other action needed
        if (! $new->hasInstantSsl()) {
            return;
        }

        $addedCnames = array_diff($old->getCnames(), $new->getCnames());
        $deletedCnames = array_diff($new->getCnames(), $old->getCnames());
        $cnamesChanged = count($addedCnames) > 0 || count($deletedCnames) > 0;

        // no changes in cnames - no action needed when instant ssl was already active
        if (! $instantSslChanged && ! $cnamesChanged) {
            return;
        }

        $this->requestManager->cancelAllPendingRequests($new, RequestStateReason::CancelledCnameChange);

        // no current custom cnames: remove certificate and let it fall back to default shared certificate
        if (! $new->hasCustomCnames()) {
            try {
                $this->certificateManager->deleteCertificate($new);
            } catch (CertificateDoesNotExist) {
                // no certificate existed, ignore
            }

            return;
        }

        $this->requestManager->createAndScheduleRequest($new, new DateTimeImmutable());
    }
}
