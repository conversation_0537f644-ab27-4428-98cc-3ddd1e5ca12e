<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\LetsEncrypt;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\Request;
use Cdn77\NxgApi\LetsEncrypt\Domain\Finder\RequestFinder;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateManager;
use Cdn77\NxgApi\Service\Legacy\Certificate\Exception\CertificateDoesNotExist;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Throwable;

use function array_chunk;
use function count;
use function implode;
use function sprintf;

class RenewalManager
{
    /** @var CertificateManager */
    private $certificateManager;

    private DateTimeImmutable $dateTimeProvider;

    /** @var RequestManager */
    private $requestManager;

    /** @var CdnResourceRepository */
    private $resourceRepository;

    private EntityManagerInterface $entityManager;

    public function __construct(
        public RequestFinder $requestFinder,
        CertificateManager $certificateManager,
        RequestManager $requestManager,
        CdnResourceRepository $resourceRepository,
        EntityManagerInterface $entityManager,
    ) {
        $this->certificateManager = $certificateManager;
        $this->dateTimeProvider = new DateTimeImmutable();
        $this->requestManager = $requestManager;
        $this->resourceRepository = $resourceRepository;
        $this->entityManager = $entityManager;
    }

    /**
     * @param int[] $resourceIds
     *
     * @return Request[]
     */
    public function enqueueByResourceIds(array $resourceIds, bool $runAsSoonAsPossible): iterable
    {
        $resources = $this->resourceRepository->findBy(['id' => $resourceIds]);

        return $this->enqueue($resources, $runAsSoonAsPossible);
    }

    /** @return Request[] */
    public function enqueueOldCertificates(): iterable
    {
        $certificates = $this->certificateManager->getCertificates(
            true,
            false,
            SslFile::TYPE_LETSENCRYPT,
            $this->dateTimeProvider->modify('+ 20 days'),
        );

        $resources = [];
        foreach ($certificates as $certificate) {
            $resources[] = $certificate->getSsl()->getResource();
        }

        return $this->enqueue($resources);
    }

    /**
     * @param CdnResource[] $resources
     *
     * @return Request[]
     */
    private function enqueue(array $resources, bool $runAsSoonAsPossible = false): iterable
    {
        $past = $this->dateTimeProvider->modify('-5 years');
        $problemResources = [];
        $resourcesWithoutCnames = [];

        $chunks = array_chunk($resources, 10);
        foreach ($chunks as $minute => $resourcesChunk) {
            foreach ($resourcesChunk as $resource) {
                try {
                    if ($this->isResourceInvalidForRenewal($resource)) {
                        continue;
                    }

                    if (! $resource->hasCustomCnames()) {
                        $resourcesWithoutCnames[] = $resource;

                        continue;
                    }

                    $runAt = $runAsSoonAsPossible
                        ? $past
                        : $this->dateTimeProvider->modify(sprintf('+%d minutes', $minute));

                    yield  $this->requestManager->createAndScheduleRequest($resource, $runAt);
                } catch (Throwable) {
                    $problemResources[] = $resource;
                }
            }
        }

        $this->entityManager->flush();

        // this must be done after rescheduling, otherwise the certificates are not deleted
        $this->deleteCertificateOnResourcesWithoutCnames($resourcesWithoutCnames);

        $this->throwExceptionForResourcesWithProblem($problemResources);
    }

    private function isResourceInvalidForRenewal(CdnResource $resource): bool
    {
        return $resource->isDeleted() || $this->requestFinder->hasResourcePendingOrStale($resource);
    }

    /** @param CdnResource[] $problemResources */
    private function throwExceptionForResourcesWithProblem(array $problemResources): void
    {
        if (count($problemResources) === 0) {
            return;
        }

        $resourcesIds = [];
        foreach ($problemResources as $cdnResource) {
            $resourcesIds[] = $cdnResource->getId();
        }

        throw new RequestRenewal(
            'LE renewal failed for these CDN resources: ' . implode(',', $resourcesIds),
        );
    }

    /** @param CdnResource[] $resourcesWithoutCnames */
    private function deleteCertificateOnResourcesWithoutCnames(array $resourcesWithoutCnames): void
    {
        foreach ($resourcesWithoutCnames as $resource) {
            try {
                $this->certificateManager->deleteCertificate($resource);
            } catch (CertificateDoesNotExist) {
                // it's OK, just continue
            }
        }
    }
}
