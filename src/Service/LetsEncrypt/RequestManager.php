<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\LetsEncrypt;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\LetsEncrypt\Request;
use Cdn77\NxgApi\Entity\LetsEncrypt\RequestState;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\LetsEncrypt\Domain\Value\RequestStateReason;
use Cdn77\NxgApi\Repository\LetsEncrypt\RequestRepository;
use Cdn77\NxgApi\Repository\LetsEncrypt\TaskRepository;
use Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser\DomainChooser;
use DateTimeImmutable;
use Doctrine\DBAL\LockMode;
use Doctrine\ORM\EntityManagerInterface;
use Throwable;

class RequestManager
{
    /** @var DomainChooser */
    private $domainChooser;

    /** @var EntityManagerInterface */
    private $entityManager;

    /** @var RequestRepository */
    private $requestRepository;

    /** @var TaskRepository */
    private $taskRepository;

    public function __construct(
        DomainChooser $domainChooser,
        EntityManagerInterface $entityManager,
        RequestRepository $requestRepository,
        TaskRepository $taskRepository,
    ) {
        $this->domainChooser = $domainChooser;
        $this->entityManager = $entityManager;
        $this->requestRepository = $requestRepository;
        $this->taskRepository = $taskRepository;
    }

    public function createAndScheduleRequest(CdnResource $resource, DateTimeImmutable $runAt): Request
    {
        $now = new DateTimeImmutable();

        $request = new Request(
            $resource,
            $this->domainChooser->choose($resource),
            new DateTimeImmutable(),
            RequestState::getPending(),
        );
        $this->requestRepository->add($request);

        $this->taskRepository->add(new Task($request, $now, $runAt));

        return $request;
    }

    public function cancelAllPendingRequests(CdnResource $resource, RequestStateReason $reason): void
    {
        $this->entityManager->beginTransaction();

        try {
            /** @var Request[] $requests */
            $requests = $this->entityManager->createQueryBuilder()
                ->from(Request::class, 'r')
                ->select('r')
                ->where('r.resource = :resource')
                ->setParameter('resource', $resource)
                ->andWhere('r.state = :pending')
                ->setParameter('pending', RequestState::PENDING)
                ->getQuery()
                ->setLockMode(LockMode::PESSIMISTIC_WRITE)
                ->getResult();

            foreach ($requests as $request) {
                $request->cancel(new DateTimeImmutable(), $reason);
                $this->taskRepository->removeRequestTask($request);
            }

            $this->entityManager->commit();
        } catch (Throwable $e) {
            $this->entityManager->rollback();

            throw $e;
        }
    }
}
