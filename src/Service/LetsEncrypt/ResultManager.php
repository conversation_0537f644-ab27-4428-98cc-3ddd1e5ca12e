<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\LetsEncrypt;

use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Entity\LetsEncrypt\Exception\RequestFinished;
use Cdn77\NxgApi\Entity\LetsEncrypt\Request;
use Cdn77\NxgApi\Entity\LetsEncrypt\Result;
use Cdn77\NxgApi\Entity\LetsEncrypt\ResultStatus;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\LetsEncrypt\Domain\Value\RequestStateReason;
use Cdn77\NxgApi\Repository\LetsEncrypt\ResultRepository;
use Cdn77\NxgApi\Repository\LetsEncrypt\TaskRepository;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateManager;
use DateTimeImmutable;
use Doctrine\DBAL\LockMode;
use Doctrine\ORM\EntityManagerInterface;
use Webmozart\Assert\Assert;

use function mail;
use function rand;
use function Sentry\captureMessage;
use function sprintf;

final class ResultManager
{
    private const ONE_DAY_IN_SECONDS = 86400;
    private const TWO_DAYS_IN_SECONDS = 172800;
    private const ONE_WEEK_IN_SECONDS = 604800;
    private const TWO_WEEKS_IN_SECONDS = 1209600;
    private const ONE_MONTH_IN_SECONDS = 2419200;

    private const LE_CERTIFICATES_PER_REGISTERED_DOMAIN_RANGE = '-7 days';
    private const LE_CERTIFICATES_PER_REGISTERED_DOMAIN_LIMIT = 5;
    private const OUR_CERTIFICATES_PER_REGISTERED_DOMAIN_LIMIT = self::LE_CERTIFICATES_PER_REGISTERED_DOMAIN_LIMIT - 2;

    private CertificateManager $certificateManager;

    private EntityManagerInterface $entityManager;

    private TaskRepository $taskRepository;

    private ResultRepository $resultRepository;

    public function __construct(
        CertificateManager $certificateManager,
        EntityManagerInterface $entityManager,
        TaskRepository $taskRepository,
        ResultRepository $resultRepository,
    ) {
        $this->certificateManager = $certificateManager;
        $this->entityManager = $entityManager;
        $this->taskRepository = $taskRepository;
        $this->resultRepository = $resultRepository;
    }

    public function createResult(
        Request $request,
        ResultStatus $status,
        CertificatePair|null $certificatePair,
        string|null $description,
    ): void {
        $now = new DateTimeImmutable();

        $task = $this->taskRepository->getRequestTask($request);
        $result = new Result($request, $task->getCreatedAt(), $task->getRunAt(), $now, $status, $description);

        $this->entityManager->persist($result);

        $this->entityManager->lock($request, LockMode::PESSIMISTIC_WRITE);
        $this->entityManager->refresh($request);

        if (! $request->getState()->isPending()) {
            throw RequestFinished::new();
        }

        $this->taskRepository->removeRequestTask($request);

        if ($status->isSuccess()) {
            Assert::notNull($certificatePair);

            $request->complete($now);
            $this->certificateManager->setCertificate(
                $request->getResource(),
                SslFile::TYPE_LETSENCRYPT,
                $certificatePair,
            );
        } elseif ($this->isRequestTooOld($request)) {
            $request->cancel($now, RequestStateReason::CancelledTooOld);
        } elseif ($request->getResource()->isInactive()) {
            $request->cancel($now, RequestStateReason::CancelledInactiveResource);
        } else {
            $nextRunAt = $this->calculateNextRun($request, $status);
            $this->taskRepository->add(new Task($request, $now, $nextRunAt));
            $request->markUpdated($now);
        }
    }

    private function calculateNextRun(Request $request, ResultStatus $status): DateTimeImmutable
    {
        $actualTime = new DateTimeImmutable();
        $secondsFromFirstAttemptToCreateCertificate = $request->age();

        if (
            $this->resultRepository->countSuccessCertificateGenerationForDomainsSinceDate(
                $request->getDomains(),
                new DateTimeImmutable(self::LE_CERTIFICATES_PER_REGISTERED_DOMAIN_RANGE),
            ) >= self::OUR_CERTIFICATES_PER_REGISTERED_DOMAIN_LIMIT
        ) {
            $msg = sprintf(
                'Lets Encrypt limit is almost reached for resource %s and request ID %s',
                $request->getResource()->getId(),
                $request->getId()->toString(),
            );

            captureMessage($msg);
            mail('<EMAIL>,<EMAIL>', 'Lets Encrypt limit almost reached in NXG API', $msg);

            return $actualTime->modify('+1 day');
        }

        if ($status->isValidationError() && $secondsFromFirstAttemptToCreateCertificate < self::ONE_DAY_IN_SECONDS) {
            return $actualTime->modify('+5 minutes +' . rand(1, 60) . ' seconds');
        }

        if ($secondsFromFirstAttemptToCreateCertificate < self::TWO_DAYS_IN_SECONDS) {
            return $actualTime->modify('+30 minutes +' . rand(1, 180) . ' seconds');
        }

        if ($secondsFromFirstAttemptToCreateCertificate < self::ONE_WEEK_IN_SECONDS) {
            return $actualTime->modify('+60 minutes +' . rand(1, 360) . ' seconds');
        }

        if ($secondsFromFirstAttemptToCreateCertificate < self::TWO_WEEKS_IN_SECONDS) {
            return $actualTime->modify('+120 minutes +' . rand(1, 720) . ' seconds');
        }

        return $actualTime->modify('+20 hours +' . rand(1, 240) . ' minutes');
    }

    private function isRequestTooOld(Request $request): bool
    {
        return $request->age() > self::ONE_MONTH_IN_SECONDS;
    }
}
