<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service;

use Cdn77\NxgApi\Exception\Runtime;

final class ResourceEvents
{
    public const CREATED = self::NAMESPACE . 'created';
    public const CHANGED = self::NAMESPACE . 'changed';
    public const DELETED = self::NAMESPACE . 'deleted';
    public const UNDELETED = self::NAMESPACE . 'undeleted';
    public const REMOVED = self::NAMESPACE . 'removed';

    public const SUSPENDED = self::NAMESPACE . 'suspended';
    public const UNSUSPENDED = self::NAMESPACE . 'unsuspended';

    public const CERTIFICATE_CREATED = self::NAMESPACE . 'certificate.created';
    public const CERTIFICATE_DELETED = self::NAMESPACE . 'certificate.deleted';

    public const FULL_LOG_ENABLED = self::NAMESPACE . 'full_log.enabled';
    public const FULL_LOG_DISABLED = self::NAMESPACE . 'full_log.disabled';

    public const GEO_PROTECTION_ENABLED = self::NAMESPACE . 'geo_protection.enabled';
    public const GEO_PROTECTION_UPDATED = self::NAMESPACE . 'geo_protection.updated';
    public const GEO_PROTECTION_DISABLED = self::NAMESPACE . 'geo_protection.disabled';

    public const IP_PROTECTION_ENABLED = self::NAMESPACE . 'ip_protection.enabled';
    public const IP_PROTECTION_UPDATED = self::NAMESPACE . 'ip_protection.updated';
    public const IP_PROTECTION_DISABLED = self::NAMESPACE . 'ip_protection.disabled';

    public const REFERER_PROTECTION_ENABLED = self::NAMESPACE . 'referer_protection.enabled';
    public const REFERER_PROTECTION_UPDATED = self::NAMESPACE . 'referer_protection.updated';
    public const REFERER_PROTECTION_DISABLED = self::NAMESPACE . 'referer_protection.disabled';

    public const CUSTOM_LOCATION_ENABLED = self::NAMESPACE . 'custom_location.enabled';
    public const CUSTOM_LOCATION_UPDATED = self::NAMESPACE . 'custom_location.updated';
    public const CUSTOM_LOCATION_DISABLED = self::NAMESPACE . 'custom_location.disabled';

    private const NAMESPACE = 'service.resource.';

    public function __construct()
    {
        throw new Runtime(static::class . ' is static and could not be instantiated.');
    }
}
