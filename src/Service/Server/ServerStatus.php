<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Server;

use Cdn77\NxgApi\Core\Application\Payload\ServerStatusSchema;
use Cdn77\NxgApi\Entity\Legacy\Server;

class ServerStatus
{
    private Server $server;

    private bool $autoUp;

    private bool $up;

    private bool $paused;

    private bool|null $forced;

    public function __construct(Server $server, bool $autoUp, bool $up, bool $paused, bool|null $forced)
    {
        $this->server = $server;
        $this->autoUp = $autoUp;
        $this->up = $up;
        $this->paused = $paused;
        $this->forced = $forced;
    }

    public function getServer(): Server
    {
        return $this->server;
    }

    public function isAutoUp(): bool
    {
        return $this->autoUp;
    }

    public function isUp(): bool
    {
        return $this->up;
    }

    public function isPaused(): bool
    {
        return $this->paused;
    }

    public function getForced(): bool|null
    {
        return $this->forced;
    }

    public function forcedStatus(): string|null
    {
        if ($this->forced === null) {
            return ServerStatusSchema::FORCED_NONE;
        }

        return $this->forced
            ? ServerStatusSchema::FORCED_UP
            : ServerStatusSchema::FORCED_DOWN;
    }
}
