<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Server;

use Cdn77\NxgApi\Entity\Legacy\Server;
use Cdn77\NxgApi\Entity\Legacy\ServerCurrentStatus;
use Cdn77\NxgApi\Entity\Legacy\ServerLastDownStatus;
use Cdn77\NxgApi\Repository\Legacy\ServerCurrentStatusRepository;
use Cdn77\NxgApi\Repository\Legacy\ServerLastDownStatusRepository;
use DateTimeImmutable;
use Doctrine\DBAL\LockMode;
use Doctrine\ORM\EntityManagerInterface;
use Throwable;

class ServerStatusManager
{
    /** @var EntityManagerInterface */
    private $entityManager;

    /** @var ServerCurrentStatusRepository */
    private $serverCurrentStatusRepository;

    /** @var ServerLastDownStatusRepository */
    private $serverLastDownStatusRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        ServerCurrentStatusRepository $serverCurrentStatusRepository,
        ServerLastDownStatusRepository $serverLastDownStatusRepository,
    ) {
        $this->entityManager = $entityManager;
        $this->serverCurrentStatusRepository = $serverCurrentStatusRepository;
        $this->serverLastDownStatusRepository = $serverLastDownStatusRepository;
    }

    public function handleStatusChange(Server $server, ServerStatus $oldStatus, ServerStatus $newStatus): void
    {
        $this->entityManager->beginTransaction();
        try {
            $this->processServerStatusChange($server, $newStatus);
            $this->processLastDownUpdate($server, $oldStatus, $newStatus);

            $this->entityManager->flush();
            $this->entityManager->commit();
        } catch (Throwable $e) {
            $this->entityManager->rollback();

            throw $e;
        }
    }

    private function processServerStatusChange(Server $server, ServerStatus $newStatus): void
    {
        $when = new DateTimeImmutable();
        $up = $newStatus->isUp();
        $reason = $this->getCurrentStatusReason($newStatus);

        $current = $this->serverCurrentStatusRepository->find($server, LockMode::PESSIMISTIC_WRITE);
        if ($current === null) {
            $current = new ServerCurrentStatus($server, $when, $up, $reason);
            $this->entityManager->persist($current);

            return;
        }

        $current->update($when, $up, $reason);
    }

    private function processLastDownUpdate(Server $server, ServerStatus $oldStatus, ServerStatus $newStatus): void
    {
        if ($newStatus->isUp()) {
            return;
        }

        $reason = $this->getLastDownReason($newStatus);

        $lastDown = $this->serverLastDownStatusRepository->find($server, LockMode::PESSIMISTIC_WRITE);
        if ($lastDown === null) {
            $lastDown = new ServerLastDownStatus($server, new DateTimeImmutable(), $reason);
            $this->entityManager->persist($lastDown);

            return;
        }

        if ($this->hasServerJustWentDown($oldStatus, $newStatus)) {
            $lastDown->update(new DateTimeImmutable(), $reason);

            return;
        }

        $lastDown->update($lastDown->getWentDownAt(), $reason);
    }

    private function getCurrentStatusReason(ServerStatus $status): string
    {
        return $this->isServerForcedDown($status)
            ? ServerCurrentStatus::REASON_FORCED
            : ServerCurrentStatus::REASON_AUTO;
    }

    private function getLastDownReason(ServerStatus $status): string
    {
        return $this->isServerForcedDown($status)
            ? ServerLastDownStatus::REASON_FORCED
            : ServerLastDownStatus::REASON_AUTO;
    }

    private function isServerForcedDown(ServerStatus $status): bool
    {
        return $status->isPaused() || $status->getForced() !== null;
    }

    private function hasServerJustWentDown(ServerStatus $oldStatus, ServerStatus $newStatus): bool
    {
        return $oldStatus->isUp() && ! $newStatus->isUp();
    }
}
