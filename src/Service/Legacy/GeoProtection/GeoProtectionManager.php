<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\GeoProtection;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceGeoProtectionLocation;
use Cdn77\NxgApi\Repository\Legacy\ResourceGeoProtectionLocationRepository;
use Cdn77\NxgApi\Repository\Legacy\ResourceGeoProtectionRepository;
use Cdn77\NxgApi\Service\Event\ResourceEvent;
use Cdn77\NxgApi\Service\Legacy\GeoProtection\Exception\GeoProtectionAlreadyEnabled;
use Cdn77\NxgApi\Service\Legacy\GeoProtection\Exception\GeoProtectionNotEnabled;
use Doctrine\DBAL\LockMode;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

use function in_array;

class GeoProtectionManager
{
    private EntityManagerInterface $entityManager;

    private EventDispatcherInterface $eventDispatcher;

    private ResourceGeoProtectionRepository $protectionRepository;
    private ResourceGeoProtectionLocationRepository $geoProtectionLocationRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        EventDispatcherInterface $eventDispatcher,
        ResourceGeoProtectionRepository $protectionRepository,
        ResourceGeoProtectionLocationRepository $geoProtectionLocationRepository,
    ) {
        $this->entityManager = $entityManager;
        $this->eventDispatcher = $eventDispatcher;
        $this->protectionRepository = $protectionRepository;
        $this->geoProtectionLocationRepository = $geoProtectionLocationRepository;
    }

    /** @param string[] $countries */
    public function enable(CdnResource $resource, string $type, array $countries): ResourceGeoProtection
    {
        if ($this->findProtectionForUpdate($resource) !== null) {
            throw new GeoProtectionAlreadyEnabled();
        }

        $protection = new ResourceGeoProtection($resource, $type);
        $this->entityManager->persist($protection);

        foreach ($countries as $country) {
            $location = new ResourceGeoProtectionLocation($protection, $country);
            $this->entityManager->persist($location);

            $this->entityManager->persist($location);
            $protection->getLocations()->add($location);
        }

        $this->eventDispatcher->dispatch(new ResourceEvent($resource));

        return $protection;
    }

    public function disable(CdnResource $resource): void
    {
        $protection = $this->findProtectionForUpdate($resource);

        if ($protection === null) {
            throw new GeoProtectionNotEnabled();
        }

        foreach ($this->findAllProtectionLocations($protection) as $location) {
            $this->entityManager->remove($location);
        }

        $this->entityManager->remove($protection);

        $this->eventDispatcher->dispatch(new ResourceEvent($resource));
    }

    /** @param string[]|null $countries */
    public function modify(CdnResource $resource, string|null $type, array|null $countries): void
    {
        $protection = $this->findProtectionForUpdate($resource);

        if ($protection === null) {
            throw new GeoProtectionNotEnabled();
        }

        if ($type !== null) {
            $protection->setType($type);
        }

        if ($countries !== null) {
            foreach ($protection->getLocations() as $location) {
                if (in_array($location->getCountry(), $countries, true)) {
                    continue;
                }

                $protection->getLocations()->removeElement($location);
                $this->entityManager->remove($location);
            }

            foreach ($countries as $country) {
                foreach ($protection->getLocations() as $location) {
                    if ($location->getCountry() === $country) {
                        continue 2;
                    }
                }

                $location = new ResourceGeoProtectionLocation($protection, $country);

                $this->entityManager->persist($location);
                $protection->getLocations()->add($location);
            }
        }

        $this->eventDispatcher->dispatch(new ResourceEvent($resource));
    }

    public function get(CdnResource $resource): ResourceGeoProtection
    {
        $protection = $this->protectionRepository->findOneBy(['resource' => $resource]);

        if ($protection === null) {
            throw new GeoProtectionNotEnabled();
        }

        return $protection;
    }

    private function findProtectionForUpdate(CdnResource $resource): ResourceGeoProtection|null
    {
        return $this->protectionRepository->createQueryBuilder('p')
            ->where('p.resource = :resource')
            ->setParameter('resource', $resource)
            ->getQuery()
            ->setLockMode(LockMode::PESSIMISTIC_WRITE)
            ->getOneOrNullResult();
    }

    /** @return list<ResourceGeoProtectionLocation> */
    private function findAllProtectionLocations(ResourceGeoProtection $protection): array
    {
        return $this->geoProtectionLocationRepository->createQueryBuilder('l')
            ->where('l.geoProtection = :protection')
            ->setParameter('protection', $protection)
            ->getQuery()
            ->setLockMode(LockMode::PESSIMISTIC_WRITE)
            ->getResult();
    }
}
