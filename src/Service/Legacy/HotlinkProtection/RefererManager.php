<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\HotlinkProtection;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceRefererProtectionAddress;
use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Repository\Legacy\ResourceRefererProtectionRepository;
use Cdn77\NxgApi\Service\Event\ResourceEvent;
use Cdn77\NxgApi\Service\Legacy\HotlinkProtection\Exception\RefererAlreadyExists;
use Cdn77\NxgApi\Service\Legacy\HotlinkProtection\Exception\RefererNotFound;
use Doctrine\DBAL\LockMode;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Mapping\ClassMetadata;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

use function array_diff;
use function count;
use function in_array;

class RefererManager
{
    private EntityManagerInterface $entityManager;

    private ResourceRefererProtectionRepository $refererRepository;

    private EventDispatcherInterface $eventDispatcher;

    public function __construct(
        EntityManagerInterface $entityManager,
        EventDispatcherInterface $eventDispatcher,
        ResourceRefererProtectionRepository $refererRepository,
    ) {
        $this->entityManager = $entityManager;
        $this->eventDispatcher = $eventDispatcher;
        $this->refererRepository = $refererRepository;
    }

    /** @param string[]|null $domains */
    public function enable(
        CdnResource $resource,
        string $type,
        bool $allowEmpty,
        array|null $domains = null,
    ): ResourceRefererProtection {
        if ($this->findReferer($resource, false, false) !== null) {
            throw new RefererAlreadyExists('Referer already exists.');
        }

        $referer = new ResourceRefererProtection($resource, $type, $allowEmpty);
        $this->entityManager->persist($referer);

        if ($domains !== null && count($domains) > 0) {
            $this->createDomains($referer, $domains);
        }

        $this->eventDispatcher->dispatch(new ResourceEvent($resource));

        return $referer;
    }

    /** @param string[]|null $domains */
    public function update(
        CdnResource $resource,
        string|null $type = null,
        bool|null $allowEmpty = null,
        array|null $domains = null,
    ): void {
        if ($type === null && $allowEmpty === null && $domains === null) {
            throw new InvalidArgument('Nothing to modify.');
        }

        $referer = $this->doGetReferer($resource, true, true);

        if ($type !== null) {
            $referer->setType($type);
        }

        if ($allowEmpty !== null) {
            $referer->setDenyEmpty($allowEmpty);
        }

        if ($domains !== null) {
            $this->updateDomains($referer, $domains);
        }

        $this->eventDispatcher->dispatch(new ResourceEvent($resource));
    }

    public function disable(CdnResource $resource): void
    {
        $referer = $this->doGetReferer($resource, false, true);

        $this->removeAllDomains($referer);
        $this->entityManager->remove($referer);

        $this->eventDispatcher->dispatch(new ResourceEvent($resource));
    }

    public function getReferer(CdnResource $resource): ResourceRefererProtection
    {
        return $this->doGetReferer($resource, false, false);
    }

    /** @param string[] $domains */
    private function updateDomains(ResourceRefererProtection $referer, array $domains): void
    {
        $existingDomains = $referer->getAddresses()->map(
            static fn (ResourceRefererProtectionAddress $address): string => $address->getDomain(),
        )->toArray();

        $missingDomains = array_diff($existingDomains, $domains);
        $newDomains = array_diff($domains, $existingDomains);

        if (count($missingDomains) !== 0) {
            $this->removeDomains($referer, $missingDomains);
        }

        if (count($newDomains) === 0) {
            return;
        }

        $this->createDomains($referer, $newDomains);
    }

    /** @param string[] $domains */
    private function removeDomains(ResourceRefererProtection $referer, array $domains): void
    {
        foreach ($referer->getAddresses() as $address) {
            if (! in_array($address->getDomain(), $domains, true)) {
                continue;
            }

            $referer->removeAddress($address);
            $this->entityManager->remove($address);
        }
    }

    private function removeAllDomains(ResourceRefererProtection $referer): void
    {
        foreach ($referer->getAddresses() as $address) {
            $this->entityManager->remove($address);
        }
    }

    /** @param string[] $domains */
    private function createDomains(ResourceRefererProtection $referer, array $domains): void
    {
        foreach ($domains as $domain) {
            $address = new ResourceRefererProtectionAddress($referer, $domain);
            $referer->addAddress($address);
            $this->entityManager->persist($address);
        }
    }

    private function doGetReferer(
        CdnResource $resource,
        bool $loadAddresses = true,
        bool $lock = false,
    ): ResourceRefererProtection {
        $referer = $this->findReferer($resource, $loadAddresses, $lock);

        if ($referer === null) {
            throw new RefererNotFound('Referer not found.');
        }

        return $referer;
    }

    private function findReferer(
        CdnResource $resource,
        bool $loadAddresses = true,
        bool $lock = false,
    ): ResourceRefererProtection|null {
        $query = $this->refererRepository->createQueryBuilder('r')
            ->where('r.resource = :resource')
            ->setParameter('resource', $resource)
            ->getQuery();

        if ($lock) {
            $query->setLockMode(LockMode::PESSIMISTIC_WRITE);
        }

        if ($loadAddresses) {
            $query->setFetchMode(ResourceRefererProtection::class, 'addresses', ClassMetadata::FETCH_EAGER);
        }

        return $query->getOneOrNullResult();
    }
}
