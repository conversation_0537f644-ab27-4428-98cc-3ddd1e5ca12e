<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Delete;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Filter\Resource\DeletedResourceFilter;
use Cdn77\NxgApi\Service\Event\ResourceDeletedEvent;
use Cdn77\NxgApi\Service\Legacy\Delete\Exception\ResourceAlreadyDeleted;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Throwable;
use Webmozart\Assert\Assert;

class DoctrineResourceDeleter implements ResourceDeleter
{
    /** @var EntityManagerInterface */
    private $entityManager;

    /** @var EventDispatcherInterface */
    private $eventDispatcher;

    public function __construct(EntityManagerInterface $entityManager, EventDispatcherInterface $eventDispatcher)
    {
        $this->entityManager = $entityManager;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function delete(CdnResource $resource): void
    {
        if ($resource->isDeleted()) {
            throw new ResourceAlreadyDeleted();
        }

        $this->entityManager->beginTransaction();
        try {
            $resource->setDeleted(new DateTimeImmutable());
            $this->entityManager->flush();

            $this->eventDispatcher->dispatch(new ResourceDeletedEvent($resource));

            $this->entityManager->commit();
        } catch (Throwable $e) {
            $this->entityManager->rollback();

            throw $e;
        }
    }

    public function remove(int $resourceId): void
    {
        $this->entityManager->beginTransaction();
        try {
            $this->entityManager->getFilters()->disable(DeletedResourceFilter::NAME);

            $resource = $this->entityManager->getPartialReference(CdnResource::class, $resourceId);
            Assert::isInstanceOf($resource, CdnResource::class);

            // removed resource ID is added to table used_resource_id via postgres function remember_deleted_resource_id
            $this->entityManager->remove($resource);

            $this->entityManager->getFilters()->enable(DeletedResourceFilter::NAME);
            $this->entityManager->flush();
            $this->entityManager->commit();
        } catch (Throwable $e) {
            $this->entityManager->rollback();

            throw $e;
        }
    }
}
