<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Account;

use Cdn77\NxgApi\Entity\Legacy\Account;
use Cdn77\NxgApi\Repository\Legacy\AccountRepository;
use Cdn77\NxgApi\Service\Event\AccountEvent;
use Cdn77\NxgApi\Service\Legacy\Account\Exception\AccountAlreadyExists;
use Cdn77\NxgApi\Service\Legacy\Account\Exception\AccountNotFound;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Throwable;

class DoctrineAccountManager implements AccountManager
{
    /** @var AccountRepository */
    private $accountRepository;

    /** @var EntityManagerInterface */
    private $entityManager;

    /** @var EventDispatcherInterface */
    private $eventDispatcher;

    public function __construct(
        AccountRepository $accountRepository,
        EntityManagerInterface $entityManager,
        EventDispatcherInterface $eventDispatcher,
    ) {
        $this->accountRepository = $accountRepository;
        $this->entityManager = $entityManager;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function find(int $id): Account|null
    {
        return $this->accountRepository->find($id);
    }

    public function get(int $id): Account
    {
        $account = $this->find($id);

        if ($account === null) {
            throw new AccountNotFound();
        }

        return $account;
    }

    public function create(int $id): Account
    {
        $this->entityManager->beginTransaction();
        try {
            if ($this->find($id) !== null) {
                throw new AccountAlreadyExists();
            }

            $account = new Account($id);

            $this->entityManager->persist($account);
            $this->entityManager->flush();

            $this->eventDispatcher->dispatch(new AccountEvent($account));

            $this->entityManager->commit();

            return $account;
        } catch (Throwable $e) {
            $this->entityManager->rollback();

            throw $e;
        }
    }
}
