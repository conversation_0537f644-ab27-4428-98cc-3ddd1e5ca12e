<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Add;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Entity\Legacy\Account;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\IgnoredQueryParam;
use Cdn77\NxgApi\Entity\Legacy\LocationGroup;
use Cdn77\NxgApi\Entity\Legacy\ResourceCaching;
use Cdn77\NxgApi\Entity\Legacy\SecureToken;
use Cdn77\NxgApi\Resource\Domain\DTO\NewResource;
use Cdn77\NxgApi\Resource\Domain\Exception\ResourceInvalid;
use Cdn77\NxgApi\Resource\Domain\Repository\SecureTokenRepository;
use Cdn77\NxgApi\Resource\Domain\ResourceOriginCreator;
use Cdn77\NxgApi\Service\Event\ResourceCreatedEvent;
use Cdn77\NxgApi\Service\Legacy\Account\AccountManager;
use Cdn77\NxgApi\Service\Legacy\Edit\Exception\ResourceValidationFailed;
use Cdn77\NxgApi\Service\Legacy\Edit\ResourceChanger;
use Cdn77\NxgApi\Validator\Constraints\Resource\StreamingPlaylistBypassEnabled;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Throwable;
use Webmozart\Assert\Assert;

use function assert;
use function in_array;
use function Safe\preg_match;

class ResourceAdder
{
    public const PATTERN_DB_UNIQUE_CNAME = '~CNAME "(.*)" have to be unique in all undeleted resources~';

    private AccountManager $accountManager;

    private EntityManagerInterface $entityManager;

    private EventDispatcherInterface $eventDispatcher;

    private ValidatorInterface $validator;

    private SecureTokenRepository $secureTokenRepository;

    private ResourceOriginCreator $resourceOriginCreator;

    public function __construct(
        AccountManager $accountManager,
        EntityManagerInterface $entityManager,
        EventDispatcherInterface $eventDispatcher,
        ValidatorInterface $validator,
        SecureTokenRepository $secureTokenRepository,
        ResourceOriginCreator $resourceOriginCreator,
    ) {
        $this->accountManager = $accountManager;
        $this->entityManager = $entityManager;
        $this->eventDispatcher = $eventDispatcher;
        $this->validator = $validator;
        $this->secureTokenRepository = $secureTokenRepository;
        $this->resourceOriginCreator = $resourceOriginCreator;
    }

    public function add(NewResource $newResource): CdnResource
    {
        $this->entityManager->beginTransaction();

        try {
            $resource = $this->setNewResourceEntity($newResource);

            $account = $this->getOrCreateAccount($newResource);
            $resource->setAccount($account);
            $account->getResources()->add($resource);

            $this->entityManager->persist($resource);

            $this->createSecureToken($resource, $newResource);
            $this->resourceOriginCreator->createFromNewResource($resource, $newResource);

            $this->validateResource($resource);

            $this->entityManager->flush();

            $this->eventDispatcher->dispatch(new ResourceCreatedEvent($resource));

            $this->entityManager->commit();

            return $resource;
        } catch (Throwable $e) {
            $this->entityManager->rollback();

            $this->checkUniqueCnameTriggerMessage($e);

            throw $e;
        }
    }

    private function setNewResourceEntity(NewResource $newResource): CdnResource
    {
        $now = new DateTimeImmutable();
        $resource = CdnResource::fromNewResource($now, $newResource);

        $resource->setCnames($newResource->cnames);

        $group = $this->entityManager->getRepository(LocationGroup::class)->find($newResource->groupId);
        assert($group instanceof LocationGroup);
        $resource->setGroup($group);

        $resource->setCaching($resource->getCaching()->withExpiry($newResource->cacheExpiry));
        $resource->setCaching($resource->getCaching()->withExpiry404($newResource->cacheExpiry404));

        if (in_array($newResource->groupId, ResourceCaching::LS_RESOURCE_CACHING_GROUPS, true)) {
            $resourceCaching = $resource->getCaching();

            $resourceCaching->setLivestreamCacheAgeAndCacheLockout();
            $resource->setCaching($resourceCaching);
        }

        if ($newResource->ignoredQueryParams !== null) {
            foreach ($newResource->ignoredQueryParams as $name) {
                $resource->getIgnoredQueryParams()->add(new IgnoredQueryParam($resource, $name));
            }
        }

        if ($newResource->customData !== null) {
            $resource->setCustomData($newResource->customData);
        }

        return $resource;
    }

    private function createSecureToken(CdnResource $resource, NewResource $newResource): void
    {
        if (
            $newResource->secureTokenSchema === null
            || $newResource->secureTokenSchema->secureTokenType === ResourceChanger::DISABLE_SECURE_TOKEN_TYPE_STRING
        ) {
            return;
        }

        Assert::string($newResource->secureTokenSchema->secureTokenType);
        Assert::string($newResource->secureTokenSchema->secureTokenValue);

        $secureToken = new SecureToken(
            $resource,
            $newResource->secureTokenSchema->secureTokenType,
            $newResource->secureTokenSchema->secureTokenValue,
            ValueReplacer::emptyStringToNull($newResource->secureTokenSchema->secureLinkExpiryParam),
            ValueReplacer::emptyStringToNull($newResource->secureTokenSchema->secureLinkTokenParam),
            ValueReplacer::emptyStringToNull($newResource->secureTokenSchema->secureLinkPathlenParam),
            ValueReplacer::emptyStringToNull($newResource->secureTokenSchema->secureLinkSecretParam),
            $newResource->secureTokenSchema->secureLinkRewritePlaylist,
        );

        $this->secureTokenRepository->add($secureToken);

        $resource->setResourceSecureToken($secureToken);
    }

    private function getOrCreateAccount(NewResource $newResource): Account
    {
        $accountId = $newResource->accountId;

        $account = $this->accountManager->find($accountId);

        if ($account !== null) {
            return $account;
        }

        return $this->accountManager->create($accountId);
    }

    private function validateResource(CdnResource $resource): void
    {
        $violations = $this->validator->validate(
            $resource,
            [new StreamingPlaylistBypassEnabled()],
        );

        if ($violations->count() === 0) {
            return;
        }

        throw new ResourceValidationFailed($violations);
    }

    private function checkUniqueCnameTriggerMessage(Throwable $e): void
    {
        if (preg_match(self::PATTERN_DB_UNIQUE_CNAME, $e->getMessage(), $matches) === 1) {
            throw ResourceInvalid::cnameNotUnique($matches[1]);
        }
    }
}
