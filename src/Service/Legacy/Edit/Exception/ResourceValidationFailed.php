<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Edit\Exception;

use Cdn77\NxgApi\Exception\Runtime;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;

class ResourceValidationFailed extends Runtime implements Exception
{
    /** @var ConstraintViolationListInterface<ConstraintViolationInterface> */
    private $violations;

    /** @param ConstraintViolationListInterface<ConstraintViolationInterface> $violations */
    public function __construct(ConstraintViolationListInterface $violations)
    {
        parent::__construct();

        $this->violations = $violations;
    }

    /** @return ConstraintViolationListInterface<ConstraintViolationInterface> */
    public function getViolations(): ConstraintViolationListInterface
    {
        return $this->violations;
    }
}
