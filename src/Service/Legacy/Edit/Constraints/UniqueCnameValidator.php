<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Edit\Constraints;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function count;

class UniqueCnameValidator extends ConstraintValidator
{
    /** @var CdnResourceRepository */
    protected $resourceRepository;

    public function __construct(CdnResourceRepository $resourceRepository)
    {
        $this->resourceRepository = $resourceRepository;
    }

    /**
     * @param mixed $value
     *
     * @phpcsSuppress SlevomatCodingStandard.TypeHints.TypeHintDeclaration.MissingParameterTypeHint
     */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof UniqueCname) {
            throw new UnexpectedTypeException($constraint, UniqueCname::class);
        }

        if (! $value instanceof CdnResource) {
            throw new UnexpectedTypeException($value, CdnResource::class);
        }

        $cnames = $value->getCnames();
        if (count($cnames) === 0) {
            return;
        }

        $existingCnames = $this->resourceRepository->findUsedCnamesExcludingResource($cnames, $value);

        foreach ($existingCnames as $cname) {
            $this->context->buildViolation($constraint->message)
                ->setParameter('%string%', $cname)
                ->addViolation();
        }
    }
}
