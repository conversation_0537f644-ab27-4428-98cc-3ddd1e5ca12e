<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Certificate;

use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Resource\Domain\Enum\CertificateStatus;
use DateTimeImmutable;

class CertificateRequestedStatus
{
    public string $type;

    public CertificateStatus $status;

    /** @var array<string>|null */
    public array|null $domains;

    public DateTimeImmutable|null $requestedAt;

    public DateTimeImmutable|null $lastChangeAt;

    public string|null $validationError;

    public string|null $generatingError;

    /** @param array<string> $domains */
    public function __construct(
        CertificateStatus $status,
        string $type,
        array|null $domains = null,
        DateTimeImmutable|null $requestedAt = null,
        DateTimeImmutable|null $lastChangeAt = null,
        string|null $validationError = null,
        string|null $generatingError = null,
    ) {
        if (! $status->isForInstantSsl()) {
            throw new InvalidArgument('Invalid requested certificate status given.');
        }

        $this->type = $type;
        $this->status = $status;
        $this->domains = $domains;
        $this->requestedAt = $requestedAt;
        $this->lastChangeAt = $lastChangeAt;
        $this->validationError = $validationError;
        $this->generatingError = $generatingError;
    }
}
