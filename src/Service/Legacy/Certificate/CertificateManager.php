<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Certificate;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt;
use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Repository\Legacy\SslRepository;
use Cdn77\NxgApi\Resource\Domain\Enum\CertificateStatus;
use Cdn77\NxgApi\Resource\Domain\SslFileFactory;
use Cdn77\NxgApi\Service\Event\ResourceEvent;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\CertificateBucket;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\Exception\FileCorrupted;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\Exception\FileWriteFailed;
use Cdn77\NxgApi\Service\Legacy\Certificate\Exception\CertificateDoesNotExist;
use Cdn77\NxgApi\Service\LetsEncrypt\RequestManager;
use DateTimeImmutable;
use Doctrine\DBAL\LockMode;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Throwable;

use function in_array;

class CertificateManager
{
    private EntityManagerInterface $entityManager;

    private EventDispatcherInterface $eventDispatcher;

    private SslRepository $sslRepository;

    private CertificateBucket $certificateBucket;

    private CertificatePairValidator $certificatePairValidator;

    private CertificateMetadataParser $certificateMetadataParser;

    private SslFileFactory $sslFileFactory;

    private RequestManager $requestManager;

    public function __construct(
        EntityManagerInterface $entityManager,
        EventDispatcherInterface $eventDispatcher,
        SslRepository $sslRepository,
        CertificateBucket $certificateBucket,
        CertificatePairValidator $certificatePairValidator,
        CertificateMetadataParser $certificateMetadataParser,
        SslFileFactory $sslFileFactory,
        RequestManager $requestManager,
    ) {
        $this->entityManager = $entityManager;
        $this->eventDispatcher = $eventDispatcher;
        $this->sslRepository = $sslRepository;
        $this->certificateBucket = $certificateBucket;
        $this->certificatePairValidator = $certificatePairValidator;
        $this->certificateMetadataParser = $certificateMetadataParser;
        $this->sslFileFactory = $sslFileFactory;
        $this->requestManager = $requestManager;
    }

    public function setCertificate(
        CdnResource $resource,
        string $type,
        LetsEncrypt\CertificatePair $certificatePair,
    ): SslFile {
        $this->certificatePairValidator->validate($certificatePair);

        try {
            $ssl = $this->getOrCreateSslForResource($resource);
            $certificateMetadata = $this->certificateMetadataParser->getMetadata($certificatePair);
            $file = $this->sslFileFactory->createForSsl($certificateMetadata, $ssl, $type);
            $resource->setInstantSsl($type === SslFile::TYPE_LETSENCRYPT);

            $ssl->getFiles()->add($file);

            $ssl->setAssignedAt(new DateTimeImmutable());
            $ssl->setAssignedIndex($file->getIndex());

            $this->certificateBucket->save(
                $file,
                $certificatePair,
            );
        } catch (FileCorrupted | FileWriteFailed $e) {
            $this->certificateBucket->delete($file);

            throw $e;
        }

        try {
            $this->eventDispatcher->dispatch(new ResourceEvent($resource));
        } catch (Throwable) {
            // This can fail and but it shouldn't affect the response for set certificate request
        }

        return $file;
    }

    public function deleteCertificate(CdnResource $resource, bool $enableInstantSsl = false): void
    {
        $this->entityManager->beginTransaction();

        try {
            $ssl = $this->sslRepository->find($resource, LockMode::PESSIMISTIC_WRITE);
            if ($ssl === null) {
                $this->entityManager->commit();

                throw new CertificateDoesNotExist();
            }

            if ($enableInstantSsl) {
                $this->tmpFixEnableInstantSsl($ssl->getResource());
            }

            $this->entityManager->remove($ssl);
            $this->entityManager->flush();

            $this->eventDispatcher->dispatch(new ResourceEvent($resource));

            $this->entityManager->commit();
        } catch (CertificateDoesNotExist $e) {
            // expected exception
            throw $e;
        } catch (Throwable $e) {
            $this->entityManager->rollback();

            throw $e;
        }
    }

    public function findActualStatus(CdnResource $resource): CertificateActualStatus|null
    {
        $ssl = $this->sslRepository->find($resource);

        if ($ssl === null || $ssl->getFiles()->isEmpty()) {
            return null;
        }

        return new CertificateActualStatus(
            $ssl->getAssignedAt(),
            $ssl->getAssignedSslFile(),
        );
    }

    public function findRequestedStatus(CdnResource $resource): CertificateRequestedStatus|null
    {
        if (! $resource->hasInstantSsl()) {
            return null;
        }

        $request = $this->findLatestInstantSslRequest($resource);

        if ($request === null) {
            return new CertificateRequestedStatus(
                CertificateStatus::instantSslRequestMissing(),
                SslFile::TYPE_LETSENCRYPT,
            );
        }

        if ($request->getState()->isPending()) {
            $result = $this->findLatestInstantSslResult($request);

            return $this->createCertificateRequestedStatus($request, $result);
        }

        if ($request->getState()->isCancelled()) {
            return new CertificateRequestedStatus(
                CertificateStatus::instantSslRequestCanceled(),
                SslFile::TYPE_LETSENCRYPT,
            );
        }

        return null;
    }

    /** @return SslFile[] */
    public function getCertificates(
        bool $active = true,
        bool $includeSuspended = false,
        string|null $type = null,
        DateTimeImmutable|null $expirationBefore = null,
    ): array {
        $qb = $this->entityManager->createQueryBuilder()
            ->from(SslFile::class, 'f')
            ->select('f')
            ->innerJoin('f.ssl', 's')
            ->addSelect('s');

        $qb->innerJoin('s.resource', 'r')
            ->where('r.deleted IS NULL');

        if ($active) {
            $qb->andWhere('f.index = s.assignedIndex');
        }

        if ($type !== null) {
            $this->validateFileType($type);
            $qb->andWhere('f.type = :type')
                ->setParameter('type', $type);
        }

        if (! $includeSuspended) {
            $qb->andWhere('r.suspended IS NULL');
        }

        if ($expirationBefore !== null) {
            $qb->andWhere('f.expiresAt <= :before')
                ->setParameter('before', $expirationBefore, Types::DATETIMETZ_IMMUTABLE);
        }

        return $qb->orderBy('f.expiresAt', 'ASC')->getQuery()->getResult();
    }

    private function createCertificateRequestedStatus(
        LetsEncrypt\Request $request,
        LetsEncrypt\Result|null $result,
    ): CertificateRequestedStatus {
        $status = CertificateStatus::instantSslGenerating();
        $validationError = null;
        $generatingError = null;

        if ($result !== null) {
            $status = CertificateStatus::instantSslRetrying();
            $validationError = $result->getStatus()->isValidationError() ? $result->getDescription() : null;
            $generatingError = $result->getStatus()->isError() ? $result->getDescription() : null;
        }

        return new CertificateRequestedStatus(
            $status,
            SslFile::TYPE_LETSENCRYPT,
            $request->getDomains(),
            $request->getCreatedAt(),
            $request->getUpdatedAt(),
            $validationError,
            $generatingError,
        );
    }

    private function tmpFixEnableInstantSsl(CdnResource $resource): void
    {
        $resource->setInstantSsl(true);
        $this->entityManager->persist($resource);

        $this->requestManager->createAndScheduleRequest($resource, new DateTimeImmutable());
    }

    private function findLatestInstantSslRequest(CdnResource $resource): LetsEncrypt\Request|null
    {
        return $this->entityManager->createQueryBuilder()
            ->from(LetsEncrypt\Request::class, 'r')
            ->select('r')
            ->where('r.resource = :resource')
            ->setParameter('resource', $resource)
            ->orderBy('r.updatedAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    private function findLatestInstantSslResult(LetsEncrypt\Request $request): LetsEncrypt\Result|null
    {
        return $this->entityManager->createQueryBuilder()
            ->from(LetsEncrypt\Result::class, 'r')
            ->select('r')
            ->where('r.request = :request')
            ->setParameter('request', $request)
            ->orderBy('r.completedAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    private function getOrCreateSslForResource(CdnResource $resource): Ssl
    {
        $ssl = $this->sslRepository->find($resource, LockMode::PESSIMISTIC_WRITE);
        if ($ssl !== null) {
            return $ssl;
        }

        $ssl = new Ssl();
        $ssl->setResource($resource);
        $this->entityManager->persist($ssl);
        $this->entityManager->flush();

        return $ssl;
    }

    private function validateFileType(string $type): void
    {
        if (! in_array($type, [SslFile::TYPE_CUSTOM, SslFile::TYPE_LETSENCRYPT], true)) {
            throw new InvalidArgument('Invalid SslFile type.');
        }
    }
}
