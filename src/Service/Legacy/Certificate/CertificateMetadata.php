<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Certificate;

use DateTimeImmutable;

class CertificateMetadata
{
    /** @var string */
    private $serial;

    /** @var DateTimeImmutable */
    private $validSince;

    /** @var DateTimeImmutable */
    private $validUntil;

    /** @var string|null */
    private $commonName;

    /** @var string[]|null */
    private $alternativeNames;

    /** @param string[]|null $alternativeNames */
    public function __construct(
        string $serial,
        DateTimeImmutable $validSince,
        DateTimeImmutable $validUntil,
        string|null $commonName,
        array|null $alternativeNames,
    ) {
        $this->serial = $serial;
        $this->validSince = $validSince;
        $this->validUntil = $validUntil;
        $this->commonName = $commonName;
        $this->alternativeNames = $alternativeNames;
    }

    public function getSerial(): string
    {
        return $this->serial;
    }

    public function getValidSince(): DateTimeImmutable
    {
        return $this->validSince;
    }

    public function getValidUntil(): DateTimeImmutable
    {
        return $this->validUntil;
    }

    public function getCommonName(): string|null
    {
        return $this->commonName;
    }

    /** @return string[] */
    public function getAlternativeNames(): array
    {
        return $this->alternativeNames ?? [];
    }
}
