<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Certificate;

use Cdn77\NxgApi\Entity\LetsEncrypt;
use Cdn77\NxgApi\Service\Legacy\Certificate\Exception\InvalidCertificatePair;
use Cdn77\NxgApi\Validator\Constraints\Certificate\Certificate;
use Cdn77\NxgApi\Validator\Constraints\Certificate\CertificateChain;
use Cdn77\NxgApi\Validator\Constraints\Certificate\CertificatePair;
use Cdn77\NxgApi\Validator\Constraints\Certificate\PrivateKey;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class CertificatePairValidator
{
    private ValidatorInterface $innerValidator;

    public function __construct(ValidatorInterface $innerValidator)
    {
        $this->innerValidator = $innerValidator;
    }

    public function validate(LetsEncrypt\CertificatePair $certificatePair): void
    {
        $violations = new ConstraintViolationList();

        $violations1 = $this->innerValidator->validate($certificatePair->getCertificate(), [new Certificate()]);
        $violations2 = $this->innerValidator->validate($certificatePair->getPrivateKey(), [new PrivateKey()]);
        $violations3 = $this->innerValidator->validate($certificatePair, [new CertificatePair()]);
        $violations4 = $this->innerValidator->validate($certificatePair->getCertificate(), [new CertificateChain()]);

        $violations->addAll($violations1);
        $violations->addAll($violations2);
        $violations->addAll($violations3);
        $violations->addAll($violations4);

        if ($violations->count() === 0) {
            return;
        }

        throw new InvalidCertificatePair($violations);
    }
}
