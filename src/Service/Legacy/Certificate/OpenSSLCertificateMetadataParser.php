<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Certificate;

use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Exception\Runtime;
use DateTimeImmutable;
use DateTimeZone;

use function openssl_x509_parse;
use function Safe\preg_match;
use function Safe\preg_split;

/**
 * OpenSSL-based parser.
 */
class OpenSSLCertificateMetadataParser implements CertificateMetadataParser
{
    public function getMetadata(CertificatePair $certificatePair): CertificateMetadata
    {
        $certificate = $this->parseCertificate($certificatePair);

        return new CertificateMetadata(
            $this->parseSerial($certificate),
            $this->parseValidSince($certificate),
            $this->parseValidUntil($certificate),
            $this->parseCommonName($certificate),
            $this->parseAlternativeNames($certificate),
        );
    }

    /** @return mixed[] */
    private function parseCertificate(CertificatePair $certificatePair): array
    {
        $certificate = @openssl_x509_parse($certificatePair->getCertificate(), false);

        if ($certificate === false) {
            throw new Runtime('Malformed certificate data.'); // TODO InvalidCertificateException
        }

        return $certificate;
    }

    /** @param mixed[] $certificate */
    private function parseSerial(array $certificate): string
    {
        return $certificate['serialNumber'];
    }

    /** @param mixed[] $certificate */
    private function parseValidSince(array $certificate): DateTimeImmutable
    {
        return $this->parseValidityField($certificate['validFrom_time_t']);
    }

    /** @param mixed[] $certificate */
    private function parseValidUntil(array $certificate): DateTimeImmutable
    {
        return $this->parseValidityField($certificate['validTo_time_t']);
    }

    private function parseValidityField(int $validity): DateTimeImmutable
    {
        return new DateTimeImmutable('@' . $validity, new DateTimeZone('UTC'));
    }

    /** @param mixed[] $certificate */
    private function parseCommonName(array $certificate): string|null
    {
        return $certificate['subject']['commonName'] ?? null;
    }

    /**
     * @param mixed[] $certificate
     *
     * @return string[]
     */
    private function parseAlternativeNames(array $certificate): array
    {
        $subjectAltName = $certificate['extensions']['subjectAltName'] ?? null;

        if (! $subjectAltName) {
            return [];
        }

        $candidates = preg_split('~,\s*~', $subjectAltName);

        $names = [];
        foreach ($candidates as $candidate) {
            if (preg_match('~^DNS:(.+)$~', $candidate, $match) !== 1) {
                continue;
            }

            $names[] = $match[1];
        }

        return $names;
    }
}
