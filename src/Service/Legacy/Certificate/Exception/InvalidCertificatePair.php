<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Certificate\Exception;

use Cdn77\NxgApi\Exception\Runtime;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;

use function count;
use function sprintf;

class InvalidCertificatePair extends Runtime
{
    /** @var ConstraintViolationListInterface<ConstraintViolationInterface> */
    private $violations;

    /** @param ConstraintViolationListInterface<ConstraintViolationInterface> $violations */
    public function __construct(ConstraintViolationListInterface $violations)
    {
        $this->violations = $violations;

        parent::__construct(sprintf('Validation failed with %d error(s).', count($violations)));
    }

    /** @return ConstraintViolationListInterface<ConstraintViolationInterface> */
    public function getViolations(): ConstraintViolationListInterface
    {
        return $this->violations;
    }
}
