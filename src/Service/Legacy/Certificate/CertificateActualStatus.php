<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Certificate;

use Cdn77\NxgApi\Entity\Legacy\SslFile;
use DateTimeImmutable;

class CertificateActualStatus
{
    public DateTimeImmutable $lastChangeAt;

    public SslFile $activeFile;

    public function __construct(DateTimeImmutable $lastChangeAt, SslFile $activeFile)
    {
        $this->lastChangeAt = $lastChangeAt;
        $this->activeFile = $activeFile;
    }
}
