<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Locations;

use Cdn77\NxgApi\DTO\Legacy\ResourceLocationDTO;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\CustomLocation;
use Cdn77\NxgApi\Entity\Legacy\Location;
use Cdn77\NxgApi\Repository\Legacy\CustomLocationRepository;
use Cdn77\NxgApi\Repository\Legacy\LocationRepository;
use Cdn77\NxgApi\Service\Event\ResourceEvent;
use Cdn77\NxgApi\Service\Legacy\Locations\Exception\NoCustomLocationExists;
use Cdn77\NxgApi\Service\Legacy\Locations\Exception\NotEnoughActiveLocations;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\ResultSetMapping;
use Doctrine\ORM\Query\ResultSetMappingBuilder;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Throwable;

use function array_filter;
use function assert;
use function count;
use function sprintf;

class ResourceLocationsChanger
{
    /** @var EntityManagerInterface */
    private $entityManager;

    /** @var EventDispatcherInterface */
    private $eventDispatcher;

    /** @var LocationRepository */
    private $locationRepository;

    /** @var CustomLocationRepository */
    private $customLocationRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        EventDispatcherInterface $eventDispatcher,
        LocationRepository $locationRepository,
        CustomLocationRepository $customLocationRepository,
    ) {
        $this->entityManager = $entityManager;
        $this->eventDispatcher = $eventDispatcher;
        $this->locationRepository = $locationRepository;
        $this->customLocationRepository = $customLocationRepository;
    }

    public function resetCustomLocations(CdnResource $resource): void
    {
        $hasCustomLocations = $this->customLocationRepository->hasResourceCustomLocation($resource);

        if (! $hasCustomLocations) {
            throw new NoCustomLocationExists('Resource has no custom location set.');
        }

        $this->entityManager->beginTransaction();
        try {
            $this->customLocationRepository
                ->createQueryBuilder('c')
                ->delete()
                ->where('c.resource = :resource')
                ->setParameter('resource', $resource)
                ->getQuery()
                ->execute();

            $this->eventDispatcher->dispatch(new ResourceEvent($resource));

            $this->entityManager->commit();
        } catch (Throwable $e) {
            $this->entityManager->rollback();

            throw $e;
        }
    }

    public function createCustomLocation(CdnResource $resource): CustomLocation
    {
        $customLocation = $this->createNewCustomLocationEntity($resource);

        $this->entityManager->beginTransaction();
        try {
            $this->entityManager->persist($customLocation);

            $this->entityManager->flush();

            $this->eventDispatcher->dispatch(new ResourceEvent($resource));

            $this->entityManager->commit();

            return $customLocation;
        } catch (Throwable $e) {
            $this->entityManager->rollback();

            throw $e;
        }
    }

    /** @param Location[] $locations */
    public function setCustomLocations(CdnResource $resource, array $locations, int $minimumActiveLocations = 0): void
    {
        assert($minimumActiveLocations >= 0);

        $this->entityManager->beginTransaction();

        try {
            $new = false;
            $customLocation = $this->customLocationRepository->findOneBy(['resource' => $resource]);
            if ($customLocation === null) {
                $customLocation = $this->createNewCustomLocationEntity($resource);
                $this->entityManager->persist($customLocation);
                $this->entityManager->flush();
                $new = true;
            }

            $possiblePops = $this->getPossiblePops($resource);
            $currentPops = $this->getCurrentPops($resource);

            $allPossiblePops = $currentPops + $possiblePops;

            $locationRequested = static function (string $id) use ($locations): bool {
                foreach ($locations as $location) {
                    if ($location->getId() === $id) {
                        return true;
                    }
                }

                return false;
            };

            foreach ($allPossiblePops as $popId => $popInfo) {
                if ($popInfo['isActive'] !== true || $locationRequested($popInfo['locationId'])) {
                    continue;
                }

                $deleteQuery = $this->entityManager->createNativeQuery(
                    '
                        DELETE FROM custom_location_relation
                        WHERE resource_id = :resource
                            AND pop_id = :pop
                    ',
                    new ResultSetMapping(),
                );
                $deleteQuery->setParameter('resource', $resource->getId());
                $deleteQuery->setParameter('pop', $popInfo['popId']);
                $deleteQuery->execute();
            }

            foreach ($allPossiblePops as $popId => $popInfo) {
                if ($popInfo['isActive'] !== false || ! $locationRequested($popInfo['locationId'])) {
                    continue;
                }

                $insertQuery = $this->entityManager->createNativeQuery(
                    '
                        INSERT INTO custom_location_relation (pop_id, resource_id)
                        VALUES (:pop, :resource)
                    ',
                    new ResultSetMapping(),
                );
                $insertQuery->setParameter('resource', $resource->getId());
                $insertQuery->setParameter('pop', $popInfo['popId']);
                $insertQuery->execute();
            }

            // TODO dirty - should be count sql query
            $updatedCustomLocations = $this->locationRepository->findCustomLocationsForResource($resource);
            $updatedCustomLocationsCount = count(
                array_filter(
                    $updatedCustomLocations,
                    static fn (
                        ResourceLocationDTO $locationDTO,
                    ): bool => $locationDTO->isActive() && $locationDTO->isAssigned(),
                ),
            );

            if ($updatedCustomLocationsCount < $minimumActiveLocations) {
                throw new NotEnoughActiveLocations(
                    $updatedCustomLocationsCount,
                    sprintf('Not enough active locations to meet %d requirement.', $minimumActiveLocations),
                );
            }

            $this->eventDispatcher->dispatch(new ResourceEvent($resource));

            $this->entityManager->commit();
        } catch (Throwable $e) {
            $this->entityManager->rollback();

            throw $e;
        }
    }

    /** @return mixed[][] */
    public function getCurrentPops(CdnResource $resource): array
    {
        $sql = <<<'SQL'
            SELECT p.id,
                l.id location_id,
                TRUE AS active
            FROM pop p
            JOIN custom_location_relation clr
                ON clr.pop_id = p.id
            JOIN location l
                ON l.id = p.location_id
            JOIN custom_location cl
                ON cl.resource_id = clr.resource_id
            JOIN resource r
                ON r.id = cl.resource_id
            WHERE r.id = :resource
SQL;

        $rsm = new ResultSetMappingBuilder($this->entityManager);
        $rsm->addScalarResult('id', 'popId', 'integer');
        $rsm->addScalarResult('location_id', 'locationId', 'string');
        $rsm->addScalarResult('active', 'isActive', 'boolean');

        $query = $this->entityManager->createNativeQuery($sql, $rsm);
        $query->setParameter('resource', $resource->getId());

        return $this->reindexResultBy($query->getArrayResult(), 'popId');
    }

    /**
     * @param mixed[]    $result
     * @param int|string $key
     *
     * @return mixed[]
     */
    public function reindexResultBy(array $result, $key): array
    {
        $indexedResult = [];
        foreach ($result as $item) {
            $indexedResult[$item[$key]] = $item;
        }

        return $indexedResult;
    }

    private function createNewCustomLocationEntity(CdnResource $resource): CustomLocation
    {
        $customLocation = new CustomLocation();
        $customLocation->setResource($resource);
        $customLocation->setGroup($resource->getGroup());

        return $customLocation;
    }

    /** @return mixed[][] */
    private function getPossiblePops(CdnResource $resource): array
    {
        $sql = <<<'SQL'
            SELECT p.id,
                l.id location_id,
                FALSE active
            FROM pop p
            JOIN group_pop_relation gpr
                ON gpr.pop_id = p.id
            JOIN location_group g
                ON g.id = gpr.group_id
            JOIN location l
                ON l.id = p.location_id
            WHERE g.id = (
                SELECT group_id
                FROM resource
                WHERE id = :resource
            )
SQL;

        $rsm = new ResultSetMappingBuilder($this->entityManager);
        $rsm->addScalarResult('id', 'popId', 'integer');
        $rsm->addScalarResult('location_id', 'locationId', 'string');
        $rsm->addScalarResult('active', 'isActive', 'boolean');

        $query = $this->entityManager->createNativeQuery($sql, $rsm);
        $query->setParameter('resource', $resource->getId());

        return $this->reindexResultBy($query->getArrayResult(), 'popId');
    }
}
