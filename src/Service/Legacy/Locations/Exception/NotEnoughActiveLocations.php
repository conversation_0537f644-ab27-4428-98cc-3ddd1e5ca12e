<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Locations\Exception;

use Cdn77\NxgApi\Exception\Runtime;
use Throwable;

class NotEnoughActiveLocations extends Runtime implements Exception
{
    /** @var int */
    private $count;

    public function __construct(int $count, string $message, int $code = 0, Throwable|null $previous = null)
    {
        $this->count = $count;

        parent::__construct($message, $code, $previous);
    }

    public function getCount(): int
    {
        return $this->count;
    }
}
