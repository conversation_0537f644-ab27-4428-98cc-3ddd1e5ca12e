<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\IpProtection;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceIpProtection;
use Cdn77\NxgApi\Entity\Legacy\ResourceIpProtectionAddress;
use Cdn77\NxgApi\Repository\Legacy\ResourceIpProtectionRepository;
use Cdn77\NxgApi\Service\Event\ResourceEvent;
use Cdn77\NxgApi\Service\Legacy\IpProtection\Exception\IpProtectionAlreadyEnabled;
use Cdn77\NxgApi\Service\Legacy\IpProtection\Exception\IpProtectionNotEnabled;
use Doctrine\DBAL\LockMode;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

use function in_array;

class IpProtectionManager
{
    private EntityManagerInterface $entityManager;

    private EventDispatcherInterface $eventDispatcher;

    private ResourceIpProtectionRepository $protectionRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        EventDispatcherInterface $eventDispatcher,
        ResourceIpProtectionRepository $protectionRepository,
    ) {
        $this->entityManager = $entityManager;
        $this->eventDispatcher = $eventDispatcher;
        $this->protectionRepository = $protectionRepository;
    }

    /** @param string[] $networks */
    public function enable(CdnResource $resource, string $type, array $networks): ResourceIpProtection
    {
        if ($this->findProtectionForUpdate($resource) !== null) {
            throw new IpProtectionAlreadyEnabled();
        }

        $protection = new ResourceIpProtection($resource, $type);
        $this->entityManager->persist($protection);

        foreach ($networks as $network) {
            $address = new ResourceIpProtectionAddress($protection, $network);

            $this->entityManager->persist($address);
            $protection->getAddresses()->add($address);
        }

        $this->eventDispatcher->dispatch(new ResourceEvent($resource));

        return $protection;
    }

    public function disable(CdnResource $resource): void
    {
        $protection = $this->findProtectionForUpdate($resource);

        if ($protection === null) {
            throw new IpProtectionNotEnabled();
        }

        $this->removeAllAddresses($protection);

        $this->entityManager->remove($protection);

        $this->eventDispatcher->dispatch(new ResourceEvent($resource));
    }

    /** @param string[]|null $networks */
    public function modify(CdnResource $resource, string|null $type, array|null $networks): void
    {
        $protection = $this->findProtectionForUpdate($resource);

        if ($protection === null) {
            throw new IpProtectionNotEnabled();
        }

        if ($type !== null) {
            $protection->setType($type);
        }

        if ($networks !== null) {
            $this->removeMissingAddresses($protection, $networks);
            $this->addNewAddresses($protection, $networks);
        }

        $this->eventDispatcher->dispatch(new ResourceEvent($resource));
    }

    public function get(CdnResource $resource): ResourceIpProtection
    {
        $protection = $this->protectionRepository->findOneBy(['resource' => $resource]);
        if ($protection === null) {
            throw new IpProtectionNotEnabled();
        }

        return $protection;
    }

    private function removeAllAddresses(ResourceIpProtection $protection): void
    {
        foreach ($protection->getAddresses() as $address) {
            $this->entityManager->remove($address);
        }
    }

    /** @param string[] $networks */
    private function removeMissingAddresses(ResourceIpProtection $protection, array $networks): void
    {
        foreach ($protection->getAddresses() as $address) {
            if (in_array($address->getAddress(), $networks, true)) {
                continue;
            }

            $protection->getAddresses()->removeElement($address);
            $this->entityManager->remove($address);
        }
    }

    /** @param string[] $networks */
    private function addNewAddresses(ResourceIpProtection $protection, array $networks): void
    {
        foreach ($networks as $network) {
            foreach ($protection->getAddresses() as $address) {
                if ($address->getAddress() === $network) {
                    continue 2;
                }
            }

            $address = new ResourceIpProtectionAddress($protection, $network);

            $this->entityManager->persist($address);
            $protection->getAddresses()->add($address);
        }
    }

    private function findProtectionForUpdate(CdnResource $resource): ResourceIpProtection|null
    {
        return $this->protectionRepository->createQueryBuilder('p')
            ->where('p.resource = :resource')
            ->setParameter('resource', $resource)
            ->getQuery()
            ->setLockMode(LockMode::PESSIMISTIC_WRITE)
            ->getOneOrNullResult();
    }
}
