<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain;

use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Resource\Domain\Finder\SslFileIndexFinder;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateMetadata;
use DateTimeImmutable;

final class SslFileFactory
{
    private SslFileIndexFinder $sslFileIndexFinder;

    public function __construct(SslFileIndexFinder $sslFileIndexFinder)
    {
        $this->sslFileIndexFinder = $sslFileIndexFinder;
    }

    public function createForSsl(CertificateMetadata $certificateMetadata, Ssl $ssl, string $type): SslFile
    {
        $alternativeNames = $certificateMetadata->getAlternativeNames();

        return new SslFile(
            new DateTimeImmutable(),
            $alternativeNames === [] ? (array) $certificateMetadata->getCommonName() : $alternativeNames,
            $certificateMetadata->getValidUntil(),
            $this->sslFileIndexFinder->getNextForSsl($ssl),
            $ssl,
            $type,
        );
    }
}
