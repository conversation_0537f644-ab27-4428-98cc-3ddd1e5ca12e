<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\DTO;

use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Resource\Application\Payload\SecureTokenSchema;
use Cdn77\NxgApi\Resource\Domain\DTO\Cache\CacheSettings;
use Cdn77\NxgApi\Resource\Domain\DTO\Origin\Origins;
use Cdn77\NxgApi\Resource\Domain\Value\S3ConnectionInfo;
use Ramsey\Uuid\UuidInterface;
use Webmozart\Assert\Assert;

use function array_unique;

final class EditedResource
{
    public int|null $accountId;

    public UuidInterface|null $clapOriginId;

    /** @var array<string>|null */
    public array|null $cnames;

    public string|null $resourceType;

    public string $originUrl;

    public string $originScheme;

    /** @var int<0, 65535>|null  */
    public int|null $originPort;

    public int|null $groupId;

    public bool|null $disableQueryString;

    public bool|null $ignoreSetCookie;

    public bool|null $mp4PseudoStreaming;

    public bool|null $instantSsl;

    /** @var array<string>|null */
    public array|null $ignoredQueryParams;

    public int|null $httpsRedirectCode;

    public string|null $originBaseDir;

    public bool|null $forwardHostHeader;

    public bool|null $streamingPlaylistBypass;

    /** @var int<0, 120>|null  */
    public int|null $originTimeout;

    /** @var array<mixed>|null */
    public array|null $customData;

    public bool|null $waf;

    public bool|null $quic;

    public bool|null $corsOriginHeader;

    public bool|null $corsTimingEnabled;

    public bool|null $corsWildcardEnabled;

    public S3ConnectionInfo|null $s3ConnectionInfo;

    public string|null $bucketName;

    public bool|null $sslVerifyDisable;

    public bool|null $rateLimit;

    public bool|null $contentDispositionByParam;

    /** @var array<string, string>|null */
    public array|null $originHeaders;

    public ResponseHeaders $responseHeaders;

    public SecureTokenSchema|null $secureTokenSchema;

    public FollowRedirect|null $followRedirect;

    public Origins|null $origins = null;

    /**
     * @param array<string>|null $cnames
     * @param array<string>|null $ignoredQueryParams
     * @param array<mixed>|null $customData
     * @param array<string, string>|null $originHeaders
     * @param int<0, 65535> $originPort
     * @param int<0, 120> $originTimeout
     */
    public function __construct(
        int|null $accountId,
        UuidInterface|null $clapOriginId,
        array|null $cnames,
        string|null $resourceType,
        string $originUrl,
        string $originScheme,
        int|null $originPort,
        int|null $groupId,
        public CacheSettings|null $cache,
        bool|null $disableQueryString,
        bool|null $ignoreSetCookie,
        bool|null $mp4PseudoStreaming,
        bool|null $instantSsl,
        array|null $ignoredQueryParams,
        int|null $httpsRedirectCode,
        string|null $originBaseDir,
        bool|null $forwardHostHeader,
        bool|null $streamingPlaylistBypass,
        int $originTimeout,
        array|null $customData,
        bool|null $waf,
        bool|null $quic,
        bool|null $corsOriginHeader,
        bool|null $corsTimingEnabled,
        bool|null $corsWildcardEnabled,
        S3ConnectionInfo|null $s3ConnectionInfo,
        bool|null $sslVerifyDisable,
        bool|null $rateLimit,
        bool|null $contentDispositionByParam,
        array|null $originHeaders,
        ResponseHeaders $responseHeaders,
        SecureTokenSchema|null $secureTokenSchema,
        FollowRedirect|null $followRedirect,
        Origins|null $origins,
    ) {
        $this->accountId = $accountId;
        $this->clapOriginId = $clapOriginId;
        $this->cnames = $cnames;
        $this->resourceType = $resourceType;
        $this->originUrl = $originUrl;
        $this->originScheme = $originScheme;
        $this->originPort = $originPort;
        $this->groupId = $groupId;
        $this->disableQueryString = $disableQueryString;
        $this->ignoreSetCookie = $ignoreSetCookie;
        $this->mp4PseudoStreaming = $mp4PseudoStreaming;
        $this->instantSsl = $instantSsl;
        $this->ignoredQueryParams = $ignoredQueryParams;
        $this->httpsRedirectCode = $httpsRedirectCode;
        $this->originBaseDir = $originBaseDir;
        $this->forwardHostHeader = $forwardHostHeader;
        $this->streamingPlaylistBypass = $streamingPlaylistBypass;
        $this->originTimeout = $originTimeout;
        $this->customData = $customData;
        $this->waf = $waf;
        $this->quic = $quic;
        $this->corsOriginHeader = $corsOriginHeader;
        $this->corsTimingEnabled = $corsTimingEnabled;
        $this->corsWildcardEnabled = $corsWildcardEnabled;
        $this->s3ConnectionInfo = $s3ConnectionInfo;
        $this->sslVerifyDisable = $sslVerifyDisable;
        $this->rateLimit = $rateLimit;
        $this->contentDispositionByParam = $contentDispositionByParam;
        $this->originHeaders = $originHeaders;
        $this->responseHeaders = $responseHeaders;
        $this->secureTokenSchema = $secureTokenSchema;
        $this->followRedirect = $followRedirect;
        $this->origins = $origins;
    }

    public static function fromSchema(ResourceEditSchema $resourceEditSchema): self
    {
        $resourceEditInfo = $resourceEditSchema->cdnResource;

        Assert::notNull($resourceEditInfo->originUrl);
        Assert::notNull($resourceEditInfo->responseHeaders);

        return new self(
            $resourceEditInfo->accountId,
            $resourceEditInfo->clapOriginId,
            $resourceEditInfo->cnames === null ? null : array_unique($resourceEditInfo->cnames),
            $resourceEditInfo->resourceType,
            $resourceEditInfo->originUrl,
            $resourceEditInfo->originScheme,
            $resourceEditInfo->originPort,
            $resourceEditInfo->groupId === null ? null : $resourceEditInfo->groupId[0],
            $resourceEditInfo->cache?->toDto(),
            $resourceEditInfo->disableQueryString === null ? null : $resourceEditInfo->disableQueryString === 1,
            $resourceEditInfo->ignoreSetCookie === null ? null : $resourceEditInfo->ignoreSetCookie === 1,
            $resourceEditInfo->mp4PseudoStreaming === null ? null : $resourceEditInfo->mp4PseudoStreaming === 1,
            $resourceEditInfo->instantSsl === null ? null : $resourceEditInfo->instantSsl === 1,
            $resourceEditInfo->ignoredQueryParams,
            $resourceEditInfo->httpsRedirectCode,
            $resourceEditInfo->originBasedir,
            $resourceEditInfo->forwardHostHeader === 1,
            $resourceEditInfo->streamingPlaylistBypass === null
                ? null
                : $resourceEditInfo->streamingPlaylistBypass === 1,
            $resourceEditInfo->originTimeout,
            $resourceEditInfo->customData,
            $resourceEditInfo->waf === null ? null : $resourceEditInfo->waf === 1,
            $resourceEditInfo->quic === null ? null : $resourceEditInfo->quic === 1,
            $resourceEditInfo->corsOriginHeader === null ? null : $resourceEditInfo->corsOriginHeader === 1,
            $resourceEditInfo->corsTimingEnabled,
            $resourceEditInfo->corsWildcardEnabled,
            $resourceEditInfo->s3Connection(),
            $resourceEditInfo->sslVerifyDisable,
            $resourceEditInfo->rateLimit ?? null,
            $resourceEditInfo->contentDispositionByParam ?? null,
            $resourceEditInfo->originHeaders,
            ResponseHeaders::fromSchema($resourceEditInfo->responseHeaders),
            $resourceEditInfo->secureToken,
            $resourceEditInfo->followRedirect === null
                ? null : FollowRedirect::fromSchema($resourceEditInfo->followRedirect),
            $resourceEditSchema->origins === null
                ? null : Origins::fromSchema($resourceEditSchema->origins),
        );
    }
}
