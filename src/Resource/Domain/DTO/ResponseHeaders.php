<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\DTO;

use Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeadersSchema;

final readonly class ResponseHeaders
{
    /** @param array<string, string> $headers */
    public function __construct(private array $headers)
    {
    }

    public static function empty(): self
    {
        return new self([]);
    }

    /** @param array<string, string>|null $data */
    public static function fromDbValue(array|null $data): self
    {
        if ($data === null) {
            return self::empty();
        }

        return new self($data);
    }

    public static function fromSchema(ResponseHeadersSchema $schema): self
    {
        $headers = [];

        foreach ($schema->headers as $header) {
            $headers[$header->name] = $header->value;
        }

        return new self($headers);
    }

    /** @return array<string, string> */
    public function toArray(): array
    {
        return $this->headers;
    }
}
