<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\DTO\Origin;

use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginS3Schema;

final class OriginS3
{
    public string|null $accessKeyId;

    public string|null $secret;

    public string|null $region;

    public string|null $bucketName;

    public string|null $type;

    public static function fromSchema(OriginS3Schema $schema): self
    {
        $originS3 = new self();

        $originS3->accessKeyId = $schema->accessKeyId;
        $originS3->secret = $schema->secret;
        $originS3->region = $schema->region;
        $originS3->bucketName = $schema->bucketName;
        $originS3->type = $schema->type;

        return $originS3;
    }
}
