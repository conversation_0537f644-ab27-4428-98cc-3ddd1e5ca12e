<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\DTO\Origin;

use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginSchema;
use Cdn77\NxgApi\Resource\Domain\DTO\FollowRedirect;
use Ramsey\Uuid\UuidInterface;
use Webmozart\Assert\Assert;

final class Origin
{
    public UuidInterface $clapOriginId;

    /** @var int<1, 100> */
    public int $priority;

    /** @var int<0, 120> */
    public int $timeout;

    public bool|null $forwardHostHeader;

    public bool|null $sslVerifyDisable;

    /** @var array<string, string> */
    public array|null $originHeaders;

    public OriginUrl $url;

    public OriginS3 $s3;

    public FollowRedirect $followRedirect;

    public static function fromSchema(OriginSchema $schema): self
    {
        Assert::integer($schema->priority);
        Assert::integer($schema->timeout);

        $origin = new self();

        $origin->clapOriginId = $schema->clapOriginId;
        $origin->priority = $schema->priority;
        $origin->timeout = $schema->timeout;
        $origin->forwardHostHeader = $schema->forwardHostHeader;
        $origin->sslVerifyDisable = $schema->sslVerifyDisable;
        $origin->originHeaders = $schema->originHeaders;
        $origin->url = OriginUrl::fromSchema($schema->url);
        $origin->s3 = OriginS3::fromSchema($schema->s3);
        $origin->followRedirect = FollowRedirect::fromSchema($schema->followRedirect);

        return $origin;
    }
}
