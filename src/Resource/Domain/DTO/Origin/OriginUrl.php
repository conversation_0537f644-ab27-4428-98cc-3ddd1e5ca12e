<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\DTO\Origin;

use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginUrlSchema;
use Webmozart\Assert\Assert;

final class OriginUrl
{
    public string $host;

    public string $scheme;

    public string|null $basedir;

    /** @var int<0, 65535> */
    public int $port;

    public static function fromSchema(OriginUrlSchema $schema): self
    {
        Assert::integer($schema->port);

        $originUrl = new self();
        $originUrl->host = $schema->host;
        $originUrl->scheme = $schema->scheme;
        $originUrl->basedir = $schema->basedir;
        $originUrl->port = $schema->port;

        return $originUrl;
    }
}
