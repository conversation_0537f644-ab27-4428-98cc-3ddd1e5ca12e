<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\DTO\Origin;

use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginsSchema;

final class Origins
{
    /** @var array<Origin> */
    public array $origins = [];

    public static function fromSchema(OriginsSchema $schema): self
    {
        $origins = new self();
        foreach ($schema->origins as $originSchema) {
            $origins->origins[] = Origin::fromSchema($originSchema);
        }

        return $origins;
    }
}
