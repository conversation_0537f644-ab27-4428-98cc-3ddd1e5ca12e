<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\DTO;

use Webmozart\Assert\Assert;

final class ResourceDatacenterLocation
{
    public string $city;

    public string $cityCode;

    public bool $isEnabled;

    public function __construct(string $city, string $cityCode, bool $isEnabled)
    {
        $this->city = $city;
        $this->cityCode = $cityCode;
        $this->isEnabled = $isEnabled;
    }

    /** @param array<string, string|bool> $location */
    public static function fromArray(array $location): self
    {
        Assert::keyExists($location, 'location_id');
        Assert::string($location['location_id']);
        Assert::keyExists($location, 'city_code');
        Assert::string($location['city_code']);
        Assert::keyExists($location, 'enabled');
        Assert::boolean($location['enabled']);

        return new self($location['location_id'], $location['city_code'], $location['enabled']);
    }
}
