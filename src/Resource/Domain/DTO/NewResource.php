<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\DTO;

use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddSchema;
use Cdn77\NxgApi\Resource\Application\Payload\SecureTokenSchema;
use Cdn77\NxgApi\Resource\Domain\DTO\Origin\Origins;
use Cdn77\NxgApi\Resource\Domain\Value\S3ConnectionInfo;
use Ramsey\Uuid\UuidInterface;
use Webmozart\Assert\Assert;

use function array_unique;

final class NewResource
{
    public int|null $id;

    public string|null $cdnUrl;

    public int $accountId;

    public UuidInterface|null $clapOriginId;

    /** @var string[] */
    public array $cnames;

    public string $originUrl;

    public string $originScheme;

    /** @var int<0, 65535>  */
    public int $originPort;

    public int $groupId;

    public int $cacheExpiry;

    public int|null $cacheExpiry404;

    public bool $disableQueryString;

    public bool $ignoreSetCookie;

    public bool $mp4PseudoStreaming;

    public bool $instantSsl;

    /** @var string[]|null */
    public array|null $ignoredQueryParams;

    public int|null $httpsRedirectCode;

    public string|null $originBaseDir;

    public bool|null $forwardHostHeader;

    public bool|null $streamingPlaylistBypass;

    /** @var int<0, 120>  */
    public int $originTimeout;

    /** @var mixed[]|null */
    public array|null $customData;

    public bool $waf;

    public bool $quic;

    public bool $corsOriginHeader;

    public S3ConnectionInfo|null $s3ConnectionInfo;

    public bool $sslVerifyDisable;

    public bool $rateLimit;

    public bool $contentDispositionByParam;

    /** @var array<string, string>|null */
    public array|null $originHeaders;

    public ResponseHeaders $responseHeaders;

    public SecureTokenSchema|null $secureTokenSchema;

    public FollowRedirect|null $followRedirect;

    public Origins|null $origins = null;

    /**
     * @param array<string> $cnames
     * @param array<string>|null $ignoredQueryParams
     * @param array<mixed>|null $customData
     * @param array<string, string>|null $originHeaders
     * @param int<0, 120> $originTimeout
     * @param int<0, 65535> $originPort
     */
    public function __construct(
        int|null $id,
        string|null $cdnUrl,
        int $accountId,
        UuidInterface|null $clapOriginId,
        array $cnames,
        string $originUrl,
        string $originScheme,
        int $originPort,
        int $groupId,
        int $cacheExpiry,
        int|null $cacheExpiry404,
        bool $disableQueryString,
        bool $ignoreSetCookie,
        bool $mp4PseudoStreaming,
        bool $instantSsl,
        array|null $ignoredQueryParams,
        int|null $httpsRedirectCode,
        string|null $originBaseDir,
        bool|null $forwardHostHeader,
        bool|null $streamingPlaylistBypass,
        int $originTimeout,
        array|null $customData,
        bool $waf,
        bool $quic,
        bool $corsOriginHeader,
        S3ConnectionInfo|null $s3ConnectionInfo,
        bool $sslVerifyDisable,
        bool $rateLimit,
        bool $contentDispositionByParam,
        array|null $originHeaders,
        ResponseHeaders $responseHeaders,
        SecureTokenSchema|null $secureTokenSchema,
        FollowRedirect|null $followRedirect,
        Origins|null $origins,
    ) {
        $this->id = $id;
        $this->cdnUrl = $cdnUrl;
        $this->accountId = $accountId;
        $this->clapOriginId = $clapOriginId;
        $this->cnames = $cnames;
        $this->originUrl = $originUrl;
        $this->originScheme = $originScheme;
        $this->originPort = $originPort;
        $this->groupId = $groupId;
        $this->cacheExpiry = $cacheExpiry;
        $this->cacheExpiry404 = $cacheExpiry404;
        $this->disableQueryString = $disableQueryString;
        $this->ignoreSetCookie = $ignoreSetCookie;
        $this->mp4PseudoStreaming = $mp4PseudoStreaming;
        $this->instantSsl = $instantSsl;
        $this->ignoredQueryParams = $ignoredQueryParams;
        $this->httpsRedirectCode = $httpsRedirectCode;
        $this->originBaseDir = $originBaseDir;
        $this->forwardHostHeader = $forwardHostHeader;
        $this->streamingPlaylistBypass = $streamingPlaylistBypass;
        $this->originTimeout = $originTimeout;
        $this->customData = $customData;
        $this->waf = $waf;
        $this->quic = $quic;
        $this->corsOriginHeader = $corsOriginHeader;
        $this->sslVerifyDisable = $sslVerifyDisable;
        $this->s3ConnectionInfo = $s3ConnectionInfo;
        $this->rateLimit = $rateLimit;
        $this->contentDispositionByParam = $contentDispositionByParam;
        $this->originHeaders = $originHeaders;
        $this->responseHeaders = $responseHeaders;
        $this->secureTokenSchema = $secureTokenSchema;
        $this->followRedirect = $followRedirect;
        $this->origins = $origins;
    }

    public static function fromSchema(ResourceAddSchema $resourceAddSchema): self
    {
        $resourceAddInfo = $resourceAddSchema->cdnResource;
        $accountId = $resourceAddInfo->accountId;
        $cacheExpiry = $resourceAddInfo->cacheExpiry;
        $cnames = $resourceAddInfo->cnames;
        $groupId = $resourceAddInfo->groupId;
        $originUrl = $resourceAddInfo->originUrl;

        Assert::notNull($accountId);
        Assert::notNull($cacheExpiry);
        Assert::notNull($cnames);
        Assert::notNull($groupId);
        Assert::notNull($resourceAddInfo->responseHeaders);

        return new self(
            $resourceAddInfo->id,
            $resourceAddInfo->cdnUrl,
            $accountId,
            $resourceAddInfo->clapOriginId,
            array_unique($cnames),
            $originUrl,
            $resourceAddInfo->originScheme,
            $resourceAddInfo->originPort,
            $groupId[0],
            $cacheExpiry,
            $resourceAddInfo->cacheExpiry404,
            $resourceAddInfo->disableQueryString === 1,
            $resourceAddInfo->ignoreSetCookie === 1,
            $resourceAddInfo->mp4PseudoStreaming === 1,
            $resourceAddInfo->instantSsl === 1,
            $resourceAddInfo->ignoredQueryParams,
            $resourceAddInfo->httpsRedirectCode,
            $resourceAddInfo->originBasedir,
            $resourceAddInfo->forwardHostHeader === 1,
            $resourceAddInfo->streamingPlaylistBypass === 1,
            $resourceAddInfo->originTimeout,
            $resourceAddInfo->customData,
            $resourceAddInfo->waf === 1,
            $resourceAddInfo->quic === 1,
            $resourceAddInfo->corsOriginHeader === 1,
            $resourceAddInfo->s3Connection(),
            $resourceAddInfo->sslVerifyDisable === true,
            $resourceAddInfo->rateLimit === true,
            $resourceAddInfo->contentDispositionByParam === true,
            $resourceAddInfo->originHeaders,
            ResponseHeaders::fromSchema($resourceAddInfo->responseHeaders),
            $resourceAddInfo->secureToken,
            $resourceAddInfo->followRedirect === null
                ? null : FollowRedirect::fromSchema($resourceAddInfo->followRedirect),
            $resourceAddSchema->origins === null
                ? null : Origins::fromSchema($resourceAddSchema->origins),
        );
    }
}
