<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\DTO;

use Cdn77\NxgApi\Resource\Application\Payload\FollowRedirectSchema;

final class FollowRedirect
{
    public bool $enabled;

    /** @var list<int>|null */
    public array|null $codes;

    /** @param list<int> $codes */
    public function __construct(bool $enabled, array|null $codes)
    {
        $this->enabled = $enabled;
        $this->codes = $codes;
    }

    public static function fromSchema(FollowRedirectSchema $schema): self
    {
        return new self($schema->enabled === true, $schema->codes);
    }
}
