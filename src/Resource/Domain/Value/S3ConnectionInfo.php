<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Value;

use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceInfo;
use Webmozart\Assert\Assert;

final class S3ConnectionInfo
{
    public const TYPE_CDN77_CEPH_RGW = 'cdn77-ceph-rgw';
    public const TYPE_EXTERNAL_S3 = 'external-s3';

    private string|null $s3AccessKeyId;

    private string|null $s3Secret;

    private string|null $s3Region;

    private string|null $s3BucketName;

    private string $s3Type;

    public function __construct(
        string|null $s3AccessKeyId,
        string|null $s3Secret,
        string|null $s3Region,
        string|null $s3BucketName,
        string $s3Type,
    ) {
        $this->s3AccessKeyId = $s3AccessKeyId;
        $this->s3BucketName = $s3BucketName;
        $this->s3Region = $s3Region;
        $this->s3Secret = $s3Secret;
        $this->s3Type = $s3Type;
    }

    /** @param ResourceAddInfo|ResourceEditInfo $schema */
    public static function fromSchema(ResourceInfo $schema): self|null
    {
        if ($schema->awsAccessKeyId !== null || $schema->awsRegion !== null || $schema->awsSecret !== null) {
            Assert::notNull($schema->awsAccessKeyId);
            Assert::notNull($schema->awsRegion);
            Assert::notNull($schema->awsSecret);
        }

        return $schema->s3Type === null ? null : new self(
            $schema->awsAccessKeyId,
            $schema->awsSecret,
            $schema->awsRegion,
            $schema->s3BucketName,
            $schema->s3Type,
        );
    }

    public function accessKeyId(): string|null
    {
        return $this->s3AccessKeyId;
    }

    public function secret(): string|null
    {
        return $this->s3Secret;
    }

    public function region(): string|null
    {
        return $this->s3Region;
    }

    public function bucketName(): string|null
    {
        return $this->s3BucketName;
    }

    public function type(): string
    {
        return $this->s3Type;
    }
}
