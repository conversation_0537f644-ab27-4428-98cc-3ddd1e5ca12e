<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Value;

use Cdn77\NxgApi\Resource\Domain\Enum\CertificateStatus;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateActualStatus;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateRequestedStatus;

final class SslStatus
{
    public CertificateStatus $status;

    public string $description;

    public CertificateActualStatus|null $currentStatus;

    public CertificateRequestedStatus|null $requestedStatus;

    public function __construct(
        CertificateStatus $status,
        string $description,
        CertificateActualStatus|null $currentStatus,
        CertificateRequestedStatus|null $requestedStatus,
    ) {
        $this->status = $status;
        $this->description = $description;
        $this->currentStatus = $currentStatus;
        $this->requestedStatus = $requestedStatus;
    }
}
