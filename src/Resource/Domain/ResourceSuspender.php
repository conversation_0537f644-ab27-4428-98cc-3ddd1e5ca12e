<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Service\Event\ResourceEvent;
use Cdn77\NxgApi\Service\Event\ResourceRestoredEvent;
use Cdn77\NxgApi\Service\Legacy\Suspension\Exception\ResourceAlreadySuspended;
use Cdn77\NxgApi\Service\Legacy\Suspension\Exception\ResourceAlreadyUnsuspended;
use DateTimeImmutable;
use Doctrine\DBAL\LockMode;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

use function sprintf;

class ResourceSuspender
{
    private EntityManagerInterface $entityManager;

    private EventDispatcherInterface $eventDispatcher;

    public function __construct(EntityManagerInterface $entityManager, EventDispatcherInterface $eventDispatcher)
    {
        $this->entityManager = $entityManager;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function suspend(CdnResource $resource): void
    {
        $this->entityManager->lock($resource, LockMode::PESSIMISTIC_WRITE);
        $this->entityManager->refresh($resource);

        if ($resource->isSuspended()) {
            throw new ResourceAlreadySuspended(
                sprintf('Resource #%d is already suspended.', $resource->getId()),
            );
        }

        $resource->setSuspended(new DateTimeImmutable());

        $this->entityManager->flush();

        $this->eventDispatcher->dispatch(new ResourceEvent($resource));
    }

    public function unsuspend(CdnResource $resource): void
    {
        $this->entityManager->lock($resource, LockMode::PESSIMISTIC_WRITE);
        $this->entityManager->refresh($resource);

        if (! $resource->isSuspended()) {
            throw new ResourceAlreadyUnsuspended(
                sprintf('Resource #%d is already not suspended.', $resource->getId()),
            );
        }

        $resource->setSuspended(null);

        $this->entityManager->flush();

        $this->eventDispatcher->dispatch(new ResourceRestoredEvent($resource));
    }
}
