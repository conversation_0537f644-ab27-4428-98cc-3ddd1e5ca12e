<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Core\Domain\Repository\CdnResourceRepository;
use Cdn77\NxgApi\FullLogs\Domain\FullLogsManager;
use Cdn77\NxgApi\Resource\Domain\ResourceSuspender;
use Cdn77\NxgApi\Service\Legacy\Suspension\Exception\ResourceAlreadySuspended;

final class SuspendResourceHandler implements CommandHandler
{
    private CdnResourceRepository $cdnResourceRepository;

    private FullLogsManager $fullLogsManager;

    private ResourceSuspender $suspender;

    public function __construct(
        CdnResourceRepository $cdnResourceRepository,
        FullLogsManager $fullLogsManager,
        ResourceSuspender $suspender,
    ) {
        $this->cdnResourceRepository = $cdnResourceRepository;
        $this->fullLogsManager = $fullLogsManager;
        $this->suspender = $suspender;
    }

    public function handle(SuspendResource $command): void
    {
        $resource = $this->cdnResourceRepository->get($command->resourceId);

        try {
            $this->suspender->suspend($resource);
        } catch (ResourceAlreadySuspended) {
            // ignore
        }

        $this->fullLogsManager->disable($resource);
    }
}
