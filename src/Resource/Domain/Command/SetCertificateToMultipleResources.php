<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\Command;
use Cdn77\NxgApi\Resource\Application\Payload\SetCertificateSchema;
use Webmozart\Assert\Assert;

final class SetCertificateToMultipleResources implements Command
{
    public string $certificate;

    public string $privateKey;

    /** @var list<int> */
    public array $resourceIds;

    /** @param list<int> $resourceIds */
    private function __construct(string $certificate, string $privateKey, array $resourceIds)
    {
        $this->certificate = $certificate;
        $this->privateKey = $privateKey;
        $this->resourceIds = $resourceIds;
    }

    public static function fromSchema(SetCertificateSchema $schema): self
    {
        $certificate = $schema->certificate;
        $privateKey = $schema->privateKey;
        $resourceIds = $schema->resourceIds;

        Assert::notNull($certificate);
        Assert::notNull($privateKey);
        Assert::notNull($resourceIds);

        return new self($certificate, $privateKey, $resourceIds);
    }
}
