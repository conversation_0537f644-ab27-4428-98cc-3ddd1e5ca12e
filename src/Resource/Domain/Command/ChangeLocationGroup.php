<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\Command;
use Cdn77\NxgApi\Resource\Application\Payload\ChangeLocationGroupSchema;

final class ChangeLocationGroup implements Command
{
    public int $groupId;

    public int $resourceId;

    public function __construct(int $groupId, int $resourceId)
    {
        $this->groupId = $groupId;
        $this->resourceId = $resourceId;
    }

    public static function fromResourceIdAndSchema(int $resourceId, ChangeLocationGroupSchema $schema): self
    {
        return new self($schema->getGroupId(), $resourceId);
    }
}
