<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Core\Domain\Repository\CdnResourceRepository;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateManager;

final class SetCertificateHandler implements CommandHandler
{
    private CdnResourceRepository $cdnResourceRepository;

    private CertificateManager $certificateManager;

    public function __construct(CdnResourceRepository $cdnResourceRepository, CertificateManager $certificateManager)
    {
        $this->cdnResourceRepository = $cdnResourceRepository;
        $this->certificateManager = $certificateManager;
    }

    public function handle(SetCertificate $command): SslFile
    {
        $resource = $this->cdnResourceRepository->get($command->resourceId);
        $certificatePair = new CertificatePair($command->certificate, $command->privateKey);

        return $this->certificateManager->setCertificate($resource, SslFile::TYPE_CUSTOM, $certificatePair);
    }
}
