<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\Command;
use Cdn77\NxgApi\Resource\Application\Payload\EnabledResourceDatacentersSchema;

final class EnableDatacentersForResource implements Command
{
    public int $resourceId;

    /** @var list<string> */
    public array $datacenterLocations;

    /** @param list<string> $datacenterLocations */
    public function __construct(int $resourceId, array $datacenterLocations)
    {
        $this->resourceId = $resourceId;
        $this->datacenterLocations = $datacenterLocations;
    }

    public static function fromSchema(int $resourceId, EnabledResourceDatacentersSchema $schema): self
    {
        return new self($resourceId, $schema->getResourceDatacenters());
    }
}
