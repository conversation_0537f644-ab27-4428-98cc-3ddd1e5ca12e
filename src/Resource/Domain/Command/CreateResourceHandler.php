<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Core\Domain\Certificate\SslCertificateProcessor;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\GeoProtection\Domain\SetupGeoProtection;
use Cdn77\NxgApi\HotlinkProtection\Domain\SetupHotlinkProtection;
use Cdn77\NxgApi\IpProtection\Domain\SetupIpProtection;
use Cdn77\NxgApi\Service\Legacy\Add\ResourceAdder;

final class CreateResourceHandler implements CommandHandler
{
    public function __construct(
        private ResourceAdder $resourceAdder,
        private SetupGeoProtection $setupGeoProtection,
        private SetupHotlinkProtection $setupHotlinkProtection,
        private SetupIpProtection $setupIpProtection,
        private SslCertificateProcessor $sslCertificateProcessor,
    ) {
    }

    public function handle(CreateResource $command): CdnResource
    {
        $resource = $this->resourceAdder->add($command->newResource);

        if ($command->geoProtectionInfo !== null) {
            $this->setupGeoProtection->setup($resource, $command->geoProtectionInfo);
        }

        if ($command->hotlinkProtectionInfo !== null) {
            $this->setupHotlinkProtection->setup($resource, $command->hotlinkProtectionInfo);
        }

        if ($command->ipProtectionInfo !== null) {
            $this->setupIpProtection->setup($resource, $command->ipProtectionInfo);
        }

        $certificate = $command->certificatePair;
        if ($certificate instanceof CertificatePair) {
            $this->sslCertificateProcessor->createCustomCertificateForResource($resource, $certificate);
        }

        return $resource;
    }
}
