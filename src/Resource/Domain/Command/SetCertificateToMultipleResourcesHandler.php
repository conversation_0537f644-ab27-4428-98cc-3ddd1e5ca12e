<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Certificate\Domain\PrivateKeyFileManager;
use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Resource\Domain\Exception\ResourceInvalid;
use Cdn77\NxgApi\Resource\Domain\Repository\SslRepository;
use Cdn77\NxgApi\Resource\Domain\SslFileFactory;
use Cdn77\NxgApi\Service\Event\ResourceEvent;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\CertificateBucket;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\Exception\FileCorrupted;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\Exception\FileWriteFailed;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateMetadataParser;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificatePairValidator;
use DateTimeImmutable;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Throwable;

use function count;

final class SetCertificateToMultipleResourcesHandler implements CommandHandler
{
    private CdnResourceRepository $cdnResourceRepository;

    private CertificateBucket $certificateBucket;

    private CertificateMetadataParser $certificateMetadataParser;

    private CertificatePairValidator $certificatePairValidator;

    private EventDispatcherInterface $eventDispatcher;

    private SslFileFactory $sslFileFactory;

    private SslRepository $sslRepository;

    public function __construct(
        CdnResourceRepository $cdnResourceRepository,
        CertificateBucket $certificateBucket,
        CertificateMetadataParser $certificateMetadataParser,
        CertificatePairValidator $certificatePairValidator,
        EventDispatcherInterface $eventDispatcher,
        SslFileFactory $sslFileFactory,
        SslRepository $sslRepository,
        private readonly PrivateKeyFileManager $privateKeyFileManager,
    ) {
        $this->cdnResourceRepository = $cdnResourceRepository;
        $this->certificateBucket = $certificateBucket;
        $this->certificateMetadataParser = $certificateMetadataParser;
        $this->certificatePairValidator = $certificatePairValidator;
        $this->eventDispatcher = $eventDispatcher;
        $this->sslFileFactory = $sslFileFactory;
        $this->sslRepository = $sslRepository;
    }

    public function handle(SetCertificateToMultipleResources $command): void
    {
        $resources = $this->cdnResourceRepository->getForIdList($command->resourceIds);

        $certificatePair = $this->privateKeyFileManager->loadKeyByUuid(
            new CertificatePair($command->certificate, $command->privateKey),
            $this->getAccountDirName($resources),
        );

        $this->certificatePairValidator->validate($certificatePair);

        $certificateMetadata = $this->certificateMetadataParser->getMetadata($certificatePair);

        foreach ($resources as $resource) {
            $resource->setInstantSsl(false);
            $ssl = $this->sslRepository->findForResource($resource);
            if ($ssl === null) {
                $ssl = new Ssl();
                $ssl->setResource($resource);
            }

            $sslFile = $this->sslFileFactory->createForSsl($certificateMetadata, $ssl, SslFile::TYPE_CUSTOM);
            $ssl->getFiles()->add($sslFile);
            $ssl->setAssignedAt(new DateTimeImmutable());
            $ssl->setAssignedIndex($sslFile->getIndex());
            $this->sslRepository->add($ssl);

            try {
                $this->certificateBucket->save(
                    $sslFile,
                    $certificatePair,
                );
            } catch (FileCorrupted | FileWriteFailed) {
                $this->certificateBucket->delete($sslFile);

                $this->certificateBucket->save(
                    $sslFile,
                    $certificatePair,
                );
            }

            try {
                $this->eventDispatcher->dispatch(new ResourceEvent($resource));
            } catch (Throwable) {
                // This can fail, but it shouldn't affect the response for set certificate request
            }
        }
    }

    /** @param list<CdnResource> $resources */
    private function getAccountDirName(array $resources): string
    {
        $accountId = $resources[0]->getAccount()->getId();
        $wrongResourceIds = [];

        foreach ($resources as $resource) {
            if ($resource->getAccount()->getId() === $accountId) {
                continue;
            }

            $wrongResourceIds[] = $resource->getId();
        }

        if (count($wrongResourceIds) > 0) {
            throw ResourceInvalid::notSameAccount($wrongResourceIds);
        }

        return (string) $accountId;
    }
}
