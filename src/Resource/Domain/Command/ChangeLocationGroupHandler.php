<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Resource\Domain\LocationGroupChanger;

final class ChangeLocationGroupHandler implements CommandHandler
{
    private CdnResourceRepository $cdnResourceRepository;

    private LocationGroupChanger $locationGroupChanger;

    public function __construct(
        CdnResourceRepository $cdnResourceRepository,
        LocationGroupChanger $locationGroupChanger,
    ) {
        $this->cdnResourceRepository = $cdnResourceRepository;
        $this->locationGroupChanger = $locationGroupChanger;
    }

    public function handle(ChangeLocationGroup $command): void
    {
        $resource = $this->cdnResourceRepository->get($command->resourceId);

        $this->locationGroupChanger->change($resource, $command->groupId);
    }
}
