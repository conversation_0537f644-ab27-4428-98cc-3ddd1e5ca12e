<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Core\Domain\Certificate\SslCertificateProcessor;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\GeoProtection\Domain\SetupGeoProtection;
use Cdn77\NxgApi\HotlinkProtection\Domain\SetupHotlinkProtection;
use Cdn77\NxgApi\IpProtection\Domain\SetupIpProtection;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Resource\Domain\DTO\EditedResource;
use Cdn77\NxgApi\Resource\Domain\Value\CustomCertificate;
use Cdn77\NxgApi\Resource\Domain\Value\EmptyCertificatePair;
use Cdn77\NxgApi\Service\Legacy\Edit\ResourceChanger;
use Webmozart\Assert\Assert;

final class EditResourceHandler implements CommandHandler
{
    private CdnResourceRepository $cdnResourceRepository;

    private ResourceChanger $resourceChanger;

    private SetupGeoProtection $setupGeoProtection;

    private SetupHotlinkProtection $setupHotlinkProtection;

    private SetupIpProtection $setupIpProtection;

    private SslCertificateProcessor $sslCertificateProcessor;

    public function __construct(
        CdnResourceRepository $cdnResourceRepository,
        ResourceChanger $resourceChanger,
        SetupGeoProtection $setupGeoProtection,
        SetupHotlinkProtection $setupHotlinkProtection,
        SetupIpProtection $setupIpProtection,
        SslCertificateProcessor $sslCertificateProcessor,
    ) {
        $this->cdnResourceRepository = $cdnResourceRepository;
        $this->resourceChanger = $resourceChanger;
        $this->setupGeoProtection = $setupGeoProtection;
        $this->setupHotlinkProtection = $setupHotlinkProtection;
        $this->setupIpProtection = $setupIpProtection;
        $this->sslCertificateProcessor = $sslCertificateProcessor;
    }

    public function handle(EditResource $command): void
    {
        $resource = $this->cdnResourceRepository->get($command->resourceId);

        if ($command->geoProtectionInfo !== null) {
            $this->setupGeoProtection->setup($resource, $command->geoProtectionInfo);
        }

        if ($command->hotlinkProtectionInfo !== null) {
            $this->setupHotlinkProtection->setup($resource, $command->hotlinkProtectionInfo);
        }

        if ($command->ipProtectionInfo !== null) {
            $this->setupIpProtection->setup($resource, $command->ipProtectionInfo);
        }

        if ($command->certificatePair !== null) {
            $this->handleCertificatePairUpdate($resource, $command->editedResource, $command->certificatePair);
        }

        $this->resourceChanger->modify($resource, $command->editedResource);
    }

    private function handleCertificatePairUpdate(
        CdnResource $resource,
        EditedResource $editedResource,
        CustomCertificate $certificate,
    ): void {
        if ($editedResource->instantSsl === true) {
            return;
        }

        if ($certificate instanceof EmptyCertificatePair) {
            $this->sslCertificateProcessor->deleteCertificate($resource);

            return;
        }

        Assert::isInstanceOf($certificate, CertificatePair::class);

        $this->sslCertificateProcessor->createCustomCertificateForResource($resource, $certificate);
    }
}
