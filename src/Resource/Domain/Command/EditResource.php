<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\Command;
use Cdn77\NxgApi\GeoProtection\Application\Payload\GeoProtectionInfo;
use Cdn77\NxgApi\HotlinkProtection\Application\Payload\HotlinkProtectionInfo;
use Cdn77\NxgApi\IpProtection\Application\Payload\IpProtectionInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Resource\Domain\DTO\EditedResource;
use Cdn77\NxgApi\Resource\Domain\Value\CustomCertificate;

final class EditResource implements Command
{
    public CustomCertificate|null $certificatePair;

    public EditedResource $editedResource;

    public GeoProtectionInfo|null $geoProtectionInfo;

    public HotlinkProtectionInfo|null $hotlinkProtectionInfo;

    public IpProtectionInfo|null $ipProtectionInfo;

    public int $resourceId;

    public function __construct(
        int $resourceId,
        EditedResource $editedResource,
        GeoProtectionInfo|null $geoProtectionInfo,
        HotlinkProtectionInfo|null $hotlinkProtectionInfo,
        IpProtectionInfo|null $ipProtectionInfo,
        CustomCertificate|null $certificatePair,
    ) {
        $this->editedResource = $editedResource;
        $this->geoProtectionInfo = $geoProtectionInfo;
        $this->hotlinkProtectionInfo = $hotlinkProtectionInfo;
        $this->ipProtectionInfo = $ipProtectionInfo;
        $this->resourceId = $resourceId;
        $this->certificatePair = $certificatePair;
    }

    public static function fromSchema(int $resourceId, ResourceEditSchema $schema): self
    {
        return new self(
            $resourceId,
            $schema->resourceDto(),
            $schema->getGeoProtection(),
            $schema->getHotlinkProtection(),
            $schema->getIpProtection(),
            $schema->getCertificatePair(),
        );
    }
}
