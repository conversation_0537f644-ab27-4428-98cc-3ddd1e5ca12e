<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\Command;

final class SetCertificate implements Command
{
    public string $certificate;

    public string $privateKey;

    public int $resourceId;

    public function __construct(string $certificate, string $privateKey, int $resourceId)
    {
        $this->certificate = $certificate;
        $this->privateKey = $privateKey;
        $this->resourceId = $resourceId;
    }
}
