<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Entity\Legacy\LocationGroup;
use Cdn77\NxgApi\FullLogs\Domain\FullLogsManager;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Resource\Domain\LocationGroupChanger;
use Cdn77\NxgApi\Service\Legacy\Delete\ResourceDeleter;

final class DeleteResourceHandler implements CommandHandler
{
    private CdnResourceRepository $cdnResourceRepository;

    private FullLogsManager $fullLogsManager;

    private LocationGroupChanger $locationGroupChanger;

    private ResourceDeleter $resourceDeleter;

    public function __construct(
        CdnResourceRepository $cdnResourceRepository,
        FullLogsManager $fullLogsManager,
        LocationGroupChanger $locationGroupChanger,
        ResourceDeleter $resourceDeleter,
    ) {
        $this->cdnResourceRepository = $cdnResourceRepository;
        $this->fullLogsManager = $fullLogsManager;
        $this->locationGroupChanger = $locationGroupChanger;
        $this->resourceDeleter = $resourceDeleter;
    }

    public function handle(DeleteResource $command): void
    {
        $cdnResource = $this->cdnResourceRepository->get($command->resourceId);
        $this->fullLogsManager->disable($cdnResource);
        $this->locationGroupChanger->change($cdnResource, LocationGroup::DEFAULT_ID);
        $this->resourceDeleter->delete($cdnResource);
    }
}
