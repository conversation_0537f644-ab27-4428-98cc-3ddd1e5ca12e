<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\Command;

final class UnsuspendResource implements Command
{
    public int $resourceId;

    public bool $enableFullLogs;

    public function __construct(int $resourceId, bool $enableFullLogs = false)
    {
        $this->resourceId = $resourceId;
        $this->enableFullLogs = $enableFullLogs;
    }
}
