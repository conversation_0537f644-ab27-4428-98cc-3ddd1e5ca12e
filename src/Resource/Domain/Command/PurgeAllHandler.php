<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Core\Domain\Repository\CdnResourceRepository;

final class PurgeAllHandler implements CommandHandler
{
    private CdnResourceRepository $cdnResourceRepository;

    public function __construct(CdnResourceRepository $cdnResourceRepository)
    {
        $this->cdnResourceRepository = $cdnResourceRepository;
    }

    public function handle(PurgeAll $command): void
    {
        $this->cdnResourceRepository->get($command->resourceId)->increasePurgeAllKey();
    }
}
