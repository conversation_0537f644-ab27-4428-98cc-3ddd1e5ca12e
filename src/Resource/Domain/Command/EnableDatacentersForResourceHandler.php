<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Resource\Domain\ResourceDatacentersEnabler;

final class EnableDatacentersForResourceHandler implements CommandHandler
{
    private ResourceDatacentersEnabler $resourceDatacentersEnabler;

    public function __construct(ResourceDatacentersEnabler $resourceDatacentersEnabler)
    {
        $this->resourceDatacentersEnabler = $resourceDatacentersEnabler;
    }

    public function handle(EnableDatacentersForResource $command): void
    {
        $this->resourceDatacentersEnabler->enable($command->resourceId, $command->datacenterLocations);
    }
}
