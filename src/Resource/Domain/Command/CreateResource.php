<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\Command;
use Cdn77\NxgApi\GeoProtection\Application\Payload\GeoProtectionInfo;
use Cdn77\NxgApi\HotlinkProtection\Application\Payload\HotlinkProtectionInfo;
use Cdn77\NxgApi\IpProtection\Application\Payload\IpProtectionInfo;
use Cdn77\NxgApi\Resource\Domain\DTO\NewResource;
use Cdn77\NxgApi\Resource\Domain\Value\CustomCertificate;

final class CreateResource implements Command
{
    public function __construct(
        public NewResource $newResource,
        public GeoProtectionInfo|null $geoProtectionInfo,
        public HotlinkProtectionInfo|null $hotlinkProtectionInfo,
        public IpProtectionInfo|null $ipProtectionInfo,
        public CustomCertificate|null $certificatePair,
    ) {
    }
}
