<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use Cdn77\NxgApi\Resource\Infrastructure\Finder\FilesystemMainCertificateFinder;
use DomainException;

use function sprintf;

final class FailedToGetCertificate extends DomainException implements NxgApiDomainException
{
    public static function mainNotFound(): self
    {
        return new self(sprintf('Main certificate (%s) not found', FilesystemMainCertificateFinder::MAIN_FILENAME));
    }

    public static function incompleteMainPair(int $maxIndex): self
    {
        return new self(sprintf('Incomplete main certificate pair for index %d', $maxIndex));
    }

    public static function unreadableMainPair(int $maxIndex): self
    {
        return new self(sprintf('Failed to read main certificate files for index %d', $maxIndex));
    }
}
