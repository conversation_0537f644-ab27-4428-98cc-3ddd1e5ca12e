<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use DomainException;

final class ResourceOriginMisconfigured extends DomainException implements NxgApiDomainException
{
    public static function streamImmutability(): self
    {
        return new self('Switch between stream and non-stream origins is not allowed.');
    }

    public static function streamAndNotStreamMixed(): self
    {
        return new self('All origins must be either stream or non-stream.');
    }

    public static function priorityNotUnique(): self
    {
        return new self('All origins must have unique priority.');
    }

    public static function multipleMainPriorities(): self
    {
        return new self('Only one origin can have main priority.');
    }

    public static function mainPriorityNotExists(): self
    {
        return new self('Main priority origin must exist.');
    }
}
