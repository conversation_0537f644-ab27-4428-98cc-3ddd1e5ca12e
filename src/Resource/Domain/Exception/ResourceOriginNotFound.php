<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use DomainException;

use function implode;
use function sort;
use function sprintf;

final class ResourceOriginNotFound extends DomainException implements NxgApiDomainException
{
    public static function fromResourceId(int $resourceId): self
    {
        return new self(sprintf('Origin for CDN Resource with ID "%d" not found.', $resourceId));
    }

    /** @param array<int> $resourceIds */
    public static function fromResourceIds(array $resourceIds): self
    {
        sort($resourceIds);

        return new self(sprintf('Origin for these CDN Resources not found: %s', implode(',', $resourceIds)));
    }
}
