<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use DomainException;

use function sprintf;

final class CdnResourceNotFound extends DomainException implements NxgApiDomainException
{
    public static function fromResourceId(int $resourceId): self
    {
        return new self(sprintf('CDN Resource with ID "%d" not found.', $resourceId));
    }
}
