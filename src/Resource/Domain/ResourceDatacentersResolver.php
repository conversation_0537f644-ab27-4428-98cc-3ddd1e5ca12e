<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain;

use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Repository\Legacy\CustomLocationRepository;
use Cdn77\NxgApi\Repository\Legacy\LocationRepository;
use Cdn77\NxgApi\Resource\Domain\DTO\ResourceDatacenterLocation;

final class ResourceDatacentersResolver
{
    private CdnResourceRepository $cdnResourceRepository;

    private CustomLocationRepository $customLocationRepository;

    private LocationRepository $locationRepository;

    public function __construct(
        CdnResourceRepository $cdnResourceRepository,
        CustomLocationRepository $customLocationRepository,
        LocationRepository $locationRepository,
    ) {
        $this->customLocationRepository = $customLocationRepository;
        $this->locationRepository = $locationRepository;
        $this->cdnResourceRepository = $cdnResourceRepository;
    }

    /** @return list<ResourceDatacenterLocation> */
    public function resolve(int $resourceId): array
    {
        $resource = $this->cdnResourceRepository->get($resourceId);
        $hasCustomLocation = $this->customLocationRepository->hasResourceCustomLocation($resource);

        return $hasCustomLocation
            ? $this->locationRepository->findCustomDatacenterLocationsForResource($resource)
            : $this->locationRepository->findDatacenterLocationsForResource($resource);
    }
}
