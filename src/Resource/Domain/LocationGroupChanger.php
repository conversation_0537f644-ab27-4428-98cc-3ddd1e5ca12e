<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Repository\Legacy\LocationGroupRepository;
use Cdn77\NxgApi\Service\Legacy\Locations\Exception\NoCustomLocationExists;
use Cdn77\NxgApi\Service\Legacy\Locations\ResourceLocationsChanger;

final class LocationGroupChanger
{
    private LocationGroupRepository $locationGroupRepository;

    private ResourceLocationsChanger $resourceLocationsChanger;

    public function __construct(
        LocationGroupRepository $locationGroupRepository,
        ResourceLocationsChanger $resourceLocationsChanger,
    ) {
        $this->locationGroupRepository = $locationGroupRepository;
        $this->resourceLocationsChanger = $resourceLocationsChanger;
    }

    public function change(CdnResource $resource, int $groupId): void
    {
        if ($resource->getGroup()->getId() === $groupId) {
            return;
        }

        $group = $this->locationGroupRepository->get($groupId);

        $resource->setGroup($group);

        try {
            $this->resourceLocationsChanger->resetCustomLocations($resource);
        } catch (NoCustomLocationExists) {
            // ignore
        }
    }
}
