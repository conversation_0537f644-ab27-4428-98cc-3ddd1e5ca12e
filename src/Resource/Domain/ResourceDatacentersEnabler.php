<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain;

use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Repository\Legacy\LocationRepository;
use Cdn77\NxgApi\Service\Legacy\Locations\ResourceLocationsChanger;

final class ResourceDatacentersEnabler
{
    private const MINIMUM_ACTIVE_LOCATIONS = 1;

    private CdnResourceRepository $cdnResourceRepository;

    private LocationRepository $locationRepository;

    private ResourceLocationsChanger $resourceLocationsChanger;

    public function __construct(
        CdnResourceRepository $cdnResourceRepository,
        LocationRepository $locationRepository,
        ResourceLocationsChanger $resourceLocationsChanger,
    ) {
        $this->cdnResourceRepository = $cdnResourceRepository;
        $this->locationRepository = $locationRepository;
        $this->resourceLocationsChanger = $resourceLocationsChanger;
    }

    /** @param list<string> $datacenterLocations */
    public function enable(int $resourceId, array $datacenterLocations): void
    {
        $resource = $this->cdnResourceRepository->get($resourceId);
        $locations = $this->locationRepository->findAllForCities($datacenterLocations);

        $this->resourceLocationsChanger->setCustomLocations($resource, $locations, self::MINIMUM_ACTIVE_LOCATIONS);
    }
}
