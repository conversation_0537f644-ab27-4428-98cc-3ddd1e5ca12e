<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\Query;
use DateTimeImmutable;

final class FindSslCertificates implements Query
{
    public bool $includeSuspended;

    public string|null $type;

    public DateTimeImmutable|null $expirationBefore;

    public function __construct(bool $includeSuspended, string|null $type, DateTimeImmutable|null $expirationBefore)
    {
        $this->includeSuspended = $includeSuspended;
        $this->type = $type;
        $this->expirationBefore = $expirationBefore;
    }
}
