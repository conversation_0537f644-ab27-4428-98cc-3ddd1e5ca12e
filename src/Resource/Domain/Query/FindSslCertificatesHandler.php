<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateManager;

final class FindSslCertificatesHandler implements QueryHandler
{
    private CertificateManager $certificateManager;

    public function __construct(CertificateManager $certificateManager)
    {
        $this->certificateManager = $certificateManager;
    }

    /** @return list<SslFile> */
    public function handle(FindSslCertificates $query): array
    {
        return $this->certificateManager->getCertificates(
            true,
            $query->includeSuspended,
            $query->type,
            $query->expirationBefore,
        );
    }
}
