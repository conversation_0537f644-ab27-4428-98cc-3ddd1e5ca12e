<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Resource\Domain\Finder\MainCertificateFinder;
use Cdn77\NxgApi\Resource\Domain\Repository\SslRepository;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\CertificateBucket;

final class FindAssignedCertificatePairsForResourcesHandler implements QueryHandler
{
    public function __construct(
        private CertificateBucket $certificateBucket,
        private MainCertificateFinder $mainCertificateFinder,
        private SslRepository $sslRepository,
    ) {
    }

    /** @return array<int, CertificatePair> */
    public function handle(FindAssignedCertificatePairsForResources $query): array
    {
        $ssls = $this->sslRepository->findForResources($query->resourceIds);

        $result = [1 => $this->mainCertificateFinder->findLast()];

        foreach ($ssls as $ssl) {
            $result[$ssl->getResource()->getId()] = $this->certificateBucket->get($ssl->getAssignedSslFile());
        }

        return $result;
    }
}
