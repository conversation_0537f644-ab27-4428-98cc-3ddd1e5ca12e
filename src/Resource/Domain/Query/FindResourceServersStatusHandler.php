<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\Core\Domain\Repository\CdnResourceRepository;
use Cdn77\NxgApi\Core\Domain\ServerStatusCalculator;
use Cdn77\NxgApi\Repository\Legacy\ServerRepository;
use Cdn77\NxgApi\Service\Server\ServerStatus;

final class FindResourceServersStatusHandler implements QueryHandler
{
    private CdnResourceRepository $cdnResourceRepository;

    private ServerRepository $serverRepository;

    private ServerStatusCalculator $serverStatusCalculator;

    public function __construct(
        CdnResourceRepository $cdnResourceRepository,
        ServerRepository $serverRepository,
        ServerStatusCalculator $serverStatusCalculator,
    ) {
        $this->cdnResourceRepository = $cdnResourceRepository;
        $this->serverRepository = $serverRepository;
        $this->serverStatusCalculator = $serverStatusCalculator;
    }

    /** @return list<ServerStatus> */
    public function handle(FindResourceServersStatus $query): array
    {
        $cdnResource = $this->cdnResourceRepository->get($query->resourceId);
        $servers = $this->serverRepository->findUsedByResource($cdnResource);

        return $this->serverStatusCalculator->getStatusForServers($servers);
    }
}
