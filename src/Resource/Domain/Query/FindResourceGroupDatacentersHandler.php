<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\Resource\Domain\DTO\ResourceDatacenterLocation;
use Cdn77\NxgApi\Resource\Domain\ResourceDatacentersResolver;

final class FindResourceGroupDatacentersHandler implements QueryHandler
{
    private ResourceDatacentersResolver $resourceDatacentersResolver;

    public function __construct(ResourceDatacentersResolver $resourceDatacentersResolver)
    {
        $this->resourceDatacentersResolver = $resourceDatacentersResolver;
    }

    /** @return list<ResourceDatacenterLocation> */
    public function handle(FindResourceGroupDatacenters $query): array
    {
        return $this->resourceDatacentersResolver->resolve($query->resourceId);
    }
}
