<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\Core\Domain\Repository\CdnResourceRepository;
use Cdn77\NxgApi\Resource\Domain\Enum\CertificateStatus;
use Cdn77\NxgApi\Resource\Domain\Enum\CertificateStatusDescription;
use Cdn77\NxgApi\Resource\Domain\Value\SslStatus;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateManager;

final class FindSslStatusForResourceHandler implements QueryHandler
{
    private CdnResourceRepository $cdnResourceRepository;

    private CertificateManager $certificateManager;

    public function __construct(CdnResourceRepository $cdnResourceRepository, CertificateManager $certificateManager)
    {
        $this->certificateManager = $certificateManager;
        $this->cdnResourceRepository = $cdnResourceRepository;
    }

    public function handle(FindSslStatusForResource $query): SslStatus
    {
        $resource = $this->cdnResourceRepository->get($query->resourceId);
        $actual = $this->certificateManager->findActualStatus($resource);
        $requested = $this->certificateManager->findRequestedStatus($resource);

        $status = $actual === null ? CertificateStatus::missing() : CertificateStatus::active();

        $description = CertificateStatusDescription::forStatus(
            $status,
            $requested !== null && $requested->status->isCanceled(),
        );

        return new SslStatus(
            $status,
            $description,
            $actual,
            $requested,
        );
    }
}
