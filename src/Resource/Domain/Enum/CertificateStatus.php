<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Enum;

use MyCLabs\Enum\Enum;

/** @extends Enum<string> */
final class CertificateStatus extends Enum
{
    public const MISSING = 'missing';
    public const ACTIVE = 'active';
    public const INSTANT_SSL_GENERATING = 'instant_ssl_generating';
    public const INSTANT_SSL_RETRYING = 'instant_ssl_retrying';
    public const INSTANT_SSL_REQUEST_MISSING = 'instant_ssl_request_missing';
    public const INSTANT_SSL_REQUEST_CANCELED = 'instant_ssl_request_canceled';

    public static function active(): self
    {
        return parent::from(self::ACTIVE);
    }

    public static function missing(): self
    {
        return parent::from(self::MISSING);
    }

    public static function instantSslGenerating(): self
    {
        return parent::from(self::INSTANT_SSL_GENERATING);
    }

    public static function instantSslRetrying(): self
    {
        return parent::from(self::INSTANT_SSL_RETRYING);
    }

    public static function instantSslRequestMissing(): self
    {
        return parent::from(self::INSTANT_SSL_REQUEST_MISSING);
    }

    public static function instantSslRequestCanceled(): self
    {
        return parent::from(self::INSTANT_SSL_REQUEST_CANCELED);
    }

    public function isGeneral(): bool
    {
        return $this->getValue() === self::ACTIVE || $this->getValue() === self::MISSING;
    }

    public function isForInstantSsl(): bool
    {
        return $this->getValue() === self::INSTANT_SSL_GENERATING
            || $this->getValue() === self::INSTANT_SSL_RETRYING
            || $this->getValue() === self::INSTANT_SSL_REQUEST_MISSING
            || $this->getValue() === self::INSTANT_SSL_REQUEST_CANCELED;
    }

    public function isCanceled(): bool
    {
        return $this->getValue() === self::INSTANT_SSL_REQUEST_CANCELED;
    }
}
