<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Enum;

use MyCLabs\Enum\Enum;

use function sprintf;

/** @extends Enum<string> */
final class CertificateStatusDescription extends Enum
{
    public const ACTIVE = 'Certificate was saved at "last_changed_at". ' .
                          'After save it takes few minutes before it is available in all locations.';
    public const MISSING = 'CDN resource certificate was removed or never assigned.';
    public const INSTANT_SSL_GENERATING =
        'Instant SSL certificate is being generated for the first time on this CDN resource.';
    public const INSTANT_SSL_RETRYING = 'Instant SSL certificate is being renewed.';
    public const INSTANT_SSL_REQUEST_MISSING = 'Inconsistent state: Instant SSL is enabled, '
        . 'but there is not any request to generate certificate. Developer overview is needed.';
    public const INSTANT_SSL_REQUEST_CANCELED = 'Instant SSL was canceled '
        . 'because we were not able to generate certificate for too long. '
        . 'Edit CNAMEs or re-enable Instant SSL to try again.';

    private const DESCRIPTIONS_BY_STATUS = [
        CertificateStatus::ACTIVE => self::ACTIVE,
        CertificateStatus::MISSING => self::MISSING,
        CertificateStatus::INSTANT_SSL_GENERATING => self::INSTANT_SSL_GENERATING,
        CertificateStatus::INSTANT_SSL_RETRYING => self::INSTANT_SSL_RETRYING,
        CertificateStatus::INSTANT_SSL_REQUEST_MISSING => self::INSTANT_SSL_REQUEST_MISSING,
        CertificateStatus::INSTANT_SSL_REQUEST_CANCELED => self::INSTANT_SSL_REQUEST_CANCELED,
    ];

    public static function forStatus(CertificateStatus $status, bool $withCancelledSuffix = false): string
    {
        if ($withCancelledSuffix) {
            return sprintf(
                '%s %s',
                self::DESCRIPTIONS_BY_STATUS[$status->getValue()],
                self::INSTANT_SSL_REQUEST_CANCELED,
            );
        }

        return self::DESCRIPTIONS_BY_STATUS[$status->getValue()];
    }
}
