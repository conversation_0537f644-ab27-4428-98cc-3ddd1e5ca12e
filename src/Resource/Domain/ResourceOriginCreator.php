<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;
use Cdn77\NxgApi\Resource\Domain\DTO\EditedResource;
use Cdn77\NxgApi\Resource\Domain\DTO\NewResource;
use Cdn77\NxgApi\Resource\Domain\DTO\Origin\Origin;
use Cdn77\NxgApi\Resource\Domain\DTO\Origin\Origins;
use Cdn77\NxgApi\Resource\Domain\Exception\ResourceOriginMisconfigured;
use Cdn77\NxgApi\Resource\Domain\Repository\ResourceOriginRepository;
use Cdn77\NxgApi\Service\Legacy\Edit\Exception\ResourceValidationFailed;
use Cdn77\NxgApi\Validator\Constraints\Resource\Cdn77CephRgwOriginHost;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Webmozart\Assert\Assert;

use function in_array;
use function reset;
use function Safe\preg_match;

final class ResourceOriginCreator
{
    private ValidatorInterface $validator;
    private ResourceOriginRepository $resourceOriginRepository;
    private EntityManagerInterface $entityManager;

    public function __construct(
        EntityManagerInterface $entityManager,
        ValidatorInterface $validator,
        ResourceOriginRepository $resourceOriginRepository,
    ) {
        $this->validator = $validator;
        $this->resourceOriginRepository = $resourceOriginRepository;
        $this->entityManager = $entityManager;
    }

    public function createFromNewResource(CdnResource $resource, NewResource $newResource): void
    {
        $this->createOrigins($resource, $newResource);
    }

    public function createFromEditedResource(CdnResource $resource, EditedResource $editedResource): void
    {
        $this->validateBeforeEdit($resource, $editedResource);

        $this->removeAllResourceOrigins($resource);

        $this->createOrigins($resource, $editedResource);
    }

    private function removeAllResourceOrigins(CdnResource $resource): void
    {
        $this->resourceOriginRepository->removeAllForResource($resource);
        $resource->getOrigins()->clear();

        $this->entityManager->flush();
    }

    /** @param NewResource|EditedResource $dto */
    private function createOrigins(CdnResource $resource, $dto): void
    {
        if ($dto->origins === null) {
            $origin = ResourceOrigin::fromResourceAndDto($resource, $dto);
            $this->validateAndSaveOrigin($resource, $origin);
        } else {
            $this->createOriginsFromSchema($resource, $dto->origins);
        }
    }

    private function createOriginsFromSchema(CdnResource $resource, Origins $origins): void
    {
        $this->validateAllOriginsAreStreamingOrNotStreaming($origins);
        $this->validateOnePriorityIsMain($origins);
        $this->validateAllOriginsHaveDifferentPriority($origins);

        foreach ($origins->origins as $origin) {
            $resourceOrigin = ResourceOrigin::fromResourceAndSchema($resource, $origin);
            $this->validateAndSaveOrigin($resource, $resourceOrigin);
        }
    }

    private function validateAndSaveOrigin(CdnResource $resource, ResourceOrigin $origin): void
    {
        $this->validateOrigin($origin);
        $this->resourceOriginRepository->add($origin);

        $resource->addOrigin($origin);
    }

    private function validateBeforeEdit(CdnResource $resource, EditedResource $editedResource): void
    {
        if ($editedResource->origins !== null) {
            $originSchema = reset($editedResource->origins->origins);
            Assert::isInstanceOf($originSchema, Origin::class);

            if (
                $resource->getMainOrigin()->isStreamingOrigin()
                !== $this->isStreamingOrigin($originSchema->url->host)
            ) {
                throw ResourceOriginMisconfigured::streamImmutability();
            }
        } else {
            if (
                $resource->getMainOrigin()->isStreamingOrigin()
                !== $this->isStreamingOrigin($editedResource->originUrl)
            ) {
                throw ResourceOriginMisconfigured::streamImmutability();
            }
        }
    }

    private function validateAllOriginsAreStreamingOrNotStreaming(Origins $origins): void
    {
        $originSchema = reset($origins->origins);
        Assert::isInstanceOf($originSchema, Origin::class);

        $firstOriginIsStreaming = $this->isStreamingOrigin($originSchema->url->host);

        foreach ($origins->origins as $origin) {
            if ($firstOriginIsStreaming !== $this->isStreamingOrigin($origin->url->host)) {
                throw ResourceOriginMisconfigured::streamAndNotStreamMixed();
            }
        }
    }

    private function validateAllOriginsHaveDifferentPriority(Origins $origins): void
    {
        $priorities = [];
        foreach ($origins->origins as $origin) {
            if (in_array($origin->priority, $priorities, true)) {
                throw ResourceOriginMisconfigured::priorityNotUnique();
            }

            $priorities[] = $origin->priority;
        }
    }

    private function validateOnePriorityIsMain(Origins $origins): void
    {
        $count = 0;
        foreach ($origins->origins as $origin) {
            if ($origin->priority !== ResourceOrigin::MAIN_PRIORITY) {
                continue;
            }

            $count++;
        }

        if ($count > 1) {
            throw ResourceOriginMisconfigured::multipleMainPriorities();
        }

        if ($count === 0) {
            throw ResourceOriginMisconfigured::mainPriorityNotExists();
        }
    }

    private function validateOrigin(ResourceOrigin $origin): void
    {
        $violations = $this->validator->validate(
            $origin,
            [new Cdn77CephRgwOriginHost()],
        );

        if ($violations->count() === 0) {
            return;
        }

        throw new ResourceValidationFailed($violations);
    }

    private function isStreamingOrigin(string $host): bool
    {
        return preg_match(ResourceOrigin::PATTERN_STREAMING_HOST, $host) === 1;
    }
}
