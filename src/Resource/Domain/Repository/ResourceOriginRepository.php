<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Repository;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;

interface ResourceOriginRepository
{
    public function add(ResourceOrigin $resourceOrigin): void;

    public function remove(ResourceOrigin $resourceOrigin): void;

    public function removeAllForResource(CdnResource $resource): void;
}
