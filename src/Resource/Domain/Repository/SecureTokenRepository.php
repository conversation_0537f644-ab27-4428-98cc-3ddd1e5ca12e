<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Repository;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\SecureToken;

interface SecureTokenRepository
{
    public function add(SecureToken $secureToken): void;

    public function remove(SecureToken $secureToken): void;

    public function findForResource(CdnResource $resource): SecureToken|null;
}
