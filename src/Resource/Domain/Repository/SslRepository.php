<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Repository;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;

interface SslRepository
{
    public function add(Ssl $ssl): void;

    public function findForResource(CdnResource $resource): Ssl|null;

    /**
     * @param list<int> $resourceIds
     *
     * @return list<Ssl>
     */
    public function findForResources(array $resourceIds): array;
}
