<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Infrastructure\Repository;

use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\SecureToken;
use Cdn77\NxgApi\Resource\Domain\Repository\SecureTokenRepository;
use Webmozart\Assert\Assert;

final class DoctrineSecureTokenRepository implements SecureTokenRepository
{
    use EntityManagerConstructor;

    public function add(SecureToken $secureToken): void
    {
        $this->entityManager->persist($secureToken);
    }

    public function remove(SecureToken $secureToken): void
    {
        $this->entityManager->remove($secureToken);
    }

    public function findForResource(CdnResource $resource): SecureToken|null
    {
        $secureToken = $this->entityManager->createQueryBuilder()
            ->select('st')
            ->from(SecureToken::class, 'st')
            ->where('st.resource = :resource')
            ->setParameter('resource', $resource)
            ->getQuery()
            ->getOneOrNullResult();

        Assert::nullOrIsInstanceOf($secureToken, SecureToken::class);

        return $secureToken;
    }
}
