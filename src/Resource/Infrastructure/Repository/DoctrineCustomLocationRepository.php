<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Infrastructure\Repository;

use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\Entity\Legacy\CustomLocation;
use Cdn77\NxgApi\Resource\Domain\Repository\CustomLocationRepository;

class DoctrineCustomLocationRepository implements CustomLocationRepository
{
    use EntityManagerConstructor;

    public function hasResourceCustomLocation(int $resourceId): bool
    {
        return (bool) $this->entityManager->createQueryBuilder()
            ->select('COUNT(1) cnt')
            ->from(CustomLocation::class, 'c')
            ->join('c.resource', 'r')
            ->where('r.id = :resourceId')
            ->setParameter('resourceId', $resourceId)
            ->getQuery()
            ->getSingleScalarResult();
    }
}
