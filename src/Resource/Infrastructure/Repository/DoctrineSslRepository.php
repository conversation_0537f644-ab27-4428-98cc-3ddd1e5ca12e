<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Infrastructure\Repository;

use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Resource\Domain\Repository\SslRepository;
use Webmozart\Assert\Assert;

final class DoctrineSslRepository implements SslRepository
{
    use EntityManagerConstructor;

    public function add(Ssl $ssl): void
    {
        $this->entityManager->persist($ssl);
    }

    public function findForResource(CdnResource $resource): Ssl|null
    {
        $ssl = $this->entityManager->createQueryBuilder()
            ->select('ssl')
            ->from(Ssl::class, 'ssl')
            ->where('ssl.resource = :resource')
            ->setParameter('resource', $resource)
            ->getQuery()
            ->getOneOrNullResult();

        Assert::nullOrIsInstanceOf($ssl, Ssl::class);

        return $ssl;
    }

    /** @inheritDoc */
    public function findForResources(array $resourceIds): array
    {
        return $this->entityManager->createQueryBuilder()
            ->select('ssl')
            ->from(Ssl::class, 'ssl')
            ->where('ssl.resource IN (:resourceIds)')
            ->setParameter('resourceIds', $resourceIds)
            ->getQuery()
            ->getResult();
    }
}
