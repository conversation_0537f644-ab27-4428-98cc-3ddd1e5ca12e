<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Infrastructure\Repository;

use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\ResourceOrigin;
use Cdn77\NxgApi\Resource\Domain\Repository\ResourceOriginRepository;

final class DoctrineResourceOriginRepository implements ResourceOriginRepository
{
    use EntityManagerConstructor;

    public function add(ResourceOrigin $resourceOrigin): void
    {
        $this->entityManager->persist($resourceOrigin);
    }

    public function remove(ResourceOrigin $resourceOrigin): void
    {
        $this->entityManager->remove($resourceOrigin);
    }

    public function removeAllForResource(CdnResource $resource): void
    {
        $this->entityManager->createQueryBuilder()
            ->delete(ResourceOrigin::class, 'ro')
            ->where('ro.resource = :resource')
            ->setParameter('resource', $resource)
            ->getQuery()
            ->execute();
    }
}
