<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Infrastructure\Finder;

use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Resource\Domain\Exception\FailedToGetCertificate;
use Cdn77\NxgApi\Resource\Domain\Finder\MainCertificateFinder;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\NamingStrategy;
use League\Flysystem\FilesystemOperator;
use Throwable;

use function max;
use function Safe\preg_match;
use function sprintf;

final class FilesystemMainCertificateFinder implements MainCertificateFinder
{
    public const string MAIN_FILENAME = '0000000001';
    private const string MAIN_FILENAME_PATTERN = '/^' . self::MAIN_FILENAME . '_(\d+)\.(pem|key)$/';

    public function __construct(private NamingStrategy $namingStrategy, private FilesystemOperator $filesystem)
    {
    }

    public function findLast(): CertificatePair
    {
        $files = $this->filesystem->listContents('')->toArray();

        $maxIndex = 0;
        foreach ($files as $file) {
            if (preg_match(self::MAIN_FILENAME_PATTERN, $file['path'], $matches) === 0) {
                continue;
            }

            $maxIndex = max($maxIndex, (int) $matches[1]);
        }

        if ($maxIndex === 0) {
            throw FailedToGetCertificate::mainNotFound();
        }

        $pem = sprintf('%s_%d.%s', self::MAIN_FILENAME, $maxIndex, $this->namingStrategy->getCertificateExtension());
        $key = sprintf('%s_%d.%s', self::MAIN_FILENAME, $maxIndex, $this->namingStrategy->getKeyExtension());

        if (! $this->filesystem->fileExists($pem) || ! $this->filesystem->fileExists($key)) {
            throw FailedToGetCertificate::incompleteMainPair($maxIndex);
        }

        try {
            $certificate = $this->filesystem->read($pem);
            $key = $this->filesystem->read($key);
        } catch (Throwable) {
            throw FailedToGetCertificate::unreadableMainPair($maxIndex);
        }

        return new CertificatePair($certificate, $key);
    }
}
