<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Infrastructure\Finder;

use Cdn77\NxgApi\Core\Infrastructure\DbalConnectionConstructor;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Resource\Domain\Finder\SslFileIndexFinder;
use Webmozart\Assert\Assert;

final class DbalSslFileIndexFinder implements SslFileIndexFinder
{
    use DbalConnectionConstructor;

    public function getNextForSsl(Ssl $ssl): int
    {
        $currentIndex = $this->connection->fetchOne(
            <<<'PSQL'
            SELECT COALESCE(MAX(ssl_file.index), 0) i
            FROM ssl_file
            WHERE ssl_file.resource_id = :resourceId
            PSQL
            ,
            [
                'resourceId' => $ssl->getResource()->getId(),
            ],
        );

        Assert::integer($currentIndex);

        return $currentIndex + 1;
    }
}
