<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Infrastructure\Finder;

use Cdn77\NxgApi\Core\Infrastructure\DbalConnectionConstructor;
use Cdn77\NxgApi\Resource\Domain\DTO\AccountResources;
use Cdn77\NxgApi\Resource\Domain\Finder\AccountResourcesFinder;

use function array_map;
use function Safe\json_decode;

final class DbalAccountResourcesFinder implements AccountResourcesFinder
{
    use DbalConnectionConstructor;

    /** @inheritDoc */
    public function findAllActive(): array
    {
        /** @var list<array{account_id: int, resource_ids: string}> $result */
        $result = $this->connection->fetchAllAssociative(
            <<<'PSQL'
            SELECT account_id, ARRAY_TO_JSON(ARRAY_AGG(id)) AS resource_ids
            FROM resource
            WHERE deleted IS NULL AND suspended IS NULL
            GROUP BY account_id
            PSQL,
        );

        return array_map(
            static fn (array $row) => new AccountResources($row['account_id'], json_decode($row['resource_ids'], true)),
            $result,
        );
    }
}
