<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Infrastructure\Finder;

use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Filter\Resource\DeletedResourceFilter;
use Cdn77\NxgApi\Resource\Domain\Finder\ResourcesForPermanentRemoveFinder;

use function array_column;
use function array_map;
use function Safe\date;
use function Safe\strtotime;

final class DoctrineResourcesForPermanentRemoveFinder implements ResourcesForPermanentRemoveFinder
{
    use EntityManagerConstructor;

    /** @inheritDoc */
    public function findIds(int $limit): array
    {
        $filters = $this->entityManager->getFilters();

        $filters->disable(DeletedResourceFilter::NAME);

        $result = $this->entityManager->createQueryBuilder()
            ->select('r.id')
            ->from(CdnResource::class, 'r')
            ->where('r.deleted < :deleted')
            ->setParameter('deleted', date('Y-m-d H:i:s', strtotime('-30 days')))
            ->orderBy('r.deleted')
            ->getQuery()
            ->setMaxResults($limit)
            ->getScalarResult();

        $filters->enable(DeletedResourceFilter::NAME);

        return array_map('intval', array_column($result, 'id'));
    }
}
