<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Console\Payload;

use Cdn77\NxgApi\Resource\Domain\DTO\AccountResources;
use JMS\Serializer\Annotation as Serializer;

final class AccountResourcesSchema
{
    public int $accountId;

    /**
     * @var list<int>
     * @Serializer\Type("array<int>")
     */
    public array $resourceIds;

    /** @param list<int> $resourceIds */
    public function __construct(int $accountId, array $resourceIds)
    {
        $this->accountId = $accountId;
        $this->resourceIds = $resourceIds;
    }

    public static function fromDto(AccountResources $dto): self
    {
        return new self($dto->accountId, $dto->resourceIds);
    }
}
