<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Console;

use Cdn77\MonMasterDetector\MasterStateDetectorInterface;
use Cdn77\NxgApi\CoreLibrary\Kafka\ConfigFactory;
use Cdn77\NxgApi\Resource\Console\Payload\AccountResourcesSchema;
use Cdn77\NxgApi\Resource\Domain\Finder\AccountResourcesFinder;
use JMS\Serializer\SerializerInterface;
use SimPod\Kafka\Clients\Producer\KafkaProducer;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class AccountResourcesExporterCommand extends Command
{
    public const NAME = 'kafka:export-account-resources';

    private const KAFKA_TOPIC_NAME = 'customer_resources';

    private AccountResourcesFinder $accountResourcesFinder;

    private ConfigFactory $configFactory;

    private MasterStateDetectorInterface $masterStateDetector;

    private SerializerInterface $serializer;

    public function __construct(
        AccountResourcesFinder $accountResourcesFinder,
        ConfigFactory $configFactory,
        MasterStateDetectorInterface $masterStateDetector,
        SerializerInterface $serializer,
    ) {
        $this->accountResourcesFinder = $accountResourcesFinder;
        $this->configFactory = $configFactory;
        $this->masterStateDetector = $masterStateDetector;
        $this->serializer = $serializer;

        parent::__construct();
    }

    protected function configure(): void
    {
        parent::configure();

        $this->setName(self::NAME);
        $this->setDescription('Export customer resources to kafka');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (! $this->masterStateDetector->getState()->isMaster()) {
            $output->writeln('I am not master. Exiting.');

            return 0;
        }

        $accountsResources = $this->accountResourcesFinder->findAllActive();
        $config = $this->configFactory->createProducerConfig();
        $producer = new KafkaProducer($config);

        foreach ($accountsResources as $accountResources) {
            $producer->produce(
                self::KAFKA_TOPIC_NAME,
                null,
                $this->serializer->serialize(AccountResourcesSchema::fromDto($accountResources), 'json'),
                (string) $accountResources->accountId,
            );
        }

        $producer->flushMessages();

        return 0;
    }
}
