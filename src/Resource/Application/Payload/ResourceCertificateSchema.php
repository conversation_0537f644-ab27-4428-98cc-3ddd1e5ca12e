<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

final class ResourceCertificateSchema
{
    public const FIELD_CERTIFICATE = 'certificate';
    public const FIELD_KEY = 'key';

    public string $certificate;
    public string $key;

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                self::FIELD_CERTIFICATE => new Schema([
                    'description' => 'Certificate',
                    'type' => Type::STRING,
                    'nullable' => false,
                ]),
                self::FIELD_KEY => new Schema([
                    'description' => 'Private key of certificate',
                    'type' => Type::STRING,
                    'nullable' => false,
                ]),
            ],
        ]);
    }

    private function __construct(string $certificate, string $key)
    {
        $this->certificate = $certificate;
        $this->key = $key;
    }

    public static function fromCertificatePair(CertificatePair $certificatePair): self
    {
        return new self(
            $certificatePair->getCertificate(),
            $certificatePair->getPrivateKey(),
        );
    }
}
