<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Resource\Domain\DTO\ResourceDatacenterLocation;

final class DatacenterSchema
{
    public string $cityId;

    public string $cityCode;

    public bool $isEnabled;

    public function __construct(string $cityId, string $cityCode, bool $isEnabled)
    {
        $this->cityId = $cityId;
        $this->cityCode = $cityCode;
        $this->isEnabled = $isEnabled;
    }

    public static function fromResourceDatacenterLocation(ResourceDatacenterLocation $resourceDatacenterLocation): self
    {
        return new self(
            $resourceDatacenterLocation->city,
            $resourceDatacenterLocation->cityCode,
            $resourceDatacenterLocation->isEnabled,
        );
    }
}
