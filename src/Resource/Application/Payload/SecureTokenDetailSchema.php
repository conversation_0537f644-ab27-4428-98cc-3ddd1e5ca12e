<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Entity\Legacy\SecureToken;
use Symfony\Component\Validator\Constraints as Assert;

class SecureTokenDetailSchema
{
    /** @Assert\NotBlank */
    public string $secureTokenType;

    /** @Assert\NotBlank */
    public string $secureTokenValue;

    public string|null $secureLinkExpiryParam = null;

    public string|null $secureLinkTokenParam = null;

    public string|null $secureLinkPathlenParam = null;

    public string|null $secureLinkSecretParam = null;

    public bool $secureLinkRewritePlaylist = false;

    public function __construct(
        string $secureTokenType,
        string $secureTokenValue,
        string|null $secureLinkExpiryParam,
        string|null $secureLinkTokenParam,
        string|null $secureLinkPathlenParam,
        string|null $secureLinkSecretParam,
        bool $secureLinkRewritePlaylist,
    ) {
        $this->secureTokenType = $secureTokenType;
        $this->secureTokenValue = $secureTokenValue;
        $this->secureLinkExpiryParam = $secureLinkExpiryParam;
        $this->secureLinkTokenParam = $secureLinkTokenParam;
        $this->secureLinkPathlenParam = $secureLinkPathlenParam;
        $this->secureLinkSecretParam = $secureLinkSecretParam;
        $this->secureLinkRewritePlaylist = $secureLinkRewritePlaylist;
    }

    public static function fromSecureToken(SecureToken $secureToken): SecureTokenDetailSchema
    {
        return new self(
            $secureToken->getType(),
            $secureToken->getValue(),
            $secureToken->getSecureLinkExpiryParam(),
            $secureToken->getSecureLinkTokenParam(),
            $secureToken->getSecureLinkPathlenParam(),
            $secureToken->getSecureLinkSecretParam(),
            $secureToken->hasSecureLinkRewritePlaylist(),
        );
    }
}
