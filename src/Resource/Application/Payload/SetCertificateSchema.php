<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\Schema;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

final class SetCertificateSchema implements Schema
{
    public const FIELD_CERTIFICATE = 'certificate';
    public const FIELD_PRIVATE_KEY = 'private_key';
    public const FIELD_RESOURCE_IDS = 'resource_ids';

    /** @Assert\NotNull */
    public string|null $certificate = null;

    /** @Assert\NotNull */
    public string|null $privateKey = null;

    /**
     * @Serializer\Type("array<int>")
     * @var list<int>
     */
    public array|null $resourceIds = null;

    private function __construct()
    {
    }
}
