<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Resource\Application\Payload\Cache\CacheSchema;
use Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeadersSchema;
use Cdn77\NxgApi\Resource\Domain\Value\S3ConnectionInfo;
use Cdn77\NxgApi\Validator\Constraints as CustomAssert;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Ramsey\Uuid\UuidInterface;
use Symfony\Component\Validator\Constraints as Assert;

class ResourceEditInfo implements ResourceInfo
{
    public const MAX_ORIGIN_BASEDIR_LENGTH = 255;

    public const FIELD_CACHE_EXPIRY = 'cache_expiry';
    public const FIELD_CACHE_EXPIRY_404 = 'cache_expiry_404';
    public const FIELD_CACHE_CONTENT_LENGTH_LIMIT = 'cache_content_length_limit';
    public const FIELD_CACHE_CONTENT_LOCK_AGE = 'cache_lock_age';
    public const FIELD_CACHE_CONTENT_LOCK_TIMEOUT = 'cache_lock_timeout';
    public const FIELD_INSTANT_SSL = 'instant_ssl';
    public const FIELD_ORIGIN_URL = 'origin_url';
    public const FIELD_ORIGIN_BASEDIR = 'origin_basedir';
    public const FIELD_S3_BUCKET_NAME = 's3_bucket_name';
    public const FIELD_S3_TYPE = 's3_type';
    public const FIELD_SSL_VERIFY_DISABLE = 'ssl_verify_disable';
    public const FIELD_RESPONSE_HEADERS = 'response_headers';

    /**
     * @Serializer\SerializedName("account_id")
     * @Serializer\Type("intStrict")
     * @CustomAssert\IntStrict
     */
    public int|null $accountId = null;

    /**
     * @Serializer\Type("array<string>")
     * @Assert\All({
     *     @CustomAssert\Resource\Domain,
     *     @CustomAssert\Resource\AllowedCnameDomain,
     *     @Assert\Length(max=64, maxMessage="Maximum allowed CNAME length is 64 characters.")
     * })
     * @var string[]|null
     */
    public array|null $cnames = null;

    /** @Assert\Choice(choices = {"HTTP_PULL"}, message = "Only HTTP_PULL is supported now.", strict=true) */
    public string|null $resourceType = null;

    /**
     * @CustomAssert\Resource\AllowedOriginDomain
     * @CustomAssert\Any(
     *     message="%value% is not a valid origin url or IP.",
     *     {
     *         @CustomAssert\Resource\OriginUrl,
     *         @Assert\Ip(version="4_public")
     *     }
     * )
     * @Assert\NotNull
     */
    public string|null $originUrl = null;

    /**
     * @Serializer\SerializedName("clap_origin_id")
     * @Serializer\Type("uuid")
     */
    public UuidInterface|null $clapOriginId = null;

    /** @Assert\Choice({"http", "https"}) */
    public string $originScheme = 'http';

    /**
     * @CustomAssert\Any(
     *     message="Origin port should be either in range 1-65535 or 0 to disable.",
     *     {
     *         @Assert\EqualTo(0),
     *         @Assert\Range(min=1, max=65535)
     *     }
     * )
     * @Assert\NotNull
     * @var int<0, 65535>|null
     */
    public int|null $originPort = null;

    /**
     * @Serializer\Type("array<int>")
     * @Assert\Count(min="1", max ="1", exactMessage = "Expected to contain only {{limit}} group.")
     * @CustomAssert\Resource\LocationGroup
     * @var int[]|null
     */
    public array|null $groupId = null;

    #[Serializer\Inline]
    public CacheSchema|null $cache = null;

    /** @Assert\Choice(choices = {0, 1}, message = "Allowed values: 0, 1.", strict=true) */
    public int|null $disableQueryString = null;

    /** @Assert\Choice(choices = {0, 1}, message = "Allowed values: 0, 1.", strict=true) */
    public int|null $ignoreSetCookie = null;

    /**
     * @Serializer\SerializedName("mp4_pseudo_streaming")
     * @Assert\Choice(choices = {0, 1}, message = "Allowed values: 0, 1.", strict=true)
     * @CustomAssert\Resource\Mp4
     */
    public int|null $mp4PseudoStreaming = null;

    public int|null $instantSsl = null;

    /**
     * @Serializer\Type("array<string>")
     * @Assert\Count(max=100, maxMessage="You may only use up to 100 ignored query parameters.")
     * @Assert\All({
     *     @Assert\Length(max=40, maxMessage="Maximum length is 40 characters."),
     *     @CustomAssert\Resource\QueryParameterFormat(message="Parameter is in invalid format.")
     * })
     * @CustomAssert\UniqueSet(
     *     caseSensitive=false,
     *     duplicateFoundMessage="Ignored query parameters must be unique (case-insensitive)."
     * )
     * @var string[]|null
     */
    public array|null $ignoredQueryParams = null;

    /** @Assert\Choice({0, 301, 302}, message="Allowed values: 301, 302 or 0 to disable.", strict=true) */
    public int|null $httpsRedirectCode = null;

    /**
     * @Serializer\SerializedName("origin_basedir")
     * @Serializer\Type("stringStrict")
     * @CustomAssert\Resource\OriginBaseDir
     * @CustomAssert\StringStrict
     * @Assert\Length(max=255)
     */
    public string|null $originBasedir = null;

    /**
     * @Assert\Choice({0, 1}, message="Allowed values: 0, 1.", strict=true)
     * @Assert\NotNull
     */
    public int|null $forwardHostHeader = null;

    /** @Assert\Choice({0, 1}, message="Allowed values: 0, 1.", strict=true) */
    public int|null $streamingPlaylistBypass = null;

    /**
     * @CustomAssert\Any(
     *     message="Origin timeout should be either in range 1-120 or 0 to disable.",
     *     {
     *         @Assert\EqualTo(0),
     *         @Assert\Range(min=1, max=120)
     *     }
     * )
     * @var int<0, 120>
     */
    public int $originTimeout = 0;

    /**
     * @Serializer\Type("array")
     * @var mixed[]|null
     */
    public array|null $customData = null;

    /** @Assert\Choice(choices = {0, 1}, message = "Allowed values: 0, 1.", strict=true) */
    public int|null $waf = null;

    /** @Assert\Choice(choices = {0, 1}, message = "Allowed values: 0, 1.", strict=true) */
    public int|null $quic = null;

    /** @Assert\Choice(choices = {0, 1}, message = "Allowed values: 0, 1.", strict=true) */
    public int|null $corsOriginHeader = null;

    public bool|null $corsTimingEnabled = null;

    public bool|null $corsWildcardEnabled = null;

    /** @Serializer\SerializedName("aws_access_key_id") */
    public string|null $awsAccessKeyId = null;

    /** @Serializer\SerializedName("aws_secret") */
    public string|null $awsSecret = null;

    /** @Serializer\SerializedName("aws_region") */
    public string|null $awsRegion = null;

    /**
     * @Serializer\SerializedName("s3_bucket_name")
     * @Assert\Length(min="3", max="63")
     */
    public string|null $s3BucketName = null;

    /**
     * @Serializer\SerializedName("s3_type")
     * @Assert\Choice(
     *     {"cdn77-ceph-rgw", "external-s3"},
     *     message="Allowed values: cdn77-ceph-rgw, external-s3.",
     *     strict=true
     * )
     */
    public string|null $s3Type = null;

    /**
     * @Serializer\SerializedName("ssl_verify_disable")
     * @Serializer\Type("boolStrict")
     * @CustomAssert\Boolean
     */
    public bool|null $sslVerifyDisable = null;

    public bool|null $rateLimit = null;

    public bool|null $contentDispositionByParam = null;

    /**
     * @Serializer\SerializedName("origin_headers")
     * @Serializer\Type("array")
     * @CustomAssert\Resource\OriginHeaders
     * @Assert\NotNull
     * @var array<string, string>|null
     */
    public array|null $originHeaders = null;

    /**
     * @Serializer\SerializedName("response_headers")
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeadersSchema")
     * @CustomAssert\Resource\ResponseHeaders
     * @Assert\NotNull
     * @Assert\Valid
     */
    public ResponseHeadersSchema|null $responseHeaders = null;

    /**
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\SecureTokenSchema")
     * @Assert\Valid
     */
    public SecureTokenSchema|null $secureToken = null;

    /**
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\FollowRedirectSchema")
     * @Assert\Valid
     * @Assert\NotNull
     */
    public FollowRedirectSchema|null $followRedirect = null;

    public function s3Connection(): S3ConnectionInfo|null
    {
        return S3ConnectionInfo::fromSchema($this);
    }

    public function getGroupIdAsInt(): int|null
    {
        return $this->groupId !== null ? $this->groupId[0] : null;
    }
}
