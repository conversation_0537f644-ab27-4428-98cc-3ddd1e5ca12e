<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload\Ssl;

use Cdn77\NxgApi\Core\Application\Payload\Schema;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\Type;
use DateTimeImmutable;
use J<PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

final class ListFilterParams implements Schema
{
    public const PARAMETER_INCLUDE_SUSPENDED = 'includeSuspended';
    public const PARAMETER_TYPE = 'type';
    public const PARAMETER_EXPIRATION_BEFORE = 'expirationBefore';

    public const TYPE_INSTANT_SSL = 'instant_ssl';

    /** @Serializer\SerializedName(name="includeSuspended") */
    public bool $includeSuspended = false;

    /** @Assert\Choice(choices={SslFile::TYPE_CUSTOM, self::TYPE_INSTANT_SSL}, message="Invalid certificate type.") */
    public string|null $type = null;

    /** @Serializer\SerializedName(name="expirationBefore") */
    public DateTimeImmutable|null $expirationBefore = null;

    /** @return list<Parameter> */
    public static function getSchemaParameters(): array
    {
        return [
            new Parameter([
                'name' => self::PARAMETER_INCLUDE_SUSPENDED,
                'in' => 'query',
                'required' => false,
                'schema' => new \cebe\openapi\spec\Schema([
                    'type' => Type::BOOLEAN,
                    'description' => 'Whether to include suspended resources or not.',
                ]),
            ]),
            new Parameter([
                'name' => self::PARAMETER_TYPE,
                'in' => 'query',
                'required' => false,
                'schema' => [
                    'type' => Type::STRING,
                    'description' => 'Either "instant_ssl" or "custom".',
                ],
            ]),
            new Parameter([
                'name' => self::PARAMETER_EXPIRATION_BEFORE,
                'in' => 'query',
                'required' => false,
                'schema' => new \cebe\openapi\spec\Schema([
                    'type' => Type::STRING,
                    'description' => 'Expiration date before this date.',
                    'format' => 'datetime',
                ]),
            ]),
        ];
    }

    private function __construct()
    {
    }

    public function type(): string|null
    {
        return $this->type === self::TYPE_INSTANT_SSL ? SslFile::TYPE_LETSENCRYPT : $this->type;
    }
}
