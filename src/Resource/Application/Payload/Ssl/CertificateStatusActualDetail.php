<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload\Ssl;

use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Exception\InvalidArgument;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use DateTimeImmutable;
use J<PERSON>\Serializer\Annotation as Serializer;

use function implode;
use function in_array;
use function sprintf;

final class CertificateStatusActualDetail
{
    public const FIELD_DOMAINS = 'domains';
    public const FIELD_EXPIRES_AT = 'expires_at';
    public const FIELD_TYPE = 'type';

    public string $type;

    /** @Serializer\Type("DateTimeImmutable<'Y-m-d\TH:i:s\Z'>") */
    public DateTimeImmutable $expiresAt;

    /**
     * @Serializer\Type("array<string>")
     * @var array<string>
     */
    public array $domains;

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                self::FIELD_TYPE => new Schema(['type' => Type::STRING]),
                self::FIELD_EXPIRES_AT => new Schema(['type' => Type::STRING, 'format' => 'datetime']),
                self::FIELD_DOMAINS => new Schema([
                    'type' => Type::ARRAY,
                    'items' => new Schema(['type' => Type::STRING]),
                ]),
            ],
        ]);
    }

    /** @param string[] $domains */
    public function __construct(string $type, DateTimeImmutable $expiresAt, array $domains)
    {
        if (! in_array($type, SslFile::TYPES, true)) {
            throw new InvalidArgument(
                sprintf(
                    'Invalid certificate type given: %s. Only these are allowed: %s',
                    $type,
                    implode(',', SslFile::TYPES),
                ),
            );
        }

        $this->type = $type;
        $this->expiresAt = $expiresAt;
        $this->domains = $domains;
    }

    public static function fromSslFile(SslFile $file): self
    {
        return new CertificateStatusActualDetail(
            $file->getType(),
            $file->getExpiresAt(),
            $file->getDomains(),
        );
    }
}
