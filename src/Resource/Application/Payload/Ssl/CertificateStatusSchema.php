<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload\Ssl;

use Cdn77\NxgApi\Core\Application\Payload\QueryBusResultSchema;
use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Resource\Domain\Enum\CertificateStatus;
use Cdn77\NxgApi\Resource\Domain\Value\SslStatus;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use DateTimeImmutable;
use J<PERSON>\Serializer\Annotation as Serializer;
use Web<PERSON>zart\Assert\Assert;

final class CertificateStatusSchema implements QueryBusResultSchema
{
    public const FIELD_STATUS = 'status';
    public const FIELD_STATUS_DESCRIPTION = 'status_description';
    public const FIELD_LAST_CHANGED_AT = 'last_changed_at';
    public const FIELD_ACTUAL = 'actual';
    public const FIELD_REQUESTED = 'requested';

    public string $status;

    public string $statusDescription;

    /** @Serializer\Type("DateTimeImmutable<'Y-m-d\TH:i:s\Z'>") */
    public DateTimeImmutable|null $lastChangedAt;

    public CertificateStatusActualDetail|null $actual;

    public CertificateStatusRequestedDetail|null $requested;

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                self::FIELD_STATUS => new Schema(['type' => Type::STRING]),
                self::FIELD_STATUS_DESCRIPTION => new Schema(['type' => Type::STRING]),
                self::FIELD_LAST_CHANGED_AT => new Schema(['type' => Type::STRING, 'format' => 'datetime']),
                self::FIELD_ACTUAL => CertificateStatusActualDetail::getSchemaSpec(),
                self::FIELD_REQUESTED => CertificateStatusRequestedDetail::getSchemaSpec(),
            ],
        ]);
    }

    /** @inheritDoc */
    public static function fromQueryBusResult($result): QueryBusResultSchema
    {
        Assert::isInstanceOf($result, SslStatus::class);

        return self::fromSslStatus($result);
    }

    public function __construct(
        CertificateStatus $status,
        string $statusDescription,
        DateTimeImmutable|null $lastChangedAt,
        CertificateStatusActualDetail|null $active,
        CertificateStatusRequestedDetail|null $requested,
    ) {
        if (! $status->isGeneral()) {
            throw new InvalidArgument('Invalid certificate status given.');
        }

        $this->status = $status->getValue();
        $this->statusDescription = $statusDescription;
        $this->lastChangedAt = $lastChangedAt;
        $this->actual = $active;
        $this->requested = $requested;
    }

    public static function fromSslStatus(SslStatus $status): self
    {
        return new self(
            $status->status,
            $status->description,
            $status->currentStatus?->lastChangeAt,
            $status->currentStatus === null
                ? null
                : CertificateStatusActualDetail::fromSslFile($status->currentStatus->activeFile),
            $status->requestedStatus === null
                ? null
                : CertificateStatusRequestedDetail::fromCertificateRequestedStatus($status->requestedStatus),
        );
    }
}
