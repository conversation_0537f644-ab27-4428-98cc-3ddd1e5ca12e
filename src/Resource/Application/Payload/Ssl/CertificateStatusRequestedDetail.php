<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload\Ssl;

use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Resource\Domain\Enum\CertificateStatus;
use Cdn77\NxgApi\Resource\Domain\Enum\CertificateStatusDescription;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateRequestedStatus;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use DateTimeImmutable;
use J<PERSON>\Serializer\Annotation as Serializer;

use function in_array;

final class CertificateStatusRequestedDetail
{
    public const FIELD_DOMAINS = 'domains';
    public const FIELD_GENERATING_ERROR = 'generating_error';
    public const FIELD_LAST_CHANGE_AT = 'last_change_at';
    public const FIELD_REQUESTED_AT = 'requested_at';
    public const FIELD_STATUS = 'status';
    public const FIELD_STATUS_DESCRIPTION = 'status_description';
    public const FIELD_TYPE = 'type';
    public const FIELD_VALIDATION_ERROR = 'validation_error';

    public const ALLOWED_TYPES = [
        SslFile::TYPE_LETSENCRYPT,
    ];

    public string $type;

    public string $status;

    public string $statusDescription;

    /** @Serializer\Type("DateTimeImmutable<'Y-m-d\TH:i:s\Z'>") */
    public DateTimeImmutable|null $requestedAt;

    /** @Serializer\Type("DateTimeImmutable<'Y-m-d\TH:i:s\Z'>") */
    public DateTimeImmutable|null $lastChangeAt;

    /**
     * @Serializer\Type("array<string>")
     * @var array<string>
     */
    public array|null $domains;

    public string|null $validationError;

    public string|null $generatingError;

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                self::FIELD_TYPE => new Schema(['type' => Type::STRING]),
                self::FIELD_STATUS => new Schema(['type' => Type::STRING]),
                self::FIELD_STATUS_DESCRIPTION => new Schema(['type' => Type::STRING]),
                self::FIELD_REQUESTED_AT => new Schema(['type' => Type::STRING, 'format' => 'datetime']),
                self::FIELD_LAST_CHANGE_AT => new Schema(['type' => Type::STRING, 'format' => 'datetime']),
                self::FIELD_DOMAINS => new Schema([
                    'type' => Type::ARRAY,
                    'items' => new Schema(['type' => Type::STRING]),
                ]),
                self::FIELD_VALIDATION_ERROR => new Schema(['type' => Type::STRING]),
                self::FIELD_GENERATING_ERROR => new Schema(['type' => Type::STRING]),
            ],
        ]);
    }

    /** @param array<string> $domains */
    public function __construct(
        string $type,
        CertificateStatus $status,
        string $statusDescription,
        DateTimeImmutable|null $requestedAt,
        DateTimeImmutable|null $lastChangeAt,
        array|null $domains,
        string|null $validationError,
        string|null $error,
    ) {
        if (! in_array($type, self::ALLOWED_TYPES, true)) {
            throw new InvalidArgument('Invalid certificate type given.');
        }

        $this->type = $type;
        $this->statusDescription = $statusDescription;
        $this->status = $status->getValue();
        $this->requestedAt = $requestedAt;
        $this->lastChangeAt = $lastChangeAt;
        $this->domains = $domains;
        $this->validationError = $validationError;
        $this->generatingError = $error;
    }

    public static function fromCertificateRequestedStatus(CertificateRequestedStatus $status): self
    {
        return new self(
            $status->type,
            $status->status,
            CertificateStatusDescription::forStatus($status->status),
            $status->requestedAt,
            $status->lastChangeAt,
            $status->domains,
            $status->validationError,
            $status->generatingError,
        );
    }
}
