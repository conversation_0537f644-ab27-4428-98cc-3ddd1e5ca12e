<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\Schema;
use J<PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

final class ChangeLocationGroupSchema implements Schema
{
    /**
     * @Assert\NotNull
     * @Serializer\Type("integer")
     */
    private int|null $groupId;

    private function __construct()
    {
    }

    public function getGroupId(): int
    {
        \Webmozart\Assert\Assert::notNull($this->groupId);

        return $this->groupId;
    }
}
