<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Resource\Domain\Value\CustomCertificate;
use Cdn77\NxgApi\Resource\Domain\Value\EmptyCertificatePair;

final class ResourceSslSchema
{
    public const FIELD_CERTIFICATE = 'certificate';
    public const FIELD_KEY = 'key';

    public string|null $certificate = null;

    public string|null $key = null;

    public function toDto(): CustomCertificate
    {
        return $this->certificate === null && $this->key === null
            ? new EmptyCertificatePair()
            : CertificatePair::fromSchema($this);
    }
}
