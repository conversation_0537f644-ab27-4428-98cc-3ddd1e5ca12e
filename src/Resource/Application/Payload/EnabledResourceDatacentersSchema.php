<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use JMS\Serializer\Annotation as Serializer;
use Webmozart\Assert\Assert;

final class EnabledResourceDatacentersSchema
{
    /**
     * @Serializer\Type("array<string>")
     * @Serializer\Inline
     * @var list<string>
     */
    private array|null $resourceDatacenters = null;

    private function __construct()
    {
    }

    /** @return list<string> */
    public function getResourceDatacenters(): array
    {
        Assert::notNull($this->resourceDatacenters, 'Resource datacenters list is required.');

        return $this->resourceDatacenters;
    }
}
