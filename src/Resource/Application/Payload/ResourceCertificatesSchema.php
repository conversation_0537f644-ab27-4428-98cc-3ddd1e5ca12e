<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\QueryBusResultSchema;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use J<PERSON>\Serializer\Annotation as Serializer;
use Webmozart\Assert\Assert;

final class ResourceCertificatesSchema implements QueryBusResultSchema
{
    /**
     * @Serializer\Type("array<int, Cdn77\NxgApi\Resource\Application\Payload\ResourceCertificateSchema>")
     * @Serializer\Inline
     * @var array<int, ResourceCertificateSchema>
     */
    public array $certificatePairs;

    public static function getSchemaSpec(): Schema
    {
        return new Schema([
            'description' => 'Certificate and private key for resources',
            'type' => Type::OBJECT,
            'nullable' => true,
            'additionalProperties' => ResourceCertificateSchema::getSchemaSpec(),
        ]);
    }

    /** @param array<int, ResourceCertificateSchema> $certificatePairs */
    private function __construct(array $certificatePairs)
    {
        $this->certificatePairs = $certificatePairs;
    }

    /** @param mixed $result */
    public static function fromQueryBusResult($result): self
    {
        Assert::isArray($result);
        Assert::allIsInstanceOf($result, CertificatePair::class);

        $details = [];
        foreach ($result as $resourceId => $certificatePair) {
            $details[$resourceId] = ResourceCertificateSchema::fromCertificatePair($certificatePair);
        }

        return new self($details);
    }
}
