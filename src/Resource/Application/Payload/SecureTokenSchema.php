<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Validator\Constraints as CustomAssert;
use Symfony\Component\Validator\Constraints as Assert;

final class SecureTokenSchema
{
    public const FIELD_SECURE_TOKEN_TYPE = 'secure_token_type';
    public const FIELD_SECURE_TOKEN_VALUE = 'secure_token_value';
    public const FIELD_SECURE_LINK_EXPIRY_PARAM = 'secure_link_expiry_param';
    public const FIELD_SECURE_LINK_TOKEN_PARAM = 'secure_link_token_param';
    public const FIELD_SECURE_LINK_PATHLEN_PARAM = 'secure_link_pathlen_param';
    public const FIELD_SECURE_LINK_SECRET_PARAM = 'secure_link_secret_param';
    public const FIELD_SECURE_LINK_REWRITE_PLAYLIST = 'secure_link_rewrite_playlist';

    /**
     * @Assert\NotBlank
     * @Assert\Choice(
     *     {"none", "parameter", "path", "highwinds"}, strict=true,
     *     message="Secure token type must be either parameter, path or highwinds."
     * )
     */
    public string|null $secureTokenType;

    /**
     * @Assert\Type(type="string")
     * @CustomAssert\Resource\SecureToken
     */
    public string|null $secureTokenValue;

    /**
     * @Assert\NotNull
     * @Assert\Type(type="string")
     */
    public string|null $secureLinkExpiryParam;

    /**
     * @Assert\NotNull
     * @Assert\Type(type="string")
     */
    public string|null $secureLinkTokenParam;

    /**
     * @Assert\NotNull
     * @Assert\Type(type="string")
     */
    public string|null $secureLinkPathlenParam;

    /**
     * @Assert\NotNull
     * @Assert\Type(type="string")
     */
    public string|null $secureLinkSecretParam;

    /** @Assert\Type(type="bool") */
    public bool $secureLinkRewritePlaylist = false;

    public function __construct(
        string $secureTokenType,
        string $secureTokenValue,
        string $secureLinkExpiryParam,
        string $secureLinkTokenParam,
        string $secureLinkPathlenParam,
        string $secureLinkSecretParam,
        bool $secureLinkRewritePlaylist,
    ) {
        $this->secureTokenType = $secureTokenType;
        $this->secureTokenValue = $secureTokenValue;
        $this->secureLinkExpiryParam = $secureLinkExpiryParam;
        $this->secureLinkTokenParam = $secureLinkTokenParam;
        $this->secureLinkPathlenParam = $secureLinkPathlenParam;
        $this->secureLinkSecretParam = $secureLinkSecretParam;
        $this->secureLinkRewritePlaylist = $secureLinkRewritePlaylist;
    }
}
