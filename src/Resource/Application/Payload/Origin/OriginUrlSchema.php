<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload\Origin;

use Cdn77\NxgApi\Validator\Constraints as CustomAssert;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

final class OriginUrlSchema
{
    public const FIELD_SCHEME = 'scheme';
    public const FIELD_HOST = 'host';
    public const FIELD_BASEDIR = 'basedir';
    public const FIELD_PORT = 'port';

    /**
     * @Serializer\SerializedName("host")
     * @Assert\NotBlank(message="host should not be blank")
     * @CustomAssert\Resource\AllowedOriginDomain
     * @CustomAssert\Any(
     *     message="%value% is not a valid host or IP.",
     *     {
     *         @CustomAssert\Resource\OriginUrl,
     *         @Assert\Ip(version="4_public")
     *     }
     * )
     */
    public string $host;

    /** @Assert\Choice({"http", "https"}, strict=true) */
    public string $scheme;

    /**
     * @Serializer\SerializedName("basedir")
     * @Serializer\Type("stringStrict")
     * @CustomAssert\Resource\OriginBaseDir
     * @CustomAssert\StringStrict
     * @Assert\Length(max=255)
     */
    public string|null $basedir;

    /**
     * @Serializer\Type("intStrict")
     * @CustomAssert\Any(
     *     message="Origin port should be either in range 1-65535 or 0 to disable.",
     *     {
     *         @Assert\EqualTo(0),
     *         @Assert\Range(min=1, max=65535)
     *     }
     * )
     * @CustomAssert\IntStrict
     * @var int<0, 65535>|null
     */
    public int|null $port;
}
