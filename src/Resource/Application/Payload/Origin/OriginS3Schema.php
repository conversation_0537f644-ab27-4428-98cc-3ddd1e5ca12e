<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload\Origin;

use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

final class OriginS3Schema
{
    public const FIELD_ACCESS_KEY_ID = 'access_key_id';
    public const FIELD_SECRET = 'secret';
    public const FIELD_REGION = 'region';
    public const FIELD_BUCKET_NAME = 'bucket_name';
    public const FIELD_TYPE = 'type';

    /** @Serializer\SerializedName("access_key_id") */
    public string|null $accessKeyId;

    /** @Serializer\SerializedName("secret") */
    public string|null $secret;

    /** @Serializer\SerializedName("region") */
    public string|null $region;

    /**
     * @Serializer\SerializedName("bucket_name")
     * @Assert\Length(min="3", max="63")
     */
    public string|null $bucketName;

    /**
     * @Serializer\SerializedName("type")
     * @Assert\Choice(
     *     {"cdn77-ceph-rgw", "external-s3"},
     *     message="Allowed values: cdn77-ceph-rgw, external-s3.",
     *     strict=true
     * )
     */
    public string|null $type;
}
