<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload\Origin;

use JMS\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

final class OriginsSchema
{
    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginSchema>")
     * @Serializer\Inline
     * @Assert\Valid
     * @var array<OriginSchema>
     */
    public array $origins = [];
}
