<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload\Origin;

use Cdn77\NxgApi\Resource\Application\Payload\FollowRedirectSchema;
use Cdn77\NxgApi\Validator\Constraints as CustomAssert;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Ramsey\Uuid\UuidInterface;
use Symfony\Component\Validator\Constraints as Assert;

final class OriginSchema
{
    public const FIELD_URL = 'url';
    public const FIELD_PRIORITY = 'priority';
    public const FIELD_TIMEOUT = 'timeout';
    public const FIELD_FORWARD_HOST_HEADER = 'forward_host_header';
    public const FIELD_SSL_VERIFY_DISABLE = 'ssl_verify_disable';
    public const FIELD_S3 = 's3';
    public const FIELD_FOLLOW_REDIRECT = 'follow_redirect';
    public const FIELD_ORIGIN_HEADERS = 'origin_headers';


    /**
     * @Serializer\SerializedName("clap_origin_id")
     * @Serializer\Type("uuid")
     */
    public UuidInterface $clapOriginId;

    /**
     * @Serializer\Type("intStrict")
     * @Assert\Range(min=1, max=100)
     * @CustomAssert\IntStrict
     * @var int<1, 100>|null
     */
    public int|null $priority;

    /**
     * @Serializer\Type("intStrict")
     * @CustomAssert\Any(
     *     message="Origin timeout should be either in range 1-120 or 0 to disable.",
     *     {
     *         @Assert\EqualTo(0),
     *         @Assert\Range(min=1, max=120)
     *     }
     * )
     * @CustomAssert\IntStrict
     * @var int<0, 120>|null
     */
    public int|null $timeout;

    /**
     * @Serializer\SerializedName("forward_host_header")
     * @Serializer\Type("boolStrict")
     * @CustomAssert\Boolean
     */
    public bool|null $forwardHostHeader;

    /**
     * @Serializer\SerializedName("ssl_verify_disable")
     * @Serializer\Type("boolStrict")
     * @CustomAssert\Boolean
     */
    public bool|null $sslVerifyDisable;

    /**
     * @Serializer\SerializedName("origin_headers")
     * @Serializer\Type("arrayStrict")
     * @CustomAssert\Resource\OriginHeaders
     * @CustomAssert\ArrayStrict
     * @var array<string, string>
     */
    public array|null $originHeaders;

    /**
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginUrlSchema")
     * @Assert\Valid
     */
    public OriginUrlSchema $url;

    /**
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginS3Schema")
     * @Assert\Valid
     */
    public OriginS3Schema $s3;

    /**
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\FollowRedirectSchema")
     * @Assert\Valid
     * @Assert\NotNull
     */
    public FollowRedirectSchema $followRedirect;
}
