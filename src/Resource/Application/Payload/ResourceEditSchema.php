<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\Schema;
use Cdn77\NxgApi\GeoProtection\Application\Payload\GeoProtectionInfo;
use Cdn77\NxgApi\HotlinkProtection\Application\Payload\HotlinkProtectionInfo;
use Cdn77\NxgApi\IpProtection\Application\Payload\IpProtectionInfo;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginsSchema;
use Cdn77\NxgApi\Resource\Domain\DTO\EditedResource;
use Cdn77\NxgApi\Resource\Domain\Value\CustomCertificate;
use Cdn77\NxgApi\Validator\Constraints as CustomAssert;
use JMS\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

class ResourceEditSchema implements Schema
{
    public const FIELD_CDN_RESOURCE = 'cdn_resource';
    public const FIELD_GEO_PROTECTION = 'geo_protection';
    public const FIELD_IP_PROTECTION = 'ip_protection';
    public const FIELD_HOTLINK_PROTECTION = 'hotlink_protection';
    public const FIELD_SSL = 'ssl';

    /**
     * @Serializer\SerializedName("cdn_resource")
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo")
     * @Assert\NotBlank
     * @Assert\Valid
     */
    public ResourceEditInfo $cdnResource;

    /**
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginsSchema")
     * @Assert\Valid
     */
    public OriginsSchema|null $origins = null;

    /**
     * @Serializer\SerializedName("geo_protection")
     * @Serializer\Type("Cdn77\NxgApi\GeoProtection\Application\Payload\GeoProtectionInfo")
     * @Assert\Valid
     */
    private GeoProtectionInfo|null $geoProtection = null;

    /**
     * @Serializer\SerializedName("hotlink_protection")
     * @Serializer\Type("Cdn77\NxgApi\HotlinkProtection\Application\Payload\HotlinkProtectionInfo")
     * @Assert\Valid
     */
    private HotlinkProtectionInfo|null $hotlinkProtection = null;

    /**
     * @Serializer\SerializedName("ip_protection")
     * @Serializer\Type("Cdn77\NxgApi\IpProtection\Application\Payload\IpProtectionInfo")
     * @Assert\Valid
     */
    private IpProtectionInfo|null $ipProtection = null;

    /**
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\ResourceSslSchema")
     * @Assert\Valid
     * @CustomAssert\Resource\Ssl
     */
    private ResourceSslSchema|null $ssl = null;

    public function __construct(
        ResourceEditInfo $cdnResource,
        GeoProtectionInfo|null $geoProtection = null,
        HotlinkProtectionInfo|null $hotlinkProtection = null,
        IpProtectionInfo|null $ipProtection = null,
        ResourceSslSchema|null $ssl = null,
    ) {
        $this->cdnResource = $cdnResource;
        $this->geoProtection = $geoProtection;
        $this->hotlinkProtection = $hotlinkProtection;
        $this->ipProtection = $ipProtection;
        $this->ssl = $ssl;
    }

    public function resourceDto(): EditedResource
    {
        return EditedResource::fromSchema($this);
    }

    public function getGeoProtection(): GeoProtectionInfo|null
    {
        return $this->geoProtection;
    }

    public function getHotlinkProtection(): HotlinkProtectionInfo|null
    {
        return $this->hotlinkProtection;
    }

    public function getIpProtection(): IpProtectionInfo|null
    {
        return $this->ipProtection;
    }

    public function getCertificatePair(): CustomCertificate|null
    {
        return $this->ssl?->toDto();
    }
}
