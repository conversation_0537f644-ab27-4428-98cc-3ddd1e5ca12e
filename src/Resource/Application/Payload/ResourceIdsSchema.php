<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\Schema as OASchema;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

final class ResourceIdsSchema implements OASchema
{
    public const PARAMETER_RESOURCE_IDS = 'resourceIds';

    /**
     * @Serializer\SerializedName(self::PARAMETER_RESOURCE_IDS)
     * @Serializer\Type("array<int>")
     * @Assert\NotBlank(message="Query parameter 'resourceIds' is not specified.")
     * @var list<int>
     */
    public array $resourceIds = [];

    /** @return list<Parameter> */
    public static function getParametersSpec(): array
    {
        return [
            new Parameter([
                'name' => self::PARAMETER_RESOURCE_IDS,
                'in' => 'query',
                'required' => true,
                'schema' => new Schema(['type' => Type::ARRAY]),
            ]),
        ];
    }
}
