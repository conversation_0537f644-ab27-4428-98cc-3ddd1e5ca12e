<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload\Header;

use Cdn77\NxgApi\Resource\Domain\DTO\ResponseHeaders;
use J<PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

final class ResponseHeadersSchema
{
    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeaderSchema>")
     * @Serializer\Inline
     * @Assert\Valid
     * @var array<ResponseHeaderSchema>
     */
    public array $headers = [];

    /** @param array<ResponseHeaderSchema> $headers */
    public function __construct(array $headers)
    {
        $this->headers = $headers;
    }

    public static function fromResponseHeaders(ResponseHeaders $responseHeaders): self
    {
        $headers = [];
        foreach ($responseHeaders->toArray() as $name => $value) {
            $headers[] = new ResponseHeaderSchema($name, $value);
        }

        return new self($headers);
    }

    /** @param array<string, string> $data */
    public static function fromArray(array $data): self
    {
        $headers = [];
        foreach ($data as $name => $value) {
            $headers[] = new ResponseHeaderSchema($name, $value);
        }

        return new self($headers);
    }

    /** @return array<string, string> */
    public function toArray(): array
    {
        $headers = [];
        foreach ($this->headers as $header) {
            $headers[$header->name] = $header->value;
        }

        return $headers;
    }
}
