<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload\Header;

use J<PERSON>\Serializer\Annotation as Serializer;

final class ResponseHeaderSchema
{
    /**
     * @Serializer\Type("string")
     * @Serializer\SerializedName("name")
     */
    public string $name;

    /**
     * @Serializer\Type("string")
     * @Serializer\SerializedName("value")
     */
    public string $value;

    public function __construct(string $name, string $value)
    {
        $this->name = $name;
        $this->value = $value;
    }
}
