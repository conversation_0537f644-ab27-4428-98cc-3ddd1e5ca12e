<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload\Cache;

use Cdn77\NxgApi\Resource\Domain\DTO\Cache\CacheSettings;
use JMS\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

final class CacheSchema
{
    #[Assert\Range(
        notInRangeMessage: 'The cache_expiry must be between {{ min }} and {{ max }}.',
        min: 1,
        max: 3000000,
    )]
    public int|null $cacheExpiry = null;

    #[Serializer\SerializedName('cache_expiry_404')]
    #[Assert\Range(
        notInRangeMessage: 'The cache_expiry_404 must be between {{ min }} and {{ max }}.',
        min: 1,
        max: 1000000,
    )]
    public int|null $cacheExpiry404 = null;

    #[Assert\Range(
        notInRangeMessage: 'The cache_content_length_limit must be between {{ min }} and {{ max }}.',
        min: 1,
        max: 1000000000000, //?
    )]
    public int|null $cacheContentLengthLimit = null;

    #[Assert\Range(
        notInRangeMessage: 'The cache_min_uses must be between {{ min }} and {{ max }}.',
        min: 1,
        max: 1000, //?
    )]
    public int|null $cacheLockAge = null;

    #[Assert\Range(
        notInRangeMessage: 'The cache_lock_timeout must be between {{ min }} and {{ max }}.',
        min: 1,
        max: 50, //?
    )]
    public int|null $cacheLockTimeout = null;

    public function toDto(): CacheSettings
    {
        return new CacheSettings(
            $this->cacheExpiry,
            $this->cacheExpiry404,
            $this->cacheContentLengthLimit,
            $this->cacheLockAge,
            $this->cacheLockTimeout,
        );
    }
}
