<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeadersSchema;
use Cdn77\NxgApi\Resource\Domain\Value\S3ConnectionInfo;
use Cdn77\NxgApi\Validator\Constraints as CustomAssert;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Ramsey\Uuid\UuidInterface;
use Symfony\Component\Validator\Constraints as Assert;

class ResourceAddInfo implements ResourceInfo
{
    public const FIELD_ORIGIN_ID = 'origin_id';

    public const FIELD_ORIGIN_URL = 'origin_url';
    public const FIELD_ORIGIN_BASEDIR = 'origin_basedir';
    public const FIELD_S3_BUCKET_NAME = 's3_bucket_name';
    public const FIELD_S3_TYPE = 's3_type';
    public const FIELD_RATE_LIMIT = 'rate_limit';
    public const FIELD_CONTENT_DISPOSITION_BY_PARAM = 'content_disposition_by_param';
    public const FIELD_ORIGIN_HEADERS = 'origin_headers';
    public const FIELD_SECURE_TOKEN = 'secure_token';
    public const FIELD_FOLLOW_REDIRECT = 'follow_redirect';

    /** @Serializer\SerializedName("id") */
    public int|null $id = null;

    /** @Serializer\SerializedName("cdn_url") */
    public string|null $cdnUrl = null;

    /**
     * @Serializer\SerializedName("account_id")
     * @Serializer\Type("intStrict")
     * @CustomAssert\IntStrict
     */
    public int|null $accountId = null;

    /**
     * @Serializer\SerializedName("cnames")
     * @Serializer\Type("array<string>")
     * @Assert\All({
     *     @CustomAssert\Resource\Domain,
     *     @CustomAssert\Resource\AllowedCnameDomain,
     *     @Assert\Length(max=64, maxMessage="Maximum allowed CNAME length is 64 characters.")
     * })
     * @CustomAssert\Resource\UniqueCname
     * @var array<string>|null
     */
    public array|null $cnames = null;

    /**
     * @Serializer\SerializedName("origin_url")
     * @Assert\NotBlank(message="origin_url should not be blank")
     * @CustomAssert\Resource\AllowedOriginDomain
     * @CustomAssert\Any(
     *     message="%value% is not a valid origin url or IP.",
     *     {
     *         @CustomAssert\Resource\OriginUrl,
     *         @Assert\Ip(version="4_public")
     *     }
     * )
     */
    public string $originUrl;

    /**
     * @Serializer\SerializedName("clap_origin_id")
     * @Serializer\Type("uuid")
     */
    public UuidInterface|null $clapOriginId = null;

    /**
     * TODO: make mandatory
     *
     * @Assert\Choice({"http", "https"}, strict=true)
     */
    public string $originScheme = 'http';

    /**
     * @Assert\Range(min=0, max=65535)
     * @var int<0, 65535>
     */
    public int $originPort = 0;

    /**
     * @Serializer\SerializedName("group_id")
     * @Serializer\Type("array<int>")
     * @Assert\Count(min="1", max ="1", exactMessage = "Expected to contain only {{limit}} group.")
     * @CustomAssert\Resource\LocationGroup
     * @var array<int>|null
     */
    public array|null $groupId = null;

    /**
     * @Serializer\SerializedName("cache_expiry")
     * @Assert\Range(min=1, max=3000000, notInRangeMessage="The cacheExpiry must be between {{ min }} and {{ max }}.")
     * @Assert\NotBlank(message="cache_expiry should not be blank")
     */
    public int|null $cacheExpiry = null;

    /**
     * @Serializer\SerializedName("cache_expiry_404")
     * @Assert\Range(min=1, max=1000000, notInRangeMessage="The cacheExpiry404 must be between {{ min }} and {{ max }}")
     */
    public int|null $cacheExpiry404 = null;

    /**
     * @Serializer\SerializedName("disable_query_string")
     * @Assert\NotBlank(message="disable_query_string should not be blank")
     * @Assert\Choice(choices = {0, 1}, message = "Allowed values: 0, 1.", strict=true)
     */
    public int|null $disableQueryString = null;

    /**
     * @Serializer\SerializedName("ignore_set_cookie")
     * @Assert\NotBlank(message="ignore_set_cookie should not be blank")
     * @Assert\Choice(choices = {0, 1}, message = "Allowed values: 0, 1.", strict=true)
     */
    public int|null $ignoreSetCookie = null;

    /**
     * @Serializer\SerializedName("mp4_pseudo_streaming")
     * @Assert\NotBlank(message="mp4_pseudo_on should not be blank")
     * @Assert\Choice(choices = {0, 1}, message = "Allowed values: 0, 1.", strict=true)
     * @CustomAssert\Resource\Mp4
     */
    public int|null $mp4PseudoStreaming = null;

    /**
     * @Serializer\SerializedName("instant_ssl")
     * @Assert\NotNull
     * @Assert\Choice(choices = {0, 1}, message = "Allowed values: 0, 1.", strict=true)
     */
    public int|null $instantSsl = null;

    /**
     * @Serializer\SerializedName("ignored_query_params")
     * @Serializer\Type("array<string>")
     * @Assert\Count(max=100, maxMessage="You may only use up to 100 ignored query parameters.")
     * @Assert\All({
     *     @Assert\Length(max=40, maxMessage="Maximum length is 40 characters."),
     *     @CustomAssert\Resource\QueryParameterFormat(message="Parameter is in invalid format.")
     * })
     * @CustomAssert\UniqueSet(
     *     caseSensitive=false,
     *     duplicateFoundMessage="Ignored query parameters must be unique (case-insensitive)."
     * )
     * @var array<string>|null
     */
    public array|null $ignoredQueryParams = null;

    /**
     * @Serializer\SerializedName("https_redirect_code")
     * @Assert\Choice({0, 301, 302}, message="Allowed values: 301, 302 or 0 to disable.", strict=true)
     */
    public int|null $httpsRedirectCode = null;

    /**
     * @Serializer\SerializedName("origin_basedir")
     * @Serializer\Type("stringStrict")
     * @CustomAssert\Resource\OriginBaseDir
     * @CustomAssert\StringStrict
     * @Assert\Length(max=255)
     */
    public string|null $originBasedir = null;

    /**
     * @Serializer\SerializedName("forward_host_header")
     * @Assert\Choice({0, 1}, message="Allowed values: 0, 1.", strict=true)
     * @Assert\NotNull
     */
    public int|null $forwardHostHeader = null;

    /**
     * @Serializer\SerializedName("streaming_playlist_bypass")
     * @Assert\Choice({0, 1}, message="Allowed values: 0, 1.", strict=true)
     */
    public int|null $streamingPlaylistBypass = null;

    /**
     * @Serializer\SerializedName("origin_timeout")
     * @Assert\Range(min=0, max=120)
     * @var int<0, 120>
     */
    public int $originTimeout = 0;

    /**
     * @Serializer\SerializedName("custom_data")
     * @Serializer\Type("array")
     * @var array<mixed>|null
     */
    public array|null $customData = null;

    /**
     * @Serializer\SerializedName("waf")
     * @Assert\Choice({0, 1}, message="Allowed values: 0, 1.", strict=true)
     */
    public int|null $waf = null;

    /**
     * @Serializer\SerializedName("quic")
     * @Assert\Choice({0, 1}, message="Allowed values: 0, 1.", strict=true)
     */
    public int|null $quic = null;

    /**
     * @Serializer\SerializedName("cors_origin_header")
     * @Assert\Choice({0, 1}, message="Allowed values: 0, 1.", strict=true)
     */
    public int|null $corsOriginHeader = null;

    /** @Serializer\SerializedName("aws_access_key_id") */
    public string|null $awsAccessKeyId = null;

    /** @Serializer\SerializedName("aws_secret") */
    public string|null $awsSecret = null;

    /** @Serializer\SerializedName("aws_region") */
    public string|null $awsRegion = null;

    /**
     * @Serializer\SerializedName("s3_bucket_name")
     * @Assert\Length(min="3", max="63")
     */
    public string|null $s3BucketName = null;

    /**
     * @Serializer\SerializedName("s3_type")
     * @Assert\Choice(
     *     {"cdn77-ceph-rgw", "external-s3"},
     *     message="Allowed values: cdn77-ceph-rgw, external-s3.",
     *     strict=true
     * )
     */
    public string|null $s3Type = null;

    /**
     * @Serializer\SerializedName("ssl_verify_disable")
     * @Serializer\Type("boolStrict")
     * @CustomAssert\Boolean
     */
    public bool|null $sslVerifyDisable = null;

    public bool|null $rateLimit = null;

    public bool|null $contentDispositionByParam = null;

    /**
     * @Serializer\SerializedName("origin_headers")
     * @Serializer\Type("array")
     * @CustomAssert\Resource\OriginHeaders
     * @var array<string, string>|null
     */
    public array|null $originHeaders = null;

    /**
     * @Serializer\SerializedName("response_headers")
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\Header\ResponseHeadersSchema")
     * @CustomAssert\Resource\ResponseHeaders
     * @Assert\NotNull
     * @Assert\Valid
     */
    public ResponseHeadersSchema|null $responseHeaders = null;

    /**
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\SecureTokenSchema")
     * @Assert\Valid
     */
    public SecureTokenSchema|null $secureToken = null;

    /**
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\FollowRedirectSchema")
     * @Assert\Valid
     * @Assert\NotNull
     */
    public FollowRedirectSchema|null $followRedirect = null;

    public function s3Connection(): S3ConnectionInfo|null
    {
        return S3ConnectionInfo::fromSchema($this);
    }
}
