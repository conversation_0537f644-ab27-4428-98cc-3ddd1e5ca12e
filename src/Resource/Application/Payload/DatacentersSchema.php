<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Resource\Domain\DTO\ResourceDatacenterLocation;
use JMS\Serializer\Annotation as Serializer;

use function array_map;

final class DatacentersSchema
{
    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Resource\Application\Payload\DatacenterSchema>")
     * @Serializer\Inline
     * @var list<DatacenterSchema>
     */
    public array $datacenters;

    /** @param list<DatacenterSchema> $datacenters */
    public function __construct(array $datacenters)
    {
        $this->datacenters = $datacenters;
    }

    /** @param list<ResourceDatacenterLocation> $resourceLocations */
    public static function fromResourceDatacenterLocationsList(array $resourceLocations): self
    {
        return new self(
            array_map(
                static fn (
                    ResourceDatacenterLocation $resourceLocation,
                ): DatacenterSchema => DatacenterSchema::fromResourceDatacenterLocation($resourceLocation),
                $resourceLocations,
            ),
        );
    }
}
