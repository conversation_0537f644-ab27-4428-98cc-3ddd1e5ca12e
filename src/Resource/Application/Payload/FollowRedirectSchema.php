<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Validator\Constraints as CustomAssert;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use <PERSON>ymfony\Component\Validator\Constraints as Assert;

class FollowRedirectSchema
{
    public const FIELD_ENABLED = 'enabled';
    public const FIELD_CODES = 'codes';

    /**
     * @Serializer\SerializedName("enabled")
     * @Serializer\Type("boolStrict")
     * @CustomAssert\Boolean
     */
    public bool|null $enabled = null;

    /**
     * @Serializer\SerializedName("codes")
     * @Serializer\Type("array")
     * @CustomAssert\Resource\FollowRedirectCodes
     * @Assert\All({
     *     @Assert\Type(type="integer"),
     *     @Assert\Range(min=300, max=399)
     *  })
     * @Assert\NotNull
     * @var list<int>|null
     */
    public array|null $codes = null;

    /** @param list<int>|null $codes */
    public function __construct(bool $enabled, array|null $codes)
    {
        $this->enabled = $enabled;
        $this->codes = $codes;
    }
}
