<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\Schema;
use Cdn77\NxgApi\GeoProtection\Application\Payload\GeoProtectionInfo;
use Cdn77\NxgApi\HotlinkProtection\Application\Payload\HotlinkProtectionInfo;
use Cdn77\NxgApi\IpProtection\Application\Payload\IpProtectionInfo;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginsSchema;
use Cdn77\NxgApi\Resource\Domain\DTO\NewResource;
use Cdn77\NxgApi\Resource\Domain\Value\CustomCertificate;
use Cdn77\NxgApi\Validator\Constraints as CustomAssert;
use JMS\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

class ResourceAddSchema implements Schema
{
    public const FIELD_ORIGINS = 'origins';

    /**
     * @Serializer\SerializedName("cdn_resource")
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\ResourceAddInfo")
     * @Assert\NotBlank
     * @Assert\Valid
     */
    public ResourceAddInfo $cdnResource;

    /**
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginsSchema")
     * @Assert\Valid
     */
    public OriginsSchema|null $origins = null;

    /**
     * @Serializer\SerializedName("geo_protection")
     * @Serializer\Type("Cdn77\NxgApi\GeoProtection\Application\Payload\GeoProtectionInfo")
     * @Assert\Valid
     */
    public GeoProtectionInfo|null $geoProtection = null;

    /**
     * @Serializer\SerializedName("hotlink_protection")
     * @Serializer\Type("Cdn77\NxgApi\HotlinkProtection\Application\Payload\HotlinkProtectionInfo")
     * @Assert\Valid
     */
    public HotlinkProtectionInfo|null $hotlinkProtection = null;

    /**
     * @Serializer\SerializedName("ip_protection")
     * @Serializer\Type("Cdn77\NxgApi\IpProtection\Application\Payload\IpProtectionInfo")
     * @Assert\Valid
     */
    public IpProtectionInfo|null $ipProtection = null;

    /**
     * @Serializer\Type("Cdn77\NxgApi\Resource\Application\Payload\ResourceSslSchema")
     * @Assert\Valid
     * @CustomAssert\Resource\Ssl
     */
    public ResourceSslSchema|null $ssl = null;

    public function resourceDto(): NewResource
    {
        return NewResource::fromSchema($this);
    }

    public function certificatePair(): CustomCertificate|null
    {
        return $this->ssl?->toDto();
    }
}
