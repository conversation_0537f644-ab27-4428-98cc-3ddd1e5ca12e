<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\ServerStatusSchema;
use Cdn77\NxgApi\Schema\Internal\Resource\Servers\ResourceDetail;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

class ResourceServersSchema
{
    public ResourceDetail $resource;

    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Core\Application\Payload\ServerStatusSchema>")
     * @var list<ServerStatusSchema>
     */
    public array $servers;

    /** @param list<ServerStatusSchema> $servers */
    public function __construct(ResourceDetail $resource, array $servers)
    {
        $this->resource = $resource;
        $this->servers = $servers;
    }
}
