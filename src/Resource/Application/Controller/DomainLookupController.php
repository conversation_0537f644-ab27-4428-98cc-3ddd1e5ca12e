<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Schema\Legacy\Resources\ResourceDetail;
use Cdn77\NxgApi\Schema\Legacy\Resources\ResourceDetailInfo;
use Cdn77\NxgApi\Validator\Constraints\Resource\Domain;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Validator\ValidatorInterface;

use function sprintf;
use function str_replace;

class DomainLookupController
{
    public const LEGACY_ROUTE_NAME = 'legacy.resources.domain_lookup';
    public const ROUTE_NAME = 'resources.domain_lookup';

    private CdnResourceRepository $resourceRepository;

    private SerializerInterface $serializer;

    private ValidatorInterface $validator;

    public function __construct(
        CdnResourceRepository $resourceRepository,
        SerializerInterface $serializer,
        ValidatorInterface $validator,
    ) {
        $this->resourceRepository = $resourceRepository;
        $this->serializer = $serializer;
        $this->validator = $validator;
    }

    /**
     * @Route(
     *     path="/cdn_resources/domain-lookup/{domain}",
     *     name=self::LEGACY_ROUTE_NAME,
     *     methods={Request::METHOD_GET}
     * )
     * @Route(
     *     path="/resource/domain-lookup/{domain}",
     *     name=self::ROUTE_NAME,
     *     methods={Request::METHOD_GET}
     * )
     */
    public function lookupAction(string $domain): JsonResponse
    {
        $domain = str_replace('.json', '', $domain);
        if (! $this->isDomainValid($domain)) {
            return $this->createErrorResponse(
                Response::HTTP_BAD_REQUEST,
                sprintf('The domain "%s" is not in valid format.', $domain),
            );
        }

        $resource = $this->resourceRepository->findByDomain($domain);

        if ($resource === null) {
            return $this->createErrorResponse(
                Response::HTTP_NOT_FOUND,
                sprintf('No resource found matching domain "%s".', $domain),
            );
        }

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(new ResourceDetail(ResourceDetailInfo::fromResource($resource)), 'json'),
        );
    }

    private function isDomainValid(string $domain): bool
    {
        $violations = $this->validator->validate($domain, new Domain());

        return $violations->count() === 0;
    }

    private function createErrorResponse(int $code, string $message): JsonResponse
    {
        return JsonResponse::fromJsonString(
            $this->serializer->serialize(new ErrorsSchema([$message]), 'json'),
            $code,
        );
    }
}
