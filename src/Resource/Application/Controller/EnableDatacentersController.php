<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\Resource\Application\Payload\EnabledResourceDatacentersSchema;
use Cdn77\NxgApi\Resource\Domain\Command\EnableDatacentersForResource;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use JMS\Serializer\Exception\RuntimeException;
use JMS\Serializer\SerializerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;

final class EnableDatacentersController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'resource.datacenters';
    public const ROUTE_SUMMARY = 'Enable datacenters for Resource';

    private CommandBus $commandBus;

    private LoggerInterface $logger;

    private PathGenerator $pathGenerator;

    private SerializerInterface $serializer;

    public function __construct(
        CommandBus $commandBus,
        LoggerInterface $logger,
        PathGenerator $pathGenerator,
        SerializerInterface $serializer,
    ) {
        $this->commandBus = $commandBus;
        $this->logger = $logger;
        $this->pathGenerator = $pathGenerator;
        $this->serializer = $serializer;
    }

    /**
     * @Route(
     *     path="/resource/{resourceId}/datacenters",
     *     methods={Request::METHOD_PUT},
     *     name=EnableDatacentersController::ROUTE_NAME
     * )
     */
    public function execute(Request $request, int $resourceId): Response
    {
        try {
            $schema = $this->serializer->deserialize(
                $request->getContent(),
                EnabledResourceDatacentersSchema::class,
                'json',
            );
        } catch (RuntimeException $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema([$e->getMessage()]),
                    'json',
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY,
            );
        }

        try {
            $this->commandBus->handle(EnableDatacentersForResource::fromSchema($resourceId, $schema));
        } catch (Throwable $e) {
            $this->logger->error($e->getMessage(), ['exception' => $e]);

            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema(['Unknown error occurred.']),
                    'json',
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY,
            );
        }

        return new Response('', Response::HTTP_ACCEPTED);
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $put = new Operation([
            'tags' => [Tags::RESOURCE],
            'summary' => self::ROUTE_SUMMARY,
            'parameters' => [
                new Parameter([
                    'in' => 'path',
                    'name' => 'resourceId',
                    'required' => true,
                    'schema' => new Schema(['type' => Type::INTEGER]),
                ]),
            ],
            'responses' => new Responses([
                Response::HTTP_ACCEPTED => new \cebe\openapi\spec\Response(
                    ['description' => 'Datacenters updated'],
                ),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['put' => $put])];
    }
}
