<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Schema\Legacy\Resources\ResourceDetail;
use Cdn77\NxgApi\Schema\Legacy\Resources\ResourceDetailInfo;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class DetailController implements HasOpenApiPaths
{
    public const LEGACY_ROUTE_NAME = 'legacy.resource.detail';
    public const ROUTE_NAME = 'resource.detail';
    public const ROUTE_SUMMARY = 'Detail of Resource';

    private CdnResourceRepository $cdnResourceRepository;

    private SerializerInterface $serializer;

    private PathGenerator $pathGenerator;

    public function __construct(
        CdnResourceRepository $cdnResourceRepository,
        SerializerInterface $serializer,
        PathGenerator $pathGenerator,
    ) {
        $this->cdnResourceRepository = $cdnResourceRepository;
        $this->serializer = $serializer;
        $this->pathGenerator = $pathGenerator;
    }

    /**
     * @Route(path="/resource/{resourceId}", name=self::ROUTE_NAME, methods={Request::METHOD_GET})
     * @Route(path="/cdn_resources/{resourceId}.json", name=self::LEGACY_ROUTE_NAME, methods={Request::METHOD_GET})
     */
    public function detailAction(int $resourceId): Response
    {
        $cdnResource = $this->cdnResourceRepository->find($resourceId);

        if ($cdnResource === null) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(new ErrorsSchema(['Requested object could not be found.']), 'json'),
                Response::HTTP_NOT_FOUND,
            );
        }

        $resourceDetailInfo = ResourceDetailInfo::fromResource($cdnResource);

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(new ResourceDetail($resourceDetailInfo), 'json'),
        );
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $get = new Operation([
            'tags' => [Tags::RESOURCE],
            'summary' => self::ROUTE_SUMMARY,
            'parameters' => [
                new Parameter([
                    'in' => 'path',
                    'name' => 'resourceId',
                    'required' => true,
                    'schema' => new Schema(['type' => Type::INTEGER]),
                ]),
            ],
            'responses' => new Responses([
                Response::HTTP_OK => new \cebe\openapi\spec\Response([
                    'description' => 'Detail of the given Resource returned.',
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => new Schema(['type' => Type::OBJECT]),
                        ]),
                    ],
                ]),
                Response::HTTP_NOT_FOUND => new \cebe\openapi\spec\Response([
                    'description' => 'Resource not found.',
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => new Schema(['type' => Type::OBJECT]),
                        ]),
                    ],
                ]),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['get' => $get])];
    }
}
