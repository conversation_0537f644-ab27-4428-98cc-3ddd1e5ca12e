<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\Resource\Domain\Command\PurgeAll;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class PurgeController
{
    public const LEGACY_ROUTE_NAME = 'legacy.purge.all';
    public const ROUTE_NAME = 'purge.all';

    private CommandBus $commandBus;

    public function __construct(CommandBus $commandBus)
    {
        $this->commandBus = $commandBus;
    }

    /**
     * @Route(
     *     path="/cdn_resources/{resourceId}/purge_all.json",
     *     name=self::LEGACY_ROUTE_NAME,
     *     methods={Request::METHOD_PUT}
     * )
     * @Route(
     *     path="/resource/{resourceId}/purge-all",
     *     name=self::ROUTE_NAME,
     *     methods={Request::METHOD_PUT}
     * )
     */
    public function purgeAllAction(int $resourceId): Response
    {
        $this->commandBus->handle(new PurgeAll($resourceId));

        return new Response('', Response::HTTP_NO_CONTENT);
    }
}
