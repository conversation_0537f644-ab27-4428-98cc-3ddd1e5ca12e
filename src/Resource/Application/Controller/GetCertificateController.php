<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Application\Response\ControllerQueryHandler;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceCertificatesSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceIdsSchema;
use Cdn77\NxgApi\Resource\Domain\Query\FindAssignedCertificatePairsForResources;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class GetCertificateController implements HasOpenApiPaths
{
    public const ROUTE = '/resources/certificate';
    public const ROUTE_NAME = 'resources.get-certificate-and-key';

    private const ROUTE_SUMMARY = 'List certificate and key for multiple resources';

    private PathGenerator $pathGenerator;

    private ControllerQueryHandler $controllerQueryHandler;

    private ControllerSchemaSerializer $controllerSchemaSerializer;

    public function __construct(
        ControllerSchemaSerializer $controllerSchemaSerializer,
        ControllerQueryHandler $controllerQueryHandler,
        PathGenerator $pathGenerator,
    ) {
        $this->pathGenerator = $pathGenerator;
        $this->controllerQueryHandler = $controllerQueryHandler;
        $this->controllerSchemaSerializer = $controllerSchemaSerializer;
    }

    /** @Route(path=self::ROUTE, name=self::ROUTE_NAME, methods={Request::METHOD_GET}) */
    public function execute(Request $request): Response
    {
        $schema = $this->controllerSchemaSerializer->deserializeQueryString(
            $request,
            ResourceIdsSchema::class,
        );

        if ($schema instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse(
                $schema,
                Response::HTTP_BAD_REQUEST,
            );
        }

        return $this->controllerQueryHandler->handle(
            new FindAssignedCertificatePairsForResources($schema->resourceIds),
            ResourceCertificatesSchema::class,
            Response::HTTP_OK,
            true,
        );
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $get = new Operation([
            'tags' => [Tags::RESOURCE],
            'summary' => self::ROUTE_SUMMARY,
            'parameters' => ResourceIdsSchema::getParametersSpec(),
            'responses' => new Responses([
                Response::HTTP_OK => new \cebe\openapi\spec\Response([
                    'description' => 'List of assigned certificate and key for each requested resource',
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => ResourceCertificatesSchema::getSchemaSpec(),
                        ]),
                    ],
                ]),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['get' => $get])];
    }
}
