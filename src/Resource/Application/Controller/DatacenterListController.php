<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Domain\Bus\QueryBus;
use Cdn77\NxgApi\Resource\Application\Payload\DatacentersSchema;
use Cdn77\NxgApi\Resource\Domain\Query\FindResourceGroupDatacenters;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class DatacenterListController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'resource.datacenters.list';
    public const ROUTE_SUMMARY = 'List of Resource Datacenters with enabled status';

    private PathGenerator $pathGenerator;

    private QueryBus $queryBus;

    private SerializerInterface $serializer;

    public function __construct(PathGenerator $pathGenerator, QueryBus $queryBus, SerializerInterface $serializer)
    {
        $this->pathGenerator = $pathGenerator;
        $this->queryBus = $queryBus;
        $this->serializer = $serializer;
    }

    /**
     * @Route(
     *     path="/resource/{resourceId}/datacenters",
     *     name=DatacenterListController::ROUTE_NAME,
     *     methods={Request::METHOD_GET}
     * )
     */
    public function execute(Request $request, int $resourceId): JsonResponse
    {
        $locations = $this->queryBus->handle(new FindResourceGroupDatacenters($resourceId));

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(DatacentersSchema::fromResourceDatacenterLocationsList($locations), 'json'),
        );
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $get = new Operation([
            'tags' => [Tags::RESOURCE],
            'summary' => self::ROUTE_SUMMARY,
            'parameters' => [
                new Parameter([
                    'name' => 'resourceId',
                    'in' => 'path',
                    'required' => true,
                    'schema' => new Schema(['type' => Type::INTEGER]),
                ]),
            ],
            'responses' => new Responses([
                Response::HTTP_OK => new \cebe\openapi\spec\Response(
                    ['description' => 'Datacenters returned'],
                ),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['get' => $get])];
    }
}
