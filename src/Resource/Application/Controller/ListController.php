<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Filter\Resource\SuspendedResourceFilter;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Schema\Legacy\Resources\ResourceList;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use Doctrine\ORM\EntityManagerInterface;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class ListController implements HasOpenApiPaths
{
    public const LEGACY_ROUTE_NAME = 'legacy.resource.list';
    public const ROUTE_NAME = 'resource.list';
    public const ROUTE_SUMMARY = 'List Resources';

    private CdnResourceRepository $resourceRepository;

    private EntityManagerInterface $entityManager;

    private PathGenerator $pathGenerator;

    private SerializerInterface $serializer;

    public function __construct(
        EntityManagerInterface $entityManager,
        CdnResourceRepository $resourceRepository,
        PathGenerator $pathGenerator,
        SerializerInterface $serializer,
    ) {
        $this->entityManager = $entityManager;
        $this->resourceRepository = $resourceRepository;
        $this->pathGenerator = $pathGenerator;
        $this->serializer = $serializer;
    }

    /**
     * @Route(path="/resource", name=self::ROUTE_NAME, methods={Request::METHOD_GET})
     * @Route(path="/cdn_resources.json", name=self::LEGACY_ROUTE_NAME, methods={Request::METHOD_GET})
     */
    public function listAction(Request $request): JsonResponse
    {
        $excludeSuspended = $request->query->getBoolean('excludeSuspended', true);
        if ($excludeSuspended) {
            $this->entityManager->getFilters()->enable(SuspendedResourceFilter::NAME);
        }

        $resources = $this->resourceRepository->findAll();

        if ($excludeSuspended) {
            $this->entityManager->getFilters()->disable(SuspendedResourceFilter::NAME);
        }

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(ResourceList::fromResources($resources), 'json'),
            Response::HTTP_OK,
        );
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $get = new Operation([
            'tags' => [Tags::RESOURCE],
            'summary' => self::ROUTE_SUMMARY,
            'responses' => new Responses([
                Response::HTTP_OK => new \cebe\openapi\spec\Response([
                    'description' => 'List of Resources returned.',
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => new Schema(['type' => Type::OBJECT]),
                        ]),
                    ],
                ]),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['get' => $get])];
    }
}
