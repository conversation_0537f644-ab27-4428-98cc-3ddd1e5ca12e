<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\Resource\Domain\Command\DeleteResource;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use DomainException;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

use function sprintf;

final class DeleteController implements HasOpenApiPaths
{
    public const LEGACY_ROUTE_NAME = 'legacy.resource.delete';
    public const ROUTE_NAME = 'resource.delete';
    public const ROUTE_SUMMARY = 'Delete Resource';

    private CommandBus $commandBus;

    private PathGenerator $pathGenerator;

    private SerializerInterface $serializer;

    public function __construct(CommandBus $commandBus, PathGenerator $pathGenerator, SerializerInterface $serializer)
    {
        $this->commandBus = $commandBus;
        $this->pathGenerator = $pathGenerator;
        $this->serializer = $serializer;
    }

    /**
     * @Route(path="/resource/{resourceId}", name=self::ROUTE_NAME, methods={Request::METHOD_DELETE})
     * @Route(path="/cdn_resources/{resourceId}.json", name=self::LEGACY_ROUTE_NAME, methods={Request::METHOD_DELETE})
     */
    public function deleteAction(Request $request, int $resourceId): Response
    {
        try {
            $this->commandBus->handle(new DeleteResource($resourceId));
        } catch (DomainException) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema([sprintf('Resource with id "%d" not found.', $resourceId)]),
                    'json',
                ),
                Response::HTTP_NOT_FOUND,
            );
        }

        return new Response(null, Response::HTTP_NO_CONTENT);
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $delete = new Operation([
            'tags' => [Tags::RESOURCE],
            'summary' => self::ROUTE_SUMMARY,
            'parameters' => [
                new Parameter([
                    'in' => 'path',
                    'name' => 'resourceId',
                    'required' => true,
                    'schema' => new Schema(['type' => Type::INTEGER]),
                ]),
            ],
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new \cebe\openapi\spec\Response(['description' => 'Resource added.']),
                Response::HTTP_NOT_FOUND => new \cebe\openapi\spec\Response([
                    'description' => 'Resource could not be deleted.',
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => new Schema(['type' => Type::OBJECT]),
                        ]),
                    ],
                ]),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['delete' => $delete])];
    }
}
