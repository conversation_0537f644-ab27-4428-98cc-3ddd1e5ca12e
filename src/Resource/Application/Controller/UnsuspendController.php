<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\Resource\Domain\Command\UnsuspendResource;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class UnsuspendController
{
    public const LEGACY_ROUTE_NAME = 'legacy.resource.unsuspend';
    public const ROUTE_NAME = 'resource.unsuspend';
    public const ROUTE_SUMMARY = 'Unsuspend resource';

    private CommandBus $commandBus;

    public function __construct(CommandBus $commandBus)
    {
        $this->commandBus = $commandBus;
    }

    /**
     * @Route(
     *     path="/cdn_resources/{resourceId}/unsuspend.json",
     *     name=self::LEGACY_ROUTE_NAME,
     *     methods={Request::METHOD_PUT}
     * )
     * @Route(
     *     path="/resource/{resourceId}/unsuspend",
     *     name=self::ROUTE_NAME,
     *     methods={Request::METHOD_PUT}
     * )
     */
    public function unsuspendAction(int $resourceId): JsonResponse
    {
        $this->commandBus->handle(new UnsuspendResource($resourceId));

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }
}
