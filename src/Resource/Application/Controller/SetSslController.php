<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Application\Response\ControllerCommandHandler;
use Cdn77\NxgApi\Resource\Domain\Command\SetCertificate;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Schema\Legacy\Resources\Ssl\CertificateFileDetailSchema;
use Cdn77\NxgApi\Schema\Legacy\Resources\Ssl\SetCertificateSchema;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\RequestBody;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class SetSslController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'resource.certificate.set';
    public const ROUTE_NAME_LEGACY = 'resource.certificate.set.legacy';

    private ControllerCommandHandler $controllerCommandHandler;

    private ControllerSchemaSerializer $controllerSchemaSerializer;

    private PathGenerator $pathGenerator;

    public function __construct(
        ControllerCommandHandler $controllerCommandHandler,
        ControllerSchemaSerializer $controllerSchemaSerializer,
        PathGenerator $pathGenerator,
    ) {
        $this->controllerCommandHandler = $controllerCommandHandler;
        $this->controllerSchemaSerializer = $controllerSchemaSerializer;
        $this->pathGenerator = $pathGenerator;
    }

    /**
     * @Route(
     *     path="/cdn_resources/{resourceId}/certificates.json",
     *     name=self::ROUTE_NAME_LEGACY,
     *     methods={Request::METHOD_POST}
     * )
     * @Route(path="/resource/{resourceId}/certificate", name=self::ROUTE_NAME, methods={Request::METHOD_POST})
     */
    public function setAction(Request $request, int $resourceId): JsonResponse
    {
        $payload = $this->controllerSchemaSerializer->deserialize($request, SetCertificateSchema::class);

        if ($payload instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($payload);
        }

        return $this->controllerCommandHandler->handleWithResult(
            new SetCertificate(
                $payload->certificate,
                $payload->key,
                $resourceId,
            ),
            CertificateFileDetailSchema::class,
            Response::HTTP_ACCEPTED,
        );
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $post = new Operation([
            'tags' => [Tags::RESOURCE],
            'description' => 'Set certificate to the resource.',
            'summary' => 'Set certificate to resource',
            'parameters' => [
                new Parameter([
                    'name' => 'resourceId',
                    'in' => 'path',
                    'required' => true,
                    'schema' => ['type' => 'integer'],
                ]),
            ],
            'requestBody' => new RequestBody([
                'content' => [
                    'application/json' => new MediaType([
                        'schema' => SetCertificateSchema::schemaSpec(),
                    ]),
                ],
            ]),
            'responses' => new Responses([
                Response::HTTP_ACCEPTED => new \cebe\openapi\spec\Response([
                    'description' => 'The certificate was set.',
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => CertificateFileDetailSchema::schemaSpec(),
                        ]),
                    ],
                ]),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['post' => $post])];
    }
}
