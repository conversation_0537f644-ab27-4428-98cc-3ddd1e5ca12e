<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Application\Response\ErrorResponseResolver;
use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Resource\Domain\Command\EditResource;
use Cdn77\NxgApi\Resource\Domain\Exception\ResourceInvalid;
use Cdn77\NxgApi\Resource\Domain\Exception\ResourceOriginMisconfigured;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Service\Legacy\Add\ResourceAdder;
use Cdn77\NxgApi\Service\Legacy\Certificate\Exception\CertificateDoesNotExist;
use Cdn77\NxgApi\Service\Legacy\Certificate\Exception\InvalidCertificatePair;
use Cdn77\NxgApi\Service\Legacy\Edit\Exception\ResourceValidationFailed;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use Doctrine\DBAL\Exception\DriverException;
use DomainException;
use JMS\Serializer\SerializerInterface;
use Stringable;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\ConstraintViolationInterface;

use function array_map;
use function iterator_to_array;
use function Safe\preg_match;
use function sprintf;

final class EditController implements HasOpenApiPaths
{
    public const LEGACY_ROUTE_NAME = 'legacy.resource.edit';
    public const ROUTE_NAME = 'resource.edit';
    public const ROUTE_SUMMARY = 'Edit Resource';

    private CommandBus $commandBus;

    private ControllerSchemaSerializer $controllerSchemaSerializer;

    private ErrorResponseResolver $errorResponseResolver;

    private PathGenerator $pathGenerator;

    private SerializerInterface $serializer;

    public function __construct(
        CommandBus $commandBus,
        ControllerSchemaSerializer $controllerSchemaSerializer,
        ErrorResponseResolver $errorResponseResolver,
        PathGenerator $pathGenerator,
        SerializerInterface $serializer,
    ) {
        $this->commandBus = $commandBus;
        $this->controllerSchemaSerializer = $controllerSchemaSerializer;
        $this->errorResponseResolver = $errorResponseResolver;
        $this->pathGenerator = $pathGenerator;
        $this->serializer = $serializer;
    }

    /**
     * @Route(path="/resource/{resourceId}", methods={Request::METHOD_PATCH}, name=self::ROUTE_NAME)
     * @Route(path="/cdn_resources/{resourceId}.json", methods={Request::METHOD_PUT}, name=self::LEGACY_ROUTE_NAME)
     */
    public function execute(Request $request, int $resourceId): Response
    {
        $schema = $this->controllerSchemaSerializer->deserialize($request, ResourceEditSchema::class);

        if ($schema instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($schema);
        }

        try {
            $this->commandBus->handle(EditResource::fromSchema($resourceId, $schema));
        } catch (ResourceValidationFailed $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    $this->errorResponseResolver->resolve($e->getViolations(), ErrorsSchema::class),
                    'json',
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY,
            );
        } catch (ResourceOriginMisconfigured $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema([$e->getMessage()]),
                    'json',
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY,
            );
        } catch (DomainException) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema([sprintf('Resource with id "%d" not found.', $resourceId)]),
                    'json',
                ),
                Response::HTTP_NOT_FOUND,
            );
        } catch (CertificateDoesNotExist) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema(['There is no active certificate.']),
                    'json',
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY,
            );
        } catch (InvalidCertificatePair $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema(array_map(
                        /** @return string|Stringable */
                        static fn (ConstraintViolationInterface $violation) => $violation->getMessage(),
                        iterator_to_array($e->getViolations()),
                    )),
                    'json',
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY,
            );
        } catch (DriverException $e) {
            if (preg_match(ResourceAdder::PATTERN_DB_UNIQUE_CNAME, $e->getMessage(), $matches) === 1) {
                return JsonResponse::fromJsonString(
                    $this->serializer->serialize(
                        new ErrorsSchema([sprintf(ResourceInvalid::CNAME_NOT_UNIQUE, $matches[1])]),
                        'json',
                    ),
                    Response::HTTP_UNPROCESSABLE_ENTITY,
                );
            }

            throw $e;
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $patch = new Operation([
            'tags' => [Tags::RESOURCE],
            'summary' => self::ROUTE_SUMMARY,
            'parameters' => [
                new Parameter([
                    'in' => 'path',
                    'name' => 'resourceId',
                    'required' => true,
                    'schema' => new Schema(['type' => Type::INTEGER]),
                ]),
            ],
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new \cebe\openapi\spec\Response(
                    ['description' => 'Resource updated'],
                ),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['patch' => $patch])];
    }
}
