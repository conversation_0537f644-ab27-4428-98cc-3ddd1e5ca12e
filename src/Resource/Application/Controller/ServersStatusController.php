<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\Payload\ServerStatusSchema;
use Cdn77\NxgApi\Core\Domain\Bus\QueryBus;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceServersSchema;
use Cdn77\NxgApi\Resource\Domain\Exception\CdnResourceNotFound;
use Cdn77\NxgApi\Resource\Domain\Query\FindResourceServersStatus;
use Cdn77\NxgApi\Schema\Internal\Resource\Servers\ResourceDetail;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Service\Server\ServerStatus;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

use function array_map;

class ServersStatusController
{
    public const ROUTE_NAME = 'resource.servers';
    public const ROUTE_SUMMARY = 'Resource servers';

    private QueryBus $queryBus;
    private SerializerInterface $serializer;

    public function __construct(QueryBus $queryBus, SerializerInterface $serializer)
    {
        $this->queryBus = $queryBus;
        $this->serializer = $serializer;
    }

    /** @Route(path="/internal/resource/{resourceId}/servers", name=self::ROUTE_NAME, methods={Request::METHOD_GET})  */
    public function serversAction(int $resourceId): JsonResponse
    {
        try {
            $statuses = $this->queryBus->handle(new FindResourceServersStatus($resourceId));
        } catch (CdnResourceNotFound $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema([$e->getMessage()]),
                    'json',
                ),
                Response::HTTP_NOT_FOUND,
            );
        }

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                new ResourceServersSchema(
                    new ResourceDetail($resourceId),
                    array_map(
                        static fn (ServerStatus $status) => ServerStatusSchema::fromServerStatus($status),
                        $statuses,
                    ),
                ),
                'json',
            ),
            Response::HTTP_OK,
        );
    }
}
