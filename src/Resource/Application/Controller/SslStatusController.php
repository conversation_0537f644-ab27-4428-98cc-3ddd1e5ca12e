<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Response\ControllerQueryHandler;
use Cdn77\NxgApi\Resource\Application\Payload\Ssl\CertificateStatusSchema;
use Cdn77\NxgApi\Resource\Domain\Query\FindSslStatusForResource;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class SslStatusController implements HasOpenApiPaths
{
    public const ROUTE_NAME_LEGACY = 'resource.certificate-status.legacy';
    public const ROUTE_NAME = 'resource.certificate-status';

    private ControllerQueryHandler $controllerQueryHandler;

    private PathGenerator $pathGenerator;

    public function __construct(ControllerQueryHandler $controllerQueryHandler, PathGenerator $pathGenerator)
    {
        $this->controllerQueryHandler = $controllerQueryHandler;
        $this->pathGenerator = $pathGenerator;
    }

    /**
     * @Route(
     *     path="/cdn_resources/{resourceId}/certificates/status.json",
     *     name=self::ROUTE_NAME_LEGACY,
     *     methods={Request::METHOD_GET}
     * )
     * @Route(
     *     path="/resource/{resourceId}/certificate/status",
     *     name=self::ROUTE_NAME,
     *     methods={Request::METHOD_GET}
     * )
     */
    public function statusAction(int $resourceId): JsonResponse
    {
        return $this->controllerQueryHandler->handle(
            new FindSslStatusForResource($resourceId),
            CertificateStatusSchema::class,
            Response::HTTP_OK,
            true,
        );
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        return [
            $this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem([
                'get' => new Operation([
                    'tags' => [Tags::RESOURCE],
                    'description' => 'Get the status of the certificate of the resource.',
                    'summary' => 'Get certificate status for resource',
                    'parameters' => [
                        new Parameter([
                            'name' => 'resourceId',
                            'in' => 'path',
                            'required' => true,
                            'schema' => ['type' => 'integer'],
                        ]),
                    ],
                    'responses' => new Responses([
                        Response::HTTP_OK => new \cebe\openapi\spec\Response([
                            'description' => 'The status information.',
                            'content' => [
                                'application/json' => new MediaType([
                                    'schema' => CertificateStatusSchema::getSchemaSpec(),
                                ]),
                            ],
                        ]),
                    ]),
                ]),
            ]),
        ];
    }
}
