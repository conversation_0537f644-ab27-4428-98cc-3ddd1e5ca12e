<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Application\Response\ControllerCommandHandler;
use Cdn77\NxgApi\Resource\Application\Payload\SetCertificateSchema;
use Cdn77\NxgApi\Resource\Domain\Command\SetCertificateToMultipleResources;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class SetCertificateController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'resources.set-certificate';
    private const ROUTE_SUMMARY = 'Set certificate to multiple resources';

    public function __construct(
        private ControllerCommandHandler $controllerCommandHandler,
        private ControllerSchemaSerializer $controllerSchemaSerializer,
        private PathGenerator $pathGenerator,
    ) {
    }

    /**
     * @Route(
     *     path="/resources/set-certificate",
     *     methods={Request::METHOD_POST},
     *     name=SetCertificateController::ROUTE_NAME
     * )
     */
    public function execute(Request $request): Response
    {
        $schema = $this->controllerSchemaSerializer->deserialize($request, SetCertificateSchema::class);

        if ($schema instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($schema);
        }

        return $this->controllerCommandHandler->handle(SetCertificateToMultipleResources::fromSchema($schema));
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $post = new Operation([
            'tags' => [Tags::RESOURCE],
            'summary' => self::ROUTE_SUMMARY,
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new \cebe\openapi\spec\Response(
                    ['description' => 'Certificate set to resources'],
                ),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['post' => $post])];
    }
}
