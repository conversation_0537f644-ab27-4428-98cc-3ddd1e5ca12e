<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Application\Response\ErrorResponseResolver;
use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddSchema;
use Cdn77\NxgApi\Resource\Domain\Command\CreateResource;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Schema\Legacy\Resources\ResourceDetail;
use Cdn77\NxgApi\Schema\Legacy\Resources\ResourceDetailInfo;
use Cdn77\NxgApi\Service\Legacy\Certificate\Exception\InvalidCertificatePair;
use Cdn77\NxgApi\Service\Legacy\Edit\Exception\ResourceValidationFailed;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use JMS\Serializer\SerializerInterface;
use Stringable;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Webmozart\Assert\Assert;

use function array_map;
use function iterator_to_array;

final class AddController implements HasOpenApiPaths
{
    public const LEGACY_ROUTE_NAME = 'legacy.resource.add';
    public const ROUTE_NAME = 'resource.add';
    public const ROUTE_SUMMARY = 'Add Resource';

    private CdnResourceRepository $resourceRepository;

    private CommandBus $commandBus;

    private ControllerSchemaSerializer $controllerSchemaSerializer;

    private ErrorResponseResolver $errorResponseResolver;

    private SerializerInterface $serializer;

    private PathGenerator $pathGenerator;

    public function __construct(
        CdnResourceRepository $resourceRepository,
        CommandBus $commandBus,
        ControllerSchemaSerializer $controllerSchemaSerializer,
        ErrorResponseResolver $errorResponseResolver,
        SerializerInterface $serializer,
        PathGenerator $pathGenerator,
    ) {
        $this->controllerSchemaSerializer = $controllerSchemaSerializer;
        $this->commandBus = $commandBus;
        $this->errorResponseResolver = $errorResponseResolver;
        $this->resourceRepository = $resourceRepository;
        $this->serializer = $serializer;
        $this->pathGenerator = $pathGenerator;
    }

    /**
     * @Route(path="/resource", name=self::ROUTE_NAME, methods={Request::METHOD_POST})
     * @Route(path="/cdn_resources.json", name=self::LEGACY_ROUTE_NAME, methods={Request::METHOD_POST})
     */
    public function addAction(Request $request): JsonResponse
    {
        $schema = $this->controllerSchemaSerializer->deserialize($request, ResourceAddSchema::class);

        if ($schema instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($schema);
        }

        try {
            $resource = $this->commandBus->handle(new CreateResource(
                $schema->resourceDto(),
                $schema->geoProtection,
                $schema->hotlinkProtection,
                $schema->ipProtection,
                $schema->certificatePair(),
            ));
        } catch (ResourceValidationFailed $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    $this->errorResponseResolver->resolve($e->getViolations(), ErrorsSchema::class),
                    'json',
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY,
            );
        } catch (NxgApiDomainException $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema([$e->getMessage()]),
                    'json',
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY,
            );
        } catch (InvalidCertificatePair $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema(array_map(
                    /** @return string|Stringable */
                        static fn (ConstraintViolationInterface $violation) => $violation->getMessage(),
                        iterator_to_array($e->getViolations()),
                    )),
                    'json',
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY,
            );
        }

        Assert::isInstanceOf($resource, CdnResource::class);
        $resource = $this->resourceRepository->find($resource->getId());

        Assert::notNull($resource);

        $resourceDetailInfo = ResourceDetailInfo::fromResource($resource);
        $schema = new ResourceDetail($resourceDetailInfo);

        return JsonResponse::fromJsonString(
            $this->serializer->serialize($schema, 'json'),
            Response::HTTP_OK,
        );
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $post = new Operation([
            'tags' => [Tags::RESOURCE],
            'summary' => self::ROUTE_SUMMARY,
            'responses' => new Responses([
                Response::HTTP_OK => new \cebe\openapi\spec\Response([
                    'description' => 'Resource added.',
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => new Schema(['type' => Type::OBJECT]),
                        ]),
                    ],
                ]),
                Response::HTTP_NOT_FOUND => new \cebe\openapi\spec\Response([
                    'description' => 'Resource could not be added.',
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => new Schema(['type' => Type::OBJECT]),
                        ]),
                    ],
                ]),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['post' => $post])];
    }
}
