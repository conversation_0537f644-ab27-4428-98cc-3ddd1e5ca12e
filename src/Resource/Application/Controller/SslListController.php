<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Application\Response\ControllerQueryHandler;
use Cdn77\NxgApi\Resource\Application\Payload\Ssl\ListFilterParams;
use Cdn77\NxgApi\Resource\Domain\Query\FindSslCertificates;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Schema\Legacy\Resources\Ssl\CertificatesSchema;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class SslListController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'resource.ssl.list';
    public const ROUTE_NAME_LEGACY = 'resource.ssl.list.legacy';

    private ControllerQueryHandler $controllerQueryHandler;

    private ControllerSchemaSerializer $controllerSchemaSerializer;

    private PathGenerator $pathGenerator;

    public function __construct(
        ControllerQueryHandler $controllerQueryHandler,
        ControllerSchemaSerializer $controllerSchemaSerializer,
        PathGenerator $pathGenerator,
    ) {
        $this->controllerQueryHandler = $controllerQueryHandler;
        $this->controllerSchemaSerializer = $controllerSchemaSerializer;
        $this->pathGenerator = $pathGenerator;
    }

    /**
     * @Route(path="/certificates.json", name=self::ROUTE_NAME_LEGACY, methods={Request::METHOD_GET})
     * @Route(path="/resources/certificates", name=self::ROUTE_NAME, methods={Request::METHOD_GET})
     */
    public function listAction(Request $request): JsonResponse
    {
        $filters = $this->controllerSchemaSerializer->deserializeQueryString($request, ListFilterParams::class);

        if ($filters instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($filters);
        }

        return $this->controllerQueryHandler->handle(
            new FindSslCertificates(
                $filters->includeSuspended,
                $filters->type(),
                $filters->expirationBefore,
            ),
            CertificatesSchema::class,
        );
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $get = new Operation([
            'tags' => [Tags::RESOURCE],
            'summary' => 'List of all certificates',
            'description' => 'List of all resources\' certificates.',
            'responses' => new Responses([
                Response::HTTP_OK => new \cebe\openapi\spec\Response([
                    'description' => 'List of certificates.',
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => CertificatesSchema::getSchemaSpec(),
                        ]),
                    ],
                ]),
                Response::HTTP_BAD_REQUEST => new \cebe\openapi\spec\Response([
                    'description' => 'Invalid parameters.',
                    'content' => [
                        'application/json' => new MediaType([
                            'schema' => ErrorsSchema::getSchemaSpec('Error'),
                        ]),
                    ],
                ]),
            ]),
            'parameters' => ListFilterParams::getSchemaParameters(),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['get' => $get])];
    }
}
