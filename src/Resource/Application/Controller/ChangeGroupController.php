<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\Resource\Application\Payload\ChangeLocationGroupSchema;
use Cdn77\NxgApi\Resource\Domain\Command\ChangeLocationGroup;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\Parameter;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use DomainException;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class ChangeGroupController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'resource.change-group';
    public const ROUTE_SUMMARY = 'Change Resource group';

    private CommandBus $commandBus;

    private ControllerSchemaSerializer $controllerSchemaSerializer;

    private PathGenerator $pathGenerator;

    private SerializerInterface $serializer;

    public function __construct(
        CommandBus $commandBus,
        ControllerSchemaSerializer $controllerSchemaSerializer,
        PathGenerator $pathGenerator,
        SerializerInterface $serializer,
    ) {
        $this->commandBus = $commandBus;
        $this->controllerSchemaSerializer = $controllerSchemaSerializer;
        $this->pathGenerator = $pathGenerator;
        $this->serializer = $serializer;
    }

    /**
     * @Route(
     *     path="/resource/{resourceId}/change-group",
     *     methods={Request::METHOD_PATCH},
     *     name=self::ROUTE_NAME
     * )
     */
    public function execute(Request $request, int $resourceId): Response
    {
        $schema = $this->controllerSchemaSerializer->deserialize($request, ChangeLocationGroupSchema::class);

        if ($schema instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($schema);
        }

        try {
            $this->commandBus->handle(ChangeLocationGroup::fromResourceIdAndSchema($resourceId, $schema));
        } catch (DomainException $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema([$e->getMessage()]),
                    'json',
                ),
                Response::HTTP_NOT_FOUND,
            );
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $patch = new Operation([
            'tags' => [Tags::RESOURCE],
            'summary' => self::ROUTE_SUMMARY,
            'parameters' => [
                new Parameter([
                    'name' => 'resourceId',
                    'in' => 'path',
                    'required' => true,
                    'schema' => new Schema(['type' => Type::INTEGER]),
                ]),
            ],
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new \cebe\openapi\spec\Response(
                    ['description' => 'Group changed'],
                ),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['patch' => $patch])];
    }
}
