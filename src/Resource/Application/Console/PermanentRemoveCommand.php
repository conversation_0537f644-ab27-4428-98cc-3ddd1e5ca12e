<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Console;

use Cdn77\MonMasterDetector\MasterStateDetectorInterface;
use Cdn77\NxgApi\Resource\Domain\Finder\ResourcesForPermanentRemoveFinder;
use Cdn77\NxgApi\Service\Legacy\Delete\ResourceDeleter;
use DateTimeImmutable;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;
use Webmozart\Assert\Assert;

use function count;
use function implode;
use function Sentry\captureMessage;
use function sprintf;

class PermanentRemoveCommand extends Command
{
    private const OPTION_LIMIT = 'limit';

    private const TIME_FORMAT = 'Y-m-d H:i:s.u';

    private const COMMAND_NAME = 'resource:permanent-remove';

    private MasterStateDetectorInterface $masterStateDetector;
    private ResourcesForPermanentRemoveFinder $resourcesForPermanentRemoveFinder;
    private ResourceDeleter $resourceDeleter;
    private OutputInterface $output;

    public function __construct(
        ResourcesForPermanentRemoveFinder $resourcesForPermanentRemoveFinder,
        ResourceDeleter $resourceDeleter,
        MasterStateDetectorInterface $masterStateDetector,
    ) {
        $this->resourcesForPermanentRemoveFinder = $resourcesForPermanentRemoveFinder;
        $this->resourceDeleter = $resourceDeleter;
        $this->masterStateDetector = $masterStateDetector;

        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName(self::COMMAND_NAME)
            ->setDescription('Remove rows from resource table where deleted column date is older than 30 days')
            ->addOption(
                self::OPTION_LIMIT,
                'l',
                InputOption::VALUE_OPTIONAL,
                'How many resources can be removed',
                '10',
            );

        parent::configure();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->output = $output;

        try {
            $limit = $this->getLimit($input);
            $this->log(sprintf('Start: Removing resources with "%s" and limit %d.', self::COMMAND_NAME, $limit));

            $this->checkMaster();

            $resourceIds = $this->resourcesForPermanentRemoveFinder->findIds($limit);

            if ($resourceIds === []) {
                $this->log('Finish: No resources to remove.');

                return 0;
            }

            $this->log(sprintf('Removing %d resources: %s', count($resourceIds), implode(', ', $resourceIds)));

            $this->removeResources($resourceIds);
        } catch (Throwable $e) {
            $this->log('Finish: Error: ' . $e->getMessage());

            return 1;
        }

        $this->log('Finish: All resources removed successfully.');

        return 0;
    }

    /** @param array<int> $resourcesIds */
    private function removeResources(array $resourcesIds): void
    {
        $allRemoved = true;

        foreach ($resourcesIds as $resourceId) {
            try {
                $this->checkMaster();

                $this->log('Resource to remove ID: ' . $resourceId);
                $this->resourceDeleter->remove($resourceId);
                $this->log('Successfully removed ID: ' . $resourceId);
            } catch (Throwable $e) {
                $allRemoved = false;
                $this->log(sprintf('Problem removing ID %d: %s', $resourceId, $e->getMessage()));
                captureMessage(sprintf(
                    'Problem removing resource ID "%d" when running command "%s": %s',
                    $resourceId,
                    self::COMMAND_NAME,
                    $e->getMessage(),
                ));
            }
        }

        if (! $allRemoved) {
            throw new Exception('Some resources not removed. Check the log.');
        }
    }

    private function checkMaster(): void
    {
        if (! $this->masterStateDetector->getState()->isMaster()) {
            throw new Exception('I am not master.');
        }
    }

    private function log(string $message): void
    {
        $dateTime = new DateTimeImmutable();
        $this->output->writeln(sprintf('%s: %s', $dateTime->format(self::TIME_FORMAT), $message));
    }

    private function getLimit(InputInterface $input): int
    {
        $limit = $input->getOption(self::OPTION_LIMIT);
        Assert::numeric($limit);

        return (int) $limit;
    }
}
