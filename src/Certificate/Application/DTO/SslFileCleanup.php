<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Application\DTO;

final class SslFileCleanup
{
    public function __construct(public bool $archive, public int $year, public int $month, public int $accountId)
    {
    }

    /** @param array<string, int|bool|string> $row */
    public static function fromFetchResult(array $row): self
    {
        return new self((bool) $row['archive'], (int) $row['year'], (int) $row['month'], (int) $row['account_id']);
    }
}
