<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\QueryBusResultSchema;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Webmozart\Assert\Assert;

final class LoadPrivateKeysUuidResultSchema implements QueryBusResultSchema
{
    /**
     * @Serializer\Type("array<string, int>")
     * @Serializer\Inline
     * @var array<string, int>
     */
    public array $uuidToAccountMap;

    /** @param array<string, int> $uuidToAccountMap */
    private function __construct(array $uuidToAccountMap)
    {
        $this->uuidToAccountMap = $uuidToAccountMap;
    }

    /** @param mixed $result */
    public static function fromQueryBusResult($result): self
    {
        Assert::isArray($result);

        return new self($result);
    }
}
