<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\Schema;
use Symfony\Component\Validator\Constraints as Assert;

final class StorePrivateKeySchema implements Schema
{
    public const FIELD_ACCOUNT_ID = 'account_id';
    public const FIELD_CERTIFICATE = 'certificate';
    public const FIELD_PRIVATE_KEY = 'key';
    public const FIELD_UUID = 'uuid';

    /**
     * @Assert\NotNull
     * @Assert\Type("integer")
     * @Assert\GreaterThan(0)
     */
    public int|null $accountId = null;

    /**
     * @Assert\NotNull
     * @Assert\NotBlank
     * @Assert\Type("string")
     * @Assert\Uuid(versions={4}, message="UUID must be a valid version 4 UUID")
     */
    public string|null $uuid = null;

    /**
     * @Assert\NotNull
     * @Assert\Type("string")
     */
    public string|null $certificate = null;

    /**
     * @Assert\NotNull
     * @Assert\Type("string")
     */
    public string|null $key = null;

    private function __construct()
    {
    }
}
