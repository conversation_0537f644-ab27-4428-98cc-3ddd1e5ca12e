<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Application\Console;

use Cdn77\MonMasterDetector\MasterStateDetectorInterface;
use Cdn77\NxgApi\Certificate\Domain\CertificateCleaner;
use Cdn77\NxgApi\Certificate\Domain\Enum\MoveOnlyParameter;
use Cdn77\NxgApi\Command\Font;
use Cdn77\NxgApi\Command\OutputWriter;
use InvalidArgumentException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;
use Webmozart\Assert\Assert;

use function array_map;
use function implode;
use function Sentry\captureException;
use function sprintf;

final class CertificateCleanupCommand extends Command
{
    public const NAME = 'certificate:cleanup';

    private const OPTION_LIMIT = 'limit';
    private const OPTION_DRY_RUN = 'dry-run';
    private const OPTION_MOVE_ONLY = 'move-only';
    private const OPTION_IGNORE_MASTER = 'ignore-master';
    private const OPTION_IGNORE_SKIPPING_LOG = 'skip-skipping-log';

    public const DEFAULT_LIMIT = 100;

    public function __construct(
        private CertificateCleaner $certificateCleaner,
        private MasterStateDetectorInterface $masterStateDetector,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        parent::configure();

        $this->setName(self::NAME)
            ->setDescription('Move unused certificates from bucket storage to cold storage.')->addOption(
                self::OPTION_LIMIT,
                'l',
                InputOption::VALUE_OPTIONAL,
                'How many resources can be removed',
                (string) self::DEFAULT_LIMIT,
            )->addOption(
                self::OPTION_DRY_RUN,
                'd',
                InputOption::VALUE_NONE,
                'Only show affected files, do not move them',
            )->addOption(
                self::OPTION_IGNORE_MASTER,
                null,
                InputOption::VALUE_NONE,
                'Ignore master state detection',
            )->addOption(
                self::OPTION_IGNORE_SKIPPING_LOG,
                null,
                InputOption::VALUE_NONE,
                'Skip logging of skipped files',
            )->addOption(
                self::OPTION_MOVE_ONLY,
                null,
                InputOption::VALUE_REQUIRED,
                sprintf(
                    'Move only files that match the condition. Possible values: %s',
                    implode(', ', array_map(static fn ($s) => $s->value, MoveOnlyParameter::cases())),
                ),
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $outputWriter = new OutputWriter($output);
        $outputWriter->message('Cleaning old certificates started');

        if ($this->masterStateIsInvalid($input, $outputWriter)) {
            return Command::INVALID;
        }

        $dryRun = $this->getDryRun($input, $outputWriter);

        try {
            $movedFilesCount = $this->certificateCleaner->clean(
                $outputWriter,
                $this->getLimit($input, $outputWriter),
                $dryRun,
                $this->getIgnoreSkippingLog($input, $outputWriter),
                $this->getMoveOnly($input, $outputWriter),
            );
        } catch (Throwable $e) {
            captureException($e);
            $outputWriter->messageRed(sprintf('The cleaner failed because of an exception: %s', $e->getMessage()));

            return Command::FAILURE;
        }

        $outputWriter->message(Font::green(sprintf(
            'The cleaner finished successfully. Number of moved certificate & key pairs: %d',
            $movedFilesCount,
        )));

        if ($dryRun) {
            $outputWriter->message(Font::yellow('! Dry run was enabled - nothing was moved !'));
        }

        return Command::SUCCESS;
    }

    private function masterStateIsInvalid(InputInterface $input, OutputWriter $outputWriter): bool
    {
        $ignoreMasterDetection = (bool) $input->getOption(self::OPTION_IGNORE_MASTER);
        if ($ignoreMasterDetection) {
            $outputWriter->messageYellow('! Master state detection is disabled !');
        } elseif (! $this->masterStateDetector->getState()->isMaster()) {
            $outputWriter->messageYellow('I am not master. Exiting');

            return true;
        } else {
            $outputWriter->message("I am master. Let's get started");
        }

        return false;
    }

    private function getDryRun(InputInterface $input, OutputWriter $outputWriter): bool
    {
        $dryRun = (bool) $input->getOption(self::OPTION_DRY_RUN);

        if ($dryRun) {
            $outputWriter->messageYellow('! Dry run enabled. No files will be moved !');
        }

        return $dryRun;
    }

    private function getLimit(InputInterface $input, OutputWriter $outputWriter): int
    {
        $limit = $input->getOption(self::OPTION_LIMIT);
        Assert::numeric($limit);

        $outputWriter->message(sprintf('Limit of archived files: %d', $limit));

        return (int) $limit;
    }

    private function getIgnoreSkippingLog(InputInterface $input, OutputWriter $outputWriter): bool
    {
        $ignoreSkippingLog = (bool) $input->getOption(self::OPTION_IGNORE_SKIPPING_LOG);

        if ($ignoreSkippingLog) {
            $outputWriter->messageYellow('Skipping log of skipped files is disabled');
        }

        return $ignoreSkippingLog;
    }

    private function getMoveOnly(InputInterface $input, OutputWriter $outputWriter): MoveOnlyParameter|null
    {
        $moveOnlyString = $input->getOption(self::OPTION_MOVE_ONLY);

        if ($moveOnlyString === null) {
            return null;
        }

        $moveOnly = MoveOnlyParameter::tryFrom($moveOnlyString);

        if ($moveOnly === null) {
            throw new InvalidArgumentException(sprintf('Invalid value for move-only option: %s', $moveOnlyString));
        }

        $outputWriter->messageYellow($moveOnly->getDescription());

        return $moveOnly;
    }
}
