<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Application\Controller;

use Cdn77\NxgApi\Certificate\Application\Payload\LoadPrivateKeysUuidResultSchema;
use Cdn77\NxgApi\Certificate\Domain\Command\LoadPrivateKeysUuid;
use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Response\ControllerQueryHandler;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Response;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

final class LoadPrivateKeysController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'certificates.load.private-keys-uuid';
    private const ROUTE_SUMMARY = 'Load all private keys UUIDs mapped to their account IDs';

    public function __construct(
        private readonly ControllerQueryHandler $controllerQueryHandler,
        private readonly PathGenerator $pathGenerator,
    ) {
    }

    /** @Route(path="/certificate/key", name=self::ROUTE_NAME, methods={Request::METHOD_GET}) */
    public function execute(Request $request): JsonResponse
    {
        return $this->controllerQueryHandler->handle(
            new LoadPrivateKeysUuid(),
            LoadPrivateKeysUuidResultSchema::class,
        );
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $get = new Operation([
            'tags' => [Tags::CERTIFICATE],
            'summary' => self::ROUTE_SUMMARY,
            'responses' => new Responses([
                JsonResponse::HTTP_OK => new Response([
                    'description' => 'Private keys UUIDs loaded successfully',
                    'content' => [
                        'application/json' => [
                            'schema' => [
                                'type' => 'object',
                                'additionalProperties' => ['type' => 'string'],
                            ],
                        ],
                    ],
                ]),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['get' => $get])];
    }
}
