<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Application\Controller;

use Cdn77\NxgApi\Certificate\Application\Payload\DeletePrivateKeySchema;
use Cdn77\NxgApi\Certificate\Domain\Command\DeletePrivateKey;
use Cdn77\NxgApi\Certificate\Domain\Exception\FailedToDeletePrivateKey;
use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;

final class DeletePrivateKeyController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'certificates.delete.private-key';
    private const ROUTE_SUMMARY = 'Delete a certificate from storage for an account by UUID';

    public function __construct(
        private readonly CommandBus $commandBus,
        private readonly ControllerSchemaSerializer $controllerSchemaSerializer,
        private readonly PathGenerator $pathGenerator,
    ) {
    }

    /** @Route(path="/certificate/key", methods={Request::METHOD_DELETE}, name=self::ROUTE_NAME) */
    public function execute(Request $request): Response
    {
        $schema = $this->controllerSchemaSerializer->deserializeQueryString($request, DeletePrivateKeySchema::class);

        if ($schema instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($schema);
        }

        try {
            $this->commandBus->handle(DeletePrivateKey::fromSchema($schema));
        } catch (FailedToDeletePrivateKey $e) {
            return new JsonResponse(
                ['error' => $e->getMessage()],
                Response::HTTP_NOT_FOUND,
            );
        } catch (Throwable) {
            return new JsonResponse(
                ['error' => 'Failed to delete private key.'],
                Response::HTTP_INTERNAL_SERVER_ERROR,
            );
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $delete = new Operation([
            'tags' => [Tags::CERTIFICATE],
            'summary' => self::ROUTE_SUMMARY,
            'parameters' => [
                [
                    'name' => DeletePrivateKeySchema::FIELD_ACCOUNT_ID,
                    'in' => 'query',
                    'description' => 'Account ID',
                    'required' => true,
                    'schema' => [
                        'type' => 'integer',
                        'minimum' => 1,
                    ],
                ],
                [
                    'name' => DeletePrivateKeySchema::FIELD_UUID,
                    'in' => 'query',
                    'description' => 'Private key UUID',
                    'required' => true,
                    'schema' => ['type' => 'string'],
                ],
            ],
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new \cebe\openapi\spec\Response(
                    ['description' => 'Private key deleted successfully'],
                ),
                Response::HTTP_INTERNAL_SERVER_ERROR => new \cebe\openapi\spec\Response(
                    ['description' => 'Server error when deleting private key'],
                ),
                Response::HTTP_NOT_FOUND => new \cebe\openapi\spec\Response(
                    ['description' => 'Private key not found'],
                ),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['delete' => $delete])];
    }
}
