<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Application\Controller;

use Cdn77\NxgApi\Certificate\Application\Payload\StorePrivateKeySchema;
use Cdn77\NxgApi\Certificate\Domain\Command\StorePrivateKey;
use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Service\Legacy\Certificate\Exception\InvalidCertificatePair;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use JMS\Serializer\SerializerInterface;
use Stringable;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Throwable;

use function array_map;
use function iterator_to_array;

final class StorePrivateKeyController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'certificates.store.private-key';
    private const ROUTE_SUMMARY = 'Store account private key to storage under a specific UUID';

    private CommandBus $commandBus;

    private ControllerSchemaSerializer $controllerSchemaSerializer;

    private PathGenerator $pathGenerator;

    private SerializerInterface $serializer;

    public function __construct(
        CommandBus $commandBus,
        ControllerSchemaSerializer $controllerSchemaSerializer,
        PathGenerator $pathGenerator,
        SerializerInterface $serializer,
    ) {
        $this->commandBus = $commandBus;
        $this->controllerSchemaSerializer = $controllerSchemaSerializer;
        $this->pathGenerator = $pathGenerator;
        $this->serializer = $serializer;
    }

    /** @Route(path="/certificate/key", methods={Request::METHOD_POST}, name=self::ROUTE_NAME) */
    public function execute(Request $request): Response
    {
        $schema = $this->controllerSchemaSerializer->deserialize($request, StorePrivateKeySchema::class);

        if ($schema instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($schema);
        }

        try {
            $this->commandBus->handle(StorePrivateKey::fromSchema($schema));
        } catch (InvalidCertificatePair $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema(array_map(
                        /** @return string|Stringable */
                        static fn (ConstraintViolationInterface $violation) => $violation->getMessage(),
                        iterator_to_array($e->getViolations()),
                    )),
                    'json',
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY,
            );
        } catch (Throwable) {
            return new JsonResponse(
                ['error' => 'Failed to store private key.'],
                Response::HTTP_INTERNAL_SERVER_ERROR,
            );
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $post = new Operation([
            'tags' => [Tags::CERTIFICATE],
            'summary' => self::ROUTE_SUMMARY,
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new \cebe\openapi\spec\Response(
                    ['description' => 'Private key stored successfully'],
                ),
                Response::HTTP_INTERNAL_SERVER_ERROR => new \cebe\openapi\spec\Response(
                    ['description' => 'Server error when storing private key'],
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY => new \cebe\openapi\spec\Response(
                    ['description' => 'Certificate and private key is not valid pair'],
                ),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['post' => $post])];
    }
}
