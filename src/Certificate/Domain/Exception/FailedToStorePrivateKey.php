<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use DomainException;

use function sprintf;

final class FailedToStorePrivate<PERSON>ey extends DomainException implements NxgApiDomainException
{
    public static function failedToCreateAccountDir(string $accountDir): self
    {
        return new self(
            sprintf('Failed to create account directory "%s" when storing private key.', $accountDir),
        );
    }

    public static function failedToWritePrivateKey(string $accountDir): self
    {
        return new self(sprintf('Failed to write private key to directory "%s".', $accountDir));
    }

    public static function failedToValidatePrivateKey(string $accountDir): self
    {
        return new self(sprintf('Failed to validate private key "%s" after storing.', $accountDir));
    }
}
