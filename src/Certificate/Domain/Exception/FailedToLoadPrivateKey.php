<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use DomainException;

use function sprintf;

final class FailedToLoadPrivateKey extends DomainException implements NxgApiDomainException
{
    public static function unableToReadKeyFile(string $path): self
    {
        return new self(
            sprintf('Failed to read "%s" when assigning certificate to resource.', $path),
        );
    }
}
