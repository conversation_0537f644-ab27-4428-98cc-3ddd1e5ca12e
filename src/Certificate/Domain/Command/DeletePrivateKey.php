<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Command;

use Cdn77\NxgApi\Certificate\Application\Payload\DeletePrivateKeySchema;
use Cdn77\NxgApi\Core\Domain\Bus\Command;
use Webmozart\Assert\Assert;

final class DeletePrivate<PERSON>ey implements Command
{
    private function __construct(
        public readonly string $accountId,
        public readonly string $uuid,
    ) {
    }

    public static function fromSchema(DeletePrivateKeySchema $schema): self
    {
        Assert::notNull($schema->accountId);
        Assert::notNull($schema->uuid);

        return new self(
            (string) $schema->accountId,
            $schema->uuid,
        );
    }
}
