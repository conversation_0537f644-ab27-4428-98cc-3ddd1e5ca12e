<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Command;

use Cdn77\NxgApi\Certificate\Domain\Exception\FailedToDeletePrivateKey;
use Cdn77\NxgApi\Certificate\Domain\PrivateKeyFileManager;
use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use League\Flysystem\FilesystemOperator;
use Throwable;

use function Sentry\captureException;

final class DeletePrivateKeyHandler implements CommandHandler
{
    public function __construct(
        private readonly FilesystemOperator $certificateStorage,
        private readonly PrivateKeyFileManager $privateKeyFileManager,
    ) {
    }

    public function handle(DeletePrivateKey $command): void
    {
        try {
            $keyFilePath = $this->getKeyFilePath($command->accountId, $command->uuid);

            $this->deleteKeyFile($keyFilePath);

            $this->deleteAccountDirIfEmpty($command->accountId);
        } catch (Throwable $e) {
            captureException($e);

            throw $e;
        }
    }

    private function deleteKeyFile(string $keyFilePath): void
    {
        try {
            $this->certificateStorage->delete($keyFilePath);
        } catch (Throwable) {
            throw FailedToDeletePrivateKey::failedToDeleteUuidDir($keyFilePath);
        }
    }

    private function getKeyFilePath(string $accountDirName, string $uuid): string
    {
        $keyFilePath = PrivateKeyFileManager::createKeyFileName($accountDirName, $uuid);

        if (! $this->certificateStorage->directoryExists($accountDirName)) {
            throw FailedToDeletePrivateKey::accountDirNotFound($accountDirName);
        }

        if (! $this->certificateStorage->fileExists($keyFilePath)) {
            throw FailedToDeletePrivateKey::fileNotFound($keyFilePath);
        }

        return $keyFilePath;
    }

    private function deleteAccountDirIfEmpty(string $accountDirName): void
    {
        if (! $this->privateKeyFileManager->isAccountDirectoryEmpty($accountDirName)) {
            return;
        }

        try {
            $this->certificateStorage->deleteDirectory($accountDirName);
        } catch (Throwable) {
            captureException(FailedToDeletePrivateKey::failedToDeleteAccountDir($accountDirName));
        }
    }
}
