<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Command;

use Cdn77\NxgApi\Certificate\Domain\Exception\FailedToStorePrivateKey;
use Cdn77\NxgApi\Certificate\Domain\PrivateKeyFileManager;
use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificatePairValidator;
use League\Flysystem\FilesystemOperator;
use Throwable;

use function Sentry\captureException;

final class StorePrivateKeyHandler implements CommandHandler
{
    public function __construct(
        private readonly CertificatePairValidator $certificatePairValidator,
        private readonly FilesystemOperator $certificateStorage,
    ) {
    }

    public function handle(StorePrivateKey $command): void
    {
        try {
            $this->certificatePairValidator->validate(new CertificatePair($command->certificate, $command->key));

            $this->prepareAccountDir($command->accountId);

            $keyPath = PrivateKeyFileManager::createKeyFileName($command->accountId, $command->uuid);

            $this->writeKeyFile($keyPath, $command->key);
            $this->validateSavedPrivateKey($keyPath, $command->key);
        } catch (Throwable $e) {
            captureException($e);

            throw $e;
        }
    }

    private function validateSavedPrivateKey(string $keyPath, string $privateKey): void
    {
        if ($privateKey !== $this->certificateStorage->read($keyPath)) {
            throw FailedToStorePrivateKey::failedToValidatePrivateKey($keyPath);
        }
    }

    private function writeKeyFile(string $keyPath, string $privateKey): void
    {
        try {
            $this->certificateStorage->write($keyPath, $privateKey);
        } catch (Throwable) {
            throw FailedToStorePrivateKey::failedToWritePrivateKey($keyPath);
        }
    }

    private function prepareAccountDir(string $accountDir): void
    {
        if ($this->certificateStorage->directoryExists($accountDir)) {
            return;
        }

        try {
            $this->certificateStorage->createDirectory($accountDir);
        } catch (Throwable) {
            throw FailedToStorePrivateKey::failedToCreateAccountDir($accountDir);
        }
    }
}
