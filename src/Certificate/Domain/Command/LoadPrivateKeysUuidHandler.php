<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Command;

use Cdn77\NxgApi\Certificate\Domain\PrivateKeyFileManager;
use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use League\Flysystem\FileAttributes;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\StorageAttributes;
use Throwable;
use Webmozart\Assert\Assert;

use function basename;
use function Sentry\captureException;
use function str_replace;

final class LoadPrivateKeysUuidHandler implements QueryHandler
{
    private const int MINIMUM_KEY_FILE_SIZE = 100;

    public function __construct(
        private readonly FilesystemOperator $certificateStorage,
        private readonly PrivateKeyFileManager $privateKeyFileManager,
    ) {
    }

    /** @return array<string, int> */
    public function handle(LoadPrivateKeysUuid $command): array
    {
        try {
            return $this->loadAllKeys();
        } catch (Throwable $e) {
            captureException($e);

            throw $e;
        }
    }

    /** @return array<string, int> */
    private function loadAllKeys(): array
    {
        $accountDirectories = $this->certificateStorage->listContents('')
            ->filter(static fn (StorageAttributes $attributes) => $attributes->isDir())
            ->toArray();

        $uuidToAccountMap = [];

        foreach ($accountDirectories as $accountDir) {
            $keyFiles = $this->privateKeyFileManager->getKeyFilesInAccountDir($accountDir->path());

            foreach ($keyFiles as $keyFile) {
                Assert::isInstanceOf($keyFile, FileAttributes::class);
                if ($keyFile->fileSize() < self::MINIMUM_KEY_FILE_SIZE) {
                    continue;
                }

                $uuid = str_replace(PrivateKeyFileManager::KEY_FILE_EXTENSION, '', basename($keyFile->path()));

                $uuidToAccountMap[$uuid] = (int) $accountDir->path();
            }
        }

        return $uuidToAccountMap;
    }
}
