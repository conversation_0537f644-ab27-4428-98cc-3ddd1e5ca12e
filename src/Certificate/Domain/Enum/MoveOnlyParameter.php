<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Enum;

enum MoveOnlyParameter: string
{
    case UsedIds = 'used-ids';
    case Expired = 'expired';
    case InactiveResources = 'inactive-resources';
    case InactiveSsl = 'inactive-ssl';

    public function getDescription(): string
    {
        return match ($this) {
            self::UsedIds => 'Only files for resources in used resource IDs will be moved',
            self::Expired => 'Only files with expired certificates will be moved',
            self::InactiveResources => 'Only files for long time suspended or removed resources will be moved',
            self::InactiveSsl => 'Only files for resources with inactive SSL will be moved',
        };
    }
}
