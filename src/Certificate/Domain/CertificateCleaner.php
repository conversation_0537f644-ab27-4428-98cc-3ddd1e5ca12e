<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain;

use Cdn77\NxgApi\Certificate\Application\Console\CertificateCleanupCommand;
use Cdn77\NxgApi\Certificate\Application\DTO\SslFileCleanup;
use Cdn77\NxgApi\Certificate\Domain\Enum\MoveOnlyParameter;
use Cdn77\NxgApi\Certificate\Domain\Finder\InactiveFinder;
use Cdn77\NxgApi\Certificate\Domain\Finder\SslFilesForCleanupFinder;
use Cdn77\NxgApi\Certificate\Domain\Finder\UsedResourceIdsFinder;
use Cdn77\NxgApi\Clap\Domain\AccountFinder;
use Cdn77\NxgApi\Command\Font;
use Cdn77\NxgApi\Command\OutputWriter;
use DateTimeImmutable;
use DateTimeZone;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\StorageAttributes;
use Throwable;
use Webmozart\Assert\Assert;

use function array_flip;
use function array_key_exists;
use function count;
use function explode;
use function implode;
use function in_array;
use function openssl_x509_parse;
use function Safe\date;
use function Safe\file_put_contents;
use function Safe\preg_match;
use function Sentry\captureException;
use function sprintf;
use function strpos;

use const FILE_APPEND;
use const LOCK_EX;

final class CertificateCleaner
{
    public const string CERTIFICATE_EXTENSION = 'pem';
    public const string KEY_EXTENSION = 'key';
    private const string VALID_FILENAME_PATTERN = '/^1\d{9}_\d+\.(pem|key)$/';
    private const string SPECIAL_NAME_FOR_RESOURCE_1 = '000001';

    private OutputWriter $outputWriter;

    /** @var iterable<StorageAttributes> */
    private iterable $storageFiles = [];

    /** @var array<int, int> */
    private array $usedResourceIds = [];

    /** @var array<string, SslFileCleanup> */
    private array $dbFiles = [];

    /** @var array<int> */
    private array $inactiveResources = [];

    /** @var array<int> */
    private array $inactiveSsl = [];

    /** @var array<int> */
    private array $excludedAccounts = [];

    /** @var array<string> */
    private array $movedFiles = [];

    /** @var array<string> */
    private array $errors = [];

    /** @var array<string, DateTimeImmutable> */
    private array $expirations = [];

    private bool $dryRun = false;
    private bool $ignoreSkippingLog = false;

    public function __construct(
        private FilesystemOperator $coldFilesystem,
        private FilesystemOperator $bucketFilesystem,
        private SslFilesForCleanupFinder $sslFilesForCleanupFinder,
        private UsedResourceIdsFinder $usedResourceIdsFinder,
        private AccountFinder $accountFinder,
        private InactiveFinder $inactiveFinder,
        private string $pathBucket,
        private string $pathCold,
    ) {
    }

    public function clean(
        OutputWriter $outputWriter,
        int $limit = CertificateCleanupCommand::DEFAULT_LIMIT,
        bool $dryRun = false,
        bool $ignoreSkippingLog = false,
        MoveOnlyParameter|null $moveOnly = null,
    ): int {
        $this->outputWriter = $outputWriter;
        $this->dryRun = $dryRun;
        $this->ignoreSkippingLog = $ignoreSkippingLog;

        $start = new DateTimeImmutable();

        $this->prepareData();

        $this->logToRevert(sprintf('# Following files were moved at %s', $start->format('Y-m-d H:i:s')));

        foreach ($this->storageFiles as $file) {
            try {
                $filename = $this->extractFilename($file);
                if ($filename === null) {
                    continue;
                }

                if ($this->shouldBeMoved($filename, $start, $moveOnly) === false) {
                    continue;
                }

                $this->move($filename);

                if ($this->hasReachedLimit($limit)) {
                    $this->outputWriter->message(sprintf('Moving limit %d reached', $limit));

                    break;
                }
            } catch (Throwable $e) {
                captureException($e);
                $error = sprintf('Moving failed for file "%s". Exception: %s', $file->path(), $e->getMessage());
                $this->outputWriter->messageRed($error);
                $this->errors[] = $error;
            }
        }

        $this->logToRevert(sprintf('# End moving for files with start at %s', $start->format('Y-m-d H:i:s')));

        $this->logErrors();

        return count($this->movedFiles);
    }

    private function logErrors(): void
    {
        if (count($this->errors) === 0) {
            $this->outputWriter->message(Font::green('No errors occurred'));

            return;
        }

        foreach ($this->errors as $error) {
            $this->outputWriter->messageRed($error);
        }

        $this->outputWriter->messageRed(sprintf('Errors count: %d', count($this->errors)));
    }

    private function shouldBeMoved(
        string $filename,
        DateTimeImmutable $start,
        MoveOnlyParameter|null $moveOnly,
    ): bool {
        $resourceId = $this->extractResourceIdFromFilename($filename);

        if ($this->isReadyForArchivation($filename, $resourceId) === false) {
            return false;
        }

        $reason = $this->getReasonToMove($filename, $resourceId, $start, $moveOnly);

        if ($reason === null) {
            return false;
        }

        $this->outputWriter->message(sprintf('Moving "%s". Reason: %s', $filename, $reason));

        return true;
    }

    private function isReadyForArchivation(string $filename, int $resourceId): bool
    {
        // each certificate has two files and they are moved together so we need to skip the second file
        if (in_array($filename, $this->movedFiles, true)) {
            return false;
        }

        if (! array_key_exists($resourceId, $this->usedResourceIds)) {
            if (! array_key_exists($filename, $this->dbFiles)) {
                $this->logSkip($filename, 'Not in DB files and also not in used resource IDs.');

                return false;
            }

            $accountId = $this->dbFiles[$filename]->accountId;

            if (in_array($accountId, $this->excludedAccounts, true)) {
                $this->logSkip($filename, sprintf('Owner is important account with ID %d', $accountId));

                return false;
            }

            if (! $this->dbFiles[$filename]->archive) {
                $this->logSkip($filename, 'Not ready to archive');

                return false;
            }
        }

        return true;
    }

    private function getReasonToMove(
        string $filename,
        int $resourceId,
        DateTimeImmutable $start,
        MoveOnlyParameter|null $moveOnly,
    ): string|null {
        if (array_key_exists($resourceId, $this->usedResourceIds)) {
            return 'ID in used resource IDs';
        }

        if ($moveOnly === MoveOnlyParameter::UsedIds) {
            $this->logSkip($filename, 'Not in used resource IDs and moveOnlyUsedResourceIds is enabled');

            return null;
        }

        $exp = $this->extractExpiration($filename);
        $accountId = $this->dbFiles[$filename]->accountId;

        if ($moveOnly === MoveOnlyParameter::Expired) {
            if ($exp < $start) {
                return sprintf('Certificate is expired so ready to archive for account ID %d', $accountId);
            }

            $this->logSkip($filename, 'Certificate is not expired - not ready to archive');

            return null;
        }

        if ($moveOnly === MoveOnlyParameter::InactiveResources) {
            if (in_array($resourceId, $this->inactiveResources, true)) {
                return sprintf(
                    'Assigned to inactive resource so ready to archive for account ID %d',
                    $accountId,
                );
            }

            $this->logSkip($filename, 'Certificate is not assigned to inactive resource - not ready to archive');

            return null;
        }

        if ($moveOnly === MoveOnlyParameter::InactiveSsl) {
            if (in_array($resourceId, $this->inactiveSsl, true)) {
                return sprintf(
                    'Assigned to resource with inactive SSL so ready to archive for account ID %d',
                    $accountId,
                );
            }

            $this->logSkip(
                $filename,
                'Certificate is not assigned to resource with inactive SSL - not ready to archive',
            );

            return null;
        }

        return sprintf('Ready to archive from account ID %d', $accountId);
    }

    private function hasReachedLimit(int $limit): bool
    {
        return count($this->movedFiles) >= $limit;
    }

    private function logSkip(string $filename, string $reason): void
    {
        if ($this->ignoreSkippingLog) {
            return;
        }

        $this->outputWriter->message(sprintf('Skipping "%s". Reason: %s', $filename, $reason));
    }

    private function extractFilename(StorageAttributes $file): string|null
    {
        $name = $file->path();

        if (! $file->isFile()) {
            $this->logSkip($name, 'Not a file');

            return null;
        }

        if (strpos($name, self::SPECIAL_NAME_FOR_RESOURCE_1) !== false) {
            $this->logSkip($name, 'Special certificate for resource 1');

            return null;
        }

        if (preg_match(self::VALID_FILENAME_PATTERN, $name) === 0) {
            $this->logSkip($name, 'Invalid name');

            return null;
        }

        return explode('.', $file->path())[0];
    }

    private function move(string $name): void
    {
        $targetFolder = $this->getTargetFolder($name);

        $files = [$this->getCertificateFileName($name), $this->getKeyFileName($name)];

        foreach ($files as $file) {
            $destination = $this->getDestination($targetFolder, $file);
            $fileContent = $this->bucketFilesystem->read($file);

            if ($this->dryRun) {
                $this->outputWriter->message(sprintf(
                    'Dry run for file "%s". File is readable. Target location would be %s',
                    $file,
                    $destination,
                ));

                continue;
            }

            $this->coldFilesystem->write($destination, $fileContent);
            $this->bucketFilesystem->delete($file);

            $this->logRevertFilename($file, $destination);
            $this->outputWriter->message(sprintf('File "%s" moved to %s', $file, $destination));
        }

        $this->movedFiles[] = $name;
    }

    private function getCertificateFileName(string $name): string
    {
        return sprintf('%s.%s', $name, self::CERTIFICATE_EXTENSION);
    }

    private function getKeyFileName(string $name): string
    {
        return sprintf('%s.%s', $name, self::KEY_EXTENSION);
    }

    private function getDestination(string $folder, string $filename): string
    {
        return sprintf('%s/%s', $folder, $filename);
    }

    private function getTargetFolder(string $name): string
    {
        $expiration = $this->extractExpiration($name);

        return sprintf(
            '%s/%s',
            $expiration->format('Y'),
            $expiration->format('m'),
        );
    }

    private function extractExpiration(string $name): DateTimeImmutable
    {
        if (array_key_exists($name, $this->expirations)) {
            return $this->expirations[$name];
        }

        $data = @openssl_x509_parse($this->bucketFilesystem->read($this->getCertificateFileName($name)), false);
        Assert::isArray($data, sprintf('Failed to parse certificate from file %s', $name));

        $formats = ['ymdHis\Z', 'YmdHis\Z'];
        $expiration = null;

        foreach ($formats as $format) {
            $expiration = DateTimeImmutable::createFromFormat($format, $data['validTo'], new DateTimeZone('UTC'));
            if ($expiration !== false) {
                break;
            }
        }

        Assert::isInstanceOf(
            $expiration,
            DateTimeImmutable::class,
            sprintf('Failed to parse expiration date from file %s', $name),
        );

        $this->expirations[$name] = $expiration;

        return $expiration;
    }

    private function prepareData(): void
    {
        $this->storageFiles = $this->bucketFilesystem->listContents('')->sortByPath();
        $this->outputWriter->message(
            sprintf('Number of loaded bucket storage files: %d', count($this->storageFiles->toArray())),
        );

        $this->usedResourceIds = array_flip($this->usedResourceIdsFinder->find());
        $this->outputWriter->message(sprintf('Number of loaded used resource ids: %d', count($this->usedResourceIds)));

        $this->dbFiles = $this->sslFilesForCleanupFinder->find();
        $this->outputWriter->message(sprintf('Number of loaded $dbFiles: %d', count($this->dbFiles)));

        $this->inactiveResources = $this->inactiveFinder->findInactiveResources();
        $this->outputWriter->message(
            sprintf('Number of loaded $inactiveResources: %d', count($this->inactiveResources)),
        );

        $this->inactiveSsl = $this->inactiveFinder->findResourcesWithInactiveSsl();
        $this->outputWriter->message(
            sprintf('Number of loaded $inactiveSsl: %d', count($this->inactiveSsl)),
        );

        $this->excludedAccounts = $this->accountFinder->findVipAndTop();
        $this->outputWriter->message(
            sprintf(
                'Loaded %d important accounts: %s',
                count($this->excludedAccounts),
                implode(',', $this->excludedAccounts),
            ),
        );
    }

    private function logRevertFilename(string $filename, string $destination): void
    {
        $this->logToRevert(sprintf(
            'mv %s/%s %s/%s',
            $this->pathCold,
            $destination,
            $this->pathBucket,
            $filename,
        ));
    }

    private function logToRevert(string $content): void
    {
        $filename = sprintf('_revert/%s.sh', date('Y-m-d'));
        $filenameWithFullPath = sprintf('%s/_revert/%s.sh', $this->pathCold, date('Y-m-d'));

        if (! $this->coldFilesystem->fileExists($filename)) {
            $this->coldFilesystem->write($filename, "#!/bin/bash\n");
        }

        if ($this->dryRun) {
            $content = sprintf('#dry-run: %s', $content);
        }

        file_put_contents($filenameWithFullPath, sprintf("%s\n", $content), FILE_APPEND | LOCK_EX);
    }

    private function extractResourceIdFromFilename(string $filename): int
    {
        return (int) explode('_', $filename)[0];
    }
}
