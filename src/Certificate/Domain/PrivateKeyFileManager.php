<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain;

use Cdn77\NxgApi\Certificate\Domain\Exception\FailedToLoadPrivateKey;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\StorageAttributes;
use Throwable;

use function count;
use function sprintf;
use function str_ends_with;

final class PrivateKeyFileManager
{
    public const string KEY_FILE_EXTENSION = '.key';

    public function __construct(
        private readonly FilesystemOperator $certificateStorage,
    ) {
    }

    /** @return array<StorageAttributes> */
    public function getKeyFilesInAccountDir(string $accountDirName): array
    {
        return $this->certificateStorage->listContents($accountDirName)
            ->filter(static fn (StorageAttributes $attributes) => self::isKeyFile($attributes))
            ->toArray();
    }

    public function loadKeyByUuid(CertificatePair $certificatePair, string|int $accountDirName): CertificatePair
    {
        if (! $certificatePair->isPrivateKeyUuid()) {
            return $certificatePair;
        }

        $certificatePair->setPrivateKey($this->getContent(
            (string) $accountDirName,
            $certificatePair->getPrivateKey(),
        ));

        return $certificatePair;
    }

    private function getContent(string $accountDirName, string $uuid): string
    {
        try {
            return $this->certificateStorage->read(self::createKeyFileName($accountDirName, $uuid));
        } catch (Throwable) {
            throw FailedToLoadPrivateKey::unableToReadKeyFile(self::createKeyFileName($accountDirName, $uuid));
        }
    }

    public function isAccountDirectoryEmpty(string $accountDirName): bool
    {
        $remainingKeyFiles = $this->getKeyFilesInAccountDir($accountDirName);

        return count($remainingKeyFiles) === 0;
    }

    public static function createKeyFileName(string $accountDirName, string $uuid): string
    {
        return sprintf('%s/%s%s', $accountDirName, $uuid, self::KEY_FILE_EXTENSION);
    }

    private static function isKeyFile(StorageAttributes $attributes): bool
    {
        return $attributes->isFile() && str_ends_with($attributes->path(), self::KEY_FILE_EXTENSION);
    }
}
