<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Infrastructure\Finder;

use Cdn77\NxgApi\Certificate\Domain\Finder\UsedResourceIdsFinder;
use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;

use function array_map;

final class DbalUsedResourceIdsFinder implements UsedResourceIdsFinder
{
    use EntityManagerConstructor;

    /** @return array<int> */
    public function find(): array
    {
        $result = $this->entityManager->getConnection()->fetchFirstColumn(
            '
            SELECT id
            FROM used_resource_id
            ORDER BY id
        ',
        );

        return array_map('intval', $result);
    }
}
