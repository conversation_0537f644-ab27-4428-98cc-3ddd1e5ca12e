<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Infrastructure\Finder;

use Cdn77\NxgApi\Certificate\Domain\Finder\InactiveFinder;
use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\NgxConfGen\Infrastructure\Finder\DbalResourceConfigurationFinder;

use function array_column;
use function sprintf;

final class DbalInactiveFinder implements InactiveFinder
{
    use EntityManagerConstructor;

    /** @return array<int> */
    public function findInactiveResources(): array
    {
        $rows = $this->entityManager->getConnection()->fetchAllAssociative(
            sprintf('
            SELECT id
            FROM resource
            WHERE id NOT IN (SELECT id FROM resource r WHERE %s) OR deleted IS NOT NULL
            ORDER BY deleted ASC, suspended ASC
        ', DbalResourceConfigurationFinder::SUSPENDED_CONDITION),
        );

        return array_column($rows, 'id');
    }

    /** @return array<int> */
    public function findResourcesWithInactiveSsl(): array
    {
        $rows = $this->entityManager->getConnection()->fetchAllAssociative('
            SELECT id
            FROM resource
            WHERE id NOT IN (SELECT resource_id FROM ssl) and id IN (SELECT resource_id FROM ssl_file) 
            ORDER BY updated ASC
        ');

        return array_column($rows, 'id');
    }
}
