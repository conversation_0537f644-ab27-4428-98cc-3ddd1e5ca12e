<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Infrastructure\Finder;

use Cdn77\NxgApi\Certificate\Application\DTO\SslFileCleanup;
use Cdn77\NxgApi\Certificate\Domain\Finder\SslFilesForCleanupFinder;
use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\DefaultNamingStrategy;

use function sprintf;

final class DbalSslFilesForCleanupFinder implements SslFilesForCleanupFinder
{
    use EntityManagerConstructor;

    /** @return array<string, SslFileCleanup> */
    public function find(): array
    {
        $rows = $this->entityManager->getConnection()->fetchAllAssociative(
            sprintf("
            SELECT ((COALESCE(s.assigned_index, max_sf.max_index) - sf.index) >= 2)::BOOLEAN AS archive,
                   EXTRACT(YEAR FROM sf.expires_at)::INTEGER AS year,
                   EXTRACT(MONTH FROM sf.expires_at)::INTEGER AS month,
                   format('%s', sf.resource_id, sf.index) AS key,
                   r.account_id::INTEGER AS account_id
            FROM ssl_file sf
            JOIN resource r ON sf.resource_id = r.id
            LEFT JOIN ssl s ON sf.resource_id = s.resource_id
            JOIN (
                SELECT resource_id, MAX(index) AS max_index
                FROM ssl_file
                GROUP BY resource_id
            ) max_sf ON sf.resource_id = max_sf.resource_id
            ORDER BY sf.resource_id ASC, sf.index ASC
        ", DefaultNamingStrategy::NAMING_PATTERN),
        );

        $result = [];

        foreach ($rows as $row) {
            $result[(string) $row['key']] = SslFileCleanup::fromFetchResult($row);
        }

        return $result;
    }
}
