<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\IpProtection\Application\Payload;

use Cdn77\NxgApi\Schema\Legacy\Resources\IpProtection\IpProtectionAddressDetail;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;

final class IpProtectionInfo
{
    /**
     * @Assert\NotBlank
     * @Serializer\Type("string")
     * @Assert\Choice(
     *     {"whitelist", "blacklist", "disabled"},
     *     message="The value should be either 'whitelist', 'blacklist' or 'disabled'",
     *     strict=true
     * )
     */
    private string $type;

    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Schema\Legacy\Resources\IpProtection\IpProtectionAddressDetail>")
     * @Assert\NotNull
     * @Assert\Valid
     * @var list<IpProtectionAddressDetail>
     */
    private array $addresses;

    /** @param list<IpProtectionAddressDetail> $addresses */
    public function __construct(string $type, array $addresses)
    {
        $this->type = $type;
        $this->addresses = $addresses;
    }

    public function getType(): string
    {
        return $this->type;
    }

    /** @return list<IpProtectionAddressDetail> */
    public function getAddresses(): array
    {
        return $this->addresses;
    }
}
