<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\IpProtection\Domain;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\IpProtection\Application\Payload\IpProtectionInfo;
use Cdn77\NxgApi\IpProtection\Domain\Value\IpProtectionType;
use Cdn77\NxgApi\Schema\Legacy\Resources\IpProtection\IpProtectionAddressDetail;
use Cdn77\NxgApi\Service\Legacy\IpProtection\Exception\IpProtectionNotEnabled;
use Cdn77\NxgApi\Service\Legacy\IpProtection\IpProtectionManager;

use function array_map;

final class SetupIpProtection
{
    private IpProtectionManager $ipProtectionManager;

    public function __construct(IpProtectionManager $ipProtectionManager)
    {
        $this->ipProtectionManager = $ipProtectionManager;
    }

    public function setup(CdnResource $cdnResource, IpProtectionInfo $ipProtectionInfo): void
    {
        $isEnabled = true;
        $shouldDisable = $ipProtectionInfo->getType() === IpProtectionType::TYPE_DISABLED;

        try {
            $this->ipProtectionManager->get($cdnResource);
        } catch (IpProtectionNotEnabled) {
            $isEnabled = false;
        }

        if (! $isEnabled && $shouldDisable) {
            return;
        }

        // @phpstan-ignore booleanNot.alwaysTrue
        if (! $isEnabled && ! $shouldDisable) {
            $this->ipProtectionManager->enable(
                $cdnResource,
                $ipProtectionInfo->getType(),
                array_map(
                    static fn (IpProtectionAddressDetail $ipDetail) => $ipDetail->getNetwork(),
                    $ipProtectionInfo->getAddresses(),
                ),
            );

            return;
        }

        if ($isEnabled && $shouldDisable) {
            $this->ipProtectionManager->disable($cdnResource);

            return;
        }

        $this->ipProtectionManager->modify(
            $cdnResource,
            $ipProtectionInfo->getType(),
            array_map(
                static fn (IpProtectionAddressDetail $ipDetail) => $ipDetail->getNetwork(),
                $ipProtectionInfo->getAddresses(),
            ),
        );
    }
}
