<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Location\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\QueryBusResultSchema;
use Cdn77\NxgApi\Entity\Legacy\LocationGroup;
use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;
use J<PERSON>\Serializer\Annotation as Serializer;
use Webmozart\Assert\Assert;

use function array_map;

final class GroupList implements QueryBusResultSchema
{
    /**
     * @Serializer\Type("array<Cdn77\NxgApi\Location\Application\Payload\GroupListEntry>")
     * @Serializer\Inline
     * @var list<GroupListEntry>
     */
    public array $groups;

    public static function schemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::ARRAY,
            'items' => GroupListEntryDetail::schemaSpec(),
        ]);
    }

    /** @param list<GroupListEntry> $groups */
    public function __construct(array $groups)
    {
        $this->groups = $groups;
    }

    /** @inheritDoc */
    public static function fromQueryBusResult($result): self
    {
        Assert::isArray($result);
        Assert::allIsInstanceOf($result, LocationGroup::class);

        return new self(
            array_map(
                static fn (LocationGroup $group) => GroupListEntry::fromLocationGroup($group),
                $result,
            ),
        );
    }
}
