<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Location\Application\Payload;

use cebe\openapi\spec\Schema;
use cebe\openapi\spec\Type;

final class GroupListEntryDetail
{
    public const FIELD_ID = 'id';
    public const FIELD_NAME = 'name';

    public int $id;
    public string|null $name;

    public static function schemaSpec(): Schema
    {
        return new Schema([
            'type' => Type::OBJECT,
            'properties' => [
                self::FIELD_ID => new Schema(['type' => Type::INTEGER]),
                self::FIELD_NAME => new Schema(['type' => Type::STRING]),
            ],
        ]);
    }

    public function __construct(int $id, string|null $name)
    {
        $this->id = $id;
        $this->name = $name;
    }
}
