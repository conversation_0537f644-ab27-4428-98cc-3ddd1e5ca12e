<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Location\Application\Payload;

use Cdn77\NxgApi\Entity\Legacy\LocationGroup;

final class GroupListEntry
{
    public GroupListEntryDetail $cdnGroup;

    public function __construct(GroupListEntryDetail $cdnGroup)
    {
        $this->cdnGroup = $cdnGroup;
    }

    public static function fromLocationGroup(LocationGroup $group): self
    {
        return new self(new GroupListEntryDetail(
            $group->getId(),
            $group->getName(),
        ));
    }
}
