<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Location\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Response\ControllerQueryHandler;
use Cdn77\NxgApi\Location\Application\Payload\GroupList;
use Cdn77\NxgApi\Location\Domain\Query\FindAllGroups;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class GroupListController implements HasOpenApiPaths
{
    public const ROUTE_NAME_LEGACY = 'location.group.list.legacy';
    public const ROUTE_NAME = 'location.group.list';

    private ControllerQueryHandler $controllerQueryHandler;

    private PathGenerator $pathGenerator;

    public function __construct(ControllerQueryHandler $controllerQueryHandler, PathGenerator $pathGenerator)
    {
        $this->controllerQueryHandler = $controllerQueryHandler;
        $this->pathGenerator = $pathGenerator;
    }

    /**
     * @Route(path="/cdn_groups.json", name=self::ROUTE_NAME_LEGACY, methods={Request::METHOD_GET})
     * @Route(path="/cdn-groups", name=self::ROUTE_NAME, methods={Request::METHOD_GET})
     */
    public function listAction(): JsonResponse
    {
        return $this->controllerQueryHandler->handle(
            new FindAllGroups(),
            GroupList::class,
        );
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        return [
            $this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem([
                'get' => new Operation([
                    'tags' => [Tags::LOCATION],
                    'summary' => 'List all groups',
                    'description' => 'List all location groups.',
                    'responses' => new Responses([
                        Response::HTTP_OK => new \cebe\openapi\spec\Response([
                            'description' => 'An array of all existing locations.',
                            'content' => [
                                'application/json' => [
                                    'schema' => GroupList::schemaSpec(),
                                ],
                            ],
                        ]),
                    ]),
                ]),
            ]),
        ];
    }
}
