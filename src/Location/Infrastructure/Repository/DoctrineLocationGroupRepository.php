<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Location\Infrastructure\Repository;

use Cdn77\NxgApi\Entity\Legacy\LocationGroup;
use Cdn77\NxgApi\Location\Domain\Repository\LocationGroupRepository;
use Doctrine\ORM\EntityManagerInterface;

final class DoctrineLocationGroupRepository implements LocationGroupRepository
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /** @inheritDoc */
    public function findAll(): array
    {
        return $this->entityManager->createQueryBuilder()
            ->select('g')
            ->from(LocationGroup::class, 'g')
            ->getQuery()
            ->getResult();
    }
}
