<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Location\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\Entity\Legacy\LocationGroup;
use Cdn77\NxgApi\Location\Domain\Repository\LocationGroupRepository;

final class FindAllGroupsHandler implements QueryHandler
{
    private LocationGroupRepository $groupRepository;

    public function __construct(LocationGroupRepository $groupRepository)
    {
        $this->groupRepository = $groupRepository;
    }

    /** @return list<LocationGroup> */
    public function handle(FindAllGroups $query): array
    {
        return $this->groupRepository->findAll();
    }
}
