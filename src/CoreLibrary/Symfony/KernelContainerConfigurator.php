<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\CoreLibrary\Symfony;

use Symfony\Component\Config\Loader\LoaderInterface;
use Symfony\Component\DependencyInjection\Definition;
use Symfony\Component\DependencyInjection\Loader\FileLoader;

use function assert;

final class KernelContainerConfigurator
{
    private LoaderInterface $loader;

    public function __construct(LoaderInterface $loader)
    {
        $this->loader = $loader;
    }

    public function configure(): void
    {
        $this->addAllClassesAsNonPublicServices();
    }

    private function addAllClassesAsNonPublicServices(): void
    {
        $directoryLoader = $this->loader->getResolver()->resolve('/');
        assert($directoryLoader instanceof FileLoader);

        $prototype = new Definition();
        $prototype->setAutowired(true);
        $prototype->setAutoconfigured(true);
        $prototype->setPublic(false);

        $directoryLoader->registerClasses(
            $prototype,
            'Cdn77\\NxgApi\\',
            '%kernel.project_dir%/src/',
        );
    }
}
