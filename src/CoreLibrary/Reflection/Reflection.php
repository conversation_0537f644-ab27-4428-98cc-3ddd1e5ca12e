<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\CoreLibrary\Reflection;

use ReflectionClass;
use ReflectionException;
use ReflectionProperty;

final class Reflection
{
    public static function getPropertyValue(object $object, string $propertyName): mixed
    {
        return self::getProperty($object, $propertyName)->getValue($object);
    }

    /** @param ReflectionClass<object> $reflectionClass */
    private static function getProperty(
        object $object,
        string $propertyName,
        ReflectionClass|null $reflectionClass = null,
    ): ReflectionProperty {
        $reflectionClass ??= new ReflectionClass($object);

        try {
            $property = $reflectionClass->getProperty($propertyName);
        } catch (ReflectionException $exception) {
            $parent = $reflectionClass->getParentClass();
            if ($parent === false) {
                throw $exception;
            }

            return self::getProperty($object, $propertyName, $parent);
        }

        $property->setAccessible(true);

        return $property;
    }
}
