<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\CoreLibrary\Kafka;

use SimPod\Kafka\Clients\CommonClientConfigs;
use SimPod\Kafka\Clients\Producer\ProducerConfig;
use SimPod\KafkaBundle\Kafka\Configuration;

final class ConfigFactory
{
    private Configuration $configuration;

    public function __construct(Configuration $configuration)
    {
        $this->configuration = $configuration;
    }

    public function createProducerConfig(): ProducerConfig
    {
        $config = new ProducerConfig();
        $config->set(CommonClientConfigs::BOOTSTRAP_SERVERS_CONFIG, $this->configuration->getBootstrapServers());

        return $config;
    }
}
