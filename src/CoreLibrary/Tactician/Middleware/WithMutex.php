<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\CoreLibrary\Tactician\Middleware;

use Webmozart\Assert\Assert;

trait WithMutex
{
    /** @internal */
    private bool $inOperation = false;

    /**
     * @param object $command
     *
     * @phpcsSuppress SlevomatCodingStandard.TypeHints.ParameterTypeHint.MissingNativeTypeHint
     */
    public function execute($command, callable $next): mixed
    {
        Assert::false(
            $this->inOperation,
            "Command bus is already in operation. You're probably invoking command bus within command bus.",
        );

        $this->inOperation = true;

        try {
            return $this->executeMutex($command, $next);
        } finally {
            $this->inOperation = false;
        }
    }

    abstract protected function executeMutex(object $command, callable $next): mixed;
}
