<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\CoreLibrary\Tactician\Middleware;

use Doctrine\ORM\EntityManagerInterface;
use League\Tactician\Middleware;

final class DoctrineTransactionMiddleware implements Middleware
{
    use WithMutex;

    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * @return mixed
     *
     * @inheritDoc
     */
    public function executeMutex(object $command, callable $next): mixed
    {
        return $this->entityManager->wrapInTransaction(
            static fn () => $next($command),
        );
    }
}
