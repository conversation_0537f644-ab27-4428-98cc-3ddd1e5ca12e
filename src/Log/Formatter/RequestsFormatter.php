<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Log\Formatter;

use Monolog\Formatter\NormalizerFormatter;

use function Safe\preg_replace;
use function sprintf;
use function substr;

class RequestsFormatter extends NormalizerFormatter
{
    private const FORMAT = "[%s @ %s] %s:\n%s\n=========================\n";

    /** @param mixed[] $record */
    public function format(array $record): string
    {
        $record['context']['payload'] = (string) $record['context']['payload'];

        $record = parent::format($record);

        $strippedPayload = $this->stripSensitiveInformation($record['context']['payload']);

        return sprintf(
            self::FORMAT,
            $record['datetime'],
            $record['extra']['request_id'],
            $record['message'],
            substr($strippedPayload, 0, 1000),
        );
    }

    private function stripSensitiveInformation(string $input): string
    {
        $regex = '~(-+BEGIN (?:[A-Z]+ )?PRIVATE KEY-+\\\\n)[a-zA-Z0-9+=/\\\\]+'
            . '(?<!\\\\n)(\\\\n-+END (?:[A-Z]+ )?PRIVATE KEY-+)~';

        return preg_replace($regex, '\1<stripped>\2', $input);
    }
}
