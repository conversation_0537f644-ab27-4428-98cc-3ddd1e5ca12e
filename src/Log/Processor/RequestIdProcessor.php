<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Log\Processor;

use Cdn77\NxgApi\RequestId\RequestIdManager;

class RequestIdProcessor
{
    /** @var RequestIdManager */
    private $requestIdManager;

    public function __construct(RequestIdManager $requestIdManager)
    {
        $this->requestIdManager = $requestIdManager;
    }

    /**
     * @param mixed[] $record
     *
     * @return mixed[]
     */
    public function __invoke(array $record): array
    {
        $record['extra']['request_id'] = $this->requestIdManager->get();

        return $record;
    }
}
