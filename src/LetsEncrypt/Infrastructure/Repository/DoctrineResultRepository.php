<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Infrastructure\Repository;

use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\Entity\LetsEncrypt\Result;
use Cdn77\NxgApi\LetsEncrypt\Domain\Repository\ResultRepository;
use DateTimeImmutable;

final class DoctrineResultRepository implements ResultRepository
{
    use EntityManagerConstructor;

    public function remove(Result $result): void
    {
        $this->entityManager->remove($result);
    }

    /** @inheritDoc */
    public function findAllCompletedBefore(DateTimeImmutable $completedAt, int $limit): array
    {
        return $this->entityManager->createQueryBuilder()
            ->select('result')
            ->from(Result::class, 'result')
            ->where('result.completedAt <= :completedAt')
            ->setParameter('completedAt', $completedAt)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }
}
