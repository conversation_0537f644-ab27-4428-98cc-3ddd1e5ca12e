<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Infrastructure\Repository;

use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\LetsEncrypt\RequestState;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\LetsEncrypt\Domain\Repository\TaskRepository;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\QueryBuilder;

final class DoctrineTaskRepository implements TaskRepository
{
    use EntityManagerConstructor;

    /** @inheritDoc */
    public function findAllRunnableUntil(DateTimeImmutable $until, bool $includeSuspended = false): array
    {
        return $this->createFindAllWithRequestsQueryBuilder($includeSuspended)
            ->andWhere('t.runAt <= :until')
            ->setParameter('until', $until, Types::DATETIMETZ_IMMUTABLE)
            ->andWhere('r.state = :state')
            ->setParameter('state', RequestState::PENDING)
            ->setMaxResults(100)
            ->getQuery()
            ->getResult();
    }

    /** @inheritDoc */
    public function findSpecificTasksByResourcesIds(array $resourcesIds, bool $includeSuspended = false): array
    {
        return $this->createFindAllWithRequestsQueryBuilder($includeSuspended)
            ->andWhere('r.resource IN (:resourcesIds)')
            ->setParameter('resourcesIds', $resourcesIds)
            ->andWhere('r.state = :state')
            ->setParameter('state', RequestState::PENDING)
            ->getQuery()
            ->getResult();
    }

    private function createFindAllWithRequestsQueryBuilder(bool $includeSuspended): QueryBuilder
    {
        $qb = $this->entityManager
            ->createQueryBuilder()
            ->select('t')
            ->from(Task::class, 't')
            ->innerJoin('t.request', 'r')
            ->orderBy('t.runAt', 'ASC');

        if ($includeSuspended === false) {
            $qb->andWhere('
                EXISTS (
                    SELECT 1
                    FROM ' . CdnResource::class . ' rsc
                    WHERE rsc = r.resource AND rsc.suspended IS NULL
                )
            ');
        }

        return $qb;
    }
}
