<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Infrastructure\Finder;

use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\LetsEncrypt\Request;
use Cdn77\NxgApi\Entity\LetsEncrypt\RequestState;
use Cdn77\NxgApi\LetsEncrypt\Domain\Finder\RequestFinder;
use Cdn77\NxgApi\LetsEncrypt\Domain\Value\RequestStateReason;

final class DoctrineRequestFinder implements RequestFinder
{
    use EntityManagerConstructor;

    public function hasResourcePendingOrStale(CdnResource $resource): bool
    {
        $latest = $this->entityManager->createQueryBuilder()
            ->select('r.state, r.stateReason')
            ->from(Request::class, 'r')
            ->where('r.resource = :resource')
            ->setParameter('resource', $resource)
            ->orderBy('r.createdAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        if ($latest === null) {
            return false;
        }

        return $latest['state'] === RequestState::PENDING
            || (
                $latest['state'] === RequestState::CANCELLED
                && $latest['stateReason'] === RequestStateReason::CancelledTooOld
            );
    }
}
