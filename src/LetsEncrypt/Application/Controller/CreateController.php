<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Application\Controller;

use Cdn77\MonMasterDetector\MasterStateDetectorInterface;
use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Application\Response\ControllerCommandHandler;
use Cdn77\NxgApi\LetsEncrypt\Application\Payload\ResultSchema;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use cebe\openapi\spec\MediaType;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\RequestBody;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class CreateController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'letsencrypt.result.create';

    private ControllerCommandHandler $controllerCommandHandler;

    private ControllerSchemaSerializer $controllerSchemaSerializer;

    private MasterStateDetectorInterface $masterStateDetector;

    private PathGenerator $pathGenerator;

    public function __construct(
        ControllerCommandHandler $controllerCommandHandler,
        ControllerSchemaSerializer $controllerSchemaSerializer,
        MasterStateDetectorInterface $masterStateDetector,
        PathGenerator $pathGenerator,
    ) {
        $this->controllerCommandHandler = $controllerCommandHandler;
        $this->controllerSchemaSerializer = $controllerSchemaSerializer;
        $this->masterStateDetector = $masterStateDetector;
        $this->pathGenerator = $pathGenerator;
    }

    /** @Route(path="/letsencrypt/result", name=self::ROUTE_NAME, methods={Request::METHOD_POST}) */
    public function createAction(Request $request): Response
    {
        if (! $this->masterStateDetector->getState()->isMaster()) {
            return $this->controllerSchemaSerializer->serializeToResponse(
                new ErrorsSchema(['Server is not master']),
                Response::HTTP_CONFLICT,
            );
        }

        $schema = $this->controllerSchemaSerializer->deserialize($request, ResultSchema::class);

        if ($schema instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($schema);
        }

        return $this->controllerCommandHandler->handle($schema->toCommand());
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $post = new Operation([
            'tags' => [Tags::LETS_ENCRYPT],
            'summary' => 'Create new result',
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new \cebe\openapi\spec\Response(
                    ['description' => 'The result is accepted and request is added to queue again.'],
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY => new \cebe\openapi\spec\Response(
                    ['description' => 'Invalid input data.'],
                ),
                Response::HTTP_CONFLICT => new \cebe\openapi\spec\Response(['description' => 'Server is not master.']),
            ]),
            'requestBody' => new RequestBody([
                'required' => true,
                'content' => ['application/json' => new MediaType(['schema' => ResultSchema::getSchemaSpec()])],
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['post' => $post])];
    }
}
