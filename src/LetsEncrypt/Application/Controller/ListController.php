<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Application\Controller;

use Cdn77\MonMasterDetector\MasterStateDetectorInterface;
use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Application\Response\ControllerQueryHandler;
use Cdn77\NxgApi\LetsEncrypt\Application\Payload\RequestsWithDomainsListSchema;
use Cdn77\NxgApi\LetsEncrypt\Domain\Query\FindAllQueuedTasks;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class ListController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'letsencrypt.queue.list';

    private ControllerQueryHandler $controllerQueryHandler;

    private ControllerSchemaSerializer $controllerSchemaSerializer;

    private MasterStateDetectorInterface $masterStateDetector;

    private PathGenerator $pathGenerator;

    public function __construct(
        ControllerQueryHandler $controllerQueryHandler,
        ControllerSchemaSerializer $controllerSchemaSerializer,
        MasterStateDetectorInterface $masterStateDetector,
        PathGenerator $pathGenerator,
    ) {
        $this->controllerSchemaSerializer = $controllerSchemaSerializer;
        $this->controllerQueryHandler = $controllerQueryHandler;
        $this->masterStateDetector = $masterStateDetector;
        $this->pathGenerator = $pathGenerator;
    }

    /** @Route(path="/letsencrypt/queue", name=self::ROUTE_NAME, methods={Request::METHOD_GET}) */
    public function listAction(): Response
    {
        if (! $this->masterStateDetector->getState()->isMaster()) {
            return $this->controllerSchemaSerializer->serializeToResponse(
                new ErrorsSchema(['Server is not master']),
                Response::HTTP_CONFLICT,
            );
        }

        return $this->controllerQueryHandler->handle(
            new FindAllQueuedTasks(),
            RequestsWithDomainsListSchema::class,
        );
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $get = new Operation([
            'tags' => [Tags::LETS_ENCRYPT],
            'summary' => 'Queued tasks',
            'description' => 'Get the queued tasks.',
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new \cebe\openapi\spec\Response(
                    ['description' => 'There are not any requests to process.'],
                ),
                Response::HTTP_OK => new \cebe\openapi\spec\Response(
                    ['description' => 'List of requests.'],
                ),
                Response::HTTP_CONFLICT => new \cebe\openapi\spec\Response(['description' => 'Server is not master.']),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['get' => $get])];
    }
}
