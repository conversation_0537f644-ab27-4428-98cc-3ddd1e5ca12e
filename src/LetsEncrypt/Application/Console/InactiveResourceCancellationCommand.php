<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Application\Console;

use Cdn77\MonMasterDetector\MasterStateDetectorInterface;
use Cdn77\NxgApi\Command\OutputWriter;
use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\LetsEncrypt\Domain\Command\CancelInactiveResourceRequests;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Webmozart\Assert\Assert;

use function count;
use function implode;
use function sprintf;

final class InactiveResourceCancellationCommand extends Command
{
    private const NAME = 'letsencrypt:request:cancel-inactive';
    private const OPTION_LIMIT = 'limit';

    public function __construct(
        private readonly CommandBus $commandBus,
        private readonly MasterStateDetectorInterface $masterStateDetector,
    ) {
        parent::__construct(self::NAME);
    }

    public function configure(): void
    {
        $this->setName(self::NAME)
            ->setDescription('Cleans up old Lets encrypt requests for inactive resources')
            ->addOption(
                self::OPTION_LIMIT,
                'l',
                InputOption::VALUE_OPTIONAL,
                'How many requests can be cancelled',
                '10',
            );
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $limit = $this->getLimit($input);

        $outputWriter = new OutputWriter($output);
        $outputWriter->message(
            sprintf('Starting cancellation of Lets encrypt requests for inactive resources with limit %d', $limit),
        );

        if (! $this->masterStateDetector->getState()->isMaster()) {
            $outputWriter->message('I am not master. Exiting.');

            return 0;
        }

        $outputWriter->message('Passed master check. Inactive resource cancellation running');

        $inactivatedResourcesIds = $this->commandBus->handle(new CancelInactiveResourceRequests($limit));

        if (count($inactivatedResourcesIds) > 0) {
            $outputWriter->message(sprintf(
                'Finished. Cancelled requests for %d resources: %s',
                count($inactivatedResourcesIds),
                implode(',', $inactivatedResourcesIds),
            ));
        } else {
            $outputWriter->message('Finished. No requests with inactive resources to cancel');
        }

        return 0;
    }

    private function getLimit(InputInterface $input): int
    {
        $limit = $input->getOption(self::OPTION_LIMIT);
        Assert::numeric($limit);

        return (int) $limit;
    }
}
