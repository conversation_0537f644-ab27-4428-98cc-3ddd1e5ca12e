<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Application\Console;

use Cdn77\MonMasterDetector\MasterStateDetectorInterface;
use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\LetsEncrypt\Domain\Command\CleanupResults;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class ResultCleanupCommand extends Command
{
    private const NAME = 'letsencrypt:result:cleanup';

    private CommandBus $commandBus;
    private MasterStateDetectorInterface $masterStateDetector;

    public function __construct(CommandBus $commandBus, MasterStateDetectorInterface $masterStateDetector)
    {
        $this->commandBus = $commandBus;
        $this->masterStateDetector = $masterStateDetector;

        parent::__construct(self::NAME);
    }

    public function configure(): void
    {
        $this->setDescription('Cleans up old letsencrypt results from the database');
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        if (! $this->masterStateDetector->getState()->isMaster()) {
            $output->writeln('I am not master. Exiting.');

            return 0;
        }

        $output->writeln('Passed master check. Cleanup running.');

        $this->commandBus->handle(new CleanupResults());

        return 0;
    }
}
