<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\Schema;
use Cdn77\NxgApi\Entity\LetsEncrypt\ResultStatus;
use Cdn77\NxgApi\LetsEncrypt\Domain\Command\CreateResult;
use cebe\openapi\spec\Schema as OpenApiSchema;
use cebe\openapi\spec\Type;
use JMS\Serializer\Annotation as Serializer;
use Ramsey\Uuid\UuidInterface;
use Symfony\Component\Validator\Constraints as Assert;

final class ResultSchema implements Schema
{
    public const FIELD_CERTIFICATE = 'certificate';
    public const FIELD_ERROR = 'error';
    public const FIELD_PRIVATE_KEY = 'private_key';
    public const FIELD_REQUEST_ID = 'request_id';
    public const FIELD_VALIDATION_ERROR = 'validation_error';

    /**
     * @Serializer\Type("uuid")
     * @Assert\NotBlank
     * @Assert\NotNull
     */
    public UuidInterface|null $requestId = null;

    public string|null $certificate = null;

    public string|null $privateKey = null;

    public string|null $validationError = null;

    public string|null $error = null;

    public static function getSchemaSpec(): OpenApiSchema
    {
        return new OpenApiSchema([
            'type' => Type::OBJECT,
            'properties' => [
                self::FIELD_REQUEST_ID => new OpenApiSchema(['type' => Type::STRING, 'format' => 'uuid']),
                self::FIELD_CERTIFICATE => new OpenApiSchema(['type' => Type::STRING]),
                self::FIELD_PRIVATE_KEY => new OpenApiSchema(['type' => Type::STRING]),
                self::FIELD_VALIDATION_ERROR => new OpenApiSchema(['type' => Type::STRING]),
                self::FIELD_ERROR => new OpenApiSchema(['type' => Type::STRING]),
            ],
        ]);
    }

    private function __construct()
    {
    }

    public function getDescription(): string|null
    {
        return $this->validationError ?? $this->error;
    }

    public function getStatus(): ResultStatus
    {
        if ($this->certificate !== null) {
            return ResultStatus::getSuccess();
        }

        if ($this->error === '' || $this->error === null) {
            return ResultStatus::getValidationError();
        }

        return ResultStatus::getError();
    }

    public function toCommand(): CreateResult
    {
        \Webmozart\Assert\Assert::notNull($this->requestId);

        return new CreateResult(
            $this->requestId,
            $this->getStatus(),
            $this->certificate,
            $this->privateKey,
            $this->getDescription(),
        );
    }
}
