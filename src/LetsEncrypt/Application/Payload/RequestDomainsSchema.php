<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Application\Payload;

use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\Exception\InvalidArgument;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Ramsey\Uuid\UuidInterface;

use function count;

final class RequestDomainsSchema
{
    /** @Serializer\Type("uuid") */
    public UuidInterface $requestId;

    /**
     * @Serializer\Type("array<string>")
     * @var list<string>
     */
    public array $domains;

    /** @param list<string> $domains */
    public function __construct(UuidInterface $requestId, array $domains)
    {
        $this->requestId = $requestId;

        if (count($domains) === 0) {
            throw new InvalidArgument('No domains given.');
        }

        $this->domains = $domains;
    }

    public static function fromTask(Task $task): self
    {
        return new self(
            $task->getRequest()->getId(),
            $task->getRequest()->getDomains(),
        );
    }
}
