<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\QueryBusResultSchema;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Web<PERSON>zart\Assert\Assert;

use function array_map;

class RequestsWithDomainsListSchema implements QueryBusResultSchema
{
    /**
     * @Serializer\Type("array<Cdn77\NxgApi\LetsEncrypt\Application\Payload\RequestDomainsSchema>")
     * @Serializer\Inline
     * @var list<RequestDomainsSchema>
     */
    public array $tasks;

    /** @param list<RequestDomainsSchema> $tasks */
    public function __construct(array $tasks)
    {
        $this->tasks = $tasks;
    }

    /** @inheritDoc */
    public static function fromQueryBusResult($result): self
    {
        Assert::isArray($result);
        Assert::allIsInstanceOf($result, Task::class);

        return new self(
            array_map(
                static fn (Task $task) => RequestDomainsSchema::fromTask($task),
                $result,
            ),
        );
    }
}
