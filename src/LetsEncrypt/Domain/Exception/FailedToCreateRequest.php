<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use DomainException;

final class FailedToCreateRequest extends DomainException implements NxgApiDomainException
{
    public static function alreadyFinished(): self
    {
        return new self('The request is finished and could not be changed.');
    }
}
