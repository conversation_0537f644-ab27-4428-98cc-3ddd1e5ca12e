<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use DomainException;
use Ramsey\Uuid\UuidInterface;

use function sprintf;

final class RequestNotFound extends DomainException implements NxgApiDomainException
{
    public static function fromRequestId(UuidInterface $requestId): self
    {
        return new self(sprintf('Request "%s" doesn\'t exist', $requestId));
    }
}
