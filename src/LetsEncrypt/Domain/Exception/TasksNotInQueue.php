<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use DomainException;

final class TasksNotInQueue extends DomainException implements NxgApiDomainException
{
    public static function forDebug(): self
    {
        return new self('Some debug resources are not in tasks. Try add them with letsencrypt:renew command');
    }
}
