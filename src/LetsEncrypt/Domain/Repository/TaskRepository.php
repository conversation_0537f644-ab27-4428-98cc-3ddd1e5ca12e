<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Domain\Repository;

use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use DateTimeImmutable;

interface TaskRepository
{
    /** @return list<Task> */
    public function findAllRunnableUntil(DateTimeImmutable $until, bool $includeSuspended = false): array;

    /**
     * @param array<int> $resourcesIds
     *
     * @return list<Task>
     */
    public function findSpecificTasksByResourcesIds(array $resourcesIds, bool $includeSuspended = false): array;
}
