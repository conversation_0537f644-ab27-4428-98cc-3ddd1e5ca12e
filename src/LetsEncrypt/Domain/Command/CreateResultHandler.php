<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\LetsEncrypt\Domain\Exception\FailedToCreateRequest;
use Cdn77\NxgApi\LetsEncrypt\Domain\Exception\RequestNotFound;
use Cdn77\NxgApi\Repository\LetsEncrypt\RequestRepository;
use Cdn77\NxgApi\Service\LetsEncrypt\ResultManager;
use Webmozart\Assert\Assert;

final class CreateResultHandler implements CommandHandler
{
    private RequestRepository $requestRepository;

    private ResultManager $resultManager;

    public function __construct(RequestRepository $requestRepository, ResultManager $resultManager)
    {
        $this->requestRepository = $requestRepository;
        $this->resultManager = $resultManager;
    }

    public function handle(CreateResult $command): void
    {
        $request = $this->requestRepository->find($command->requestId);

        if ($request === null) {
            throw RequestNotFound::fromRequestId($command->requestId);
        }

        if (! $request->getState()->isPending()) {
            throw FailedToCreateRequest::alreadyFinished();
        }

        $certificatePair = null;
        if ($command->status->isSuccess()) {
            Assert::notNull($command->certificate);
            Assert::notNull($command->privateKey);

            $certificatePair = CertificatePair::decode($command->certificate, $command->privateKey);
        }

        $this->resultManager->createResult(
            $request,
            $command->status,
            $certificatePair,
            $command->description,
        );
    }
}
