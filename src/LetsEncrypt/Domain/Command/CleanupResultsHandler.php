<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\LetsEncrypt\Domain\Repository\ResultRepository;
use DateTimeImmutable;

final class CleanupResultsHandler implements CommandHandler
{
    private const BATCH_SIZE = 10000;

    private ResultRepository $resultRepository;

    public function __construct(ResultRepository $resultRepository)
    {
        $this->resultRepository = $resultRepository;
    }

    public function handle(CleanupResults $command): void
    {
        $removeBeforeDate = new DateTimeImmutable();
        $results = $this->resultRepository->findAllCompletedBefore(
            $removeBeforeDate->modify('-3 months'),
            self::BATCH_SIZE,
        );

        foreach ($results as $result) {
            $this->resultRepository->remove($result);
        }
    }
}
