<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Domain\Command;

use Cdn77\NxgApi\Core\Domain\Bus\Command;
use Cdn77\NxgApi\Entity\LetsEncrypt\ResultStatus;
use Ramsey\Uuid\UuidInterface;

final class CreateResult implements Command
{
    public string|null $certificate;

    public string|null $description;

    public string|null $privateKey;

    public UuidInterface $requestId;

    public ResultStatus $status;

    public function __construct(
        UuidInterface $requestId,
        ResultStatus $status,
        string|null $certificate,
        string|null $privateKey,
        string|null $description,
    ) {
        $this->certificate = $certificate;
        $this->description = $description;
        $this->privateKey = $privateKey;
        $this->requestId = $requestId;
        $this->status = $status;
    }
}
