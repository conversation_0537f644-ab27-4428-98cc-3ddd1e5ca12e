<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Domain\Command;

use Cdn77\NxgApi\Certificate\Domain\Finder\InactiveFinder;
use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Entity\LetsEncrypt\RequestState;
use Cdn77\NxgApi\Filter\Resource\DeletedResourceFilter;
use Cdn77\NxgApi\LetsEncrypt\Domain\Value\RequestStateReason;
use Cdn77\NxgApi\Repository\LetsEncrypt\RequestRepository;
use Cdn77\NxgApi\Repository\LetsEncrypt\TaskRepository as LegacyTaskRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Clock\ClockInterface;

use function count;

final class CancelInactiveResourceRequestsHandler implements CommandHandler
{
    public function __construct(
        private readonly ClockInterface $clock,
        private readonly InactiveFinder $inactiveFinder,
        private readonly RequestRepository $requestRepository,
        private readonly LegacyTaskRepository $legacyTaskRepository,
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    /** @return list<int> */
    public function handle(CancelInactiveResourceRequests $command): array
    {
        $filters = $this->entityManager->getFilters();
        $filters->disable(DeletedResourceFilter::NAME);

        $inactiveResourcesIds = $this->inactiveFinder->findInactiveResources();
        $requests = $this->requestRepository->findBy(
            ['resource' => $inactiveResourcesIds, 'state' => RequestState::PENDING],
        );

        $inactivatedResourcesIds = [];

        foreach ($requests as $request) {
            $request->cancel($this->clock->now(), RequestStateReason::CancelledInactiveResource);
            $this->legacyTaskRepository->removeRequestTask($request);

            $inactivatedResourcesIds[] = $request->getResource()->getId();

            if (count($inactivatedResourcesIds) >= $command->limit) {
                break;
            }
        }

        $filters->enable(DeletedResourceFilter::NAME);

        return $inactivatedResourcesIds;
    }
}
