<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\LetsEncrypt\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\Core\Domain\Exception\NoContent;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\LetsEncrypt\Domain\Exception\TasksNotInQueue;
use Cdn77\NxgApi\LetsEncrypt\Domain\Repository\TaskRepository;
use Symfony\Component\Clock\ClockInterface;

use function count;

final class FindAllQueuedTasksHandler implements QueryHandler
{
    /** @param list<int> $debugResourceIds */
    public function __construct(
        private readonly ClockInterface $clock,
        private TaskRepository $doctrineTaskRepository,
        private array $debugResourceIds,
    ) {
    }

    /**
     * @return list<Task>
     *
     * @throws NoContent
     */
    public function handle(FindAllQueuedTasks $query): array
    {
        $isDebugRequest = $this->debugResourceIds !== [];
        $tasks = $isDebugRequest
            ? $this->doctrineTaskRepository->findSpecificTasksByResourcesIds($this->debugResourceIds)
            : $this->doctrineTaskRepository->findAllRunnableUntil($this->clock->now());

        if ($isDebugRequest && count($tasks) < count($this->debugResourceIds)) {
            throw TasksNotInQueue::forDebug();
        }

        if (! $isDebugRequest && $tasks === []) {
            throw new NoContent();
        }

        return $tasks;
    }
}
