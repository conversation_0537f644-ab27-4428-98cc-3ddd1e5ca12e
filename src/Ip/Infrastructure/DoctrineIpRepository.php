<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Ip\Infrastructure;

use Cdn77\NxgApi\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\NxgApi\Entity\Legacy\Ip;
use Cdn77\NxgApi\Ip\Domain\Exception\IpNotFound;
use Cdn77\NxgApi\Ip\Domain\Repository\IpRepository;

final class DoctrineIpRepository implements IpRepository
{
    use EntityManagerConstructor;

    public function getForIp(string $ip): Ip
    {
        $ipEntity = $this->findForIp($ip);

        if ($ipEntity === null) {
            throw IpNotFound::fromIp($ip);
        }

        return $ipEntity;
    }

    public function findForIp(string $ip): Ip|null
    {
        return $this->entityManager->getRepository(Ip::class)->findOneBy(['ip' => $ip]);
    }
}
