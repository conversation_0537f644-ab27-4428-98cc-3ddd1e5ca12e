<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Ip\Domain\Query;

use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use Cdn77\NxgApi\Core\Domain\ServerStatusCalculator;
use Cdn77\NxgApi\Ip\Domain\Dto\IpStatus;
use Cdn77\NxgApi\Ip\Domain\Repository\IpRepository;

final class FindIpStatusHandler implements QueryHandler
{
    private IpRepository $ipRepository;

    private ServerStatusCalculator $serverStatusCalculator;

    public function __construct(IpRepository $ipRepository, ServerStatusCalculator $serverStatusCalculator)
    {
        $this->ipRepository = $ipRepository;
        $this->serverStatusCalculator = $serverStatusCalculator;
    }

    public function handle(FindIpStatus $query): IpStatus
    {
        $ip = $this->ipRepository->getForIp($query->ip);
        $serverStatus = $this->serverStatusCalculator->getServerStatus($ip->getServer());

        return new IpStatus($ip, $serverStatus);
    }
}
