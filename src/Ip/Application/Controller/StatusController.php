<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Ip\Application\Controller;

use Cdn77\NxgApi\Core\Application\Payload\ServerStatusSchema;
use Cdn77\NxgApi\Core\Domain\Bus\QueryBus;
use Cdn77\NxgApi\Ip\Domain\Exception\IpNotFound;
use Cdn77\NxgApi\Ip\Domain\Query\FindIpStatus;
use Cdn77\NxgApi\Schema\Internal\Ip\Status\IpStatusSchema;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class StatusController
{
    public const ROUTE_NAME = 'ip.status';
    public const ROUTE_SUMMARY = 'Ip Status';

    private QueryBus $queryBus;

    private SerializerInterface $serializer;

    public function __construct(QueryBus $queryBus, SerializerInterface $serializer)
    {
        $this->queryBus = $queryBus;
        $this->serializer = $serializer;
    }

    /** @Route(path="/internal/ip/{ip}", name=self::ROUTE_NAME, methods={Request::METHOD_GET}) */
    public function statusAction(string $ip): JsonResponse
    {
        try {
            $ipStatus = $this->queryBus->handle(new FindIpStatus($ip));
        } catch (IpNotFound $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(new ErrorsSchema([$e->getMessage()]), 'json'),
                Response::HTTP_NOT_FOUND,
            );
        }

        return JsonResponse::fromJsonString(
            $this->serializer->serialize(
                new IpStatusSchema(
                    $ipStatus->ip->getIp(),
                    $ipStatus->ip->isUp(),
                    $ipStatus->ip->isPrimary(),
                    ServerStatusSchema::fromServerStatus($ipStatus->serverStatus),
                ),
                'json',
            ),
            Response::HTTP_OK,
        );
    }
}
