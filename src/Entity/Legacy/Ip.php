<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Repository\Legacy\IpRepository;
use Doctrine\ORM\Mapping as ORM;

use function filter_var;

use const FILTER_FLAG_IPV6;
use const FILTER_VALIDATE_IP;

/**
 * @ORM\Entity(repositoryClass=IpRepository::class)
 * @ORM\Table(name="ip", uniqueConstraints={@ORM\UniqueConstraint(name="ip_idx", columns={"ip"})})
 */
class Ip
{
    /**
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="SEQUENCE")
     * @ORM\SequenceGenerator(sequenceName="ip_id_seq", allocationSize=1, initialValue=1)
     */
    private int $id;

    /**
     * @ORM\ManyToOne(targetEntity=Server::class, inversedBy="ips")
     * @ORM\JoinColumn(name="server_id", referencedColumnName="id", nullable=false)
     */
    private Server $server;

    /** @ORM\Column(name="ip", type="string", length=15) */
    private string $ip;

    /** @ORM\Column(name="`primary`", type="boolean") */
    private bool $primary = false;

    /** @ORM\Column(name="up", type="boolean") */
    private bool $up = true;

    /** @ORM\Column(name="disabled", type="boolean") */
    private bool $disabled = false;

    public function getId(): int
    {
        return $this->id;
    }

    public function getServer(): Server
    {
        return $this->server;
    }

    public function setServer(Server $server): void
    {
        $this->server = $server;
    }

    public function getIp(): string
    {
        return $this->ip;
    }

    public function setIp(string $ip): void
    {
        $this->ip = $ip;
    }

    public function isPrimary(): bool
    {
        return $this->primary;
    }

    public function setPrimary(bool $primary): void
    {
        $this->primary = $primary;
    }

    public function isUp(): bool
    {
        return $this->up;
    }

    public function setUp(bool $up): void
    {
        $this->up = $up;
    }

    public function isDown(): bool
    {
        return ! $this->up;
    }

    public function isDisabled(): bool
    {
        return $this->disabled;
    }

    public function setDisabled(bool $disabled): void
    {
        $this->disabled = $disabled;
    }

    public function isV6(): bool
    {
        return filter_var($this->ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6) !== false;
    }
}
