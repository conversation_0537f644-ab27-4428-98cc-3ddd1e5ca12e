<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Repository\Legacy\CustomLocationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=CustomLocationRepository::class)
 * @ORM\Table(name="custom_location", indexes={@ORM\Index(name="custom_location_idx", columns={"group_id"})})
 */
class CustomLocation
{
    /**
     * @ORM\OneToOne(targetEntity=CdnResource::class)
     * @ORM\JoinColumn(name="resource_id", referencedColumnName="id", nullable=false)
     * @ORM\Id
     *
     * @var CdnResource
     */
    private $resource;

    /**
     * @ORM\ManyToOne(targetEntity=LocationGroup::class)
     * @ORM\JoinColumn(name="group_id", referencedColumnName="id", nullable=false)
     *
     * @var LocationGroup
     */
    private $group;

    /**
     * @ORM\ManyToMany(targetEntity=Pop::class, inversedBy="customLocations", fetch="EXTRA_LAZY")
     * @ORM\JoinTable(name="custom_location_relation",
     *   joinColumns={
     *     @ORM\JoinColumn(name="resource_id", referencedColumnName="resource_id")
     *   },
     *   inverseJoinColumns={
     *     @ORM\JoinColumn(name="pop_id", referencedColumnName="id")
     *   }
     * )
     *
     * @var Collection<int, Pop>
     */
    private $pops;

    public function __construct()
    {
        $this->pops = new ArrayCollection();
    }

    public function getResource(): CdnResource
    {
        return $this->resource;
    }

    public function setResource(CdnResource $resource): void
    {
        $this->resource = $resource;
    }

    public function getGroup(): LocationGroup
    {
        return $this->group;
    }

    public function setGroup(LocationGroup $group): void
    {
        $this->group = $group;
    }

    /** @return Collection<int, Pop> */
    public function getPops(): Collection
    {
        return $this->pops;
    }
}
