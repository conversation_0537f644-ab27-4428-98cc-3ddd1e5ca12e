<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity
 * @ORM\Table(name="ipv6", uniqueConstraints={@ORM\UniqueConstraint(name="ipv6_idx", columns={"ip"})})
 */
class Ipv6
{
    /**
     * @ORM\Id
     * @ORM\Column(type="integer")
     * @ORM\GeneratedValue(strategy="SEQUENCE")
     * @ORM\SequenceGenerator(sequenceName="ipv6_id_seq")
     */
    private int $id;

    /**
     * @ORM\ManyToOne(targetEntity=Server::class, inversedBy="ips")
     * @ORM\JoinColumn(name="server_id", referencedColumnName="id", nullable=false)
     */
    private Server $server;

    /** @ORM\Column(type="string", length=45) */
    private string $ip;

    /** @ORM\Column(type="boolean") */
    private bool $primary = false;

    /** @ORM\Column(type="boolean") */
    private bool $up = true;

    /** @ORM\Column(type="boolean") */
    private bool $disabled = false;

    public function getIp(): string
    {
        return $this->ip;
    }

    public function isPrimary(): bool
    {
        return $this->primary;
    }
}
