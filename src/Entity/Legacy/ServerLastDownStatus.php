<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Repository\Legacy\ServerLastDownStatusRepository;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

use function in_array;

/**
 * @ORM\Entity(repositoryClass=ServerLastDownStatusRepository::class)
 * @ORM\Table(name="server_last_down", schema="status")
 */
class ServerLastDownStatus
{
    public const REASON_AUTO = 'auto';
    public const REASON_FORCED = 'forced';
    public const REASONS = [self::REASON_AUTO, self::REASON_FORCED];

    /**
     * @ORM\OneToOne(targetEntity="Cdn77\NxgApi\Entity\Legacy\Server")
     * @ORM\JoinColumn(nullable=false)
     * @ORM\Id
     *
     * @var Server
     */
    private $server;

    /**
     * @ORM\Column(type="datetimetz_immutable", name="went_down_ts")
     *
     * @var DateTimeImmutable
     */
    private $wentDownAt;

    /**
     * @ORM\Column
     *
     * @var string
     */
    private $reason;

    public function __construct(Server $server, DateTimeImmutable $wentDownAt, string $reason)
    {
        $this->server = $server;
        $this->update($wentDownAt, $reason);
    }

    public function getServer(): Server
    {
        return $this->server;
    }

    public function getWentDownAt(): DateTimeImmutable
    {
        return $this->wentDownAt;
    }

    public function getReason(): string
    {
        return $this->reason;
    }

    public function update(DateTimeImmutable $newLastDownAt, string $newReason): void
    {
        if (! in_array($newReason, self::REASONS, true)) {
            throw new InvalidArgument('Invalid reason.');
        }

        $this->wentDownAt = $newLastDownAt;
        $this->reason = $newReason;
    }
}
