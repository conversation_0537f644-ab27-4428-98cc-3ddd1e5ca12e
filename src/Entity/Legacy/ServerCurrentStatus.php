<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Repository\Legacy\ServerCurrentStatusRepository;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

use function in_array;

/**
 * @ORM\Entity(repositoryClass=ServerCurrentStatusRepository::class)
 * @ORM\Table(name="server_current", schema="status")
 */
class ServerCurrentStatus
{
    public const REASON_AUTO = 'auto';
    public const REASON_FORCED = 'forced';
    public const REASONS = [self::REASON_AUTO, self::REASON_FORCED];

    /**
     * @ORM\OneToOne(targetEntity="Cdn77\NxgApi\Entity\Legacy\Server")
     * @ORM\JoinColumn(nullable=false)
     * @ORM\Id
     *
     * @var Server
     */
    private $server;

    /**
     * @ORM\Column(type="datetimetz_immutable", name="last_change_ts")
     *
     * @var DateTimeImmutable
     */
    private $lastUpdateAt;

    /**
     * @ORM\Column(type="boolean")
     *
     * @var bool
     */
    private $up;

    /**
     * @ORM\Column
     *
     * @var string
     */
    private $reason;

    public function __construct(Server $server, DateTimeImmutable $lastUpdateAt, bool $up, string $reason)
    {
        $this->server = $server;
        $this->update($lastUpdateAt, $up, $reason);
    }

    public function getServer(): Server
    {
        return $this->server;
    }

    public function getLastUpdateAt(): DateTimeImmutable
    {
        return $this->lastUpdateAt;
    }

    public function isUp(): bool
    {
        return $this->up;
    }

    public function getReason(): string
    {
        return $this->reason;
    }

    public function update(DateTimeImmutable $lastUpdateAt, bool $up, string $newReason): void
    {
        if (! in_array($newReason, self::REASONS, true)) {
            throw new InvalidArgument('Invalid reason.');
        }

        $this->lastUpdateAt = $lastUpdateAt;
        $this->up = $up;
        $this->reason = $newReason;
    }
}
