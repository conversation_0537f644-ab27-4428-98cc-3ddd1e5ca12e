<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Doctrine\ORM\Mapping as ORM;

/** @ORM\Embeddable */
class ResourceCaching
{
    public const LS_RESOURCE_CACHING_GROUPS = [158, 163, 194];
    private const LS_DEFAULT_CACHE_LOCK_AGE_SECONDS = 60;
    private const LS_DEFAULT_CACHE_LOCK_TIMEOUT_SECONDS = 5;

    /**
     * @ORM\Column(type="integer")
     *
     * @var int
     */
    private $expiry;

    /**
     * @ORM\Column(type="integer", nullable=true, name="expiry_404")
     *
     * @var int|null
     */
    private $expiry404;

    /**
     * @ORM\Column(type="boolean")
     *
     * @var bool
     */
    private $bypass = false;

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     * @var int|null
     */
    private $minUses;

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     * @var int|null
     */
    private $lockAge;

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     * @var int|null
     */
    private $lockTimeout;

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     * @var int|null
     */
    private $contentLengthLimit;

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     * @var int|null
     */
    private $missingContentLengthLimit;

    public function getExpiry(): int|null
    {
        return $this->expiry;
    }

    public function getExpiry404(): int|null
    {
        return $this->expiry404;
    }

    public function withExpiry(int|null $expiry): self
    {
        $this->expiry = $expiry ?? $this->expiry;

        return $this;
    }

    public function withExpiry404(int|null $expiry404): self
    {
        $this->expiry404 = $expiry404;

        return $this;
    }

    public function setLivestreamCacheAgeAndCacheLockout(): self
    {
        if ($this->lockAge === null && $this->lockTimeout === null) {
            $this->lockAge = self::LS_DEFAULT_CACHE_LOCK_AGE_SECONDS;
            $this->lockTimeout = self::LS_DEFAULT_CACHE_LOCK_TIMEOUT_SECONDS;
        }

        return $this;
    }

    public function setDefaultCacheAgeAndCacheLockout(): self
    {
        $this->lockAge = null;
        $this->lockTimeout = null;

        return $this;
    }

    public function isBypassed(): bool
    {
        return $this->bypass;
    }

    public function withBypass(bool $bypass): self
    {
        $new = clone $this;
        $new->bypass = $bypass;

        return $new;
    }

    public function getMinUses(): int|null
    {
        return $this->minUses;
    }

    public function withMinUses(int|null $minUses): self
    {
        $new = clone $this;
        $new->minUses = $minUses;

        return $new;
    }

    public function getLockAge(): int|null
    {
        return $this->lockAge;
    }

    public function withLockAge(int|null $lockAge): self
    {
        $new = clone $this;
        $new->lockAge = $lockAge;

        return $new;
    }

    public function getLockTimeout(): int|null
    {
        return $this->lockTimeout;
    }

    public function withLockTimeout(int|null $lockTimeout): self
    {
        $new = clone $this;
        $new->lockTimeout = $lockTimeout;

        return $new;
    }

    public function getContentLengthLimit(): int|null
    {
        return $this->contentLengthLimit;
    }

    public function withContentLengthLimit(int|null $contentLengthLimit): self
    {
        $new = clone $this;
        $new->contentLengthLimit = $contentLengthLimit;

        return $new;
    }

    public function getMissingContentLengthLimit(): int|null
    {
        return $this->missingContentLengthLimit;
    }

    public function withMissingContentLengthLimit(int $missingContentLengthLimit): self
    {
        $new = clone $this;
        $new->missingContentLengthLimit = $missingContentLengthLimit;

        return $new;
    }
}
