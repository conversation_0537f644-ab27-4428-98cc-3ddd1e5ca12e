<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Repository\Legacy\IgnoredQueryParamRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=IgnoredQueryParamRepository::class)
 * @ORM\Table(name="resource_ignored_query_param")
 */
class IgnoredQueryParam
{
    /**
     * @ORM\Column(type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\SequenceGenerator(sequenceName="resource_ignored_query_param_id_seq", allocationSize=1, initialValue=1)
     *
     * @var int
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="CdnResource", inversedBy="ignoredQueryParams")
     *
     * @var CdnResource
     */
    private $resource;

    /**
     * @ORM\Column
     *
     * @var string
     */
    private $name;

    public function __construct(CdnResource $resource, string $name)
    {
        $this->resource = $resource;
        $this->name = $name;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getResource(): CdnResource
    {
        return $this->resource;
    }

    public function setResource(CdnResource $resource): void
    {
        $this->resource = $resource;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }
}
