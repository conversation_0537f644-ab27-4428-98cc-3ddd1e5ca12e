<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Repository\Legacy\ResourceRefererProtectionAddressRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=ResourceRefererProtectionAddressRepository::class)
 * @ORM\Table(name="referer_address", schema="hotlink_protection")
 */
class ResourceRefererProtectionAddress
{
    public const FIELD_DOMAIN = 'domain';

    /**
     * @ORM\Column(type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="SEQUENCE")
     * @ORM\SequenceGenerator(
     *     sequenceName="hotlink_protection.referer_address_id_seq",
     *     allocationSize=1,
     *     initialValue=1
     * )
     *
     * @var int
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=ResourceRefererProtection::class, inversedBy="addresses")
     * @ORM\JoinColumn(nullable=false, onDelete="CASCADE")
     *
     * @var ResourceRefererProtection
     */
    private $referer;

    /**
     * @ORM\Column
     *
     * @var string
     */
    private $domain;

    public function __construct(ResourceRefererProtection $referer, string $domain)
    {
        $this->referer = $referer;
        $this->domain = $domain;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getReferer(): ResourceRefererProtection
    {
        return $this->referer;
    }

    public function setReferer(ResourceRefererProtection $referer): void
    {
        $this->referer = $referer;
    }

    public function getDomain(): string
    {
        return $this->domain;
    }

    public function setDomain(string $domain): void
    {
        $this->domain = $domain;
    }
}
