<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Resource\Domain\DTO\EditedResource;
use Cdn77\NxgApi\Resource\Domain\DTO\NewResource;
use Cdn77\NxgApi\Resource\Domain\DTO\Origin\Origin;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\JoinColumn;
use Ramsey\Uuid\Doctrine\UuidGenerator;
use Ramsey\Uuid\UuidInterface;
use Webmozart\Assert\Assert;

use function in_array;
use function Safe\preg_match;

/**
 * @ORM\Entity
 * @ORM\Table(name="resource_origin")
 */
class ResourceOrigin
{
    public const MAIN_PRIORITY = 1;

    public const FIELD_HOST = 'host';
    public const SCHEME_HTTP = 'http';
    public const SCHEME_HTTPS = 'https';
    public const SCHEMES = [self::SCHEME_HTTP, self::SCHEME_HTTPS];

    public const ALLOWED_STREAMING_ORIGINS = ['prg-test.s.cdn77.com'];
    public const PATTERN_STREAMING_HOST = '~^(\w+-)?\d+\.s\.cdn77\.(com|eu)\z~i';

    /**
     * @ORM\Id
     * @ORM\Column(type="uuid")
     * @ORM\GeneratedValue(strategy="CUSTOM")
     * @ORM\CustomIdGenerator(UuidGenerator::class)
     */
    private UuidInterface $id;

    /** @ORM\Column(name="clap_origin_id", type="uuid", nullable=true) */
    private UuidInterface|null $clapOriginId = null;

    /**
     * @ORM\ManyToOne(targetEntity="CdnResource", inversedBy="origins")
     *
     * @JoinColumn(name="resource_id", nullable=false)
     */
    private CdnResource $resource;

    /** @ORM\Column(name="priority", type="integer") */
    private int $priority;

    /** @ORM\Column(name="host", type="string") */
    private string $host;

    /** @ORM\Column(name="scheme", type="string") */
    private string $scheme = self::SCHEME_HTTP;

    /** @ORM\Column(name="port", type="integer", nullable=true) */
    private int|null $port = null;

    /** @ORM\Column(name="basedir", type="string", nullable=true) */
    private string|null $basedir = null;

    /** @ORM\Column(name="timeout", type="integer", nullable=true) */
    private int|null $timeout = null;

    /** @ORM\Embedded(class="ResourceOriginS3") */
    private ResourceOriginS3 $s3;

    /** @ORM\Column(name="forward_host_header", type="boolean") */
    private bool $forwardHostHeader = false;

    /** @ORM\Column(name="ssl_verify_disable", type="boolean") */
    private bool $sslVerifyDisable = false;

    /**
     * @ORM\Column(type="json", nullable=true, options={"jsonb"=true})
     *
     * @var array<string, string>|null
     */
    private array|null $originHeaders = null;

    /** @ORM\Column(name="follow_redirect_origin", type="boolean") */
    private bool $followRedirectOrigin = false;

    /**
     * @ORM\Column(type="json", nullable=true, options={"jsonb"=true})
     *
     * @var list<int>|null
     */
    private array|null $followRedirectCodes = null;

    /**
     * @param array<int>|null $followRedirectCodes
     * @param array<string, string>|null $originHeaders
     * @param int<0, 65535>|null $port
     * @param int<1, 100> $priority
     * @param int<0, 120>|null $timeout
     */
    public function __construct(
        UuidInterface|null $clapOriginId,
        CdnResource $resource,
        int $priority,
        string|null $host,
        string $scheme = self::SCHEME_HTTP,
        int|null $port = 0,
        string|null $basedir = '',
        int|null $timeout = 0,
        ResourceOriginS3|null $s3 = null,
        bool $forwardHostHeader = false,
        bool $sslVerifyDisable = false,
        array|null $originHeaders = null,
        bool $followRedirectOrigin = false,
        array|null $followRedirectCodes = null,
    ) {
        $this->clapOriginId = $clapOriginId;
        $this->resource = $resource;
        $this->setPriority($priority);
        $this->setHost($host);
        $this->setBasedir($basedir);
        $this->setScheme($scheme);
        $this->setPort($port);
        $this->setTimeout($timeout);
        $this->s3 = $s3 ?? new ResourceOriginS3();
        $this->forwardHostHeader = $forwardHostHeader;
        $this->sslVerifyDisable = $sslVerifyDisable;
        $this->setOriginHeaders($originHeaders);
        $this->setupFollowRedirect($followRedirectOrigin, $followRedirectCodes);
    }

    /** @param NewResource|EditedResource $dto */
    public static function fromResourceAndDto(CdnResource $resource, $dto): self
    {
        return new self(
            $dto->clapOriginId,
            $resource,
            self::MAIN_PRIORITY,
            $dto->originUrl,
            $dto->originScheme,
            $dto->originPort,
            $dto->originBaseDir,
            $dto->originTimeout,
            $dto->s3ConnectionInfo === null ? null : ResourceOriginS3::fromS3ConnectionInfo($dto->s3ConnectionInfo),
            $dto->forwardHostHeader === true,
            $dto->sslVerifyDisable === true,
            $dto->originHeaders,
            $dto->followRedirect === null ? false : $dto->followRedirect->enabled,
            $dto->followRedirect?->codes,
        );
    }

    public static function fromResourceAndSchema(CdnResource $resource, Origin $origin): self
    {
        return new self(
            $origin->clapOriginId,
            $resource,
            $origin->priority,
            $origin->url->host,
            $origin->url->scheme,
            $origin->url->port,
            $origin->url->basedir,
            $origin->timeout,
            new ResourceOriginS3(
                $origin->s3->accessKeyId,
                $origin->s3->secret,
                $origin->s3->region,
                $origin->s3->bucketName,
                $origin->s3->type,
            ),
            $origin->forwardHostHeader === true,
            $origin->sslVerifyDisable === true,
            $origin->originHeaders,
            $origin->followRedirect->enabled === true,
            $origin->followRedirect->codes,
        );
    }

    public function getId(): UuidInterface
    {
        return $this->id;
    }

    public function getClapOriginId(): UuidInterface|null
    {
        return $this->clapOriginId;
    }

    public function getResource(): CdnResource
    {
        return $this->resource;
    }

    /** @return positive-int */
    public function getPriority(): int
    {
        Assert::positiveInteger($this->priority);

        return $this->priority;
    }

    public function setPriority(int $priority): void
    {
        if ($priority < 1 || $priority > 100) {
            throw new InvalidArgument('Invalid priority, allowed range is 1-100.');
        }

        $this->priority = $priority;
    }

    public function getHost(): string
    {
        return $this->host;
    }

    public function setHost(string|null $host): void
    {
        if ($host === null || $host === '') {
            throw new InvalidArgument('Host (origin_url) cannot be empty.');
        }

        $this->host = $host;
    }

    public function getScheme(): string
    {
        return $this->scheme;
    }

    /** @return int<0, 65535>|null */
    public function getPort(): int|null
    {
        Assert::nullOrRange($this->port, 0, 65535);

        return $this->port;
    }

    public function getBasedir(): string|null
    {
        return $this->basedir;
    }

    public function setBasedir(string|null $basedir): void
    {
        $this->basedir = ValueReplacer::emptyStringToNull($basedir);
    }

    /** @return positive-int|null */
    public function getTimeout(): int|null
    {
        Assert::nullOrPositiveInteger($this->timeout);

        return $this->timeout;
    }

    public function isStreamingOrigin(): bool
    {
        return preg_match(self::PATTERN_STREAMING_HOST, $this->host) === 1;
    }

    public function setScheme(string $scheme): void
    {
        if (! in_array($scheme, self::SCHEMES, true)) {
            throw new InvalidArgument('Invalid origin scheme.');
        }

        $this->scheme = $scheme;
    }

    public function setPort(int|null $port): void
    {
        if ($port === 0 || $port === null) {
            $this->port = null;

            return;
        }

        if ($port < 1 || $port > 65535) {
            throw new InvalidArgument('Invalid port, allowed range is 1-65535. Use 0 to set null eg. default value.');
        }

        $this->port = $port;
    }

    public function setTimeout(int|null $timeout): void
    {
        if ($timeout === 0 || $timeout === null) {
            $this->timeout = null;

            return;
        }

        if ($timeout < 1 || $timeout > 120) {
            throw new InvalidArgument(
                'Invalid timeout, allowed range is 1-120. Use 0 to set null eg. default value.',
            );
        }

        $this->timeout = $timeout;
    }

    public function getS3(): ResourceOriginS3
    {
        return $this->s3;
    }

    public function setS3(ResourceOriginS3 $s3): void
    {
        $this->s3 = $s3;
    }

    public function hasForwardHostHeader(): bool
    {
        return $this->forwardHostHeader;
    }

    public function setForwardHostHeader(bool $forwardHostHeader): void
    {
        $this->forwardHostHeader = $forwardHostHeader;
    }

    public function hasSslVerifyDisable(): bool
    {
        return $this->sslVerifyDisable;
    }

    public function setSslVerifyDisable(bool $sslVerifyDisable): void
    {
        $this->sslVerifyDisable = $sslVerifyDisable;
    }

    /** @return array<string, string>|null */
    public function getOriginHeaders(): array|null
    {
        return ValueReplacer::emptyArrayToNull($this->originHeaders);
    }

    /** @param array<string, string>|null $originHeaders */
    public function setOriginHeaders(array|null $originHeaders): void
    {
        $this->originHeaders = ValueReplacer::emptyArrayToNull($originHeaders);
    }

    /** @return array<int>|null */
    public function getFollowRedirectCodes(): array|null
    {
        return ValueReplacer::emptyArrayToNull($this->followRedirectCodes);
    }

    public function hasFollowRedirectOrigin(): bool
    {
        return $this->followRedirectOrigin;
    }

    /** @param array<int, int>|null $codes */
    public function setupFollowRedirect(bool $enabled, array|null $codes): void
    {
        $this->followRedirectOrigin = $enabled;
        $this->followRedirectCodes = ValueReplacer::emptyArrayToNull($codes);
    }
}
