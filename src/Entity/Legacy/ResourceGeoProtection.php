<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Repository\Legacy\ResourceGeoProtectionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

use function in_array;

/**
 * @ORM\Entity(repositoryClass=ResourceGeoProtectionRepository::class)
 * @ORM\Table(name="geo_protection", schema="access_protection")
 */
class ResourceGeoProtection
{
    public const TYPE_WHITELIST = 'whitelist';
    public const TYPE_BLACKLIST = 'blacklist';
    public const TYPES = [
        self::TYPE_WHITELIST,
        self::TYPE_BLACKLIST,
    ];

    /**
     * @ORM\Column(type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="SEQUENCE")
     * @ORM\SequenceGenerator(
     *     sequenceName="access_protection.geo_protection_id_seq",
     *     allocationSize=1,
     *     initialValue=1
     * )
     *
     * @var int
     */
    private $id;

    /**
     * @ORM\OneToOne(targetEntity=CdnResource::class)
     *
     * @var CdnResource
     */
    private $resource;

    /**
     * @ORM\Column
     *
     * @var string
     */
    private $type;

    /**
     * @ORM\OneToMany(targetEntity=ResourceGeoProtectionLocation::class, mappedBy="geoProtection", fetch="EXTRA_LAZY")
     *
     * @var Collection<int, ResourceGeoProtectionLocation>
     */
    private $locations;

    public function __construct(CdnResource $resource, string $type)
    {
        $this->resource = $resource;
        $this->setType($type);
        $this->locations = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getResource(): CdnResource
    {
        return $this->resource;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        if (! in_array($type, self::TYPES, true)) {
            throw new InvalidArgument('Invalid type.');
        }

        $this->type = $type;
    }

    /** @return Collection<int, ResourceGeoProtectionLocation> */
    public function getLocations(): Collection
    {
        return $this->locations;
    }
}
