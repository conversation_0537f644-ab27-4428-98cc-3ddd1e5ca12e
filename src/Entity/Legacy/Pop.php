<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Repository\Legacy\PopRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=PopRepository::class)
 * @ORM\Table(name="pop")
 */
class Pop
{
    /**
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="SEQUENCE")
     * @ORM\SequenceGenerator(sequenceName="pops_id_seq", allocationSize=1, initialValue=1)
     *
     * @var int
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Location::class, inversedBy="pops")
     * @ORM\JoinColumn(name="location_id", referencedColumnName="id")
     *
     * @var Location
     */
    private $location;

    /**
     * @ORM\Column(name="use_ip", type="boolean")
     *
     * @var bool
     */
    private $useIp = false;

    /**
     * @ORM\Column(name="description", type="text", nullable=true)
     *
     * @var string|null
     */
    private $description;

    /**
     * @ORM\Column(name="backup", type="boolean")
     *
     * @var bool
     */
    private $backup = false;

    /**
     * @ORM\Column(name="tag", type="string", nullable=true)
     *
     * @var string|null
     */
    private $tag;

    /**
     * @ORM\Column(name="qat", type="boolean")
     *
     * @var bool
     */
    private $qat = false;

    /**
     * @ORM\Column(name="nodedup", type="boolean")
     *
     * @var bool
     */
    private $noDedup = false;

    /**
     * @ORM\ManyToMany(targetEntity=CustomLocation::class, mappedBy="pops", fetch="EXTRA_LAZY")
     *
     * @var Collection<int, CustomLocation>
     */
    private $customLocations;

    /**
     * @ORM\ManyToMany(targetEntity=LocationGroup::class, mappedBy="pops", fetch="EXTRA_LAZY")
     *
     * @var Collection<int, LocationGroup>
     */
    private $groups;

    /**
     * @ORM\OneToMany(targetEntity=Server::class, mappedBy="pop", fetch="EXTRA_LAZY")
     *
     * @var Collection<int, Server>
     */
    private $servers;

    public function __construct()
    {
        $this->customLocations = new ArrayCollection();
        $this->groups = new ArrayCollection();
        $this->servers = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getLocation(): Location
    {
        return $this->location;
    }

    public function setLocation(Location $location): void
    {
        $this->location = $location;
    }

    public function getUseIp(): bool
    {
        return $this->useIp;
    }

    public function setUseIp(bool $useIp): void
    {
        $this->useIp = $useIp;
    }

    public function getDescription(): string|null
    {
        return $this->description;
    }

    public function setDescription(string|null $description): void
    {
        $this->description = $description;
    }

    public function getBackup(): bool
    {
        return $this->backup;
    }

    public function setBackup(bool $backup): void
    {
        $this->backup = $backup;
    }

    public function getTag(): string|null
    {
        return $this->tag;
    }

    public function setTag(string|null $tag): void
    {
        $this->tag = $tag;
    }

    public function getQat(): bool
    {
        return $this->qat;
    }

    public function setQat(bool $qat): void
    {
        $this->qat = $qat;
    }

    public function getNoDedup(): bool
    {
        return $this->noDedup;
    }

    public function setNoDedup(bool $noDedup): void
    {
        $this->noDedup = $noDedup;
    }

    /** @return Collection<int, CustomLocation> */
    public function getCustomLocations(): Collection
    {
        return $this->customLocations;
    }

    /** @return Collection<int, LocationGroup> */
    public function getGroups(): Collection
    {
        return $this->groups;
    }

    /** @return Collection<int, Server> */
    public function getServers(): Collection
    {
        return $this->servers;
    }
}
