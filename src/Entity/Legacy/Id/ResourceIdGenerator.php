<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy\Id;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Doctrine\DBAL\Platforms\PostgreSQL100Platform;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Id\AbstractIdGenerator;
use Webmozart\Assert\Assert;

use function assert;

/**
 * Resource ID generator relying on generate_resource_id() PgSQL function.
 * This is an optimistic implementation.
 */
class ResourceIdGenerator extends AbstractIdGenerator
{
    /** @inheritDoc */
    public function generate(EntityManager $em, $entity): int
    {
        Assert::isInstanceOf($entity, CdnResource::class);

        if ($entity->getResourceId() !== null) {
            return $entity->getResourceId();
        }

        $connection = $em->getConnection();
        assert($connection->getDatabasePlatform() instanceof PostgreSQL100Platform);

        return (int) $connection->fetchOne('SELECT generate_resource_id()');
    }
}
