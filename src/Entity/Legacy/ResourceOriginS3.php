<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Resource\Domain\Value\S3ConnectionInfo;
use Doctrine\ORM\Mapping as ORM;

/** @ORM\Embeddable */
class ResourceOriginS3
{
    /** @ORM\Column(name="access_key_id", type="string", nullable=true) */
    private string|null $accessKeyId = null;

    /** @ORM\Column(name="secret", type="string", nullable=true) */
    private string|null $secret = null;

    /** @ORM\Column(name="region", type="string", nullable=true) */
    private string|null $region = null;

    /** @ORM\Column(name="bucket_name", type="string", nullable=true) */
    private string|null $bucketName = null;

    /** @ORM\Column(name="type", type="string", nullable=true) */
    private string|null $type = null;

    public function __construct(
        string|null $accessKeyId = null,
        string|null $secret = null,
        string|null $region = null,
        string|null $bucketName = null,
        string|null $type = null,
    ) {
        $this->accessKeyId = $accessKeyId;
        $this->secret = $secret;
        $this->region = $region;
        $this->bucketName = $bucketName;
        $this->type = $type;
    }

    public static function fromS3ConnectionInfo(S3ConnectionInfo $info): self
    {
        return new self(
            $info->accessKeyId(),
            $info->secret(),
            $info->region(),
            $info->bucketName(),
            $info->type(),
        );
    }

    public function getAccessKeyId(): string|null
    {
        return $this->accessKeyId;
    }

    public function setAccessKeyId(string|null $accessKeyId): void
    {
        $this->accessKeyId = $accessKeyId;
    }

    public function getSecret(): string|null
    {
        return $this->secret;
    }

    public function setSecret(string|null $secret): void
    {
        $this->secret = $secret;
    }

    public function getRegion(): string|null
    {
        return $this->region;
    }

    public function setRegion(string|null $region): void
    {
        $this->region = $region;
    }

    public function getBucketName(): string|null
    {
        return $this->bucketName;
    }

    public function setBucketName(string|null $bucketName): void
    {
        $this->bucketName = $bucketName;
    }

    public function getType(): string|null
    {
        return $this->type;
    }

    public function setType(string|null $type): void
    {
        $this->type = $type;
    }
}
