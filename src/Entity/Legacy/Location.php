<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Repository\Legacy\LocationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=LocationRepository::class)
 * @ORM\Table(name="location")
 */
class Location
{
    /**
     * @ORM\Id
     * @ORM\Column(name="id", type="string", length=100)
     *
     * @var string
     */
    private $id;

    /**
     * @ORM\Column(name="city", type="string", length=100)
     *
     * @var string
     */
    private $city;

    /**
     * @ORM\Column(name="country_code", type="string", length=2)
     *
     * @var string
     */
    private $countryCode;

    /**
     * @ORM\Column(name="region_code", type="string", length=2, nullable=true)
     *
     * @var string|null
     */
    private $regionCode;

    /**
     * @ORM\Embedded(class=LocationCoordinate::class, columnPrefix=false)
     *
     * @var LocationCoordinate
     */
    private $coordinate;

    /**
     * @ORM\Column(name="continent", type="string", length=2)
     *
     * @var string
     */
    private $continent;

    /**
     * @ORM\OneToMany(targetEntity=Pop::class, mappedBy="location", fetch="EXTRA_LAZY")
     *
     * @var Collection<int, Pop>
     */
    private $pops;

    public function __construct()
    {
        $this->coordinate = new LocationCoordinate();
        $this->pops = new ArrayCollection();
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id): void
    {
        $this->id = $id;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function setCity(string $city): void
    {
        $this->city = $city;
    }

    public function getCountryCode(): string
    {
        return $this->countryCode;
    }

    public function setCountryCode(string $countryCode): void
    {
        $this->countryCode = $countryCode;
    }

    public function getRegionCode(): string|null
    {
        return $this->regionCode;
    }

    public function setRegionCode(string|null $regionCode): void
    {
        $this->regionCode = $regionCode;
    }

    public function getCoordinate(): LocationCoordinate
    {
        return $this->coordinate;
    }

    public function setCoordinate(LocationCoordinate $coordinate): void
    {
        $this->coordinate = $coordinate;
    }

    public function getContinent(): string
    {
        return $this->continent;
    }

    public function setContinent(string $continent): void
    {
        $this->continent = $continent;
    }

    /** @return Collection<int, Pop> */
    public function getPops(): Collection
    {
        return $this->pops;
    }
}
