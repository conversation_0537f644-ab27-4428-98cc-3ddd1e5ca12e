<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Repository\Legacy\ServerHttp2Repository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=ServerHttp2Repository::class)
 * @ORM\Table(name="server_http2")
 */
class ServerHttp2
{
    /**
     * @ORM\Id
     * @ORM\OneToOne(targetEntity=Server::class)
     * @ORM\JoinColumn(name="server_id", referencedColumnName="id")
     *
     * @var Server
     */
    private $server;

    public function getServer(): Server
    {
        return $this->server;
    }

    public function setServer(Server $server): void
    {
        $this->server = $server;
    }
}
