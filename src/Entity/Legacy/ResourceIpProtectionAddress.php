<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Repository\Legacy\ResourceIpProtectionAddressRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=ResourceIpProtectionAddressRepository::class)
 * @ORM\Table(name="ip_protection_address", schema="access_protection")
 */
class ResourceIpProtectionAddress
{
    /**
     * @ORM\Column(type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="SEQUENCE")
     * @ORM\SequenceGenerator(
     *     sequenceName="access_protection.ip_protection_address_id_seq",
     *     allocationSize=1,
     *     initialValue=1
     * )
     *
     * @var int
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=ResourceIpProtection::class, inversedBy="addresses")
     *
     * @var ResourceIpProtection
     */
    private $ipProtection;

    /**
     * @ORM\Column
     *
     * @var string
     */
    private $address;

    public function __construct(ResourceIpProtection $ipProtection, string $address)
    {
        $this->ipProtection = $ipProtection;
        $this->address = $address;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getIpProtection(): ResourceIpProtection
    {
        return $this->ipProtection;
    }

    public function getAddress(): string
    {
        return $this->address;
    }
}
