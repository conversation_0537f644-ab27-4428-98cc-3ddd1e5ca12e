<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity
 * @ORM\Table(name="resource_secure_token")
 */
class SecureToken
{
    /**
     * @ORM\OneToOne(targetEntity=CdnResource::class, inversedBy="resourceSecureToken")
     * @ORM\JoinColumn(name="resource_id", referencedColumnName="id", nullable=false)
     * @ORM\Id
     */
    private CdnResource $resource;

    /** @ORM\Column(name="secure_token_type", type="string", nullable=false) */
    private string $type;

    /** @ORM\Column(name="secure_token_value", type="string", nullable=false) */
    private string $value;

    /** @ORM\Column(name="secure_link_expiry_param", type="string", nullable=true) */
    private string|null $secureLinkExpiryParam;

    /** @ORM\Column(name="secure_link_token_param", type="string", nullable=true) */
    private string|null $secureLinkTokenParam;

    /** @ORM\Column(name="secure_link_pathlen_param", type="string", nullable=true) */
    private string|null $secureLinkPathlenParam;

    /** @ORM\Column(name="secure_link_secret_param", type="string", nullable=true) */
    private string|null $secureLinkSecretParam;

    /** @ORM\Column(name="secure_link_rewrite_playlist", type="boolean", nullable=false) */
    private bool $secureLinkRewritePlaylist;

    public function __construct(
        CdnResource $resource,
        string $type,
        string $value,
        string|null $secureLinkExpiryParam,
        string|null $secureLinkTokenParam,
        string|null $secureLinkPathlenParam,
        string|null $secureLinkSecretParam,
        bool $secureLinkRewritePlaylist,
    ) {
        $this->resource = $resource;
        $this->type = $type;
        $this->value = $value;
        $this->secureLinkExpiryParam = $secureLinkExpiryParam;
        $this->secureLinkTokenParam = $secureLinkTokenParam;
        $this->secureLinkPathlenParam = $secureLinkPathlenParam;
        $this->secureLinkSecretParam = $secureLinkSecretParam;
        $this->secureLinkRewritePlaylist = $secureLinkRewritePlaylist;
    }

    public function getResource(): CdnResource
    {
        return $this->resource;
    }

    public function setResource(CdnResource $resource): void
    {
        $this->resource = $resource;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function getSecureLinkExpiryParam(): string|null
    {
        return $this->secureLinkExpiryParam;
    }

    public function getSecureLinkTokenParam(): string|null
    {
        return $this->secureLinkTokenParam;
    }

    public function getSecureLinkPathlenParam(): string|null
    {
        return $this->secureLinkPathlenParam;
    }

    public function getSecureLinkSecretParam(): string|null
    {
        return $this->secureLinkSecretParam;
    }

    public function hasSecureLinkRewritePlaylist(): bool
    {
        return $this->secureLinkRewritePlaylist;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function setValue(string $value): void
    {
        $this->value = $value;
    }

    public function setSecureLinkExpiryParam(string|null $secureLinkExpiryParam): void
    {
        $this->secureLinkExpiryParam = $secureLinkExpiryParam;
    }

    public function setSecureLinkTokenParam(string|null $secureLinkTokenParam): void
    {
        $this->secureLinkTokenParam = $secureLinkTokenParam;
    }

    public function setSecureLinkPathlenParam(string|null $secureLinkPathlenParam): void
    {
        $this->secureLinkPathlenParam = $secureLinkPathlenParam;
    }

    public function setSecureLinkSecretParam(string|null $secureLinkSecretParam): void
    {
        $this->secureLinkSecretParam = $secureLinkSecretParam;
    }

    public function setSecureLinkRewritePlaylist(bool $secureLinkRewritePlaylist): void
    {
        $this->secureLinkRewritePlaylist = $secureLinkRewritePlaylist;
    }
}
