<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Repository\Legacy\LocationGroupRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=LocationGroupRepository::class)
 * @ORM\Table(name="location_group")
 */
class LocationGroup
{
    public const DEFAULT_ID = 4;

    /**
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="SEQUENCE")
     * @ORM\SequenceGenerator(sequenceName="location_group_id_seq", allocationSize=1, initialValue=1)
     *
     * @var int
     */
    private $id;

    /**
     * @ORM\Column(name="name", type="string", length=100, nullable=true)
     *
     * @var string|null
     */
    private $name;

    /**
     * @ORM\Column(name="`primary`", type="boolean")
     *
     * @var bool
     */
    private $primary = false;

    /**
     * @ORM\Column(name="allow_backup_pops", type="boolean")
     *
     * @var bool
     */
    private $allowBackupPops = true;

    /**
     * @ORM\ManyToMany(targetEntity=Pop::class, inversedBy="groups", fetch="EXTRA_LAZY")
     * @ORM\JoinTable(name="group_pop_relation",
     *   joinColumns={
     *     @ORM\JoinColumn(name="group_id", referencedColumnName="id")
     *   },
     *   inverseJoinColumns={
     *     @ORM\JoinColumn(name="pop_id", referencedColumnName="id")
     *   }
     * )
     *
     * @var Collection<int, Pop>
     */
    private $pops;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->pops = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getName(): string|null
    {
        return $this->name;
    }

    public function setName(string|null $name): void
    {
        $this->name = $name;
    }

    public function isPrimary(): bool
    {
        return $this->primary;
    }

    public function setPrimary(bool $primary): void
    {
        $this->primary = $primary;
    }

    public function isAllowBackupPops(): bool
    {
        return $this->allowBackupPops;
    }

    public function setAllowBackupPops(bool $allowBackupPops): void
    {
        $this->allowBackupPops = $allowBackupPops;
    }

    /** @return Collection<int, Pop> */
    public function getPops(): Collection
    {
        return $this->pops;
    }
}
