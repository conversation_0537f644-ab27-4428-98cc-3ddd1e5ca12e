<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Exception\InvalidArgument;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

use function in_array;

/**
 * @ORM\Entity
 * @ORM\Table(
 *     name="ssl_file",
 *     uniqueConstraints={
 *         @ORM\UniqueConstraint(name="ssl_file_unique_key", columns={"resource_id", "index"})
 *     }
 * )
 */
class SslFile
{
    public const TYPE_CUSTOM = 'custom';
    public const TYPE_LETSENCRYPT = 'letsencrypt';

    public const TYPES = [self::TYPE_CUSTOM, self::TYPE_LETSENCRYPT];

    /**
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="SEQUENCE")
     * @ORM\SequenceGenerator(sequenceName="ssl_file_id_seq", allocationSize=1, initialValue=1)
     *
     * @var int
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Ssl::class, inversedBy="files", cascade={"persist"})
     * @ORM\JoinColumn(name="resource_id", referencedColumnName="resource_id", nullable=false)
     *
     * @var Ssl
     */
    private $ssl;

    /**
     * @ORM\Column(name="index", type="integer")
     *
     * @var int
     */
    private $index;

    /**
     * @ORM\Column(name="created_at", type="datetimetz_immutable")
     *
     * @var DateTimeImmutable
     */
    private $createdAt;

    /**
     * @ORM\Column(name="type", type="string")
     *
     * @var string
     */
    private $type = self::TYPE_CUSTOM;

    /**
     * @ORM\Column(name="expires_at", type="datetimetz_immutable")
     *
     * @var DateTimeImmutable
     */
    private $expiresAt;

    /**
     * @ORM\Column(type="json")
     *
     * @var string[]
     */
    private $domains = [];

    /** @param list<string> $domains */
    public function __construct(
        DateTimeImmutable $createdAt,
        array $domains,
        DateTimeImmutable $expiresAt,
        int $index,
        Ssl $ssl,
        string $type,
    ) {
        $this->createdAt = $createdAt;
        $this->domains = $domains;
        $this->expiresAt = $expiresAt;
        $this->index = $index;
        $this->ssl = $ssl;
        $this->type = $type;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getSsl(): Ssl
    {
        return $this->ssl;
    }

    public function setSsl(Ssl $ssl): void
    {
        $this->ssl = $ssl;
    }

    public function getIndex(): int
    {
        return $this->index;
    }

    public function setIndex(int $index): void
    {
        $this->index = $index;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        if (! in_array($type, [self::TYPE_CUSTOM, self::TYPE_LETSENCRYPT], true)) {
            throw new InvalidArgument('Invalid type.');
        }

        $this->type = $type;
    }

    public function getExpiresAt(): DateTimeImmutable
    {
        return $this->expiresAt;
    }

    public function setExpiresAt(DateTimeImmutable $expiresAt): void
    {
        $this->expiresAt = $expiresAt;
    }

    /** @return string[] */
    public function getDomains(): array
    {
        return $this->domains;
    }

    /** @param string[] $domains */
    public function setDomains(array $domains): void
    {
        $this->domains = $domains;
    }
}
