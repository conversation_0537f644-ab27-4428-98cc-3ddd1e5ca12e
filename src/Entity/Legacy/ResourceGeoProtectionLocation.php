<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Repository\Legacy\ResourceGeoProtectionLocationRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=ResourceGeoProtectionLocationRepository::class)
 * @ORM\Table(name="geo_protection_location", schema="access_protection")
 */
class ResourceGeoProtectionLocation
{
    /**
     * @ORM\Column(type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="SEQUENCE")
     * @ORM\SequenceGenerator(
     *     sequenceName="access_protection.geo_protection_location_id_seq",
     *     allocationSize=1,
     *     initialValue=1
     * )
     *
     * @var int
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=ResourceGeoProtection::class, inversedBy="locations")
     *
     * @var ResourceGeoProtection
     */
    private $geoProtection;

    /**
     * @ORM\Column
     *
     * @var string
     */
    private $country;

    public function __construct(ResourceGeoProtection $geoProtection, string $country)
    {
        $this->geoProtection = $geoProtection;
        $this->country = $country;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getGeoProtection(): ResourceGeoProtection
    {
        return $this->geoProtection;
    }

    public function getCountry(): string
    {
        return $this->country;
    }
}
