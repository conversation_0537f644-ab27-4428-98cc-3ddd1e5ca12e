<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Repository\Legacy\AccountRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=AccountRepository::class)
 * @ORM\Table(name="account")
 */
class Account
{
    /**
     * @ORM\Column(type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="NONE")
     */
    private int $id;

    /**
     * @ORM\OneToMany(targetEntity=CdnResource::class, mappedBy="account", fetch="EXTRA_LAZY")
     *
     * @var Collection<int, CdnResource>
     */
    private $resources;

    /** @param CdnResource[] $resources */
    public function __construct(int $id, array $resources = [])
    {
        $this->id = $id;
        $this->resources = new ArrayCollection($resources);
    }

    public function getId(): int
    {
        return $this->id;
    }

    /** @return Collection<int, CdnResource> */
    public function getResources(): Collection
    {
        return $this->resources;
    }
}
