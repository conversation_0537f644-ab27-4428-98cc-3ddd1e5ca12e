<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Exception\InvalidState;
use Cdn77\NxgApi\Repository\Legacy\ServerRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;

use function assert;

/**
 * @ORM\Entity(repositoryClass=ServerRepository::class)
 * @ORM\Table(name="server", indexes={@ORM\Index(name="server_idx", columns={"pop_id"})})
 */
class Server
{
    /**
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="SEQUENCE")
     * @ORM\SequenceGenerator(sequenceName="server_id_seq", allocationSize=1, initialValue=1)
     */
    private int $id;

    /**
     * @ORM\ManyToOne(targetEntity=Pop::class, inversedBy="servers", fetch="EXTRA_LAZY")
     * @ORM\JoinColumn(name="pop_id", referencedColumnName="id")
     */
    private Pop $pop;

    /** @ORM\Column(name="max_bandwidth", type="integer", nullable=true) */
    private int|null $maxBandwidth = null;

    /** @ORM\Column(name="paused", type="boolean") */
    private bool $paused = false;

    /** @ORM\Column(name="max_cache_size", type="integer", nullable=true) */
    private int|null $maxCacheSize = null;

    /** @ORM\Column(name="forced_state", type="boolean", nullable=true) */
    private bool|null $forcedState = null;

    /** @ORM\Column(name="keys_size", type="integer") */
    private int $keysSize;

    /** @ORM\Column(name="uid", type="integer") */
    private int $uid;

    /** @ORM\Column(name="worker_count", type="integer") */
    private int $workerCount;

    /** @ORM\Column(name="drive_count", type="integer") */
    private int $driveCount;

    /**
     * @ORM\OneToMany(targetEntity="Cdn77\NxgApi\Entity\Legacy\Ip", mappedBy="server", fetch="EXTRA_LAZY")
     *
     * @var Collection<int, Ip>
     */
    private Collection $ips;

    /**
     * @ORM\OneToMany(targetEntity="Cdn77\NxgApi\Entity\Legacy\Ipv6", mappedBy="server", fetch="EXTRA_LAZY")
     *
     * @var Collection<int, Ipv6>
     */
    private Collection $ipv6Addresses;

    public function __construct()
    {
        $this->ips = new ArrayCollection();
        $this->ipv6Addresses = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getPop(): Pop
    {
        return $this->pop;
    }

    public function setPop(Pop $pop): void
    {
        $this->pop = $pop;
    }

    public function getMaxBandwidth(): int|null
    {
        return $this->maxBandwidth;
    }

    public function setMaxBandwidth(int $maxBandwidth): void
    {
        $this->maxBandwidth = $maxBandwidth;
    }

    public function isPaused(): bool
    {
        return $this->paused;
    }

    public function setPaused(bool $paused): void
    {
        $this->paused = $paused;
    }

    public function pause(): void
    {
        assert($this->paused === false);
        $this->paused = true;
    }

    public function unpause(): void
    {
        assert($this->paused === true);
        $this->paused = false;
    }

    public function getMaxCacheSize(): int|null
    {
        return $this->maxCacheSize;
    }

    public function setMaxCacheSize(int|null $maxCacheSize): void
    {
        assert($maxCacheSize === null || $maxCacheSize >= 0);
        $this->maxCacheSize = $maxCacheSize;
    }

    public function getForcedState(): bool|null
    {
        return $this->forcedState;
    }

    public function setForcedState(bool|null $forcedState): void
    {
        $this->forcedState = $forcedState;
    }

    public function getKeysSize(): int
    {
        return $this->keysSize;
    }

    public function setKeysSize(int $keysSize): void
    {
        $this->keysSize = $keysSize;
    }

    public function getUid(): int
    {
        return $this->uid;
    }

    public function setUid(int $uid): void
    {
        $this->uid = $uid;
    }

    public function getWorkerCount(): int
    {
        return $this->workerCount;
    }

    public function setWorkerCount(int $workerCount): void
    {
        $this->workerCount = $workerCount;
    }

    public function getDriveCount(): int
    {
        return $this->driveCount;
    }

    public function setDriveCount(int $driveCount): void
    {
        $this->driveCount = $driveCount;
    }

    /** @return Collection<int, Ip> */
    public function getIps(): Collection
    {
        return $this->ips;
    }

    /** @return Collection<int, Ipv6> */
    public function getIpv6Addresses(): Collection
    {
        return $this->ipv6Addresses;
    }

    public function getPrimaryIp(): Ip|null
    {
        $matches = $this->ips->matching(
            Criteria::create()
                ->where(Criteria::expr()->eq('primary', true)),
        );

        if ($matches->count() > 1) {
            throw new InvalidState('The server shall not have more than one primary IPv4 address.');
        }

        $first = $matches->first();

        return $first === false ? null : $first;
    }

    /** @return array<Ip> */
    public function getSecondaryIps(): array
    {
        $matches = $this->ips->matching(
            Criteria::create()
                ->where(Criteria::expr()->eq('primary', false)),
        );

        return $matches->toArray();
    }

    public function getPrimaryIpv6(): Ipv6|null
    {
        $matches = $this->ipv6Addresses->matching(
            Criteria::create()
                ->where(Criteria::expr()->eq('primary', true)),
        );

        if ($matches->count() > 1) {
            throw new InvalidState('The server shall not have more than one primary IPv6 address.');
        }

        $first = $matches->first();

        return $first === false ? null : $first;
    }

    /** @return array<Ipv6> */
    public function getSecondaryIpv6Addresses(): array
    {
        $matches = $this->ipv6Addresses->matching(
            Criteria::create()
                ->where(Criteria::expr()->eq('primary', false)),
        );

        return $matches->toArray();
    }
}
