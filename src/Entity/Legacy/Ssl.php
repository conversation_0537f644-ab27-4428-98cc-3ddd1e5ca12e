<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Repository\Legacy\SslRepository;
use Cdn77\NxgApi\Resource\Domain\Exception\SslFileNotFound;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Webmozart\Assert\Assert;

/**
 * @ORM\Entity(repositoryClass=SslRepository::class)
 * @ORM\Table(name="ssl")
 */
class Ssl
{
    /**
     * @ORM\OneToOne(targetEntity=CdnResource::class, cascade={"persist"})
     * @ORM\JoinColumn(name="resource_id", referencedColumnName="id", nullable=false)
     * @ORM\Id
     *
     * @var CdnResource
     */
    private $resource;

    /** @ORM\Column(name="assigned_index", type="integer", nullable=false) */
    private int $assignedIndex;

    /** @ORM\Column(name="assigned_at", type="datetimetz_immutable", nullable=false) */
    private DateTimeImmutable $assignedAt;

    /**
     * @ORM\OneToMany(
     *     targetEntity=SslFile::class,
     *     mappedBy="ssl",
     *     cascade={"persist", "refresh", "merge", "detach"},
     *     fetch="EXTRA_LAZY",
     *     indexBy="index"
     * )
     *
     * @var Collection<int, SslFile>
     */
    private $files;

    public function __construct()
    {
        $this->files = new ArrayCollection();
    }

    public function getResource(): CdnResource
    {
        return $this->resource;
    }

    public function setResource(CdnResource $resource): void
    {
        $this->resource = $resource;
    }

    public function getAssignedIndex(): int
    {
        return $this->assignedIndex;
    }

    public function setAssignedIndex(int $assignedIndex): void
    {
        $this->assignedIndex = $assignedIndex;
    }

    public function getAssignedAt(): DateTimeImmutable
    {
        return $this->assignedAt;
    }

    public function setAssignedAt(DateTimeImmutable $assignedAt): void
    {
        $this->assignedAt = $assignedAt;
    }

    /** @return Collection<int, SslFile> */
    public function getFiles(): Collection
    {
        return $this->files;
    }

    public function getAssignedSslFile(): SslFile
    {
        $files = $this->getFiles();

        if (! $files->containsKey($this->getAssignedIndex())) {
            throw SslFileNotFound::forAssignedIndex();
        }

        $sslFile = $files->get($this->getAssignedIndex());

        Assert::isInstanceOf($sslFile, SslFile::class);

        return $sslFile;
    }
}
