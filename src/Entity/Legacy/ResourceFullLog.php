<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Repository\Legacy\ResourceFullLogRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=ResourceFullLogRepository::class)
 * @ORM\Table(name="resource_full_log")
 */
class ResourceFullLog
{
    /**
     * @ORM\OneToOne(targetEntity=CdnResource::class)
     * @ORM\Id
     *
     * @var CdnResource
     */
    private $resource;

    public function __construct(CdnResource $resource)
    {
        $this->resource = $resource;
    }

    public function getResource(): CdnResource
    {
        return $this->resource;
    }
}
