<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\HotlinkProtection\Domain\Value\HotlinkProtectionType;
use Cdn77\NxgApi\Repository\Legacy\ResourceRefererProtectionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

use function in_array;

/**
 * @ORM\Entity(repositoryClass=ResourceRefererProtectionRepository::class)
 * @ORM\Table(name="referer", schema="hotlink_protection")
 */
class ResourceRefererProtection
{
    public const FIELD_ADDRESSES = 'addresses';
    public const FIELD_DENY_EMPTY = 'denyEmpty';
    public const FIELD_TYPE = 'type';

    public const TYPE_BLACKLIST = HotlinkProtectionType::TYPE_BLACKLIST;
    public const TYPE_DISABLED = HotlinkProtectionType::TYPE_DISABLED;
    public const TYPE_WHITELIST = HotlinkProtectionType::TYPE_WHITELIST;

    public const TYPES = [
        self::TYPE_BLACKLIST,
        self::TYPE_DISABLED,
        self::TYPE_WHITELIST,
    ];

    /**
     * @ORM\Column(type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="SEQUENCE")
     * @ORM\SequenceGenerator(sequenceName="hotlink_protection.referer_id_seq", allocationSize=1, initialValue=1)
     *
     * @var int
     */
    private $id;

    /**
     * @ORM\OneToOne(targetEntity=CdnResource::class)
     * @ORM\JoinColumn(name="resource_id", referencedColumnName="id", nullable=false, onDelete="CASCADE")
     *
     * @var CdnResource
     */
    private $resource;

    /**
     * @ORM\Column
     *
     * @var string
     */
    private $type;

    /**
     * @ORM\Column(type="boolean")
     *
     * @var bool
     */
    private $denyEmpty;

    /**
     * @ORM\OneToMany(
     *     targetEntity=ResourceRefererProtectionAddress::class,
     *     mappedBy="referer",
     *     cascade={"all"},
     *     fetch="EXTRA_LAZY"
     * )
     *
     * @var Collection<int, ResourceRefererProtectionAddress>
     */
    private $addresses;

    public function __construct(CdnResource $resource, string $type, bool $denyEmpty)
    {
        $this->resource = $resource;
        $this->denyEmpty = $denyEmpty;
        $this->addresses = new ArrayCollection();

        $this->setType($type);
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getResource(): CdnResource
    {
        return $this->resource;
    }

    public function setResource(CdnResource $resource): void
    {
        $this->resource = $resource;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        if (! in_array($type, self::TYPES, true)) {
            throw new InvalidArgument('Invalid type.');
        }

        $this->type = $type;
    }

    public function isEmptyDenied(): bool
    {
        return $this->denyEmpty;
    }

    public function setDenyEmpty(bool $denyEmpty): void
    {
        $this->denyEmpty = $denyEmpty;
    }

    /** @return Collection<int, ResourceRefererProtectionAddress> */
    public function getAddresses(): Collection
    {
        return $this->addresses;
    }

    public function addAddress(ResourceRefererProtectionAddress $address): void
    {
        $this->addresses->add($address);
    }

    public function removeAddress(ResourceRefererProtectionAddress $address): void
    {
        $this->addresses->removeElement($address);
    }
}
