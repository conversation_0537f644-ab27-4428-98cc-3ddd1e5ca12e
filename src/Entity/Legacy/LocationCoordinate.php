<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Doctrine\ORM\Mapping as ORM;

/** @ORM\Embeddable */
class LocationCoordinate
{
    /**
     * @ORM\Column(name="longitude", type="float", precision=10, scale=0)
     *
     * @var float
     */
    private $longitude;

    /**
     * @ORM\Column(name="latitude", type="float", precision=10, scale=0)
     *
     * @var float
     */
    private $latitude;

    public function getLatitude(): float
    {
        return $this->latitude;
    }

    public function setLatitude(float $latitude): void
    {
        $this->latitude = $latitude;
    }

    public function getLongitude(): float
    {
        return $this->longitude;
    }

    public function setLongitude(float $longitude): void
    {
        $this->longitude = $longitude;
    }
}
