<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\PopRedirection;

use Cdn77\NxgApi\Entity\Legacy\Pop;
use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\Repository\PopRedirection\RedirectionRepository;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Mapping as ORM;

use function assert;
use function count;
use function in_array;

/**
 * @ORM\Entity(repositoryClass=RedirectionRepository::class)
 * @ORM\Table(name="redirection", schema="pop_redirection")
 */
class Redirection
{
    /**
     * @ORM\Column(type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue("SEQUENCE")
     * @ORM\SequenceGenerator("pop_redirection.redirection_id_seq")
     *
     * @var int
     */
    private $id;

    /**
     * @ORM\OneToOne(targetEntity=Pop::class)
     *
     * @var Pop
     */
    private $pop;

    /**
     * @ORM\Column(type="float")
     *
     * @var float
     */
    private $probability;

    /**
     * @ORM\OneToMany(targetEntity=Target::class, mappedBy="redirection", cascade={"ALL"})
     * TODO: OrderBy is incompatible with fetch=EAGER
     * @ORM\OrderBy({"priority" = "ASC"})
     *
     * @var Collection<int, Target>
     */
    private $targets;

    /**
     * @ORM\Column(type="boolean")
     *
     * @var bool
     */
    private $active = false;

    /**
     * @ORM\Column(type="boolean")
     *
     * @var bool
     */
    private $useNextBestPop = false;

    /**
     * @ORM\Column(type="datetimetz_immutable")
     *
     * @var DateTimeImmutable
     */
    private $createdAt;

    /**
     * @ORM\Column(type="datetimetz_immutable")
     *
     * @var DateTimeImmutable
     */
    private $updatedAt;

    public function __construct(Pop $pop, float $probability, bool $useNextBestPop, DateTimeImmutable $createdAt)
    {
        $this->pop = $pop;
        $this->setProbability($probability);
        $this->useNextBestPop = $useNextBestPop;
        $this->createdAt = $createdAt;
        $this->updatedAt = $createdAt;
        $this->targets = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getPop(): Pop
    {
        return $this->pop;
    }

    public function setPop(Pop $pop): void
    {
        assert(in_array($pop, $this->getTargetPops(), true) === false);

        $this->pop = $pop;
    }

    public function getProbability(): float
    {
        return $this->probability;
    }

    public function setProbability(float $probability): void
    {
        assert($probability > 0 && $probability <= 100);

        $this->probability = $probability;
    }

    public function addTarget(Pop $pop, DateTimeImmutable $date): Target
    {
        assert($this->pop !== $pop);
        assert($this->targets->count() < 8);
        assert(in_array($pop, $this->getTargetPops(), true) === false);

        $target = new Target($this, $pop, $this->targets->count() + 1, $date);

        $this->targets->add($target);

        return $target;
    }

    public function deleteTarget(Pop $pop, EntityManagerInterface $entityManager): void
    {
        foreach ($this->targets as $target) {
            if ($target->getPop() !== $pop) {
                continue;
            }

            $this->targets->removeElement($target);
            $entityManager->remove($target);

            break;
        }
    }

    /** @return Target[] */
    public function getTargets(): array
    {
        return $this->targets->getValues();
    }

    /** @return Pop[] */
    public function getTargetPops(): array
    {
        return $this->targets->map(
            static fn (Target $target): Pop => $target->getPop(),
        )->getValues();
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): void
    {
        assert($active === false || $this->targets->count() > 0 || $this->isUseNextBestPop());

        $this->active = $active;
    }

    public function isUseNextBestPop(): bool
    {
        return $this->useNextBestPop;
    }

    public function setUseNextBestPop(bool $useNextBestPop): void
    {
        $this->useNextBestPop = $useNextBestPop;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(DateTimeImmutable $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function reorder(Pop ...$pops): void
    {
        if (count($pops) !== $this->targets->count()) {
            throw new InvalidArgument('The given pops do not match the number of this redirection.');
        }

        foreach ($pops as $pop) {
            foreach ($this->targets as $target) {
                if ($target->getPop() === $pop) {
                    continue 2;
                }
            }

            throw new InvalidArgument('This pop is not a target of this redirection.');
        }

        $position = 0;
        foreach ($pops as $pop) {
            foreach ($this->targets as $unorderedTarget) {
                if ($pop !== $unorderedTarget->getPop()) {
                    continue;
                }

                $unorderedTarget->setPriority(++$position);

                continue 2;
            }
        }
    }
}
