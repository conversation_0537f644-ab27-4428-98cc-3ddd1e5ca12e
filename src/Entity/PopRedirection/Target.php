<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\PopRedirection;

use Cdn77\NxgApi\Entity\Legacy\Pop;
use Cdn77\NxgApi\Repository\PopRedirection\TargetRepository;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

use function assert;

/**
 * @ORM\Entity(repositoryClass=TargetRepository::class)
 * @ORM\Table(name="target", schema="pop_redirection")
 */
class Target
{
    /**
     * @ORM\Column(type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue("SEQUENCE")
     * @ORM\SequenceGenerator("pop_redirection.target_id_seq")
     *
     * @var int
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Redirection::class, inversedBy="targets")
     *
     * @var Redirection
     */
    private $redirection;

    /**
     * @ORM\ManyToOne(targetEntity=Pop::class)
     *
     * @var Pop
     */
    private $pop;

    /**
     * @ORM\Column(type="integer")
     *
     * @var int
     */
    private $priority;

    /**
     * @ORM\Column(type="datetimetz_immutable")
     *
     * @var DateTimeImmutable
     */
    private $createdAt;

    public function __construct(Redirection $redirection, Pop $pop, int $priority, DateTimeImmutable $createdAt)
    {
        $this->redirection = $redirection;
        $this->pop = $pop;
        $this->priority = $priority;
        $this->createdAt = $createdAt;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getRedirection(): Redirection
    {
        return $this->redirection;
    }

    public function getPop(): Pop
    {
        return $this->pop;
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function setPriority(int $priority): void
    {
        assert($priority > 0);

        $this->priority = $priority;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }
}
