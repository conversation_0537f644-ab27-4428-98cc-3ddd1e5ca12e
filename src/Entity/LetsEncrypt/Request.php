<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\LetsEncrypt;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Exception\InvalidArgument;
use Cdn77\NxgApi\LetsEncrypt\Domain\Value\RequestStateReason;
use Cdn77\NxgApi\Repository\LetsEncrypt\RequestRepository;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

use function count;

/**
 * @ORM\Entity(repositoryClass=RequestRepository::class)
 * @ORM\Table(name="request", schema="letsencrypt")
 */
class Request
{
    use UuidIdentified;

    /**
     * @ORM\ManyToOne(targetEntity=CdnResource::class)
     * @ORM\JoinColumn(name="resource_id", nullable=false)
     *
     * @var CdnResource
     */
    private $resource;

    /**
     * @ORM\Column(type="json")
     *
     * @var string[]
     */
    private $domains;

    /**
     * @ORM\Column(type="datetimetz_immutable", name="created_at")
     *
     * @var DateTimeImmutable
     */
    private $createdAt;

    /**
     * @ORM\Column(type="datetimetz_immutable", name="updated_at")
     *
     * @var DateTimeImmutable
     */
    private $updatedAt;

    /** @ORM\Column(type="string") */
    private string $state;

    /** @ORM\Column(type="string", enumType=RequestStateReason::class, nullable=true) */
    private RequestStateReason|null $stateReason = null;

    /** @param string[] $domains */
    public function __construct(
        CdnResource $resource,
        array $domains,
        DateTimeImmutable $createdAt,
        RequestState $state,
        RequestStateReason|null $stateReason = null,
    ) {
        $this->resource = $resource;
        $this->createdAt = $createdAt;
        $this->updatedAt = $createdAt;

        if (count($domains) === 0) {
            throw new InvalidArgument('Request must have at least one domain');
        }

        $this->domains = $domains;
        $this->state = $state->getValue();
        $this->stateReason = $stateReason;
    }

    public function complete(DateTimeImmutable $now): void
    {
        $this->state = RequestState::getCompleted()->getValue();
        $this->markUpdated($now);
    }

    public function cancel(DateTimeImmutable $now, RequestStateReason $reason): void
    {
        $this->state = RequestState::getCanceled()->getValue();
        $this->stateReason = $reason;
        $this->markUpdated($now);
    }

    public function markUpdated(DateTimeImmutable $when): void
    {
        $this->updatedAt = $when;
    }

    public function getResource(): CdnResource
    {
        return $this->resource;
    }

    /** @return string[] */
    public function getDomains(): array
    {
        return $this->domains;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function getState(): RequestState
    {
        return RequestState::from($this->state);
    }

    public function getStateReason(): RequestStateReason|null
    {
        return $this->stateReason;
    }

    public function age(): int
    {
        $actualTime = new DateTimeImmutable();

        return $actualTime->getTimestamp() - $this->getCreatedAt()->getTimestamp();
    }
}
