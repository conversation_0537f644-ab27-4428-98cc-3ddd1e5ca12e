<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\LetsEncrypt;

use Doctrine\ORM\Mapping as ORM;
use <PERSON>\Uuid\Doctrine\UuidGenerator;
use <PERSON>\Uuid\UuidInterface;

trait UuidIdentified
{
    /**
     * @ORM\Id
     * @ORM\Column(type="uuid")
     * @ORM\GeneratedValue(strategy="CUSTOM")
     * @ORM\CustomIdGenerator(UuidGenerator::class)
     *
     * @var UuidInterface
     */
    private $id;

    public function getId(): UuidInterface
    {
        return $this->id;
    }
}
