<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\LetsEncrypt;

use MyCLabs\Enum\Enum;

/** @extends Enum<string> */
final class ResultStatus extends Enum
{
    public const SUCCESS = 'success';
    public const VALIDATION_ERROR = 'validationError';
    public const ERROR = 'error';

    public static function getSuccess(): self
    {
        return self::from(self::SUCCESS);
    }

    public static function getValidationError(): self
    {
        return self::from(self::VALIDATION_ERROR);
    }

    public static function getError(): self
    {
        return self::from(self::ERROR);
    }

    public function isSuccess(): bool
    {
        return $this->getValue() === self::SUCCESS;
    }

    public function isValidationError(): bool
    {
        return $this->getValue() === self::VALIDATION_ERROR;
    }

    public function isError(): bool
    {
        return $this->getValue() === self::ERROR;
    }
}
