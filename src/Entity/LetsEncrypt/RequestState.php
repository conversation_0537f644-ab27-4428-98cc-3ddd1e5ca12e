<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\LetsEncrypt;

use MyCLabs\Enum\Enum;

/** @extends Enum<string> */
final class RequestState extends Enum
{
    public const PENDING = 'pending';
    public const COMPLETED = 'completed';
    public const CANCELLED = 'cancelled';

    public static function getPending(): self
    {
        return self::from(self::PENDING);
    }

    public static function getCompleted(): self
    {
        return self::from(self::COMPLETED);
    }

    public static function getCanceled(): self
    {
        return self::from(self::CANCELLED);
    }

    public function isPending(): bool
    {
        return $this->getValue() === self::PENDING;
    }

    public function isCompleted(): bool
    {
        return $this->getValue() === self::COMPLETED;
    }

    public function isCancelled(): bool
    {
        return $this->getValue() === self::CANCELLED;
    }
}
