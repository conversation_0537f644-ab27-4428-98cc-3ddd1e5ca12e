<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\LetsEncrypt;

use Cdn77\NxgApi\Repository\LetsEncrypt\ResultRepository;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=ResultRepository::class)
 * @ORM\Table(name="result", schema="letsencrypt")
 */
class Result
{
    use UuidIdentified;

    /**
     * @ORM\ManyToOne(targetEntity=Request::class)
     * @ORM\JoinColumn(nullable=false)
     *
     * @var Request
     */
    private $request;

    /**
     * @ORM\Column(type="datetimetz_immutable")
     *
     * @var DateTimeImmutable
     */
    private $requestedAt;

    /**
     * @ORM\Column(type="datetimetz_immutable")
     *
     * @var DateTimeImmutable
     */
    private $runAt;

    /**
     * @ORM\Column(type="datetimetz_immutable")
     *
     * @var DateTimeImmutable
     */
    private $completedAt;

    /** @ORM\Column(type="string") */
    private string $status;

    /**
     * @ORM\Column(nullable=true)
     *
     * @var string|null
     */
    private $description;

    public function __construct(
        Request $request,
        DateTimeImmutable $requestedAt,
        DateTimeImmutable $runAt,
        DateTimeImmutable $completedAt,
        ResultStatus $status,
        string|null $description,
    ) {
        $this->request = $request;
        $this->requestedAt = $requestedAt;
        $this->runAt = $runAt;
        $this->completedAt = $completedAt;
        $this->status = $status->getValue();
        $this->description = $description;
    }

    public function getRequest(): Request
    {
        return $this->request;
    }

    public function getRequestedAt(): DateTimeImmutable
    {
        return $this->requestedAt;
    }

    public function getRunAt(): DateTimeImmutable
    {
        return $this->runAt;
    }

    public function getCompletedAt(): DateTimeImmutable
    {
        return $this->completedAt;
    }

    public function getStatus(): ResultStatus
    {
        return ResultStatus::from($this->status);
    }

    public function getDescription(): string|null
    {
        return $this->description;
    }
}
