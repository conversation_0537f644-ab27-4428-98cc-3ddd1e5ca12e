<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\LetsEncrypt;

use Cdn77\NxgApi\Resource\Application\Payload\ResourceSslSchema;
use Cdn77\NxgApi\Resource\Domain\Value\CustomCertificate;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Uid\UuidV4;
use Throwable;
use Webmozart\Assert\Assert;

use function Safe\base64_decode;

class CertificatePair implements CustomCertificate
{
    private string $certificate;

    private string $privateKey;

    public function __construct(string $certificate, string $privateKey)
    {
        $this->certificate = $certificate;
        $this->privateKey = $privateKey;
    }

    public static function decode(string $certificate, string $privateKey): self
    {
        return new self(base64_decode($certificate, true), base64_decode($privateKey, true));
    }

    public static function fromSchema(ResourceSslSchema $schema): self
    {
        Assert::notNull($schema->certificate);
        Assert::notNull($schema->key);

        return new self($schema->certificate, $schema->key);
    }

    public function getCertificate(): string
    {
        return $this->certificate;
    }

    public function getPrivateKey(): string
    {
        return $this->privateKey;
    }

    public function setPrivateKey(string $privateKey): void
    {
        $this->privateKey = $privateKey;
    }

    public function isPrivateKeyUuid(): bool
    {
        try {
            $uuid = Uuid::fromString($this->privateKey);

            return $uuid instanceof UuidV4;
        } catch (Throwable) {
            return false;
        }
    }
}
