<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\LetsEncrypt;

use Cdn77\NxgApi\Repository\LetsEncrypt\TaskRepository;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=TaskRepository::class)
 * @ORM\Table(name="queue_task", schema="letsencrypt")
 */
class Task
{
    use UuidIdentified;

    /**
     * @ORM\OneToOne(targetEntity="Request")
     * @ORM\JoinColumn(name="request_id", referencedColumnName="id")
     *
     * @var Request
     */
    private $request;

    /**
     * @ORM\Column(type="datetimetz_immutable", name="created_at")
     *
     * @var DateTimeImmutable
     */
    private $createdAt;

    /**
     * @ORM\Column(type="datetimetz_immutable", name="run_at")
     *
     * @var DateTimeImmutable
     */
    private $runAt;

    public function __construct(Request $request, DateTimeImmutable $createdAt, DateTimeImmutable $runAt)
    {
        $this->request = $request;
        $this->createdAt = $createdAt;
        $this->runAt = $runAt;
    }

    public function getRequest(): Request
    {
        return $this->request;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getRunAt(): DateTimeImmutable
    {
        return $this->runAt;
    }
}
