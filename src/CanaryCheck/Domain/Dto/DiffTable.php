<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\CanaryCheck\Domain\Dto;

use Cdn77\NxgApi\Command\Font;
use Symfony\Component\Console\Helper\TableCell;
use Symfony\Component\Console\Helper\TableSeparator;

use function array_splice;
use function count;
use function sprintf;

final class DiffTable
{
    private const ROWS_LIMIT = 20;

    public string $title;

    /** @var array<array<int|string|TableCell>> */
    private array $rows;

    public function __construct(string $title)
    {
        $this->title = Font::red($title);
    }

    /** @param array<int|string> $row */
    public function addDiffRow(array $row): void
    {
        $this->rows[] = $row;
    }

    /** @return array<array<int|string|TableCell>|TableSeparator> */
    public function getRows(): array
    {
        $rowCount = count($this->rows);

        if ($rowCount < self::ROWS_LIMIT) {
            return $this->rows;
        }

        $rows = array_splice($this->rows, 0, self::ROWS_LIMIT);

        $rows[] = new TableSeparator();
        $rows[] = $this->getFooter($rowCount);

        return $rows;
    }

    /** @return array<TableCell> */
    private function getFooter(int $count): array
    {
        return [
            new TableCell(
                sprintf(
                    'Only first %d is shown, another %d remaining',
                    self::ROWS_LIMIT,
                    $count - self::ROWS_LIMIT,
                ),
                ['colspan' => 3],
            ),
        ];
    }
}
