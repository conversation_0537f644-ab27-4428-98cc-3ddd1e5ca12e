<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\CanaryCheck\Domain\Dto;

use function count;

final class Errors
{
    /** @var array<string, array<string, int>> */
    public array $items = [];

    public function add(string $path, string $error): void
    {
        if (isset($this->items[$path][$error])) {
            $this->items[$path][$error]++;
        } else {
            $this->items[$path][$error] = 1;
        }
    }

    public function isEmpty(): bool
    {
        return count($this->items) === 0;
    }
}
