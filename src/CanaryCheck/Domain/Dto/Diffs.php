<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\CanaryCheck\Domain\Dto;

use function is_bool;
use function is_scalar;
use function Safe\json_encode;
use function strval;

final class Diffs
{
    /** @var array<string, array<Diff>> */
    public array $items = [];

    public int $count = 0;

    /**
     * @param int|string $id
     * @param mixed $valueCanary
     * @param mixed $valueProduction
     */
    public function add($id, string $propertyName, $valueCanary, $valueProduction): void
    {
        $this->items[$propertyName][] = new Diff(
            $id,
            $propertyName,
            $this->convertToString($valueCanary),
            $this->convertToString($valueProduction),
        );

        $this->count++;
    }

    /** @param mixed $value */
    private function convertToString($value): string
    {
        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }

        if (is_scalar($value)) {
            return strval($value);
        }

        return json_encode($value);
    }
}
