<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\CanaryCheck\Domain\Dto;

final class Diff
{
    /** @var int|string */
    private $id;

    private string $propertyName;

    private string $production;

    private string $canary;

    /** @param int|string $id */
    public function __construct($id, string $propertyName, string $canary, string $production)
    {
        $this->id = $id;
        $this->propertyName = $propertyName;
        $this->canary = $canary;
        $this->production = $production;
    }

    /** @return array<int, int|string> */
    public function toArrayForList(): array
    {
        return [
            $this->id,
            $this->canary,
            $this->production,
        ];
    }

    /** @return array<string> */
    public function toArrayForDetail(): array
    {
        return [
            $this->propertyName,
            $this->canary,
            $this->production,
        ];
    }
}
