<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\CanaryCheck\Domain\Comparison;

use Cdn77\NxgApi\CanaryCheck\Domain\Dto\DiffTable;

use function array_diff;
use function array_key_exists;
use function array_keys;
use function array_splice;
use function count;
use function implode;
use function reset;
use function sprintf;

final class ComparisonList extends Comparison
{
    /**
     * @param array<int|string, mixed> $canary
     * @param array<int|string, mixed> $production
     */
    public function __construct(string $path, array $canary, array $production)
    {
        parent::__construct($path, $this->reindex($canary), $this->reindex($production));
    }

    public function compare(): void
    {
        $this->compareIndexes();

        foreach ($this->production as $id => $detailProduction) {
            if (! array_key_exists($id, $this->canary)) {
                continue;
            }

            $this->compareDetail(
                $id,
                $this->canary[$id],
                $detailProduction,
            );

            $this->count++;
        }
    }

    /** @return array<int<0, max>, DiffTable> */
    public function getDiffs(): array
    {
        $result = [];

        foreach ($this->diffs->items as $propertyName => $propertyDiffs) {
            $table = new DiffTable(sprintf('%sx %s', count($propertyDiffs), $propertyName));

            foreach ($propertyDiffs as $diff) {
                $table->addDiffRow($diff->toArrayForList());
            }

            $result[] = $table;
        }

        return $result;
    }

    /**
     * @param array<int|string, mixed> $data
     *
     * @return array<int|string, mixed>
     */
    private function reindex(array $data): array
    {
        $reindexKey = array_key_exists('uid', reset($data)) ? 'uid' : 'id';

        $result = [];

        foreach ($data as $items) {
            $result[$items[$reindexKey]] = $items;
        }

        return $result;
    }

    private function compareIndexes(): void
    {
        $canaryKeys = array_keys($this->canary);
        $productionKeys = array_keys($this->production);

        $canaryMissing = array_diff($productionKeys, $canaryKeys);
        if (count($canaryMissing) > 0) {
            $this->errors->add(
                $this->path,
                sprintf(
                    'Missing items in canary. Count: %d. First 20 IDs: %s',
                    count($canaryMissing),
                    implode(',', array_splice($canaryMissing, 0, 20)),
                ),
            );
        }

        $productionMissing = array_diff($canaryKeys, $productionKeys);
        if (count($productionMissing) <= 0) {
            return;
        }

        $this->errors->add(
            $this->path,
            sprintf(
                'Missing items in production. Count: %d. First 20 IDs: %s',
                count($productionMissing),
                implode(',', array_splice($productionMissing, 0, 20)),
            ),
        );
    }
}
