<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\CanaryCheck\Domain\Comparison;

use Cdn77\NxgApi\CanaryCheck\Domain\Dto\DiffTable;

use function sprintf;

final class ComparisonDetail extends Comparison
{
    /**
     * @param array<int|string, mixed> $canary
     * @param array<int|string, mixed> $production
     */
    public function __construct(string $path, array $canary, array $production)
    {
        parent::__construct($path, $canary, $production);
    }

    public function compare(): void
    {
        $this->compareDetail(0, $this->canary, $this->production);
    }

    /** @return array<DiffTable> */
    public function getDiffs(): array
    {
        $table = new DiffTable(sprintf('Diffs for %s', $this->path));

        foreach ($this->diffs->items as $propertyDiffs) {
            foreach ($propertyDiffs as $propertyDiff) {
                $table->addDiffRow($propertyDiff->toArrayForDetail());
            }
        }

        return [$table];
    }
}
