<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\CanaryCheck\Domain\Comparison;

use Cdn77\NxgApi\CanaryCheck\Domain\Dto\Diffs;
use Cdn77\NxgApi\CanaryCheck\Domain\Dto\DiffTable;
use Cdn77\NxgApi\CanaryCheck\Domain\Dto\Errors;
use Webmozart\Assert\Assert;

use function array_diff;
use function array_key_exists;
use function array_keys;
use function count;
use function implode;
use function sprintf;

abstract class Comparison
{
    private const MISSING_PROPERTY = 'MISSING';

    public string $path;

    /** @var array<int|string, mixed> */
    public array $canary = [];

    /** @var array<int|string, mixed> */
    public array $production = [];

    public int $count = 0;

    public Diffs $diffs;

    public Errors $errors;

    /**
     * @param array<int|string, mixed> $canary
     * @param array<int|string, mixed> $production
     */
    public function __construct(string $path, array $canary = [], array $production = [])
    {
        $this->path = $path;
        $this->canary = $canary;
        $this->production = $production;
        $this->diffs = new Diffs();
        $this->errors = new Errors();
    }

    abstract public function compare(): void;

    /** @return array<DiffTable> */
    abstract public function getDiffs(): array;

    /**
     * @param int|string $id
     * @param array<int|string, mixed> $canary
     * @param array<int|string, mixed> $production
     */
    public function compareDetail($id, array $canary, array $production): void
    {
        $this->findExtraPropertiesInCanary($canary, $production);

        foreach ($production as $propertyName => $valueProduction) {
            Assert::string($propertyName);
            $valueCanary = $this->getCanaryValueForDetailProperty($propertyName, $canary);

            if ($this instanceof ComparisonDetail) {
                $this->count++;
            }

            if ($valueProduction === $valueCanary) {
                continue;
            }

            $this->errors->add($this->path, 'There are some differences when comparing detail.');

            $this->diffs->add($id, $propertyName, $valueCanary, $valueProduction);
        }
    }

    /**
     * @param array<int|string, mixed> $canary
     * @param array<int|string, mixed> $production
     */
    protected function findExtraPropertiesInCanary(array $canary, array $production): void
    {
        $extraCanaryProperties = array_diff(array_keys($canary), array_keys($production));
        if (count($extraCanaryProperties) <= 0) {
            return;
        }

        $this->errors->add(
            $this->path,
            sprintf('Canary has these extra properties: %s', implode(',', $extraCanaryProperties)),
        );
    }

    /** @param array<int|string, mixed> $detailCanary */
    protected function getCanaryValueForDetailProperty(string $propertyName, array $detailCanary): mixed
    {
        if (array_key_exists($propertyName, $detailCanary)) {
            return $detailCanary[$propertyName];
        }

        $this->errors->add($this->path, sprintf('Missing property "%s" in canary detail.', $propertyName));

        return self::MISSING_PROPERTY;
    }
}
