<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\CanaryCheck\Domain;

use Cdn77\NxgApi\CanaryCheck\Domain\Comparison\Comparison;
use Cdn77\NxgApi\CanaryCheck\Domain\Comparison\ComparisonDetail;
use Cdn77\NxgApi\CanaryCheck\Domain\Comparison\ComparisonList;
use Cdn77\NxgApi\CanaryCheck\Domain\Dto\Errors;
use Cdn77\NxgApi\Command\Font;
use Cdn77\NxgApi\Command\OutputWriter;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Throwable;

use function array_keys;
use function count;
use function sprintf;

final class Comparator
{
    public const NAME = 'canary:check';
    public const PATH_TYPE_LIST = 'list';
    public const PATH_TYPE_DETAIL = 'detail';

    public const PATHS = [
        'internal/server' => self::PATH_TYPE_LIST,
        'internal/servers/statuses' => self::PATH_TYPE_LIST,
        'ngx-conf-gen/resources' => self::PATH_TYPE_LIST,
        'ngx-conf-gen/resources/1464091238' => self::PATH_TYPE_DETAIL, //TT
        'ngx-conf-gen/resources/1480222913' => self::PATH_TYPE_DETAIL, //Xvid
    ];

    private const CANARY = 'canary';
    private const PRODUCTION = 'production';

    private const RESULT_TABLE_HEADER_CANARY = 'Canary';
    private const RESULT_TABLE_HEADER_PRODUCTION = 'Production';
    private const RESULT_TABLE_HEADER_COMPARISONS = 'Comparisons';
    private const RESULT_TABLE_HEADER_DIFF_TYPES = 'Diff types';
    private const RESULT_TABLE_HEADER_DIFFS = 'Diffs';
    private const RESULT_TABLE_HEADERS = [
        self::RESULT_TABLE_HEADER_CANARY,
        self::RESULT_TABLE_HEADER_PRODUCTION,
        self::RESULT_TABLE_HEADER_COMPARISONS,
        self::RESULT_TABLE_HEADER_DIFF_TYPES,
        self::RESULT_TABLE_HEADER_DIFFS,
    ];

    /** @var array<string> */
    private array $hosts;

    private HttpClientInterface $client;

    private OutputWriter $outputWriter;

    private Errors $errors;

    public function __construct(string $urlCanary, string $urlProduction, HttpClientInterface $client)
    {
        $this->client = $client;
        $this->client = HttpClient::create(['verify_peer' => false]);

        $this->errors = new Errors();
        $this->hosts = [
            self::CANARY => $urlCanary,
            self::PRODUCTION => $urlProduction,
        ];
    }

    public function compare(OutputWriter $outputWriter): Errors
    {
        $this->outputWriter = $outputWriter;

        $responses = $this->callUrlsGetResponses();

        $this->outputWriter->separator();

        $this->compareResponses($responses);
        $this->renderProcessingErrors();

        return $this->errors;
    }

    /** @param array<Comparison> $responses */
    private function compareResponses($responses): void
    {
        foreach ($responses as $comparison) {
            $comparison->compare();

            $this->renderResult($comparison);

            $this->renderComparisonErrors($comparison);

            $this->outputWriter->separator();
        }
    }

    /** @return array<Comparison> */
    private function callUrlsGetResponses(): array
    {
        $responses = [];

        foreach (self::PATHS as $path => $responseType) {
            $promises = $this->preparePathPromises($path);

            try {
                $responses[] = $this->prepareComparison(
                    $path,
                    $promises[self::CANARY]->toArray(),
                    $promises[self::PRODUCTION]->toArray(),
                );
            } catch (Throwable $e) {
                $this->errors->add($path, $e->getMessage());
                $this->outputWriter->messageRed(
                    sprintf('Error response for path %s: %s', $path, $e->getMessage()),
                );
            }
        }

        return $responses;
    }

    /** @return array<string, ResponseInterface> */
    private function preparePathPromises(string $path): array
    {
        $promises = [];

        foreach ($this->hosts as $hostType => $host) {
            $url = $this->getUrl($host, $path);
            $this->outputWriter->message(sprintf('Calling request for URL %s', $url));
            $promises[$hostType] = $this->client->request('GET', $url);
        }

        return $promises;
    }

    /**
     * @param array<int|string, mixed> $canary
     * @param array<int|string, mixed> $production
     */
    private function prepareComparison(string $path, array $canary, array $production): Comparison
    {
        return self::PATHS[$path] === self::PATH_TYPE_DETAIL
            ? new ComparisonDetail($path, $canary, $production)
            : new ComparisonList($path, $canary, $production);
    }

    private function renderResult(Comparison $comparison): void
    {
        $canaryCount = count($comparison->canary);
        $productionCount = count($comparison->production);
        $diffTypeCount = count($comparison->diffs->items);

        $countsOk = $canaryCount === $productionCount;
        $comparisonsOk = $productionCount === $comparison->count;

        if (! $countsOk) {
            $this->errors->add($comparison->path, 'Different amount of items.');
        }

        if (! $comparisonsOk) {
            $this->errors->add($comparison->path, 'Wrong amount of comparisons.');
        }

        $this->outputWriter->table(
            sprintf('Stats for %s', $comparison->path),
            self::RESULT_TABLE_HEADERS,
            [
                [
                    $countsOk
                        ? Font::green($canaryCount)
                        : Font::red($canaryCount),
                    $countsOk
                        ? Font::green($productionCount)
                        : Font::red($productionCount),
                    $comparisonsOk
                        ? Font::green($comparison->count)
                        : Font::red($comparison->count),
                    $diffTypeCount === 0
                        ? Font::green(0)
                        : Font::red($diffTypeCount),
                    $diffTypeCount === 0
                        ? Font::green(0)
                        : Font::red($comparison->diffs->count),
                ],
            ],
        );

        if ($diffTypeCount === 0) {
            return;
        }

        foreach ($comparison->getDiffs() as $table) {
            $this->outputWriter->table(
                $table->title,
                ['Item', 'Canary', 'Production'],
                $table->getRows(),
            );
        }
    }

    private function renderComparisonErrors(Comparison $comparison): void
    {
        if ($comparison->errors->isEmpty()) {
            return;
        }

        $this->errors->add(
            $comparison->path,
            sprintf('Comparison failed for path "%s", see specific errors above.', $comparison->path),
        );

        $rows = [];
        foreach ($comparison->errors->items[$comparison->path] as $error => $count) {
            $rows[] = [$count, Font::red($error)];
        }

        $this->outputWriter->table(
            Font::red(sprintf('Errors for path %s: ', $comparison->path)),
            [Font::red('Count'), Font::red('Error')],
            $rows,
        );
    }

    private function renderProcessingErrors(): void
    {
        if ($this->errors->isEmpty()) {
            return;
        }

        foreach ($this->errors->items as $path => $errors) {
            $this->outputWriter->table(
                Font::red(sprintf('Errors for path %s: ', $path)),
                [],
                [array_keys($errors)],
            );
        }
    }

    private function getUrl(string $host, string $path): string
    {
        return sprintf('https://%s/%s', $host, $path);
    }
}
