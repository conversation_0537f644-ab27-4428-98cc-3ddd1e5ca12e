<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\CanaryCheck\Application\Console;

use Cdn77\NxgApi\CanaryCheck\Domain\Comparator;
use Cdn77\NxgApi\Command\Font;
use Cdn77\NxgApi\Command\OutputWriter;
use <PERSON>ymfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;

use function array_keys;
use function count;
use function implode;
use function sprintf;

final class CanaryCommand extends Command
{
    public const NAME = 'canary:check';

    private Comparator $comparator;

    public function __construct(Comparator $comparator)
    {
        parent::__construct();

        $this->comparator = $comparator;
    }

    protected function configure(): void
    {
        parent::configure();

        $this->setName(self::NAME);
        $this->setDescription('Check the results of important URLs from canary and production NXG API.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $outputWriter = new OutputWriter($output);
        $outputWriter->message(sprintf(
            'Canary check started for these %d paths: %s',
            count($this->comparator::PATHS),
            implode(', ', array_keys($this->comparator::PATHS)),
        ), 1);

        try {
            $errors = $this->comparator->compare($outputWriter);
        } catch (Throwable $e) {
            $outputWriter->messageRed(sprintf('The canary check failed because of an exception: %s', $e->getMessage()));

            return 1;
        }

        if (! $errors->isEmpty()) {
            $outputWriter->messageRed('The canary check failed.', 2);

            return 1;
        }

        // This check is for situation when we temporarily disable some PATHS and forget to enable it again
        // @phpstan-ignore notIdentical.alwaysFalse
        if (count($this->comparator::PATHS) !== 5) {
            $outputWriter->messageYellow(
                'The canary check failed because there is a weird amount of paths to check. Otherwise all is fine.',
                2,
            );

            return 1;
        }

        $outputWriter->message(Font::green('The canary check finished successfully.'), 2);

        return 0;
    }
}
