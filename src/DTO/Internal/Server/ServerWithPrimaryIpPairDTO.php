<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\DTO\Internal\Server;

use Cdn77\NxgApi\Entity\Legacy\Ip;
use Cdn77\NxgApi\Entity\Legacy\Server;

class ServerWithPrimaryIpPairDTO
{
    /** @var Server */
    private $server;

    /** @var Ip */
    private $primaryIp;

    public function __construct(Server $server, Ip $primaryIp)
    {
        $this->server = $server;
        $this->primaryIp = $primaryIp;
    }

    public function getServer(): Server
    {
        return $this->server;
    }

    public function getPrimaryIp(): Ip
    {
        return $this->primaryIp;
    }
}
