<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\DTO\Internal\Server;

class ServerCertificateStatusDTO
{
    /** @var int */
    private $resourceId;

    /** @var int */
    private $fileIndex;

    /** @var bool */
    private $ready;

    public function __construct(int $resourceId, int $fileIndex, bool $ready)
    {
        $this->resourceId = $resourceId;
        $this->fileIndex = $fileIndex;
        $this->ready = $ready;
    }

    public function getResourceId(): int
    {
        return $this->resourceId;
    }

    public function getFileIndex(): int
    {
        return $this->fileIndex;
    }

    public function isReady(): bool
    {
        return $this->ready;
    }
}
