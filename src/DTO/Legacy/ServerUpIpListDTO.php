<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\DTO\Legacy;

class ServerUpIpListDTO
{
    /** @var int */
    private $locationId;

    /** @var int */
    private $serverId;

    /** @var bool */
    private $inDns;

    /** @var bool */
    private $ok;

    /** @var bool */
    private $up;

    public function __construct(int $locationId, int $serverId, bool $inDns, bool $ok, bool $up)
    {
        $this->locationId = $locationId;
        $this->serverId = $serverId;
        $this->inDns = $inDns;
        $this->ok = $ok;
        $this->up = $up;
    }

    public function getLocationId(): int
    {
        return $this->locationId;
    }

    public function getServerId(): int
    {
        return $this->serverId;
    }

    public function isInDns(): bool
    {
        return $this->inDns;
    }

    public function isOk(): bool
    {
        return $this->ok;
    }

    public function isUp(): bool
    {
        return $this->up;
    }
}
