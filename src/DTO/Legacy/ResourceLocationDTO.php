<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\DTO\Legacy;

use Cdn77\NxgApi\Entity\Legacy\Location;

class ResourceLocationDTO
{
    /** @var Location */
    private $location;

    /** @var string */
    private $locationId;

    /** @var bool */
    private $active;

    /** @var bool */
    private $assigned;

    public function __construct(Location $location, string $locationId, bool $active, bool $assigned)
    {
        $this->location = $location;
        $this->locationId = $locationId;
        $this->active = $active;
        $this->assigned = $assigned;
    }

    public function getLocation(): Location
    {
        return $this->location;
    }

    public function getLocationId(): string
    {
        return $this->locationId;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function isAssigned(): bool
    {
        return $this->assigned;
    }
}
