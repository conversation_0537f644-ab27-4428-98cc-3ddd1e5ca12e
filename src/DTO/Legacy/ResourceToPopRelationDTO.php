<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\DTO\Legacy;

class ResourceToPopRelationDTO
{
    /** @var int */
    private $resourceId;

    /** @var int */
    private $popId;

    public function __construct(int $resourceId, int $popId)
    {
        $this->resourceId = $resourceId;
        $this->popId = $popId;
    }

    public function getResourceId(): int
    {
        return $this->resourceId;
    }

    public function getPopId(): int
    {
        return $this->popId;
    }
}
