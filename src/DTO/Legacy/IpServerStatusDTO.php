<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\DTO\Legacy;

use Cdn77\NxgApi\Entity\Legacy\Ip;
use Cdn77\NxgApi\Entity\Legacy\Server;

class IpServerStatusDTO
{
    /** @var Ip */
    private $ip;

    /** @var bool */
    private $up;

    /** @var bool */
    private $paused;

    /** @var bool */
    private $forced;

    /** @var bool */
    private $autoUp;

    /** @var bool|null */
    private $forcedUp;

    public function __construct(Ip $ip, bool $up, bool $paused, bool $forced, bool $autoUp, bool|null $forcedUp)
    {
        $this->ip = $ip;
        $this->up = $up;
        $this->paused = $paused;
        $this->forced = $forced;
        $this->autoUp = $autoUp;
        $this->forcedUp = $forcedUp;
    }

    public function getIp(): Ip
    {
        return $this->ip;
    }

    public function getServer(): Server
    {
        return $this->ip->getServer();
    }

    public function isUp(): bool
    {
        return $this->up;
    }

    public function isPaused(): bool
    {
        return $this->paused;
    }

    public function isForced(): bool
    {
        return $this->forced;
    }

    public function isAutoUp(): bool
    {
        return $this->autoUp;
    }

    public function getForcedUp(): bool|null
    {
        return $this->forcedUp;
    }
}
