<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\DTO\NgxConfGen;

class ServerDTO
{
    /** @var string */
    private $locationId;

    /** @var string */
    private $ip;

    /** @var int|null */
    private $maxCacheSize;

    /** @var int */
    private $keysSize;

    /** @var int */
    private $uid;

    /** @var bool */
    private $backup;

    /** @var int */
    private $workerCount;

    /** @var int */
    private $driveCount;

    /** @var bool */
    private $http2;

    public function __construct(
        string $locationId,
        string $ip,
        int|null $maxCacheSize,
        int $keysSize,
        int $uid,
        bool $backup,
        int $workerCount,
        int $driveCount,
        bool $http2,
    ) {
        $this->locationId = $locationId;
        $this->ip = $ip;
        $this->maxCacheSize = $maxCacheSize;
        $this->keysSize = $keysSize;
        $this->uid = $uid;
        $this->backup = $backup;
        $this->workerCount = $workerCount;
        $this->driveCount = $driveCount;
        $this->http2 = $http2;
    }

    public function getLocationId(): string
    {
        return $this->locationId;
    }

    public function getIp(): string
    {
        return $this->ip;
    }

    public function getMaxCacheSize(): int|null
    {
        return $this->maxCacheSize;
    }

    public function getKeysSize(): int
    {
        return $this->keysSize;
    }

    public function getUid(): int
    {
        return $this->uid;
    }

    public function hasBackup(): bool
    {
        return $this->backup;
    }

    public function getWorkerCount(): int
    {
        return $this->workerCount;
    }

    public function getDriveCount(): int
    {
        return $this->driveCount;
    }

    public function hasHttp2(): bool
    {
        return $this->http2;
    }
}
